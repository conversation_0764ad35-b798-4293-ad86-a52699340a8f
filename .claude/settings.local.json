{"permissions": {"allow": ["Bash(rails:*)", "Bash(git checkout:*)", "Bash(find:*)", "Bash(bundle exec rails:*)", "Bash(grep:*)", "Bash(git add:*)", "Bash(bundle exec rspec:*)", "Bash(RAILS_ENV=test bundle exec rails db:migrate)", "Bash(RAILS_ENV=test bundle exec rails db:drop)", "Bash(RAILS_ENV=test bundle exec rails db:create)", "Bash(bundle install)", "<PERSON><PERSON>(env)", "Bash(ls:*)", "Bash(git commit:*)", "Bash(git push:*)"], "deny": []}}