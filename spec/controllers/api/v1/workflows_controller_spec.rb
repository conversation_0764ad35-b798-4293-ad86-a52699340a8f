# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Api::V1::WorkflowsController, type: :controller do
  let(:tenant) { create(:tenant) }
  let(:user) { create(:user, tenant: tenant) }
  let(:campaign) { create(:campaign, tenant: tenant, created_by: user) }

  before do
    sign_in user
    ActsAsTenant.current_tenant = tenant
  end

  describe 'GET #status' do
    it 'returns active workflows status' do
      workflow1 = create(:agent_workflow, :running, tenant: tenant, campaign: campaign)
      workflow2 = create(:agent_workflow, :pending, tenant: tenant, campaign: campaign)
      create(:agent_workflow, :completed, tenant: tenant, campaign: campaign)

      get :status

      expect(response).to have_http_status(:success)

      json_response = JSON.parse(response.body)
      expect(json_response['workflows'].count).to eq(2)
      expect(json_response['total_active']).to eq(2)
      expect(json_response).to have_key('last_updated')
    end
  end

  describe 'GET #metrics' do
    it 'returns workflow metrics' do
      create(:agent_workflow, :completed, tenant: tenant, campaign: campaign)
      create(:agent_workflow, :failed, tenant: tenant, campaign: campaign)
      create(:agent_workflow, :running, tenant: tenant, campaign: campaign)

      get :metrics

      expect(response).to have_http_status(:success)

      json_response = JSON.parse(response.body)
      metrics = json_response['metrics']

      expect(metrics['total_workflows']).to eq(3)
      expect(metrics['active_workflows']).to eq(1)
      expect(metrics['completed_workflows']).to eq(1)
      expect(metrics['failed_workflows']).to eq(1)
      expect(metrics['success_rate']).to eq(50.0)
    end
  end

  describe 'GET #show' do
    it 'returns workflow details' do
      workflow = create(:agent_workflow, :running, tenant: tenant, campaign: campaign)

      get :show, params: { id: workflow.id }

      expect(response).to have_http_status(:success)

      json_response = JSON.parse(response.body)
      workflow_data = json_response['workflow']

      expect(workflow_data['id']).to eq(workflow.id)
      expect(workflow_data['status']).to eq('running')
      expect(workflow_data['campaign']['name']).to eq(campaign.name)
    end
  end

  describe 'PATCH #cancel' do
    context 'with running workflow' do
      it 'cancels the workflow successfully' do
        workflow = create(:agent_workflow, :running, tenant: tenant, campaign: campaign)

        patch :cancel, params: { id: workflow.id }

        expect(response).to have_http_status(:success)

        json_response = JSON.parse(response.body)
        expect(json_response['success']).to be true
        expect(json_response['workflow']['status']).to eq('cancelled')
      end
    end

    context 'with completed workflow' do
      it 'returns error for non-cancellable workflow' do
        workflow = create(:agent_workflow, :completed, tenant: tenant, campaign: campaign)

        patch :cancel, params: { id: workflow.id }

        expect(response).to have_http_status(:unprocessable_entity)

        json_response = JSON.parse(response.body)
        expect(json_response['success']).to be false
      end
    end
  end

  describe 'POST #retry' do
    context 'with failed workflow' do
      it 'retries the workflow successfully' do
        workflow = create(:agent_workflow, :failed, tenant: tenant, campaign: campaign)

        post :retry, params: { id: workflow.id }

        expect(response).to have_http_status(:success)

        json_response = JSON.parse(response.body)
        expect(json_response['success']).to be true
        expect(json_response).to have_key('new_workflow_id')
      end
    end

    context 'with running workflow' do
      it 'returns error for non-retryable workflow' do
        workflow = create(:agent_workflow, :running, tenant: tenant, campaign: campaign)

        post :retry, params: { id: workflow.id }

        expect(response).to have_http_status(:unprocessable_entity)

        json_response = JSON.parse(response.body)
        expect(json_response['success']).to be false
      end
    end
  end
end
