# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'dashboard/index.html.erb', type: :view do
  let(:tenant) { create(:tenant) }
  let(:user) { create(:user, tenant: tenant) }
  let(:campaigns) { create_list(:campaign, 3, tenant: tenant, created_by: user) }

  before do
    ActsAsTenant.current_tenant = tenant
    allow(view).to receive(:current_user).and_return(user)
    allow(view).to receive(:user_signed_in?).and_return(true)

    # Set up instance variables that the view expects
    assign(:campaign_stats, {
      total: 5,
      active: 3,
      draft: 1,
      completed: 1,
      paused: 0
    })

    assign(:budget_stats, {
      total_budget: 50000.0,
      active_budget: 30000.0,
      spent_budget: 15000.0,
      remaining_budget: 35000.0
    })

    assign(:platform_stats, {
      email_campaigns: 2,
      social_campaigns: 2,
      multi_channel: 1,
      seo_campaigns: 0
    })

    assign(:vibe_metrics, {
      overall_vibe_score: 7.8,
      sentiment_distribution: { positive: 68, neutral: 22, negative: 10 },
      trending_vibes: [ "Authentic", "Optimistic", "Innovative", "Trustworthy", "Inspiring" ],
      vibe_performance_trend: [ 7.1, 7.3, 7.8, 8.0, 8.2, 8.1, 8.4 ],
      total_analyzed: 5
    })

    assign(:emotional_resonance, {
      primary_emotion: "Joy",
      emotion_intensity: 8.1,
      emotion_distribution: { joy: 35, trust: 28, anticipation: 20, surprise: 12, other: 5 },
      resonance_score: 8.4,
      engagement_correlation: 0.72
    })

    assign(:authenticity_scores, {
      average_score: 7.8,
      flagged_campaigns: 0,
      approval_rate: 85.2,
      improvement_trend: "+12%",
      risk_assessment: "Low"
    })

    assign(:cultural_alignment, {
      alignment_score: 8.6,
      cultural_moments_captured: 3,
      trending_topics: [ "Sustainability", "Digital Wellness", "Authentic Storytelling" ],
      cultural_fit_rating: "Excellent",
      regional_performance: { north_america: 8.8, europe: 8.2, asia_pacific: 7.9 }
    })

    assign(:recent_campaigns, campaigns)
    assign(:active_campaigns, campaigns.first(2))
    assign(:performance_metrics, {
      success_rate: 85.0,
      avg_roi: 245,
      engagement_rate: 8.2,
      conversion_rate: 4.5,
      cost_per_acquisition: 45.67
    })

    assign(:dashboard_settings, {
      layout_preference: "standard",
      visible_widgets: [ "campaigns", "vibe_metrics", "performance", "budget" ],
      refresh_interval: 30000,
      theme: "light"
    })
  end

  describe 'header section' do
    it 'displays personalized greeting' do
      render
      expect(rendered).to have_content("Good")
      expect(rendered).to have_content(user.email.split('@').first.titleize)
      expect(rendered).to have_content("vibe marketing intelligence dashboard")
    end

    it 'includes dashboard controls' do
      render
      expect(rendered).to have_select('', options: [ 'Last 7 days', 'Last 30 days', 'Last 90 days' ])
      expect(rendered).to have_button('', title: 'Refresh Dashboard')
      expect(rendered).to have_link('New Campaign', href: new_campaign_path)
    end
  end

  describe 'KPI cards' do
    it 'displays overall vibe score card' do
      render
      expect(rendered).to have_content('Overall Vibe Score')
      expect(rendered).to have_content('7.8/10')
      expect(rendered).to have_content('+15.2%')
      expect(rendered).to have_content('Excellent engagement')
    end

    it 'displays emotional resonance card' do
      render
      expect(rendered).to have_content('Emotional Resonance')
      expect(rendered).to have_content('8.4/10')
      expect(rendered).to have_content('Joy dominant')
    end

    it 'displays authenticity score card' do
      render
      expect(rendered).to have_content('Authenticity Score')
      expect(rendered).to have_content('7.8/10')
      expect(rendered).to have_content('85.2% approval rate')
      expect(rendered).to have_content('Low')
    end

    it 'displays cultural alignment card' do
      render
      expect(rendered).to have_content('Cultural Alignment')
      expect(rendered).to have_content('8.6/10')
      expect(rendered).to have_content('3 moments captured')
      expect(rendered).to have_content('Excellent')
    end
  end

  describe 'vibe performance trend section' do
    it 'displays vibe performance trend chart placeholder' do
      render
      expect(rendered).to have_content('Vibe Performance Trend')
      expect(rendered).to have_content('Last 7 days')
      expect(rendered).to have_content('Interactive chart showing vibe performance over time')
      expect(rendered).to have_content('Average score: 7.8/10')
    end
  end

  describe 'sentiment distribution section' do
    it 'displays sentiment distribution bars' do
      render
      expect(rendered).to have_content('Sentiment Distribution')
      expect(rendered).to have_content('Positive')
      expect(rendered).to have_content('68%')
      expect(rendered).to have_content('Neutral')
      expect(rendered).to have_content('22%')
      expect(rendered).to have_content('Negative')
      expect(rendered).to have_content('10%')
    end

    it 'includes progress bars with correct widths' do
      render
      expect(rendered).to include('style="width: 68%"')
      expect(rendered).to include('style="width: 22%"')
      expect(rendered).to include('style="width: 10%"')
    end
  end

  describe 'recent campaigns section' do
    context 'with campaigns' do
      it 'displays recent campaigns' do
        render
        expect(rendered).to have_content('Recent Campaign Performance')
        campaigns.each do |campaign|
          expect(rendered).to have_content(campaign.name)
          expect(rendered).to have_content(campaign.status.titleize)
        end
      end

      it 'includes vibe scores for campaigns' do
        render
        expect(rendered).to include('Vibe:')
        expect(rendered).to include('/10')
      end

      it 'includes view links for campaigns' do
        render
        campaigns.each do |campaign|
          expect(rendered).to have_link('View', href: campaign_path(campaign))
        end
      end
    end

    context 'with no campaigns' do
      before { assign(:recent_campaigns, []) }

      it 'displays empty state' do
        render
        expect(rendered).to have_content('No campaigns yet')
        expect(rendered).to have_content('Create your first vibe-optimized campaign to get started.')
        expect(rendered).to have_link('Create Campaign', href: new_campaign_path)
      end
    end
  end

  describe 'trending vibes section' do
    it 'displays trending vibes with rankings' do
      render
      expect(rendered).to have_content('Trending Vibes')
      trending_vibes = assigns(:vibe_metrics)[:trending_vibes]
      trending_vibes.each_with_index do |vibe, index|
        expect(rendered).to have_content((index + 1).to_s)
        expect(rendered).to have_content(vibe)
      end
    end
  end

  describe 'emotional breakdown section' do
    it 'displays emotional breakdown with percentages' do
      render
      expect(rendered).to have_content('Emotional Breakdown')
      emotion_distribution = assigns(:emotional_resonance)[:emotion_distribution]
      emotion_distribution.each do |emotion, percentage|
        expect(rendered).to have_content(emotion.to_s.capitalize)
        expect(rendered).to have_content("#{percentage}%")
      end
    end
  end

  describe 'cultural moments section' do
    it 'displays cultural moments' do
      render
      expect(rendered).to have_content('Cultural Moments')
      trending_topics = assigns(:cultural_alignment)[:trending_topics]
      trending_topics.each do |topic|
        expect(rendered).to have_content(topic)
      end
    end
  end

  describe 'quick actions section' do
    it 'displays quick actions card' do
      render
      expect(rendered).to have_content('Ready to Create Magic?')
      expect(rendered).to have_content('Launch your next vibe-optimized campaign with AI-powered insights.')
      expect(rendered).to have_link('Create Vibe Campaign', href: new_campaign_path)
      expect(rendered).to have_link('View All Campaigns', href: campaigns_path)
    end
  end

  describe 'javascript enhancements' do
    it 'includes dashboard javascript for animations' do
      render
      expect(rendered).to include("data-dashboard-target=\"metric\"")
      expect(rendered).to include("document.addEventListener('DOMContentLoaded'")
      expect(rendered).to include("animate-pulse")
    end
  end

  describe 'responsive design elements' do
    it 'includes responsive grid classes' do
      render
      expect(rendered).to include('grid-cols-1 sm:grid-cols-2 lg:grid-cols-4')
      expect(rendered).to include('lg:col-span-2')
      expect(rendered).to include('flex-col lg:flex-row')
    end

    it 'includes responsive text sizes' do
      render
      expect(rendered).to include('text-3xl lg:text-4xl')
      expect(rendered).to include('text-lg')
      expect(rendered).to include('text-sm')
    end
  end

  describe 'accessibility features' do
    it 'includes proper ARIA labels and titles' do
      render
      expect(rendered).to include('title="Refresh Dashboard"')
    end

    it 'uses semantic HTML structure' do
      render
      expect(rendered).to have_css('h1')
      expect(rendered).to have_css('h2')
      expect(rendered).to have_css('h3')
    end

    it 'includes proper contrast colors' do
      render
      expect(rendered).to include('text-slate-900')
      expect(rendered).to include('text-slate-600')
      expect(rendered).to include('text-white')
    end
  end

  describe 'visual design elements' do
    it 'includes gradient backgrounds' do
      render
      expect(rendered).to include('bg-gradient-to-br')
      expect(rendered).to include('bg-gradient-to-r')
    end

    it 'includes shadow effects' do
      render
      expect(rendered).to include('shadow-lg')
      expect(rendered).to include('shadow-sm')
      expect(rendered).to include('shadow-xl')
    end

    it 'includes transition effects' do
      render
      expect(rendered).to include('transition-all')
      expect(rendered).to include('transition-colors')
      expect(rendered).to include('transition-shadow')
    end

    it 'includes hover effects' do
      render
      expect(rendered).to include('hover:bg-')
      expect(rendered).to include('hover:text-')
      expect(rendered).to include('hover:shadow-')
    end
  end
end
