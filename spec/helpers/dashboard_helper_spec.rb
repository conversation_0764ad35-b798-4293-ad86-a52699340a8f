# frozen_string_literal: true

require 'rails_helper'

RSpec.describe DashboardHelper, type: :helper do
  let(:tenant) { create(:tenant) }
  let(:user) { create(:user, tenant: tenant) }
  let(:campaign) { create(:campaign, tenant: tenant, created_by: user) }

  before do
    ActsAsTenant.current_tenant = tenant
    allow(helper).to receive(:current_tenant).and_return(tenant)
  end

  describe '#active_workflows_count' do
    it 'returns the count of active agent workflows' do
      create(:agent_workflow, :running, tenant: tenant, campaign: campaign)
      create(:agent_workflow, :pending, tenant: tenant, campaign: campaign)
      create(:agent_workflow, :completed, tenant: tenant, campaign: campaign)

      expect(helper.active_workflows_count).to eq(2)
    end

    it 'returns 0 when no active workflows exist' do
      create(:agent_workflow, :completed, tenant: tenant, campaign: campaign)

      expect(helper.active_workflows_count).to eq(0)
    end
  end

  describe '#orchestration_workflows_count' do
    it 'returns the count of marketing orchestration workflows' do
      create(:agent_workflow, :running, :marketing_orchestration, tenant: tenant, campaign: campaign)
      create(:agent_workflow, :pending, :marketing_orchestration, tenant: tenant, campaign: campaign)
      create(:agent_workflow, :running, :email_specialist, tenant: tenant, campaign: campaign)

      expect(helper.orchestration_workflows_count).to eq(2)
    end
  end

  describe '#email_specialist_workflows_count' do
    it 'returns the count of email specialist workflows' do
      create(:agent_workflow, :running, :email_specialist, tenant: tenant, campaign: campaign)
      create(:agent_workflow, :pending, :email_specialist, tenant: tenant, campaign: campaign)
      create(:agent_workflow, :running, :marketing_orchestration, tenant: tenant, campaign: campaign)

      expect(helper.email_specialist_workflows_count).to eq(2)
    end
  end

  describe '#social_specialist_workflows_count' do
    it 'returns the count of social specialist workflows' do
      create(:agent_workflow, :running, :social_specialist, tenant: tenant, campaign: campaign)
      create(:agent_workflow, :pending, :social_specialist, tenant: tenant, campaign: campaign)

      expect(helper.social_specialist_workflows_count).to eq(2)
    end
  end

  describe '#seo_specialist_workflows_count' do
    it 'returns the count of SEO specialist workflows' do
      create(:agent_workflow, :running, :seo_specialist, tenant: tenant, campaign: campaign)
      create(:agent_workflow, :completed, :seo_specialist, tenant: tenant, campaign: campaign)

      expect(helper.seo_specialist_workflows_count).to eq(1)
    end
  end

  describe '#active_workflows' do
    it 'returns active workflows ordered by creation date' do
      old_workflow = create(:agent_workflow, :running, tenant: tenant, campaign: campaign, created_at: 2.hours.ago)
      new_workflow = create(:agent_workflow, :pending, tenant: tenant, campaign: campaign, created_at: 1.hour.ago)
      create(:agent_workflow, :completed, tenant: tenant, campaign: campaign)

      result = helper.active_workflows

      expect(result.count).to eq(2)
      expect(result.first).to eq(new_workflow)
      expect(result.second).to eq(old_workflow)
    end

    it 'limits results to 10 workflows' do
      15.times do |i|
        create(:agent_workflow, :running, tenant: tenant, campaign: campaign, created_at: i.hours.ago)
      end

      expect(helper.active_workflows.count).to eq(10)
    end
  end

  describe '#total_workflows_count' do
    it 'returns the total count of all workflows' do
      create(:agent_workflow, :running, tenant: tenant, campaign: campaign)
      create(:agent_workflow, :completed, tenant: tenant, campaign: campaign)
      create(:agent_workflow, :failed, tenant: tenant, campaign: campaign)

      expect(helper.total_workflows_count).to eq(3)
    end
  end

  describe '#workflow_success_rate' do
    context 'with workflows' do
      it 'calculates the success rate percentage' do
        create(:agent_workflow, :completed, tenant: tenant, campaign: campaign)
        create(:agent_workflow, :completed, tenant: tenant, campaign: campaign)
        create(:agent_workflow, :failed, tenant: tenant, campaign: campaign)
        create(:agent_workflow, :running, tenant: tenant, campaign: campaign)

        # Success rate = 2 completed / (2 completed + 1 failed) = 67%
        expect(helper.workflow_success_rate).to eq(67)
      end

      it 'excludes pending and running workflows from calculation' do
        create(:agent_workflow, :completed, tenant: tenant, campaign: campaign)
        create(:agent_workflow, :pending, tenant: tenant, campaign: campaign)
        create(:agent_workflow, :running, tenant: tenant, campaign: campaign)

        expect(helper.workflow_success_rate).to eq(100)
      end
    end

    context 'with no completed workflows' do
      it 'returns 0' do
        create(:agent_workflow, :pending, tenant: tenant, campaign: campaign)
        create(:agent_workflow, :running, tenant: tenant, campaign: campaign)

        expect(helper.workflow_success_rate).to eq(0)
      end
    end
  end

  describe '#average_workflow_duration' do
    context 'with completed workflows' do
      it 'calculates average duration in human-readable format' do
        completed_workflow1 = create(:agent_workflow, :completed, tenant: tenant, campaign: campaign,
                                   started_at: 2.hours.ago, completed_at: 1.hour.ago)
        completed_workflow2 = create(:agent_workflow, :completed, tenant: tenant, campaign: campaign,
                                   started_at: 4.hours.ago, completed_at: 2.hours.ago)

        # Average should be 1.5 hours = 90 minutes
        expect(helper.average_workflow_duration).to eq("1h 30m")
      end

      it 'handles workflows with nil start times' do
        create(:agent_workflow, :completed, tenant: tenant, campaign: campaign,
               started_at: nil, completed_at: 1.hour.ago)
        create(:agent_workflow, :completed, tenant: tenant, campaign: campaign,
               started_at: 2.hours.ago, completed_at: 1.hour.ago)

        expect(helper.average_workflow_duration).to eq("1h 0m")
      end
    end

    context 'with no completed workflows' do
      it 'returns "-"' do
        create(:agent_workflow, :running, tenant: tenant, campaign: campaign)

        expect(helper.average_workflow_duration).to eq("-")
      end
    end
  end

  describe '#parallel_workflows_count' do
    it 'returns the count of currently running workflows' do
      create(:agent_workflow, :running, tenant: tenant, campaign: campaign)
      create(:agent_workflow, :running, tenant: tenant, campaign: campaign)
      create(:agent_workflow, :pending, tenant: tenant, campaign: campaign)
      create(:agent_workflow, :completed, tenant: tenant, campaign: campaign)

      expect(helper.parallel_workflows_count).to eq(2)
    end
  end

  describe '#format_workflow_duration' do
    it 'formats seconds into human-readable duration' do
      expect(helper.format_workflow_duration(3661)).to eq("1h 1m") # 1 hour 1 minute 1 second
      expect(helper.format_workflow_duration(3600)).to eq("1h 0m") # 1 hour
      expect(helper.format_workflow_duration(300)).to eq("5m") # 5 minutes
      expect(helper.format_workflow_duration(45)).to eq("45s") # 45 seconds
      expect(helper.format_workflow_duration(0)).to eq("0s") # 0 seconds
    end

    it 'handles nil input' do
      expect(helper.format_workflow_duration(nil)).to eq("-")
    end
  end
end
