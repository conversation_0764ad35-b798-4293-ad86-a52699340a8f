# frozen_string_literal: true

require 'rails_helper'

RSpec.describe EmotionalResonanceProfile, type: :model do
  let(:tenant) { create(:tenant) }

  before do
    ActsAsTenant.current_tenant = tenant
  end

  describe 'associations' do
    it { should belong_to(:tenant) }
  end

  describe 'validations' do
    subject { build(:emotional_resonance_profile, tenant: tenant) }

    it { should validate_presence_of(:profile_name) }
    it { should validate_presence_of(:emotional_patterns) }
    it { should validate_presence_of(:response_triggers) }
    it { should validate_presence_of(:engagement_factors) }
    it { should validate_presence_of(:intensity_score) }
    it { should validate_inclusion_of(:intensity_score).in_range(0.0..10.0) }
    it { should validate_uniqueness_of(:profile_name).scoped_to(:tenant_id) }
  end

  describe 'scopes' do
    let!(:high_intensity_profile) { create(:emotional_resonance_profile, :high_intensity, tenant: tenant) }
    let!(:low_intensity_profile) { create(:emotional_resonance_profile, intensity_score: 3.0, tenant: tenant) }
    let!(:active_profile) { create(:emotional_resonance_profile, is_active: true, tenant: tenant) }
    let!(:inactive_profile) { create(:emotional_resonance_profile, is_active: false, tenant: tenant) }

    describe '.high_intensity' do
      it 'returns profiles with intensity score >= 8.0' do
        expect(EmotionalResonanceProfile.high_intensity).to include(high_intensity_profile)
        expect(EmotionalResonanceProfile.high_intensity).not_to include(low_intensity_profile)
      end
    end

    describe '.active' do
      it 'returns active profiles' do
        expect(EmotionalResonanceProfile.active).to include(active_profile)
        expect(EmotionalResonanceProfile.active).not_to include(inactive_profile)
      end
    end

    describe '.by_intensity_range' do
      it 'returns profiles within specified intensity range' do
        mid_range_profiles = EmotionalResonanceProfile.by_intensity_range(3.0, 7.0)
        expect(mid_range_profiles).to include(low_intensity_profile)
        expect(mid_range_profiles).not_to include(high_intensity_profile)
      end
    end
  end

  describe 'instance methods' do
    subject { create(:emotional_resonance_profile, tenant: tenant) }

    describe '#dominant_emotions' do
      it 'returns the top emotional patterns' do
        profile = create(:emotional_resonance_profile,
          tenant: tenant,
          emotional_patterns: {
            'joy' => 8.5,
            'excitement' => 7.8,
            'trust' => 6.2,
            'anticipation' => 5.9
          }
        )

        dominant = profile.dominant_emotions
        expect(dominant).to be_an(Array)
        expect(dominant.first).to eq('joy')
        expect(dominant.second).to eq('excitement')
      end
    end

    describe '#engagement_strength' do
      it 'calculates overall engagement strength' do
        profile = create(:emotional_resonance_profile,
          tenant: tenant,
          engagement_factors: {
            'visual_appeal' => 8.0,
            'emotional_connection' => 7.5,
            'call_to_action_strength' => 6.8,
            'storytelling_quality' => 8.2
          }
        )

        strength = profile.engagement_strength
        expect(strength).to be_a(Float)
        expect(strength).to be_between(0, 10)
      end
    end

    describe '#trigger_effectiveness' do
      it 'evaluates response trigger effectiveness' do
        effectiveness = subject.trigger_effectiveness
        expect(effectiveness).to be_a(Hash)
        expect(effectiveness).to have_key('urgency')
        expect(effectiveness).to have_key('social_proof')
        expect(effectiveness).to have_key('personal_relevance')
      end
    end

    describe '#optimal_messaging_tone' do
      it 'determines optimal tone based on emotional patterns' do
        tone = subject.optimal_messaging_tone
        expect(tone).to be_a(String)
        expect(%w[enthusiastic empathetic authoritative casual professional]).to include(tone)
      end
    end

    describe '#content_recommendations' do
      it 'provides content recommendations based on profile' do
        recommendations = subject.content_recommendations
        expect(recommendations).to be_a(Hash)
        expect(recommendations).to have_key('content_types')
        expect(recommendations).to have_key('messaging_themes')
        expect(recommendations).to have_key('visual_elements')
      end
    end

    describe '#audience_segments' do
      it 'identifies compatible audience segments' do
        segments = subject.audience_segments
        expect(segments).to be_an(Array)
        expect(segments).not_to be_empty
      end
    end

    describe '#emotional_journey_map' do
      it 'creates emotional journey mapping' do
        journey = subject.emotional_journey_map
        expect(journey).to be_a(Hash)
        expect(journey).to have_key('awareness_stage')
        expect(journey).to have_key('consideration_stage')
        expect(journey).to have_key('decision_stage')
        expect(journey).to have_key('retention_stage')
      end
    end

    describe '#high_intensity?' do
      context 'with high intensity score' do
        subject { create(:emotional_resonance_profile, :high_intensity, tenant: tenant) }

        it 'returns true' do
          expect(subject.high_intensity?).to be true
        end
      end

      context 'with low intensity score' do
        subject { create(:emotional_resonance_profile, intensity_score: 5.0, tenant: tenant) }

        it 'returns false' do
          expect(subject.high_intensity?).to be false
        end
      end
    end
  end

  describe 'JSON attribute handling' do
    it 'properly handles emotional_patterns as JSON' do
      patterns = { 'joy' => 8.5, 'excitement' => 7.0, 'trust' => 6.5 }
      profile = create(:emotional_resonance_profile, emotional_patterns: patterns, tenant: tenant)

      expect(profile.emotional_patterns).to eq(patterns)
    end

    it 'properly handles response_triggers as JSON' do
      triggers = { 'urgency' => 7.5, 'social_proof' => 8.0, 'scarcity' => 6.0 }
      profile = create(:emotional_resonance_profile, response_triggers: triggers, tenant: tenant)

      expect(profile.response_triggers).to eq(triggers)
    end

    it 'properly handles engagement_factors as JSON' do
      factors = { 'visual_appeal' => 8.0, 'emotional_connection' => 7.5 }
      profile = create(:emotional_resonance_profile, engagement_factors: factors, tenant: tenant)

      expect(profile.engagement_factors).to eq(factors)
    end
  end

  describe 'callbacks' do
    it 'sets default values on creation' do
      profile = create(:emotional_resonance_profile, tenant: tenant)
      expect(profile.is_active).to be true
      expect(profile.created_at).to be_present
    end

    it 'updates timestamp on modification' do
      profile = create(:emotional_resonance_profile, tenant: tenant)
      original_updated_at = profile.updated_at

      sleep(0.1) # Ensure time difference
      profile.update(intensity_score: 8.5)

      expect(profile.updated_at).to be > original_updated_at
    end
  end

  describe 'factory validity' do
    it 'creates a valid emotional resonance profile' do
      profile = build(:emotional_resonance_profile, tenant: tenant)
      expect(profile).to be_valid
    end

    it 'creates valid high intensity profile' do
      profile = build(:emotional_resonance_profile, :high_intensity, tenant: tenant)
      expect(profile).to be_valid
      expect(profile.intensity_score).to be >= 8.5
      expect(profile.profile_name).to eq('High Energy Enthusiast')
    end
  end
end
