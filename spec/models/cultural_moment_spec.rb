# frozen_string_literal: true

require 'rails_helper'

RSpec.describe CulturalMoment, type: :model do
  let(:tenant) { create(:tenant) }

  before do
    ActsAsTenant.current_tenant = tenant
  end

  describe 'associations' do
    it { should belong_to(:tenant) }
  end

  describe 'validations' do
    subject { build(:cultural_moment, tenant: tenant) }

    it { should validate_presence_of(:title) }
    it { should validate_presence_of(:description) }
    it { should validate_presence_of(:category) }
    it { should validate_presence_of(:start_date) }
    it { should validate_presence_of(:end_date) }
    it { should validate_presence_of(:relevance_score) }
    it { should validate_inclusion_of(:relevance_score).in_range(0.0..10.0) }
  end

  describe 'enums' do
    it 'defines category enum' do
      expect(CulturalMoment.categories.keys).to include('social_awareness', 'technology', 'entertainment', 'sports', 'politics', 'environment')
    end
  end

  describe 'scopes' do
    let!(:active_moment) { create(:cultural_moment, tenant: tenant, start_date: 1.day.ago, end_date: 1.week.from_now) }
    let!(:future_moment) { create(:cultural_moment, :future, tenant: tenant) }
    let!(:expired_moment) { create(:cultural_moment, :expired, tenant: tenant) }
    let!(:trending_moment) { create(:cultural_moment, :trending, tenant: tenant, start_date: 1.day.ago, end_date: 1.week.from_now) }

    describe '.active' do
      it 'returns moments that are currently active' do
        expect(CulturalMoment.active).to include(active_moment, trending_moment)
        expect(CulturalMoment.active).not_to include(future_moment, expired_moment)
      end
    end

    describe '.trending' do
      it 'returns moments with high relevance scores' do
        expect(CulturalMoment.trending).to include(trending_moment)
      end
    end

    describe '.by_category' do
      it 'returns moments in specified category' do
        tech_moment = create(:cultural_moment, :technology_focused, tenant: tenant)
        expect(CulturalMoment.by_category('technology')).to include(tech_moment)
      end
    end

    describe '.upcoming' do
      it 'returns future moments' do
        expect(CulturalMoment.upcoming).to include(future_moment)
        expect(CulturalMoment.upcoming).not_to include(active_moment, expired_moment)
      end
    end
  end

  describe 'instance methods' do
    subject { create(:cultural_moment, tenant: tenant) }

    describe '#active?' do
      context 'when moment is currently active' do
        subject { create(:cultural_moment, tenant: tenant, start_date: 1.day.ago, end_date: 1.week.from_now) }

        it 'returns true' do
          expect(subject.active?).to be true
        end
      end

      context 'when moment has expired' do
        subject { create(:cultural_moment, :expired, tenant: tenant) }

        it 'returns false' do
          expect(subject.active?).to be false
        end
      end
    end

    describe '#trending?' do
      context 'with high relevance score' do
        subject { create(:cultural_moment, :trending, tenant: tenant) }

        it 'returns true' do
          expect(subject.trending?).to be true
        end
      end

      context 'with low relevance score' do
        subject { create(:cultural_moment, relevance_score: 6.0, tenant: tenant) }

        it 'returns false' do
          expect(subject.trending?).to be false
        end
      end
    end

    describe '#engagement_strength' do
      it 'calculates engagement strength from metrics' do
        moment = create(:cultural_moment,
          tenant: tenant,
          engagement_metrics: {
            social_mentions: 5000,
            hashtag_usage: 2500,
            media_coverage: 100,
            sentiment_score: 8.5,
            viral_potential: 8.0
          }
        )

        strength = moment.engagement_strength
        expect(strength).to be_a(Float)
        expect(strength).to be > 0
      end
    end

    describe '#peak_engagement_times' do
      it 'returns optimal engagement times from cultural data' do
        times = subject.peak_engagement_times
        expect(times).to be_an(Array)
        expect(times).to include('10:00', '14:00', '19:00')
      end
    end

    describe '#target_demographics' do
      it 'returns demographic information from cultural data' do
        demographics = subject.target_demographics
        expect(demographics).to be_a(Hash)
        expect(demographics).to have_key('primary_age_groups')
        expect(demographics).to have_key('geographic_focus')
      end
    end

    describe '#viral_potential_score' do
      it 'returns viral potential from engagement metrics' do
        score = subject.viral_potential_score
        expect(score).to be_a(Numeric)
        expect(score).to be_between(0, 10)
      end
    end
  end

  describe 'callbacks' do
    it 'tracks changes after update' do
      moment = create(:cultural_moment, tenant: tenant)
      original_score = moment.relevance_score

      moment.update(relevance_score: original_score + 1.0)

      # This would test any callback logic if implemented
      expect(moment.relevance_score).to eq(original_score + 1.0)
    end
  end

  describe 'factory validity' do
    it 'creates a valid cultural moment' do
      moment = build(:cultural_moment, tenant: tenant)
      expect(moment).to be_valid
    end

    it 'creates valid trending moment' do
      moment = build(:cultural_moment, :trending, tenant: tenant)
      expect(moment).to be_valid
      expect(moment.relevance_score).to be >= 8.5
    end

    it 'creates valid technology focused moment' do
      moment = build(:cultural_moment, :technology_focused, tenant: tenant)
      expect(moment).to be_valid
      expect(moment.category).to eq('technology')
      expect(moment.title).to eq('AI Innovation Week')
    end
  end
end
