# frozen_string_literal: true

require 'rails_helper'

RSpec.describe VibeCampaignOrchestration, type: :model do
  let(:tenant) { create(:tenant) }
  let(:campaign) { create(:campaign, tenant: tenant) }

  before do
    ActsAsTenant.current_tenant = tenant
  end

  describe 'associations' do
    it { should belong_to(:tenant) }
    it { should belong_to(:campaign) }
  end

  describe 'validations' do
    subject { build(:vibe_campaign_orchestration, tenant: tenant, campaign: campaign) }

    it { should validate_presence_of(:orchestration_strategy) }
    it { should validate_presence_of(:coordination_rules) }
    it { should validate_presence_of(:timing_optimization) }
    it { should validate_presence_of(:performance_metrics) }
    it { should validate_presence_of(:optimization_score) }
    it { should validate_inclusion_of(:optimization_score).in_range(0.0..10.0) }
    it { should validate_inclusion_of(:status).in_array(%w[planning active paused completed failed]) }
  end

  describe 'enums' do
    it { should define_enum_for(:status).with_values(planning: 0, active: 1, paused: 2, completed: 3, failed: 4) }
  end

  describe 'scopes' do
    let!(:active_orchestration) { create(:vibe_campaign_orchestration, status: 'active', tenant: tenant, campaign: campaign) }
    let!(:completed_orchestration) { create(:vibe_campaign_orchestration, status: 'completed', tenant: tenant, campaign: campaign) }
    let!(:high_performance) { create(:vibe_campaign_orchestration, :performance_optimized, tenant: tenant, campaign: campaign) }
    let!(:cultural_surfing) { create(:vibe_campaign_orchestration, :cultural_surfing, tenant: tenant, campaign: campaign) }

    describe '.active' do
      it 'returns active orchestrations' do
        expect(VibeCampaignOrchestration.active).to include(active_orchestration)
        expect(VibeCampaignOrchestration.active).not_to include(completed_orchestration)
      end
    end

    describe '.completed' do
      it 'returns completed orchestrations' do
        expect(VibeCampaignOrchestration.completed).to include(completed_orchestration)
        expect(VibeCampaignOrchestration.completed).not_to include(active_orchestration)
      end
    end

    describe '.high_performance' do
      it 'returns orchestrations with score >= 8.0' do
        expect(VibeCampaignOrchestration.high_performance).to include(high_performance)
      end
    end

    describe '.by_strategy' do
      it 'returns orchestrations with specified strategy' do
        cultural_orchestrations = VibeCampaignOrchestration.by_strategy('cultural_surfing')
        expect(cultural_orchestrations).to include(cultural_surfing)
      end
    end

    describe '.recent' do
      let!(:recent_orchestration) { create(:vibe_campaign_orchestration, tenant: tenant, campaign: campaign, created_at: 1.day.ago) }
      let!(:old_orchestration) { create(:vibe_campaign_orchestration, tenant: tenant, campaign: campaign, created_at: 2.weeks.ago) }

      it 'returns orchestrations from the last week' do
        expect(VibeCampaignOrchestration.recent).to include(recent_orchestration)
        expect(VibeCampaignOrchestration.recent).not_to include(old_orchestration)
      end
    end
  end

  describe 'instance methods' do
    subject { create(:vibe_campaign_orchestration, tenant: tenant, campaign: campaign) }

    describe '#strategy_components' do
      it 'returns orchestration strategy components' do
        orchestration = create(:vibe_campaign_orchestration,
          tenant: tenant,
          campaign: campaign,
          orchestration_strategy: {
            'primary_strategy' => 'cultural_surfing',
            'secondary_strategies' => [ 'authenticity_focus', 'emotional_resonance' ],
            'integration_level' => 'high',
            'adaptation_frequency' => 'real_time'
          }
        )

        components = orchestration.strategy_components
        expect(components).to be_a(Hash)
        expect(components['primary_strategy']).to eq('cultural_surfing')
        expect(components['secondary_strategies']).to be_an(Array)
      end
    end

    describe '#coordination_effectiveness' do
      it 'calculates coordination effectiveness score' do
        effectiveness = subject.coordination_effectiveness
        expect(effectiveness).to be_a(Float)
        expect(effectiveness).to be_between(0, 10)
      end
    end

    describe '#timing_recommendations' do
      it 'provides timing optimization recommendations' do
        recommendations = subject.timing_recommendations
        expect(recommendations).to be_a(Hash)
        expect(recommendations).to have_key('optimal_launch_time')
        expect(recommendations).to have_key('peak_engagement_windows')
        expect(recommendations).to have_key('adjustment_triggers')
      end
    end

    describe '#performance_analysis' do
      it 'analyzes orchestration performance' do
        analysis = subject.performance_analysis
        expect(analysis).to be_a(Hash)
        expect(analysis).to have_key('efficiency_metrics')
        expect(analysis).to have_key('coordination_success')
        expect(analysis).to have_key('improvement_areas')
      end
    end

    describe '#optimization_opportunities' do
      it 'identifies optimization opportunities' do
        opportunities = subject.optimization_opportunities
        expect(opportunities).to be_an(Array)
        expect(opportunities).not_to be_empty
        opportunities.each do |opportunity|
          expect(opportunity).to have_key('area')
          expect(opportunity).to have_key('potential_impact')
          expect(opportunity).to have_key('implementation_effort')
        end
      end
    end

    describe '#coordination_health' do
      it 'evaluates coordination health status' do
        health = subject.coordination_health
        expect(health).to be_a(Hash)
        expect(health).to have_key('overall_status')
        expect(health).to have_key('component_health')
        expect(health).to have_key('risk_factors')
      end
    end

    describe '#adaptive_adjustments' do
      it 'provides adaptive adjustment recommendations' do
        adjustments = subject.adaptive_adjustments
        expect(adjustments).to be_a(Hash)
        expect(adjustments).to have_key('strategy_refinements')
        expect(adjustments).to have_key('timing_adjustments')
        expect(adjustments).to have_key('resource_reallocation')
      end
    end

    describe '#integration_score' do
      it 'calculates integration effectiveness score' do
        score = subject.integration_score
        expect(score).to be_a(Float)
        expect(score).to be_between(0, 10)
      end
    end

    describe '#high_performance?' do
      context 'with high optimization score' do
        subject { create(:vibe_campaign_orchestration, :performance_optimized, tenant: tenant, campaign: campaign) }

        it 'returns true' do
          expect(subject.high_performance?).to be true
        end
      end

      context 'with low optimization score' do
        subject { create(:vibe_campaign_orchestration, optimization_score: 5.0, tenant: tenant, campaign: campaign) }

        it 'returns false' do
          expect(subject.high_performance?).to be false
        end
      end
    end

    describe '#requires_attention?' do
      context 'with low optimization score' do
        subject { create(:vibe_campaign_orchestration, optimization_score: 4.0, tenant: tenant, campaign: campaign) }

        it 'returns true' do
          expect(subject.requires_attention?).to be true
        end
      end

      context 'with high optimization score' do
        subject { create(:vibe_campaign_orchestration, optimization_score: 8.5, tenant: tenant, campaign: campaign) }

        it 'returns false' do
          expect(subject.requires_attention?).to be false
        end
      end
    end

    describe '#activate!' do
      subject { create(:vibe_campaign_orchestration, status: 'planning', tenant: tenant, campaign: campaign) }

      it 'changes status to active' do
        expect { subject.activate! }.to change { subject.status }.from('planning').to('active')
      end

      it 'updates activated_at timestamp' do
        expect { subject.activate! }.to change { subject.activated_at }.from(nil)
      end
    end

    describe '#pause!' do
      subject { create(:vibe_campaign_orchestration, status: 'active', tenant: tenant, campaign: campaign) }

      it 'changes status to paused' do
        expect { subject.pause! }.to change { subject.status }.from('active').to('paused')
      end

      it 'updates paused_at timestamp' do
        expect { subject.pause! }.to change { subject.paused_at }.from(nil)
      end
    end

    describe '#complete!' do
      subject { create(:vibe_campaign_orchestration, status: 'active', tenant: tenant, campaign: campaign) }

      it 'changes status to completed' do
        expect { subject.complete! }.to change { subject.status }.from('active').to('completed')
      end

      it 'updates completed_at timestamp' do
        expect { subject.complete! }.to change { subject.completed_at }.from(nil)
      end
    end
  end

  describe 'JSON attribute handling' do
    it 'properly handles orchestration_strategy as JSON' do
      strategy = {
        'primary_strategy' => 'cultural_surfing',
        'secondary_strategies' => [ 'authenticity_focus', 'emotional_resonance' ],
        'integration_level' => 'high'
      }
      orchestration = create(:vibe_campaign_orchestration, orchestration_strategy: strategy, tenant: tenant, campaign: campaign)

      expect(orchestration.orchestration_strategy).to eq(strategy)
    end

    it 'properly handles coordination_rules as JSON' do
      rules = {
        'timing_constraints' => [ 'peak_hours_only', 'avoid_competitor_launches' ],
        'resource_limits' => { 'budget_cap' => 10000, 'time_limit' => '2_weeks' },
        'quality_thresholds' => { 'min_authenticity_score' => 7.0, 'min_cultural_relevance' => 8.0 }
      }
      orchestration = create(:vibe_campaign_orchestration, coordination_rules: rules, tenant: tenant, campaign: campaign)

      expect(orchestration.coordination_rules).to eq(rules)
    end

    it 'properly handles timing_optimization as JSON' do
      timing = {
        'launch_window' => { 'start' => '2024-01-15T10:00:00Z', 'end' => '2024-01-15T12:00:00Z' },
        'engagement_peaks' => [ 'morning_commute', 'lunch_break', 'evening_wind_down' ],
        'adjustment_frequency' => 'hourly'
      }
      orchestration = create(:vibe_campaign_orchestration, timing_optimization: timing, tenant: tenant, campaign: campaign)

      expect(orchestration.timing_optimization).to eq(timing)
    end

    it 'properly handles performance_metrics as JSON' do
      metrics = {
        'efficiency_score' => 8.5,
        'coordination_success_rate' => 92.5,
        'adaptation_speed' => 7.8,
        'resource_utilization' => 85.0
      }
      orchestration = create(:vibe_campaign_orchestration, performance_metrics: metrics, tenant: tenant, campaign: campaign)

      expect(orchestration.performance_metrics).to eq(metrics)
    end
  end

  describe 'callbacks' do
    it 'sets default status to planning on creation' do
      orchestration = create(:vibe_campaign_orchestration, tenant: tenant, campaign: campaign)
      expect(orchestration.status).to eq('planning')
    end

    it 'updates timestamp on status change' do
      orchestration = create(:vibe_campaign_orchestration, tenant: tenant, campaign: campaign)
      original_updated_at = orchestration.updated_at

      sleep(0.1) # Ensure time difference
      orchestration.activate!

      expect(orchestration.updated_at).to be > original_updated_at
    end
  end

  describe 'factory validity' do
    it 'creates a valid vibe campaign orchestration' do
      orchestration = build(:vibe_campaign_orchestration, tenant: tenant, campaign: campaign)
      expect(orchestration).to be_valid
    end

    it 'creates valid cultural surfing orchestration' do
      orchestration = build(:vibe_campaign_orchestration, :cultural_surfing, tenant: tenant, campaign: campaign)
      expect(orchestration).to be_valid
      expect(orchestration.orchestration_strategy['primary_strategy']).to eq('cultural_surfing')
    end

    it 'creates valid authenticity focused orchestration' do
      orchestration = build(:vibe_campaign_orchestration, :authenticity_focused, tenant: tenant, campaign: campaign)
      expect(orchestration).to be_valid
      expect(orchestration.orchestration_strategy['primary_strategy']).to eq('authenticity_focus')
    end

    it 'creates valid performance optimized orchestration' do
      orchestration = build(:vibe_campaign_orchestration, :performance_optimized, tenant: tenant, campaign: campaign)
      expect(orchestration).to be_valid
      expect(orchestration.optimization_score).to be >= 8.5
    end
  end
end
