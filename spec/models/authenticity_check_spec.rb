# frozen_string_literal: true

require 'rails_helper'

RSpec.describe AuthenticityCheck, type: :model do
  let(:tenant) { create(:tenant) }
  let(:campaign) { create(:campaign, tenant: tenant) }

  before do
    ActsAsTenant.current_tenant = tenant
  end

  describe 'associations' do
    it { should belong_to(:tenant) }
    it { should belong_to(:campaign) }
  end

  describe 'validations' do
    subject { build(:authenticity_check, tenant: tenant, campaign: campaign) }

    it { should validate_presence_of(:check_type) }
    it { should validate_presence_of(:status) }
    it { should validate_presence_of(:authenticity_score) }

    # Custom test for inclusion validation with custom message
    it "validates that authenticity_score is between 0.0 and 10.0" do
      check = build(:authenticity_check, authenticity_score: 11.0)
      expect(check).not_to be_valid
      expect(check.errors[:authenticity_score]).to include("must be between 0.0 and 10.0")
    end

    # Custom test for status inclusion
    it "validates that status is in the allowed values" do
      valid_statuses = %w[pending analyzing completed flagged requires_review approved rejected]
      valid_statuses.each do |valid_status|
        check = build(:authenticity_check, status: valid_status)
        check.valid?
        expect(check.errors[:status]).not_to include("is not included in the list")
      end

      # Since we're using enum, we can't directly assign an invalid value
      # It would raise an ArgumentError instead
      expect {
        build(:authenticity_check, status: 'invalid_status')
      }.to raise_error(ArgumentError, /'invalid_status' is not a valid status/)
    end
  end

  describe 'enums' do
    # Test for string-based enum instead of integer-based
    it "defines status as a string-based enum" do
      expected_values = {
        pending: 'pending',
        analyzing: 'analyzing',
        completed: 'completed',
        flagged: 'flagged',
        requires_review: 'requires_review',
        approved: 'approved',
        rejected: 'rejected'
      }

      expect(AuthenticityCheck.defined_enums["status"]).to eq(expected_values.stringify_keys)
    end
  end

  describe 'scopes' do
    let!(:flagged_check) { create(:authenticity_check, :flagged, tenant: tenant, campaign: campaign) }
    let!(:approved_check) { create(:authenticity_check, status: 'approved', tenant: tenant, campaign: campaign) }
    let!(:high_score_check) { create(:authenticity_check, authenticity_score: 8.5, tenant: tenant, campaign: campaign) }
    let!(:low_score_check) { create(:authenticity_check, authenticity_score: 4.0, tenant: tenant, campaign: campaign) }

    describe '.flagged' do
      it 'returns flagged authenticity checks' do
        expect(AuthenticityCheck.flagged).to include(flagged_check)
        expect(AuthenticityCheck.flagged).not_to include(approved_check)
      end
    end

    describe '.approved' do
      it 'returns approved authenticity checks' do
        expect(AuthenticityCheck.approved).to include(approved_check)
        expect(AuthenticityCheck.approved).not_to include(flagged_check)
      end
    end

    describe '.high_score' do
      it 'returns checks with score >= 8.0' do
        expect(AuthenticityCheck.high_score).to include(high_score_check)
        expect(AuthenticityCheck.high_score).not_to include(low_score_check)
      end
    end

    describe '.by_score_range' do
      it 'returns checks within specified score range' do
        mid_range_checks = AuthenticityCheck.by_score_range(4.0, 6.0)
        expect(mid_range_checks).to include(low_score_check)
        expect(mid_range_checks).not_to include(high_score_check)
      end
    end

    describe '.recent' do
      let!(:recent_check) { create(:authenticity_check, tenant: tenant, campaign: campaign, created_at: 1.day.ago) }
      let!(:old_check) { create(:authenticity_check, tenant: tenant, campaign: campaign, created_at: 2.weeks.ago) }

      it 'returns checks from the last week' do
        expect(AuthenticityCheck.recent).to include(recent_check)
        expect(AuthenticityCheck.recent).not_to include(old_check)
      end
    end
  end

  describe 'instance methods' do
    subject { create(:authenticity_check, tenant: tenant, campaign: campaign) }

    describe '#dimension_scores' do
      it 'returns authenticity dimension scores' do
        check = create(:authenticity_check,
          tenant: tenant,
          campaign: campaign,
          authenticity_dimensions: {
            'brand_consistency' => 8.5,
            'cultural_sensitivity' => 7.2,
            'emotional_genuineness' => 9.1,
            'claim_accuracy' => 6.8
          }
        )

        scores = check.dimension_scores
        expect(scores).to be_a(Hash)
        expect(scores['brand_consistency']).to eq(8.5)
        expect(scores['emotional_genuineness']).to eq(9.1)
      end
    end

    describe '#primary_concerns' do
      it 'identifies main authenticity concerns' do
        concerns = subject.primary_concerns
        expect(concerns).to be_an(Array)
        expect(concerns).not_to be_empty
      end
    end

    describe '#authenticity_level' do
      context 'with high score' do
        subject { create(:authenticity_check, authenticity_score: 8.5, tenant: tenant, campaign: campaign) }

        it 'returns high' do
          expect(subject.authenticity_level).to eq('high')
        end
      end

      context 'with medium score' do
        subject { create(:authenticity_check, authenticity_score: 6.5, tenant: tenant, campaign: campaign) }

        it 'returns medium' do
          expect(subject.authenticity_level).to eq('medium')
        end
      end

      context 'with low score' do
        subject { create(:authenticity_check, authenticity_score: 3.0, tenant: tenant, campaign: campaign) }

        it 'returns low' do
          expect(subject.authenticity_level).to eq('low')
        end
      end
    end

    describe '#risk_assessment' do
      it 'provides risk assessment based on score and flags' do
        assessment = subject.risk_assessment
        expect(assessment).to be_a(Hash)
        expect(assessment).to have_key('risk_level')
        expect(assessment).to have_key('mitigation_strategies')
        expect(assessment).to have_key('compliance_notes')
      end
    end

    describe '#improvement_suggestions' do
      it 'provides specific improvement suggestions' do
        suggestions = subject.improvement_suggestions
        expect(suggestions).to be_an(Array)
        expect(suggestions).not_to be_empty
        expect(suggestions.first).to be_a(Hash)
        expect(suggestions.first).to have_key('area')
        expect(suggestions.first).to have_key('suggestion')
        expect(suggestions.first).to have_key('priority')
      end
    end

    describe '#compliance_status' do
      it 'evaluates compliance with authenticity standards' do
        status = subject.compliance_status
        expect(status).to be_a(Hash)
        expect(status).to have_key('overall_compliance')
        expect(status).to have_key('specific_violations')
        expect(status).to have_key('corrective_actions')
      end
    end

    describe '#flagged_content_analysis' do
      it 'analyzes flagged content elements' do
        analysis = subject.flagged_content_analysis
        expect(analysis).to be_a(Hash)
        expect(analysis).to have_key('content_types')
        expect(analysis).to have_key('severity_levels')
        expect(analysis).to have_key('resolution_timeframe')
      end
    end

    describe '#requires_manual_review?' do
      context 'when flagged' do
        subject { create(:authenticity_check, :flagged, tenant: tenant, campaign: campaign) }

        it 'returns true' do
          expect(subject.requires_manual_review?).to be true
        end
      end

      context 'when approved with high score' do
        subject { create(:authenticity_check, status: 'approved', authenticity_score: 9.0, tenant: tenant, campaign: campaign) }

        it 'returns false' do
          expect(subject.requires_manual_review?).to be false
        end
      end

      context 'with low score' do
        subject { create(:authenticity_check, authenticity_score: 4.0, tenant: tenant, campaign: campaign) }

        it 'returns true' do
          expect(subject.requires_manual_review?).to be true
        end
      end
    end

    describe '#approve!' do
      subject { create(:authenticity_check, status: 'pending', tenant: tenant, campaign: campaign) }

      it 'changes status to approved' do
        expect { subject.approve! }.to change { subject.status }.from('pending').to('approved')
      end

      it 'updates approved_at timestamp' do
        expect { subject.approve! }.to change { subject.approved_at }.from(nil)
      end
    end

    describe '#flag!' do
      subject { create(:authenticity_check, status: 'pending', tenant: tenant, campaign: campaign) }

      it 'changes status to flagged' do
        expect { subject.flag! }.to change { subject.status }.from('pending').to('flagged')
      end

      it 'updates flagged_at timestamp' do
        expect { subject.flag! }.to change { subject.flagged_at }.from(nil)
      end
    end

    describe '#reject!' do
      subject { create(:authenticity_check, status: 'pending', tenant: tenant, campaign: campaign) }

      it 'changes status to rejected' do
        expect { subject.reject! }.to change { subject.status }.from('pending').to('rejected')
      end

      it 'updates rejected_at timestamp' do
        expect { subject.reject! }.to change { subject.rejected_at }.from(nil)
      end
    end
  end

  describe 'JSON attribute handling' do
    it 'properly handles authenticity_dimensions as JSON' do
      dimensions = {
        'brand_consistency' => 8.5,
        'cultural_sensitivity' => 7.2,
        'emotional_genuineness' => 9.1,
        'claim_accuracy' => 6.8
      }
      check = create(:authenticity_check, authenticity_dimensions: dimensions, tenant: tenant, campaign: campaign)

      expect(check.authenticity_dimensions).to eq(dimensions)
    end

    it 'properly handles flagged_elements as JSON' do
      elements = [ 'misleading_claim', 'cultural_insensitivity', 'brand_mismatch' ]
      check = create(:authenticity_check, flagged_elements: elements, tenant: tenant, campaign: campaign)

      expect(check.flagged_elements).to eq(elements)
    end

    it 'properly handles recommendations as JSON' do
      recommendations = [
        { 'area' => 'cultural_sensitivity', 'action' => 'Review cultural references', 'priority' => 'high' },
        { 'area' => 'brand_consistency', 'action' => 'Align with brand guidelines', 'priority' => 'medium' }
      ]
      check = create(:authenticity_check, recommendations: recommendations, tenant: tenant, campaign: campaign)

      expect(check.recommendations).to eq(recommendations)
    end
  end

  describe 'callbacks' do
    it 'sets default status to pending on creation' do
      check = create(:authenticity_check, tenant: tenant, campaign: campaign)
      expect(check.status).to eq('pending')
    end

    it 'updates timestamp on status change' do
      check = create(:authenticity_check, tenant: tenant, campaign: campaign)
      original_updated_at = check.updated_at

      sleep(0.1) # Ensure time difference
      check.approve!

      expect(check.updated_at).to be > original_updated_at
    end
  end

  describe 'factory validity' do
    it 'creates a valid authenticity check' do
      check = build(:authenticity_check, tenant: tenant, campaign: campaign)
      expect(check).to be_valid
    end

    it 'creates valid flagged check' do
      check = build(:authenticity_check, :flagged, tenant: tenant, campaign: campaign)
      expect(check).to be_valid
      expect(check.status).to eq('flagged')
      expect(check.authenticity_score).to be < 5.0
    end

    it 'creates valid rejected check' do
      check = build(:authenticity_check, :rejected, tenant: tenant, campaign: campaign)
      expect(check).to be_valid
      expect(check.status).to eq('rejected')
      expect(check.authenticity_score).to be < 4.0
    end
  end
end
