# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Tenant, type: :model do
  describe 'validations' do
    subject { build(:tenant) }

    it { should validate_presence_of(:name) }
    it { should validate_uniqueness_of(:name) }
    it { should validate_presence_of(:subdomain) }
    it { should validate_uniqueness_of(:subdomain).case_insensitive }

    it 'validates subdomain format' do
      tenant = build(:tenant, subdomain: 'Invalid Subdomain!')
      expect(tenant).not_to be_valid
      expect(tenant.errors[:subdomain]).to include('must be lowercase alphanumeric with dashes only')
    end

    it 'normalizes subdomain to lowercase' do
      tenant = build(:tenant, subdomain: 'ACME-Corp')
      tenant.valid?
      expect(tenant.subdomain).to eq('acme-corp')
    end
  end

  describe 'associations' do
    it { should have_many(:users).dependent(:destroy) }
  end

  describe '#active?' do
    it 'returns true when status is active' do
      tenant = build(:tenant, status: 'active')
      expect(tenant.active?).to be true
    end

    it 'returns false when status is not active' do
      tenant = build(:tenant, status: 'suspended')
      expect(tenant.active?).to be false
    end
  end

  describe '#full_domain' do
    it 'returns the full domain with subdomain' do
      tenant = build(:tenant, subdomain: 'acme-corp')
      expect(tenant.full_domain).to eq('acme-corp.localhost:3000')
    end
  end
end
