# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Campaign, type: :model do
  describe 'validations' do
    subject { build(:campaign) }

    it { should validate_presence_of(:name) }
    it { should validate_presence_of(:campaign_type) }
    it { should validate_presence_of(:status) }
    it { should validate_presence_of(:target_audience) }

    it 'validates name uniqueness within tenant scope' do
      tenant1 = create(:tenant)
      tenant2 = create(:tenant)

      ActsAsTenant.with_tenant(tenant1) do
        create(:campaign, name: 'Holiday Sale Campaign')
      end

      # Same name should be allowed in different tenant
      ActsAsTenant.with_tenant(tenant2) do
        expect {
          create(:campaign, name: 'Holiday Sale Campaign')
        }.not_to raise_error
      end
    end

    it 'prevents duplicate campaign names within same tenant' do
      tenant = create(:tenant)

      ActsAsTenant.with_tenant(tenant) do
        create(:campaign, name: 'Duplicate Campaign')

        expect {
          create(:campaign, name: 'Duplicate Campaign')
        }.to raise_error(ActiveRecord::RecordInvalid, /Name has already been taken/)
      end
    end
  end

  describe 'associations' do
    it 'belongs to tenant' do
      campaign = build(:campaign)
      expect(campaign.tenant).to be_a(Tenant)
      expect(campaign.tenant_id).to be_present
    end

    it 'belongs to created_by user' do
      campaign = build(:campaign)
      expect(campaign.created_by).to be_a(User)

      # For persisted records, check the ID
      persisted_campaign = create(:campaign)
      expect(persisted_campaign.created_by_id).to be_present
    end

    # Specialized campaign associations
    it { should have_many(:campaign_metrics).dependent(:destroy) }
    it { should have_one(:email_campaign).dependent(:destroy) }
    it { should have_one(:social_campaign).dependent(:destroy) }
    it { should have_one(:seo_campaign).dependent(:destroy) }

    # Vibe Marketing Associations
    it { should have_many(:vibe_analysis_records).dependent(:destroy) }
    it { should have_many(:authenticity_checks).dependent(:destroy) }
    it { should have_many(:vibe_campaign_orchestrations).through(:campaign_collections) }
  end

  describe 'acts_as_tenant' do
    it 'should act as tenant' do
      expect(Campaign.respond_to?(:acts_as_tenant)).to be true
    end
  end

  describe 'enums' do
    it { should define_enum_for(:status).with_values(%w[draft active paused completed cancelled]) }
    it { should define_enum_for(:campaign_type).with_values(%w[email social seo multi_channel]) }
  end

  describe 'scopes' do
    let(:tenant) { create(:tenant) }

    before do
      ActsAsTenant.current_tenant = tenant
    end

    it 'filters by status' do
      active_campaign = create(:campaign, status: 'active')
      draft_campaign = create(:campaign, status: 'draft')

      expect(Campaign.active).to include(active_campaign)
      expect(Campaign.active).not_to include(draft_campaign)
    end

    it 'filters by campaign type' do
      email_campaign = create(:campaign, campaign_type: 'email')
      social_campaign = create(:campaign, campaign_type: 'social')

      expect(Campaign.email).to include(email_campaign)
      expect(Campaign.email).not_to include(social_campaign)
    end
  end

  describe '#can_be_activated?' do
    it 'returns true for draft campaigns' do
      campaign = build(:campaign, status: 'draft')
      expect(campaign.can_be_activated?).to be true
    end

    it 'returns false for completed campaigns' do
      campaign = build(:campaign, status: 'completed')
      expect(campaign.can_be_activated?).to be false
    end
  end

  describe '#duration_in_days' do
    it 'calculates duration when both dates are present' do
      campaign = build(:campaign,
                      start_date: Date.current,
                      end_date: Date.current + 7.days)
      expect(campaign.duration_in_days).to eq(7)
    end

    it 'returns nil when dates are missing' do
      campaign = build(:campaign, start_date: nil, end_date: nil)
      expect(campaign.duration_in_days).to be_nil
    end
  end

  describe '#progress_percentage' do
    it 'calculates progress for ongoing campaigns' do
      campaign = build(:campaign,
                      start_date: 5.days.ago,
                      end_date: 5.days.from_now,
                      status: 'active')
      expect(campaign.progress_percentage).to eq(50)
    end

    it 'returns 0 for campaigns not yet started' do
      campaign = build(:campaign,
                      start_date: 1.day.from_now,
                      end_date: 8.days.from_now,
                      status: 'draft')
      expect(campaign.progress_percentage).to eq(0)
    end

    it 'returns 100 for completed campaigns' do
      campaign = build(:campaign, status: 'completed')
      expect(campaign.progress_percentage).to eq(100)
    end
  end

  describe '#budget_in_dollars' do
    it 'converts cents to dollars' do
      campaign = build(:campaign, budget_cents: 15050)
      expect(campaign.budget_in_dollars).to eq(150.50)
    end
  end

  describe '#budget_in_dollars=' do
    it 'converts dollars to cents' do
      campaign = build(:campaign)
      campaign.budget_in_dollars = 250.75
      expect(campaign.budget_cents).to eq(25075)
    end
  end

  describe 'vibe marketing extensions' do
    let(:tenant) { create(:tenant) }

    before do
      ActsAsTenant.current_tenant = tenant
    end

    describe 'factory traits' do
      it 'creates campaign with vibe data' do
        campaign = create(:campaign, :with_vibe_data, tenant: tenant)
        expect(campaign.vibe_analysis_records).not_to be_empty
        expect(campaign.vibe_score).to be_present
        expect(campaign.cultural_relevance_score).to be_present
      end

      it 'creates campaign with vibe pending status' do
        campaign = create(:campaign, :vibe_pending, tenant: tenant)
        expect(campaign.vibe_status).to eq('pending')
      end

      it 'creates campaign with vibe flagged status' do
        campaign = create(:campaign, :vibe_flagged, tenant: tenant)
        expect(campaign.vibe_status).to eq('flagged')
        expect(campaign.authenticity_checks).not_to be_empty
        expect(campaign.authenticity_checks.first.status).to eq('flagged')
      end

      it 'creates campaign with cultural validation' do
        campaign = create(:campaign, :cultural_validated, tenant: tenant)
        expect(campaign.cultural_validation_status).to eq('validated')
        expect(campaign.cultural_relevance_score).to be >= 8.0
      end

      it 'creates campaign with authenticity verification' do
        campaign = create(:campaign, :authenticity_verified, tenant: tenant)
        expect(campaign.authenticity_status).to eq('verified')
        expect(campaign.authenticity_checks).not_to be_empty
        expect(campaign.authenticity_checks.first.status).to eq('approved')
      end

      it 'creates campaign with emotional resonance' do
        campaign = create(:campaign, :with_emotional_resonance, tenant: tenant)
        expect(campaign.emotional_resonance_profile).to be_present
        expect(campaign.emotional_resonance_profile.intensity_score).to be >= 8.0
      end

      it 'creates campaign with cultural moment' do
        campaign = create(:campaign, :with_cultural_moment, tenant: tenant)
        expect(campaign.cultural_moment).to be_present
        expect(campaign.cultural_moment.trend_status).to eq('trending')
      end

      it 'creates campaign with orchestration' do
        campaign = create(:campaign, :with_orchestration, tenant: tenant)
        expect(campaign.vibe_campaign_orchestrations).not_to be_empty
        expect(campaign.vibe_campaign_orchestrations.first.status).to eq('active')
      end
    end

    describe 'vibe-specific scopes' do
      let!(:high_vibe_campaign) { create(:campaign, vibe_score: 8.5, tenant: tenant) }
      let!(:low_vibe_campaign) { create(:campaign, vibe_score: 4.0, tenant: tenant) }
      let!(:cultural_validated_campaign) { create(:campaign, :cultural_validated, tenant: tenant) }
      let!(:authenticity_verified_campaign) { create(:campaign, :authenticity_verified, tenant: tenant) }

      it 'filters by high vibe score' do
        high_vibe_campaigns = Campaign.where('vibe_score >= ?', 8.0)
        expect(high_vibe_campaigns).to include(high_vibe_campaign)
        expect(high_vibe_campaigns).not_to include(low_vibe_campaign)
      end

      it 'filters by cultural validation status' do
        culturally_validated = Campaign.where(cultural_validation_status: 'validated')
        expect(culturally_validated).to include(cultural_validated_campaign)
      end

      it 'filters by authenticity status' do
        authenticity_verified = Campaign.where(authenticity_status: 'verified')
        expect(authenticity_verified).to include(authenticity_verified_campaign)
      end
    end

    describe 'vibe-specific instance methods' do
      subject { create(:campaign, :with_vibe_data, tenant: tenant) }

      describe '#vibe_performance_summary' do
        it 'returns comprehensive vibe performance metrics' do
          summary = subject.vibe_performance_summary
          expect(summary).to be_a(Hash)
          expect(summary).to have_key('overall_vibe_score')
          expect(summary).to have_key('cultural_relevance')
          expect(summary).to have_key('authenticity_rating')
          expect(summary).to have_key('emotional_resonance')
        end
      end

      describe '#cultural_alignment' do
        it 'evaluates cultural moment alignment' do
          alignment = subject.cultural_alignment
          expect(alignment).to be_a(Hash)
          expect(alignment).to have_key('relevance_score')
          expect(alignment).to have_key('timing_effectiveness')
          expect(alignment).to have_key('audience_match')
        end
      end

      describe '#authenticity_health' do
        it 'provides authenticity health status' do
          health = subject.authenticity_health
          expect(health).to be_a(Hash)
          expect(health).to have_key('overall_health')
          expect(health).to have_key('risk_factors')
          expect(health).to have_key('improvement_recommendations')
        end
      end

      describe '#emotional_impact_analysis' do
        it 'analyzes emotional impact potential' do
          analysis = subject.emotional_impact_analysis
          expect(analysis).to be_a(Hash)
          expect(analysis).to have_key('projected_impact')
          expect(analysis).to have_key('emotional_triggers')
          expect(analysis).to have_key('resonance_factors')
        end
      end

      describe '#vibe_optimization_recommendations' do
        it 'provides vibe optimization suggestions' do
          recommendations = subject.vibe_optimization_recommendations
          expect(recommendations).to be_an(Array)
          expect(recommendations).not_to be_empty
          recommendations.each do |rec|
            expect(rec).to have_key('category')
            expect(rec).to have_key('suggestion')
            expect(rec).to have_key('impact_level')
          end
        end
      end

      describe '#cultural_moment_opportunities' do
        it 'identifies cultural moment opportunities' do
          opportunities = subject.cultural_moment_opportunities
          expect(opportunities).to be_an(Array)
          opportunities.each do |opp|
            expect(opp).to have_key('moment_type')
            expect(opp).to have_key('relevance_score')
            expect(opp).to have_key('timing_window')
          end
        end
      end

      describe '#orchestration_health' do
        it 'evaluates orchestration effectiveness' do
          health = subject.orchestration_health
          expect(health).to be_a(Hash)
          expect(health).to have_key('coordination_score')
          expect(health).to have_key('timing_optimization')
          expect(health).to have_key('resource_efficiency')
        end
      end

      describe '#vibe_ready?' do
        context 'with complete vibe data' do
          subject { create(:campaign, :with_vibe_data, :cultural_validated, :authenticity_verified, tenant: tenant) }

          it 'returns true' do
            expect(subject.vibe_ready?).to be true
          end
        end

        context 'with incomplete vibe data' do
          subject { create(:campaign, :vibe_pending, tenant: tenant) }

          it 'returns false' do
            expect(subject.vibe_ready?).to be false
          end
        end
      end

      describe '#requires_vibe_attention?' do
        context 'with flagged vibe status' do
          subject { create(:campaign, :vibe_flagged, tenant: tenant) }

          it 'returns true' do
            expect(subject.requires_vibe_attention?).to be true
          end
        end

        context 'with low vibe score' do
          subject { create(:campaign, vibe_score: 3.0, tenant: tenant) }

          it 'returns true' do
            expect(subject.requires_vibe_attention?).to be true
          end
        end

        context 'with good vibe metrics' do
          subject { create(:campaign, :cultural_validated, :authenticity_verified, vibe_score: 8.5, tenant: tenant) }

          it 'returns false' do
            expect(subject.requires_vibe_attention?).to be false
          end
        end
      end
    end

    describe 'vibe data aggregation' do
      subject { create(:campaign, tenant: tenant) }

      it 'aggregates latest vibe analysis data' do
        vibe_record = create(:vibe_analysis_record, campaign: subject, tenant: tenant)
        expect(subject.latest_vibe_analysis).to eq(vibe_record)
      end

      it 'aggregates latest authenticity check' do
        auth_check = create(:authenticity_check, campaign: subject, tenant: tenant)
        expect(subject.latest_authenticity_check).to eq(auth_check)
      end

      it 'aggregates active orchestration' do
        orchestration = create(:vibe_campaign_orchestration, campaign: subject, status: 'active', tenant: tenant)
        expect(subject.active_orchestration).to eq(orchestration)
      end
    end
  end
end
