# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Dashboard Redesign', type: :feature, js: true do
  let(:tenant) { create(:tenant) }
  let(:user) { create(:user, tenant: tenant) }
  let!(:campaigns) { create_list(:campaign, 5, tenant: tenant, created_by: user) }
  let!(:vibe_records) { campaigns.map { |c| create(:vibe_analysis_record, campaign: c) } }
  let!(:emotional_profiles) { create_list(:emotional_resonance_profile, 3, tenant: tenant, created_by: user) }
  let!(:authenticity_checks) { campaigns.map { |c| create(:authenticity_check, campaign: c) } }

  before do
    ActsAsTenant.current_tenant = tenant
    sign_in user
  end

  describe 'Dashboard Layout and Navigation' do
    it 'displays the sophisticated sidebar navigation' do
      visit '/dashboard'
      
      # Check sidebar exists with dark theme
      expect(page).to have_css('nav#sidebar')
      expect(page).to have_css('.bg-gradient-to-b.from-slate-900')
      
      # Check navigation links
      expect(page).to have_link('Dashboard')
      expect(page).to have_link('Campaigns')
      expect(page).to have_content('Vibe Analytics')
      expect(page).to have_content('Audiences')
      expect(page).to have_content('Settings')
    end

    it 'shows hover tooltips on compact navigation' do
      visit '/dashboard'
      
      # Test tooltip visibility (would need custom driver for actual hover testing)
      expect(page).to have_css('.nav-tooltip', visible: false)
    end

    it 'highlights the current page in navigation' do
      visit '/dashboard'
      
      expect(page).to have_css('a[href="/dashboard"].bg-gradient-to-r.from-blue-600.to-purple-600')
    end
  end

  describe 'Dashboard Header and Controls' do
    it 'displays personalized greeting and controls' do
      visit '/dashboard'
      
      # Personalized greeting
      expect(page).to have_content("Good")
      expect(page).to have_content(user.email.split('@').first.titleize)
      expect(page).to have_content('vibe marketing intelligence dashboard')
      
      # Dashboard controls
      expect(page).to have_select('', options: ['Last 7 days', 'Last 30 days', 'Last 90 days'])
      expect(page).to have_button('', title: 'Refresh Dashboard')
      expect(page).to have_link('New Campaign')
    end

    it 'allows filtering by time range' do
      visit '/dashboard'
      
      select 'Last 30 days', from: ''
      # Would test actual filtering functionality with more complete implementation
    end
  end

  describe 'Vibe Marketing KPI Cards' do
    it 'displays all four main KPI cards with proper metrics' do
      visit '/dashboard'
      
      # Overall Vibe Score Card
      within('[data-testid="vibe-score-card"], .bg-gradient-to-br.from-blue-50') do
        expect(page).to have_content('Overall Vibe Score')
        expect(page).to have_content('/10')
        expect(page).to have_content('+15.2%')
        expect(page).to have_content('Excellent engagement')
      end
      
      # Emotional Resonance Card  
      within('.bg-gradient-to-br.from-purple-50') do
        expect(page).to have_content('Emotional Resonance')
        expect(page).to have_content('/10')
        expect(page).to have_content('dominant')
      end
      
      # Authenticity Score Card
      within('.bg-gradient-to-br.from-emerald-50') do
        expect(page).to have_content('Authenticity Score')
        expect(page).to have_content('/10')
        expect(page).to have_content('approval rate')
      end
      
      # Cultural Alignment Card
      within('.bg-gradient-to-br.from-amber-50') do
        expect(page).to have_content('Cultural Alignment')
        expect(page).to have_content('/10')
        expect(page).to have_content('moments captured')
      end
    end

    it 'includes interactive hover effects on KPI cards' do
      visit '/dashboard'
      
      # Test hover shadow transition (visual testing would require screenshot comparison)
      expect(page).to have_css('.hover\\:shadow-md')
    end
  end

  describe 'Vibe Performance Analytics' do
    it 'displays vibe performance trend section' do
      visit '/dashboard'
      
      expect(page).to have_content('Vibe Performance Trend')
      expect(page).to have_content('Last 7 days')
      expect(page).to have_content('Interactive chart showing vibe performance over time')
    end

    it 'shows sentiment distribution with proper percentages' do
      visit '/dashboard'
      
      within('.bg-white:has-text("Sentiment Distribution")') do
        expect(page).to have_content('Sentiment Distribution')
        expect(page).to have_content('Positive')
        expect(page).to have_content('Neutral') 
        expect(page).to have_content('Negative')
        expect(page).to have_content('68%')
        expect(page).to have_content('22%')
        expect(page).to have_content('10%')
      end
    end
  end

  describe 'Campaign Management Integration' do
    it 'displays recent campaigns with vibe scores' do
      visit '/dashboard'
      
      within('.bg-white:has-text("Recent Campaign Performance")') do
        expect(page).to have_content('Recent Campaign Performance')
        expect(page).to have_link('View All')
        
        # Check that campaigns are displayed with vibe scores
        campaigns.first(3).each do |campaign|
          expect(page).to have_content(campaign.name)
          expect(page).to have_content(campaign.status.titleize)
        end
        
        expect(page).to have_content('Vibe:')
      end
    end

    it 'allows navigation to individual campaigns' do
      visit '/dashboard'
      
      within('.bg-white:has-text("Recent Campaign Performance")') do
        first_campaign = campaigns.first
        click_link('View', href: campaign_path(first_campaign))
      end
      
      expect(current_path).to eq(campaign_path(campaigns.first))
    end

    it 'provides quick campaign creation access' do
      visit '/dashboard'
      
      # Header create button
      within('.flex.items-center.space-x-4') do
        expect(page).to have_link('New Campaign', href: new_campaign_path)
      end
      
      # Sidebar create button
      within('.bg-gradient-to-br.from-slate-900') do
        expect(page).to have_link('Create Vibe Campaign', href: new_campaign_path)
      end
    end
  end

  describe 'Right Sidebar Analytics' do
    it 'displays trending vibes with rankings' do
      visit '/dashboard'
      
      within('.bg-white:has-text("Trending Vibes")') do
        expect(page).to have_content('Trending Vibes')
        
        # Check for numbered rankings
        (1..5).each do |rank|
          expect(page).to have_content(rank.to_s)
        end
        
        # Check for specific trending vibes
        %w[Authentic Optimistic Innovative Trustworthy Inspiring].each do |vibe|
          expect(page).to have_content(vibe)
        end
      end
    end

    it 'shows emotional breakdown visualization' do
      visit '/dashboard'
      
      within('.bg-white:has-text("Emotional Breakdown")') do
        expect(page).to have_content('Emotional Breakdown')
        
        # Check emotion categories
        %w[Joy Trust Anticipation Surprise Other].each do |emotion|
          expect(page).to have_content(emotion)
        end
      end
    end

    it 'displays cultural moments' do
      visit '/dashboard'
      
      within('.bg-white:has-text("Cultural Moments")') do
        expect(page).to have_content('Cultural Moments')
        expect(page).to have_content('Sustainability')
        expect(page).to have_content('Digital Wellness')
        expect(page).to have_content('Authentic Storytelling')
      end
    end
  end

  describe 'Responsive Design' do
    context 'on desktop' do
      it 'displays full sidebar and dashboard layout' do
        visit '/dashboard'
        
        # Desktop layout should show full sidebar
        expect(page).to have_css('nav#sidebar.lg\\:w-20.xl\\:w-64')
        
        # Grid should show 4 columns on large screens
        expect(page).to have_css('.grid.grid-cols-1.sm\\:grid-cols-2.lg\\:grid-cols-4')
      end
    end

    context 'on tablet' do
      before { page.driver.resize_window(768, 1024) }
      
      it 'adapts layout for tablet screens' do
        visit '/dashboard'
        
        # Should show responsive text sizing
        expect(page).to have_css('.text-3xl.lg\\:text-4xl')
      end
    end
  end

  describe 'Accessibility Features' do
    it 'includes proper semantic HTML structure' do
      visit '/dashboard'
      
      expect(page).to have_css('h1')
      expect(page).to have_css('h2')
      expect(page).to have_css('nav')
      expect(page).to have_css('main')
    end

    it 'provides keyboard navigation support' do
      visit '/dashboard'
      
      # Focus should be manageable via keyboard
      expect(page).to have_css('a[href]')
      expect(page).to have_css('button')
      expect(page).to have_css('select')
    end

    it 'includes proper color contrast' do
      visit '/dashboard'
      
      # High contrast text colors should be used
      expect(page).to have_css('.text-slate-900')
      expect(page).to have_css('.text-white')
      expect(page).to have_css('.text-slate-600')
    end
  end

  describe 'Interactive Features' do
    it 'includes real-time dashboard updates' do
      visit '/dashboard'
      
      # Check for auto-refresh functionality
      expect(page).to have_css('[data-controller="dashboard"]')
      expect(page).to have_css('[data-dashboard-refresh-interval-value="30000"]')
    end

    it 'shows loading states and animations' do
      visit '/dashboard'
      
      # Animated elements should be present
      expect(page).to have_css('.animate-pulse')
      expect(page).to have_css('[data-dashboard-target="metric"]')
    end

    it 'includes hover and transition effects' do
      visit '/dashboard'
      
      # Interactive elements should have hover states
      expect(page).to have_css('.hover\\:shadow-md')
      expect(page).to have_css('.transition-all')
      expect(page).to have_css('.hover\\:bg-slate-50')
    end
  end

  describe 'Error Handling and Edge Cases' do
    context 'with no campaigns' do
      let!(:campaigns) { [] }
      
      it 'displays appropriate empty states' do
        visit '/dashboard'
        
        expect(page).to have_content('No campaigns yet')
        expect(page).to have_content('Create your first vibe-optimized campaign to get started.')
        expect(page).to have_link('Create Campaign')
      end
    end

    context 'with loading states' do
      it 'handles metric loading gracefully' do
        visit '/dashboard'
        
        # Should show proper placeholder content
        expect(page).to have_content('Interactive chart showing vibe performance over time')
      end
    end
  end

  describe 'Integration with Existing Features' do
    it 'maintains compatibility with campaign creation flow' do
      visit '/dashboard'
      click_link 'New Campaign'
      
      expect(current_path).to eq(new_campaign_path)
    end

    it 'integrates with campaigns listing' do
      visit '/dashboard'
      click_link 'View All'
      
      expect(current_path).to eq(campaigns_path)
    end

    it 'preserves user session and tenant context' do
      visit '/dashboard'
      
      # User info should be displayed in sidebar
      expect(page).to have_content(user.email.first.upcase)
      expect(page).to have_content(user.email.split('@').first.titleize)
      expect(page).to have_content(tenant.name) if tenant.name
    end
  end
end