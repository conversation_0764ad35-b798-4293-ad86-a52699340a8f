require 'rails_helper'

RSpec.describe "Campaigns", type: :request do
  let(:tenant) { create(:tenant) }
  let(:user) { create(:user, tenant: tenant) }
  let(:campaign) { create(:campaign, tenant: tenant, created_by: user) }

  before do
    ActsAsTenant.current_tenant = tenant
  end

  describe "Authentication" do
    it "requires authentication for campaigns index" do
      get campaigns_path
      expect(response).to have_http_status(:redirect)
    end

    it "requires authentication for campaign show" do
      get campaign_path(campaign)
      expect(response).to have_http_status(:redirect)
    end

    it "requires authentication for new campaign" do
      get new_campaign_path
      expect(response).to have_http_status(:redirect)
    end
  end

  describe "Campaign Actions" do
    before do
      sign_in user
      ActsAsTenant.current_tenant = tenant
    end

    describe "PATCH /campaigns/:id/activate" do
      context "when campaign can be activated" do
        let(:campaign) { create(:campaign, status: :draft, tenant: tenant, created_by: user) }

        it "activates the campaign" do
          patch "/campaigns/#{campaign.id}/activate"

          expect(response).to redirect_to(campaign_path(campaign))
          expect(campaign.reload.status).to eq("active")
          expect(flash[:notice]).to eq("Campaign activated successfully.")
        end
      end

      context "when campaign cannot be activated" do
        let(:campaign) { create(:campaign, status: :completed, tenant: tenant, created_by: user) }

        it "does not activate the campaign" do
          patch "/campaigns/#{campaign.id}/activate"

          expect(response).to redirect_to(campaign_path(campaign))
          expect(campaign.reload.status).to eq("completed")
          expect(flash[:alert]).to eq("Campaign cannot be activated in its current state.")
        end
      end
    end

    describe "PATCH /campaigns/:id/pause" do
      context "when campaign is active" do
        let(:campaign) { create(:campaign, status: :active, tenant: tenant, created_by: user) }

        it "pauses the campaign" do
          patch "/campaigns/#{campaign.id}/pause"

          expect(response).to redirect_to(campaign_path(campaign))
          expect(campaign.reload.status).to eq("paused")
          expect(flash[:notice]).to eq("Campaign paused successfully.")
        end
      end

      context "when campaign is not active" do
        let(:campaign) { create(:campaign, status: :draft, tenant: tenant, created_by: user) }

        it "does not pause the campaign" do
          patch "/campaigns/#{campaign.id}/pause"

          expect(response).to redirect_to(campaign_path(campaign))
          expect(campaign.reload.status).to eq("draft")
          expect(flash[:alert]).to eq("Only active campaigns can be paused.")
        end
      end
    end

    describe "PATCH /campaigns/:id/complete" do
      context "when campaign is active" do
        let(:campaign) { create(:campaign, status: :active, tenant: tenant, created_by: user) }

        it "completes the campaign" do
          patch "/campaigns/#{campaign.id}/complete"

          expect(response).to redirect_to(campaign_path(campaign))
          expect(campaign.reload.status).to eq("completed")
          expect(flash[:notice]).to eq("Campaign completed successfully.")
        end
      end

      context "when campaign is paused" do
        let(:campaign) { create(:campaign, status: :paused, tenant: tenant, created_by: user) }

        it "completes the campaign" do
          patch "/campaigns/#{campaign.id}/complete"

          expect(response).to redirect_to(campaign_path(campaign))
          expect(campaign.reload.status).to eq("completed")
          expect(flash[:notice]).to eq("Campaign completed successfully.")
        end
      end

      context "when campaign cannot be completed" do
        let(:campaign) { create(:campaign, status: :draft, tenant: tenant, created_by: user) }

        it "does not complete the campaign" do
          patch "/campaigns/#{campaign.id}/complete"

          expect(response).to redirect_to(campaign_path(campaign))
          expect(campaign.reload.status).to eq("draft")
          expect(flash[:alert]).to eq("Campaign cannot be completed in its current state.")
        end
      end
    end
  end
end
