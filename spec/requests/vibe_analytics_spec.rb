# frozen_string_literal: true

require 'rails_helper'

RSpec.describe "VibeAnalytics", type: :request do
  let(:tenant) { create(:tenant) }
  let(:user) { create(:user, tenant: tenant) }

  before do
    sign_in user
    ActsAsTenant.current_tenant = tenant
  end

  describe "GET /vibe_analytics" do
    context "when user is authenticated" do
      before do
        # Create some test campaigns with vibe data
        create(:campaign,
               tenant: tenant,
               created_by: user,
               vibe_status: 'vibe_approved',
               emotional_tone: 'positive',
               cultural_relevance_score: 85.5,
               authenticity_score: 92.0)

        create(:campaign,
               tenant: tenant,
               created_by: user,
               vibe_status: 'vibe_flagged',
               emotional_tone: 'neutral',
               cultural_relevance_score: 65.0,
               authenticity_score: 78.5)
      end

      it "returns successful response" do
        get vibe_analytics_path
        expect(response).to have_http_status(:success)
      end

      it "assigns analytics data" do
        get vibe_analytics_path
        expect(assigns(:analytics_data)).to be_present
        expect(assigns(:analytics_data)).to have_key(:overview)
        expect(assigns(:analytics_data)).to have_key(:emotional_trends)
        expect(assigns(:analytics_data)).to have_key(:cultural_metrics)
        expect(assigns(:analytics_data)).to have_key(:authenticity_stats)
      end

      it "calculates overview metrics correctly" do
        get vibe_analytics_path
        overview = assigns(:analytics_data)[:overview]

        expect(overview[:total_campaigns]).to eq(2)
        expect(overview[:vibe_approved]).to eq(1)
        expect(overview[:vibe_flagged]).to eq(1)
        expect(overview[:approval_rate]).to eq(50.0)
      end

      it "handles date range filter" do
        get vibe_analytics_path, params: { date_range: '7_days' }
        expect(response).to have_http_status(:success)
        expect(assigns(:date_range)).to eq('7_days')
      end

      it "handles campaign filter" do
        get vibe_analytics_path, params: { campaign_filter: 'vibe_approved' }
        expect(response).to have_http_status(:success)
        expect(assigns(:campaign_filter)).to eq('vibe_approved')
      end

      it "returns JSON when requested" do
        get vibe_analytics_path, headers: { 'Accept' => 'application/json' }
        expect(response).to have_http_status(:success)
        expect(response.content_type).to include('application/json')

        json_response = JSON.parse(response.body)
        expect(json_response).to have_key('overview')
        expect(json_response).to have_key('emotional_trends')
      end
    end

    context "when user is not authenticated" do
      before { sign_out user }

      it "redirects to sign in" do
        get vibe_analytics_path
        expect(response).to redirect_to(new_user_session_path)
      end
    end
  end

  describe "GET /vibe_analytics/emotional_trends" do
    it "returns emotional trends data as JSON" do
      create(:campaign, tenant: tenant, created_by: user, emotional_tone: 'positive')
      create(:campaign, tenant: tenant, created_by: user, emotional_tone: 'positive')
      create(:campaign, tenant: tenant, created_by: user, emotional_tone: 'neutral')

      get vibe_analytics_emotional_trends_path
      expect(response).to have_http_status(:success)
      expect(response.content_type).to include('application/json')
    end
  end

  describe "GET /vibe_analytics/cultural_breakdown" do
    it "returns cultural breakdown data as JSON" do
      create(:campaign, tenant: tenant, created_by: user, cultural_relevance_score: 85.0)
      create(:campaign, tenant: tenant, created_by: user, cultural_relevance_score: 92.0)

      get vibe_analytics_cultural_breakdown_path
      expect(response).to have_http_status(:success)
      expect(response.content_type).to include('application/json')
    end
  end

  describe "GET /vibe_analytics/authenticity_metrics" do
    it "returns authenticity metrics data as JSON" do
      create(:campaign, tenant: tenant, created_by: user, authenticity_score: 88.0)
      create(:campaign, tenant: tenant, created_by: user, authenticity_score: 95.0)

      get vibe_analytics_authenticity_metrics_path
      expect(response).to have_http_status(:success)
      expect(response.content_type).to include('application/json')
    end
  end

  describe "GET /vibe_analytics/vibe_performance" do
    it "returns vibe performance correlation data as JSON" do
      campaign = create(:campaign, tenant: tenant, created_by: user, authenticity_score: 90.0, cultural_relevance_score: 85.0)
      create(:campaign_metric, campaign: campaign, engagement_rate: 4.5, click_through_rate: 2.8, conversion_rate: 1.2)

      get vibe_analytics_vibe_performance_path
      expect(response).to have_http_status(:success)
      expect(response.content_type).to include('application/json')
    end
  end

  describe "Analytics calculations" do
    let(:controller) { VibeAnalyticsController.new }

    before do
      controller.instance_variable_set(:@date_range, '30_days')
      controller.instance_variable_set(:@campaign_filter, 'all')
      allow(controller).to receive(:current_user).and_return(user)
      allow(ActsAsTenant).to receive(:current_tenant).and_return(tenant)
    end

    describe "#build_overview_metrics" do
      it "calculates metrics correctly with no campaigns" do
        campaigns = Campaign.none
        overview = controller.send(:build_overview_metrics, campaigns)

        expect(overview[:total_campaigns]).to eq(0)
        expect(overview[:approval_rate]).to eq(0)
        expect(overview[:avg_cultural_score]).to eq(0)
        expect(overview[:avg_authenticity_score]).to eq(0)
      end

      it "calculates metrics correctly with campaigns" do
        campaigns = [
          create(:campaign, tenant: tenant, vibe_status: 'vibe_approved', cultural_relevance_score: 80.0, authenticity_score: 85.0),
          create(:campaign, tenant: tenant, vibe_status: 'vibe_approved', cultural_relevance_score: 90.0, authenticity_score: 95.0),
          create(:campaign, tenant: tenant, vibe_status: 'vibe_flagged', cultural_relevance_score: 60.0, authenticity_score: 70.0)
        ]

        overview = controller.send(:build_overview_metrics, campaigns)

        expect(overview[:total_campaigns]).to eq(3)
        expect(overview[:vibe_approved]).to eq(2)
        expect(overview[:vibe_flagged]).to eq(1)
        expect(overview[:approval_rate]).to eq(66.7)
        expect(overview[:avg_cultural_score]).to eq(76.67)
        expect(overview[:avg_authenticity_score]).to eq(83.33)
      end
    end

    describe "#build_emotional_summary" do
      it "groups campaigns by emotional tone" do
        campaigns = [
          create(:campaign, tenant: tenant, emotional_tone: 'positive'),
          create(:campaign, tenant: tenant, emotional_tone: 'positive'),
          create(:campaign, tenant: tenant, emotional_tone: 'neutral'),
          create(:campaign, tenant: tenant, emotional_tone: nil)
        ]

        summary = controller.send(:build_emotional_summary, campaigns)

        expect(summary[:distribution]).to eq({ 'positive' => 2, 'neutral' => 1 })
        expect(summary[:total_analyzed]).to eq(3)
        expect(summary[:trending_tones]).to eq({ 'positive' => 2, 'neutral' => 1 })
      end
    end

    describe "#build_cultural_summary" do
      it "categorizes cultural relevance scores" do
        campaigns = [
          create(:campaign, tenant: tenant, cultural_relevance_score: 95.0),
          create(:campaign, tenant: tenant, cultural_relevance_score: 82.0),
          create(:campaign, tenant: tenant, cultural_relevance_score: 68.0),
          create(:campaign, tenant: tenant, cultural_relevance_score: 45.0),
          create(:campaign, tenant: tenant, cultural_relevance_score: nil)
        ]

        summary = controller.send(:build_cultural_summary, campaigns)

        expect(summary[:score_distribution]['Excellent (90-100)']).to eq(1)
        expect(summary[:score_distribution]['Good (75-89)']).to eq(1)
        expect(summary[:score_distribution]['Fair (60-74)']).to eq(1)
        expect(summary[:score_distribution]['Poor (0-59)']).to eq(1)
        expect(summary[:total_analyzed]).to eq(4)
        expect(summary[:average_score]).to eq(72.5)
      end
    end
  end
end
