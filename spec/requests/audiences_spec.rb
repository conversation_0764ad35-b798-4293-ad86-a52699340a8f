# frozen_string_literal: true

require 'rails_helper'

RSpec.describe "Audiences", type: :request do
  let(:tenant) { create(:tenant) }
  let(:user) { create(:user, tenant: tenant) }
  let(:audience) { create(:audience, tenant: tenant, created_by: user) }

  before do
    sign_in user
    ActsAsTenant.current_tenant = tenant
  end

  describe "GET /audiences" do
    context "when user is authenticated" do
      before do
        create_list(:audience, 3, tenant: tenant, created_by: user)
      end

      it "returns successful response" do
        get audiences_path
        expect(response).to have_http_status(:success)
      end

      it "assigns audiences" do
        get audiences_path
        expect(assigns(:audiences)).to be_present
        expect(assigns(:audiences).count).to eq(3)
      end

      it "filters by search term" do
        audience1 = create(:audience, name: "Tech Enthusiasts", tenant: tenant, created_by: user)
        audience2 = create(:audience, name: "Food Lovers", tenant: tenant, created_by: user)

        get audiences_path, params: { search: "Tech" }
        expect(assigns(:audiences)).to include(audience1)
        expect(assigns(:audiences)).not_to include(audience2)
      end

      it "filters by cultural context" do
        audience1 = create(:audience, cultural_context: "western", tenant: tenant, created_by: user)
        audience2 = create(:audience, cultural_context: "eastern", tenant: tenant, created_by: user)

        get audiences_path, params: { cultural_context: "western" }
        expect(assigns(:audiences)).to include(audience1)
        expect(assigns(:audiences)).not_to include(audience2)
      end

      it "filters by primary language" do
        audience1 = create(:audience, primary_language: "en", tenant: tenant, created_by: user)
        audience2 = create(:audience, primary_language: "es", tenant: tenant, created_by: user)

        get audiences_path, params: { primary_language: "en" }
        expect(assigns(:audiences)).to include(audience1)
        expect(assigns(:audiences)).not_to include(audience2)
      end

      it "returns JSON when requested" do
        get audiences_path, headers: { 'Accept' => 'application/json' }
        expect(response).to have_http_status(:success)
        expect(response.content_type).to include('application/json')
      end
    end

    context "when user is not authenticated" do
      before { sign_out user }

      it "redirects to sign in" do
        get audiences_path
        expect(response).to redirect_to(new_user_session_path)
      end
    end
  end

  describe "GET /audiences/:id" do
    it "returns successful response" do
      get audience_path(audience)
      expect(response).to have_http_status(:success)
    end

    it "assigns audience and analytics data" do
      get audience_path(audience)
      expect(assigns(:audience)).to eq(audience)
      expect(assigns(:audience_analytics)).to be_present
      expect(assigns(:campaigns)).to be_present
    end

    it "includes analytics data structure" do
      get audience_path(audience)
      analytics = assigns(:audience_analytics)

      expect(analytics).to have_key(:overview)
      expect(analytics).to have_key(:performance_metrics)
      expect(analytics).to have_key(:cultural_alignment)
      expect(analytics).to have_key(:engagement_trends)
      expect(analytics).to have_key(:vibe_compatibility)
    end
  end

  describe "GET /audiences/new" do
    it "returns successful response" do
      get new_audience_path
      expect(response).to have_http_status(:success)
    end

    it "assigns new audience" do
      get new_audience_path
      expect(assigns(:audience)).to be_a_new(Audience)
    end
  end

  describe "POST /audiences" do
    let(:valid_attributes) do
      {
        name: "Test Audience",
        description: "A test audience segment",
        primary_language: "en",
        cultural_context: "western",
        age_range_min: 25,
        age_range_max: 45,
        target_demographics: "Young professionals",
        interests: "technology, innovation",
        cultural_values: "Innovation and efficiency"
      }
    end

    context "with valid parameters" do
      it "creates a new audience" do
        expect {
          post audiences_path, params: { audience: valid_attributes }
        }.to change(Audience, :count).by(1)
      end

      it "redirects to the created audience" do
        post audiences_path, params: { audience: valid_attributes }
        expect(response).to redirect_to(Audience.last)
      end

      it "sets the created_by to current user" do
        post audiences_path, params: { audience: valid_attributes }
        expect(Audience.last.created_by).to eq(user)
      end

      it "sets the tenant correctly" do
        post audiences_path, params: { audience: valid_attributes }
        expect(Audience.last.tenant).to eq(tenant)
      end
    end

    context "with invalid parameters" do
      it "does not create a new audience" do
        expect {
          post audiences_path, params: { audience: { name: "" } }
        }.not_to change(Audience, :count)
      end

      it "renders the new template" do
        post audiences_path, params: { audience: { name: "" } }
        expect(response).to have_http_status(:unprocessable_entity)
      end
    end
  end

  describe "GET /audiences/:id/edit" do
    it "returns successful response" do
      get edit_audience_path(audience)
      expect(response).to have_http_status(:success)
    end

    it "assigns the audience" do
      get edit_audience_path(audience)
      expect(assigns(:audience)).to eq(audience)
    end
  end

  describe "PATCH /audiences/:id" do
    context "with valid parameters" do
      let(:new_attributes) do
        {
          name: "Updated Audience Name",
          description: "Updated description"
        }
      end

      it "updates the audience" do
        patch audience_path(audience), params: { audience: new_attributes }
        audience.reload
        expect(audience.name).to eq("Updated Audience Name")
        expect(audience.description).to eq("Updated description")
      end

      it "redirects to the audience" do
        patch audience_path(audience), params: { audience: new_attributes }
        expect(response).to redirect_to(audience)
      end
    end

    context "with invalid parameters" do
      it "renders the edit template" do
        patch audience_path(audience), params: { audience: { name: "" } }
        expect(response).to have_http_status(:unprocessable_entity)
      end
    end
  end

  describe "DELETE /audiences/:id" do
    it "destroys the audience" do
      audience_to_delete = create(:audience, tenant: tenant, created_by: user)
      expect {
        delete audience_path(audience_to_delete)
      }.to change(Audience, :count).by(-1)
    end

    it "redirects to audiences index" do
      delete audience_path(audience)
      expect(response).to redirect_to(audiences_path)
    end
  end

  describe "GET /audiences/:id/analytics" do
    it "returns analytics data as JSON" do
      get analytics_audience_path(audience)
      expect(response).to have_http_status(:success)
      expect(response.content_type).to include('application/json')
    end
  end

  describe "GET /audiences/:id/cultural_alignment" do
    it "returns cultural alignment data as JSON" do
      get cultural_alignment_audience_path(audience)
      expect(response).to have_http_status(:success)
      expect(response.content_type).to include('application/json')
    end
  end

  describe "GET /audiences/:id/engagement_metrics" do
    it "returns engagement metrics data as JSON" do
      get engagement_metrics_audience_path(audience)
      expect(response).to have_http_status(:success)
      expect(response.content_type).to include('application/json')
    end
  end

  describe "Audience analytics calculations" do
    let(:controller) { AudiencesController.new }

    before do
      allow(controller).to receive(:current_user).and_return(user)
      allow(ActsAsTenant).to receive(:current_tenant).and_return(tenant)
    end

    describe "#build_audience_overview" do
      it "calculates overview metrics correctly" do
        campaign1 = create(:campaign, tenant: tenant, status: 'active')
        campaign2 = create(:campaign, tenant: tenant, status: 'completed')
        campaigns = [ campaign1, campaign2 ]

        overview = controller.send(:build_audience_overview, audience, campaigns)

        expect(overview[:total_campaigns]).to eq(2)
        expect(overview[:active_campaigns]).to eq(1)
        expect(overview[:primary_language]).to eq(audience.primary_language)
        expect(overview[:cultural_alignment_score]).to eq(audience.cultural_alignment_score || 0)
      end
    end

    describe "#calculate_engagement_score" do
      it "returns 0 for audience with no campaigns" do
        score = controller.send(:calculate_engagement_score, audience)
        expect(score).to eq(0)
      end

      it "calculates average engagement rate" do
        campaign1 = create(:campaign, tenant: tenant)
        campaign2 = create(:campaign, tenant: tenant)
        create(:campaign_audience, campaign: campaign1, audience: audience, tenant: tenant)
        create(:campaign_audience, campaign: campaign2, audience: audience, tenant: tenant)
        create(:campaign_metric, campaign: campaign1, engagement_rate: 4.0)
        create(:campaign_metric, campaign: campaign2, engagement_rate: 6.0)

        score = controller.send(:calculate_engagement_score, audience)
        expect(score).to eq(5.0)
      end
    end
  end
end
