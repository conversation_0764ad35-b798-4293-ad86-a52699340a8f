# frozen_string_literal: true

require 'rails_helper'

RSpec.describe RubyLlmService, type: :service do
  let(:tenant) { create(:tenant) }
  let(:context) { { test: true } }

  describe '#initialize' do
    context 'when tenant is provided' do
      it 'initializes successfully with default settings' do
        service = described_class.new(tenant: tenant)
        expect(service.instance_variable_get(:@tenant)).to eq(tenant)
        expect(service.instance_variable_get(:@provider_strategy)).to eq(:balanced)
      end

      it 'accepts custom provider strategy' do
        service = described_class.new(tenant: tenant, provider_strategy: :cost_sensitive)
        expect(service.instance_variable_get(:@provider_strategy)).to eq(:cost_sensitive)
      end

      it 'accepts custom context' do
        service = described_class.new(tenant: tenant, context: context)
        expect(service.instance_variable_get(:@context)).to eq(context)
      end
    end

    context 'when tenant is missing' do
      it 'raises ConfigurationError' do
        expect { described_class.new(tenant: nil) }.to raise_error(
          RubyLlmService::ConfigurationError,
          "Tenant must be present"
        )
      end
    end

    context 'when no providers are available' do
      before do
        allow(RubyLLMExtensions).to receive(:check_provider_health).and_return(false)
      end

      it 'raises ConfigurationError' do
        expect { described_class.new(tenant: tenant) }.to raise_error(
          RubyLlmService::ConfigurationError,
          "No AI providers configured"
        )
      end
    end
  end

  describe '#provider_health_check' do
    let(:service) { described_class.new(tenant: tenant) }

    before do
      allow(RubyLLMExtensions).to receive(:check_provider_health).and_return(true)
    end

    it 'returns health status for all providers' do
      health_check = service.provider_health_check

      expect(health_check).to be_a(Hash)
      expect(health_check.keys).to include('openai', 'anthropic', 'gemini', 'deepseek')

      health_check.each do |provider, status|
        expect(status).to have_key(:available)
        expect(status).to have_key(:last_error)
        expect(status).to have_key(:circuit_open)
      end
    end
  end

  describe '#generate_content' do
    let(:service) { described_class.new(tenant: tenant) }
    let(:prompt) { "Test prompt for AI generation" }
    let(:mock_response) do
      double('AiResponse',
        content: 'Generated content',
        model: 'gpt-4o-mini',
        provider: 'openai',
        tokens_used: 100,
        cost: 0.001
      )
    end

    before do
      allow(RubyLLMExtensions).to receive(:check_provider_health).and_return(true)
      allow(service).to receive(:generate_content_with_error_handling).and_return(mock_response)
    end

    it 'generates content successfully' do
      result = service.generate_content(prompt, task_type: :creative_content)

      expect(result).to eq(mock_response)
      expect(result.content).to eq('Generated content')
      expect(result.provider).to eq('openai')
    end

    it 'accepts different task types' do
      expect(service).to receive(:generate_content_with_error_handling)
        .with(prompt, :data_analysis, anything)
        .and_return(mock_response)

      service.generate_content(prompt, task_type: :data_analysis)
    end

    it 'accepts custom options' do
      options = { temperature: 0.8, max_tokens: 1000 }

      expect(service).to receive(:generate_content_with_error_handling)
        .with(prompt, :creative_content, hash_including(options))
        .and_return(mock_response)

      service.generate_content(prompt, task_type: :creative_content, **options)
    end
  end

  describe 'provider selection strategies' do
    let(:service) { described_class.new(tenant: tenant, provider_strategy: :cost_sensitive) }

    before do
      allow(RubyLLMExtensions).to receive(:check_provider_health).and_return(true)
    end

    it 'uses cost-sensitive strategy' do
      expect(service.instance_variable_get(:@provider_strategy)).to eq(:cost_sensitive)
    end

    it 'supports different strategies' do
      strategies = [ :balanced, :cost_sensitive, :performance_optimized, :creative_content, :data_analysis ]

      strategies.each do |strategy|
        service = described_class.new(tenant: tenant, provider_strategy: strategy)
        expect(service.instance_variable_get(:@provider_strategy)).to eq(strategy)
      end
    end
  end

  describe 'error handling' do
    let(:service) { described_class.new(tenant: tenant) }

    before do
      allow(RubyLLMExtensions).to receive(:check_provider_health).and_return(true)
    end

    it 'handles validation errors' do
      expect { service.generate_content("", task_type: :creative_content) }
        .to raise_error(RubyLlmService::ValidationError)
    end

    it 'handles configuration errors gracefully' do
      allow(service).to receive(:validate_request!).and_raise(RubyLlmService::ConfigurationError, "Test error")

      expect { service.generate_content("test", task_type: :creative_content) }
        .to raise_error(RubyLlmService::ConfigurationError, "Test error")
    end
  end

  describe 'integration with agent services' do
    let(:campaign) { create(:campaign, tenant: tenant) }

    context 'EmailSpecialistAgentService' do
      it 'uses RubyLlmService by default' do
        service = EmailSpecialistAgentService.new(campaign: campaign)
        ai_service = service.instance_variable_get(:@ai_service)

        expect(ai_service).to be_a(RubyLlmService)
        expect(ai_service.instance_variable_get(:@tenant)).to eq(tenant)
      end
    end

    context 'SocialMediaAgentService' do
      it 'uses RubyLlmService by default' do
        service = SocialMediaAgentService.new(campaign: campaign)
        ai_service = service.instance_variable_get(:@ai_service)

        expect(ai_service).to be_a(RubyLlmService)
        expect(ai_service.instance_variable_get(:@tenant)).to eq(tenant)
      end
    end
  end
end

# Test the AiResponse wrapper class
RSpec.describe AiResponse, type: :model do
  let(:response) do
    described_class.new(
      content: 'Test content',
      model: 'gpt-4o',
      provider: 'openai',
      tokens_used: 100,
      cost: 0.001
    )
  end

  describe '#successful?' do
    it 'returns true when content is present' do
      expect(response.successful?).to be true
    end

    it 'returns false when content is blank' do
      response.content = ''
      expect(response.successful?).to be false
    end
  end

  describe '#to_h' do
    it 'returns hash representation' do
      hash = response.to_h

      expect(hash).to include(
        content: 'Test content',
        model: 'gpt-4o',
        provider: 'openai',
        tokens_used: 100,
        cost: 0.001
      )
    end
  end
end
