# frozen_string_literal: true

require 'rails_helper'

RSpec.describe VibeAnalysisService, type: :service do
  let(:tenant) { create(:tenant) }
  let(:campaign) { create(:campaign, tenant: tenant) }
  let(:service) { described_class.new(campaign) }

  before do
    ActsAsTenant.current_tenant = tenant
  end

  describe '#initialize' do
    it 'initializes with a campaign' do
      expect(service.campaign).to eq(campaign)
    end

    it 'raises error without campaign' do
      expect { described_class.new(nil) }.to raise_error(ArgumentError, 'Campaign is required')
    end
  end

  describe '#analyze' do
    let(:analysis_params) do
      {
        emotional_analysis: true,
        cultural_analysis: true,
        authenticity_analysis: true,
        psychographic_analysis: true
      }
    end

    it 'performs comprehensive vibe analysis' do
      result = service.analyze(analysis_params)

      expect(result).to be_a(Hash)
      expect(result).to have_key(:success)
      expect(result).to have_key(:vibe_analysis_record)
      expect(result).to have_key(:analysis_results)
    end

    it 'creates vibe analysis record' do
      expect { service.analyze(analysis_params) }.to change { VibeAnalysisRecord.count }.by(1)
    end

    it 'associates analysis record with campaign' do
      result = service.analyze(analysis_params)
      vibe_record = result[:vibe_analysis_record]

      expect(vibe_record.campaign).to eq(campaign)
      expect(vibe_record.tenant).to eq(tenant)
    end

    context 'with emotional analysis enabled' do
      let(:analysis_params) { { emotional_analysis: true } }

      it 'includes emotional analysis in results' do
        result = service.analyze(analysis_params)
        analysis_results = result[:analysis_results]

        expect(analysis_results).to have_key(:emotional_patterns)
        expect(analysis_results[:emotional_patterns]).to be_a(Hash)
      end
    end

    context 'with cultural analysis enabled' do
      let(:analysis_params) { { cultural_analysis: true } }

      it 'includes cultural analysis in results' do
        result = service.analyze(analysis_params)
        analysis_results = result[:analysis_results]

        expect(analysis_results).to have_key(:cultural_factors)
        expect(analysis_results[:cultural_factors]).to be_a(Hash)
      end
    end

    context 'with authenticity analysis enabled' do
      let(:analysis_params) { { authenticity_analysis: true } }

      it 'includes authenticity analysis in results' do
        result = service.analyze(analysis_params)
        analysis_results = result[:analysis_results]

        expect(analysis_results).to have_key(:authenticity_dimensions)
        expect(analysis_results[:authenticity_dimensions]).to be_a(Hash)
      end
    end

    context 'with psychographic analysis enabled' do
      let(:analysis_params) { { psychographic_analysis: true } }

      it 'includes psychographic analysis in results' do
        result = service.analyze(analysis_params)
        analysis_results = result[:analysis_results]

        expect(analysis_results).to have_key(:psychographic_profile)
        expect(analysis_results[:psychographic_profile]).to be_a(Hash)
      end
    end

    context 'with analysis failure' do
      before do
        allow(service).to receive(:perform_emotional_analysis).and_raise(StandardError, 'Analysis failed')
      end

      it 'returns error result' do
        result = service.analyze({ emotional_analysis: true })

        expect(result[:success]).to be false
        expect(result[:error]).to include('Analysis failed')
      end

      it 'does not create vibe analysis record on failure' do
        expect { service.analyze({ emotional_analysis: true }) }.not_to change { VibeAnalysisRecord.count }
      end
    end
  end

  describe '#emotional_analysis' do
    it 'analyzes emotional patterns in campaign content' do
      result = service.emotional_analysis

      expect(result).to be_a(Hash)
      expect(result).to have_key(:dominant_emotions)
      expect(result).to have_key(:emotional_intensity)
      expect(result).to have_key(:emotional_triggers)
      expect(result).to have_key(:resonance_factors)
    end

    it 'identifies dominant emotions' do
      result = service.emotional_analysis
      dominant_emotions = result[:dominant_emotions]

      expect(dominant_emotions).to be_an(Array)
      expect(dominant_emotions).not_to be_empty
    end

    it 'calculates emotional intensity score' do
      result = service.emotional_analysis
      intensity = result[:emotional_intensity]

      expect(intensity).to be_a(Float)
      expect(intensity).to be_between(0, 10)
    end
  end

  describe '#cultural_analysis' do
    it 'analyzes cultural relevance and sensitivity' do
      result = service.cultural_analysis

      expect(result).to be_a(Hash)
      expect(result).to have_key(:cultural_relevance_score)
      expect(result).to have_key(:trend_alignment)
      expect(result).to have_key(:cultural_sensitivity)
      expect(result).to have_key(:moment_opportunities)
    end

    it 'evaluates cultural relevance' do
      result = service.cultural_analysis
      relevance_score = result[:cultural_relevance_score]

      expect(relevance_score).to be_a(Float)
      expect(relevance_score).to be_between(0, 10)
    end

    it 'identifies cultural moment opportunities' do
      result = service.cultural_analysis
      opportunities = result[:moment_opportunities]

      expect(opportunities).to be_an(Array)
      opportunities.each do |opportunity|
        expect(opportunity).to have_key(:moment_type)
        expect(opportunity).to have_key(:relevance_score)
        expect(opportunity).to have_key(:timing_window)
      end
    end
  end

  describe '#authenticity_analysis' do
    it 'analyzes authenticity dimensions' do
      result = service.authenticity_analysis

      expect(result).to be_a(Hash)
      expect(result).to have_key(:overall_authenticity_score)
      expect(result).to have_key(:brand_consistency)
      expect(result).to have_key(:cultural_sensitivity)
      expect(result).to have_key(:emotional_genuineness)
      expect(result).to have_key(:claim_accuracy)
    end

    it 'calculates overall authenticity score' do
      result = service.authenticity_analysis
      score = result[:overall_authenticity_score]

      expect(score).to be_a(Float)
      expect(score).to be_between(0, 10)
    end

    it 'evaluates brand consistency' do
      result = service.authenticity_analysis
      consistency = result[:brand_consistency]

      expect(consistency).to be_a(Hash)
      expect(consistency).to have_key(:score)
      expect(consistency).to have_key(:alignment_factors)
      expect(consistency).to have_key(:deviation_points)
    end
  end

  describe '#psychographic_analysis' do
    it 'analyzes psychographic characteristics' do
      result = service.psychographic_analysis

      expect(result).to be_a(Hash)
      expect(result).to have_key(:personality_traits)
      expect(result).to have_key(:values_alignment)
      expect(result).to have_key(:lifestyle_factors)
      expect(result).to have_key(:behavioral_triggers)
    end

    it 'identifies personality traits' do
      result = service.psychographic_analysis
      traits = result[:personality_traits]

      expect(traits).to be_a(Hash)
      expect(traits).not_to be_empty
    end

    it 'evaluates values alignment' do
      result = service.psychographic_analysis
      alignment = result[:values_alignment]

      expect(alignment).to be_a(Hash)
      expect(alignment).to have_key(:core_values)
      expect(alignment).to have_key(:alignment_score)
    end
  end

  describe '#comprehensive_score' do
    before do
      allow(service).to receive(:emotional_analysis).and_return({ emotional_intensity: 8.0 })
      allow(service).to receive(:cultural_analysis).and_return({ cultural_relevance_score: 7.5 })
      allow(service).to receive(:authenticity_analysis).and_return({ overall_authenticity_score: 8.5 })
      allow(service).to receive(:psychographic_analysis).and_return({ overall_psychographic_score: 7.8 })
    end

    it 'calculates comprehensive vibe score' do
      score = service.comprehensive_score

      expect(score).to be_a(Float)
      expect(score).to be_between(0, 10)
    end

    it 'weights different analysis components appropriately' do
      score = service.comprehensive_score

      # Should be approximately the weighted average of component scores
      expected_range = 7.5..8.5
      expect(score).to be_between(expected_range.first, expected_range.last)
    end
  end

  describe '#recommendations' do
    it 'provides optimization recommendations' do
      recommendations = service.recommendations

      expect(recommendations).to be_an(Array)
      expect(recommendations).not_to be_empty

      recommendations.each do |rec|
        expect(rec).to have_key(:category)
        expect(rec).to have_key(:recommendation)
        expect(rec).to have_key(:priority)
        expect(rec).to have_key(:impact_level)
      end
    end

    it 'categorizes recommendations appropriately' do
      recommendations = service.recommendations
      categories = recommendations.map { |r| r[:category] }.uniq

      expected_categories = %w[emotional cultural authenticity psychographic]
      expect(categories).to include(*expected_categories)
    end
  end

  describe '#analysis_summary' do
    it 'provides comprehensive analysis summary' do
      summary = service.analysis_summary

      expect(summary).to be_a(Hash)
      expect(summary).to have_key(:overall_vibe_score)
      expect(summary).to have_key(:component_scores)
      expect(summary).to have_key(:key_insights)
      expect(summary).to have_key(:improvement_areas)
      expect(summary).to have_key(:recommendations)
    end

    it 'includes component scores breakdown' do
      summary = service.analysis_summary
      component_scores = summary[:component_scores]

      expect(component_scores).to have_key(:emotional)
      expect(component_scores).to have_key(:cultural)
      expect(component_scores).to have_key(:authenticity)
      expect(component_scores).to have_key(:psychographic)
    end
  end

  describe 'private methods' do
    describe '#extract_content_elements' do
      it 'extracts relevant content elements from campaign' do
        elements = service.send(:extract_content_elements)

        expect(elements).to be_a(Hash)
        expect(elements).to have_key(:text_content)
        expect(elements).to have_key(:visual_elements)
        expect(elements).to have_key(:messaging_tone)
      end
    end

    describe '#calculate_weighted_score' do
      let(:scores) { { emotional: 8.0, cultural: 7.5, authenticity: 8.5, psychographic: 7.8 } }
      let(:weights) { { emotional: 0.3, cultural: 0.25, authenticity: 0.25, psychographic: 0.2 } }

      it 'calculates weighted average score' do
        weighted_score = service.send(:calculate_weighted_score, scores, weights)

        expect(weighted_score).to be_a(Float)
        expect(weighted_score).to be_between(7.0, 9.0)
      end
    end

    describe '#prioritize_recommendations' do
      let(:recommendations) do
        [
          { category: 'emotional', impact_level: 'high', priority: 'medium' },
          { category: 'cultural', impact_level: 'medium', priority: 'high' },
          { category: 'authenticity', impact_level: 'high', priority: 'high' }
        ]
      end

      it 'sorts recommendations by priority and impact' do
        prioritized = service.send(:prioritize_recommendations, recommendations)

        expect(prioritized.first[:priority]).to eq('high')
        expect(prioritized.first[:impact_level]).to eq('high')
      end
    end
  end
end
