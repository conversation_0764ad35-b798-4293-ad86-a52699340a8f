# frozen_string_literal: true

require 'rails_helper'

RSpec.describe EmailSpecialistAgentService do
  let(:campaign) { create(:email_campaign, name: 'Test Campaign') }
  let(:audience) { create(:audience, name: 'Test Audience') }
  let(:tenant) { create(:tenant) }
  let(:service) { described_class.new(campaign: campaign) }

  before do
    allow(campaign).to receive(:tenant).and_return(tenant)
    allow(campaign).to receive(:audience).and_return(audience)
  end

  describe '#generate_campaign_content' do
    context 'when successful' do
      before do
        # Mock the successful AI response
        allow_any_instance_of(RubyLlmService).to receive(:generate_content).and_return(
          instance_double('AiResponse',
            content: {
              subject_line: 'Amazing Test Offer',
              content: '<p>This is test email content</p>',
              cta: 'Click Here',
              tone: 'friendly'
            }.to_json,
            status: 'success',
            model: 'gpt-4-turbo',
            usage: { prompt_tokens: 450, completion_tokens: 200, total_tokens: 650 }
          )
        )

        # Mock the optimization service
        allow(AiModelOptimizationService).to receive(:select_optimal_model).and_return('gpt-4-turbo')
        allow(AiModelOptimizationService).to receive(:cache_key_for).and_return('test_cache_key')
        allow(AiModelOptimizationService).to receive(:get_cached_response).and_return(nil)
      end

      it 'returns a success response with the generated content' do
        result = service.generate_campaign_content

        expect(result[:status]).to eq('success')
        expect(result[:campaign_data]).to include(
          subject_line: 'Amazing Test Offer',
          content: '<p>This is test email content</p>'
        )
      end

      it 'tracks AI usage' do
        expect(AiUsageTracker).to receive(:track_usage).with(
          hash_including(
            tenant: tenant,
            service: 'EmailSpecialistAgent',
            model: 'gpt-4-turbo',
            tokens: 650
          )
        )

        service.generate_campaign_content
      end

      it 'selects the optimal model via the optimization service' do
        expect(AiModelOptimizationService).to receive(:select_optimal_model).with(
          tenant: tenant,
          task_type: :email_generation,
          importance: :normal,
          options: hash_including(audience_size: anything)
        )

        service.generate_campaign_content
      end

      it 'checks for cached responses before generating new content' do
        expect(AiModelOptimizationService).to receive(:get_cached_response)
        service.generate_campaign_content
      end
    end

    context 'when AI service returns cached response' do
      let(:cached_response) do
        instance_double('AiResponse',
          content: {
            subject_line: 'Cached Subject Line',
            content: '<p>This is cached content</p>'
          }.to_json,
          status: 'success',
          model: 'gpt-3.5-turbo',
          usage: { prompt_tokens: 0, completion_tokens: 0, total_tokens: 0 }
        )
      end

      before do
        allow(AiModelOptimizationService).to receive(:cache_key_for).and_return('test_cache_key')
        allow(AiModelOptimizationService).to receive(:get_cached_response).and_return(cached_response)
      end

      it 'uses the cached response without calling the AI service' do
        expect_any_instance_of(RubyLlmService).not_to receive(:generate_content)

        result = service.generate_campaign_content
        expect(result[:status]).to eq('success')
        expect(result[:campaign_data]).to include(
          subject_line: 'Cached Subject Line',
          content: '<p>This is cached content</p>'
        )
        expect(result[:source]).to eq('cache')
      end
    end

    context 'when AI service raises an error' do
      before do
        allow(AiModelOptimizationService).to receive(:select_optimal_model).and_return('gpt-4-turbo')
        allow(AiModelOptimizationService).to receive(:cache_key_for).and_return('test_cache_key')
        allow(AiModelOptimizationService).to receive(:get_cached_response).and_return(nil)
      end

      it 'handles rate limit errors with user-friendly messages' do
        allow_any_instance_of(RubyLlmService).to receive(:generate_content).and_raise(
          RubyLlmService::RateLimitError.new('Rate limit exceeded')
        )

        allow(ErrorHandlingService).to receive(:log_and_process).and_return({
          error_code: 'EMAIL_GEN_001',
          error_class: 'RubyLlmService::RateLimitError',
          message: 'Rate limit exceeded',
          recovery_options: [ 'retry_later', 'use_template' ]
        })

        allow(ErrorHandlingService).to receive(:user_friendly_message).and_return(
          'Our AI service is experiencing high demand. Please try again in a few minutes.'
        )

        result = service.generate_campaign_content

        expect(result[:status]).to eq('error')
        expect(result[:message]).to include('high demand')
        expect(result[:recovery_options]).to include('retry_later')
      end

      it 'handles token limit errors appropriately' do
        allow_any_instance_of(RubyLlmService).to receive(:generate_content).and_raise(
          RubyLlmService::TokenLimitError.new('Input too long')
        )

        allow(ErrorHandlingService).to receive(:log_and_process).and_return({
          error_code: 'EMAIL_GEN_002',
          error_class: 'RubyLlmService::TokenLimitError',
          message: 'Input too long',
          recovery_options: [ 'reduce_content', 'split_request' ]
        })

        allow(ErrorHandlingService).to receive(:user_friendly_message).and_return(
          'The content is too large for our AI to process. Please try with a smaller amount of text.'
        )

        result = service.generate_campaign_content

        expect(result[:status]).to eq('error')
        expect(result[:message]).to include('too large')
        expect(result[:recovery_options]).to include('reduce_content')
      end

      it 'handles unexpected errors with graceful degradation' do
        allow_any_instance_of(RubyLlmService).to receive(:generate_content).and_raise(
          StandardError.new('Unexpected error')
        )

        allow(ErrorHandlingService).to receive(:log_and_process).and_return({
          error_code: 'EMAIL_GEN_999',
          error_class: 'StandardError',
          message: 'Unexpected error',
          recovery_options: [ 'retry', 'contact_support' ]
        })

        allow(ErrorHandlingService).to receive(:user_friendly_message).and_return(
          'An unexpected error occurred'
        )

        result = service.generate_campaign_content

        expect(result[:status]).to eq('error')
        expect(result[:message]).to include('unexpected error')
        expect(result[:error_code]).to eq('EMAIL_GEN_999')
      end
    end
  end

  describe '#optimize_subject_line' do
    let(:original_subject) { 'Original Subject Line' }

    context 'when successful' do
      before do
        allow_any_instance_of(RubyLlmService).to receive(:generate_content).and_return(
          instance_double('AiResponse',
            content: {
              optimized_subject: 'Improved Subject Line: 40% Better!',
              reasoning: 'Added numbers and specificity',
              variations: [
                'Improved Subject Line: 40% Better!',
                'Don\'t Miss Out: 40% Improvement Inside',
                'Open Now: Your 40% Performance Boost'
              ]
            }.to_json,
            status: 'success',
            model: 'gpt-3.5-turbo',
            usage: { prompt_tokens: 250, completion_tokens: 150, total_tokens: 400 }
          )
        )
      end

      it 'returns optimized subject line variations' do
        result = service.optimize_subject_line(original_subject)

        expect(result[:status]).to eq('success')
        expect(result[:optimized_subject]).to eq('Improved Subject Line: 40% Better!')
        expect(result[:variations].size).to eq(3)
        expect(result[:reasoning]).to eq('Added numbers and specificity')
      end
    end

    context 'when the input is invalid' do
      it 'returns an error for empty subject line' do
        result = service.optimize_subject_line('')

        expect(result[:status]).to eq('error')
        expect(result[:message]).to include('subject line is required')
      end

      it 'returns an error for extremely long subject lines' do
        long_subject = 'A' * 250 # Create a very long subject line

        result = service.optimize_subject_line(long_subject)

        expect(result[:status]).to eq('error')
        expect(result[:message]).to include('too long')
      end
    end
  end

  describe '#generate_email_preview' do
    let(:content) { '<p>Email content here</p>' }
    let(:subject_line) { 'Test Subject' }

    it 'generates desktop and mobile preview HTML' do
      result = service.generate_email_preview(content, subject_line)

      expect(result[:desktop_preview]).to include(content)
      expect(result[:desktop_preview]).to include(subject_line)
      expect(result[:mobile_preview]).to include(content)
      expect(result[:mobile_preview]).to include('mobile-preview')
    end

    it 'sanitizes potentially harmful content' do
      unsafe_content = '<p>Normal text</p><script>alert("XSS")</script>'

      result = service.generate_email_preview(unsafe_content, subject_line)

      expect(result[:desktop_preview]).to include('<p>Normal text</p>')
      expect(result[:desktop_preview]).not_to include('<script>')
    end
  end

  describe '#analyze_email_performance' do
    let(:email_metrics) do
      {
        open_rate: 0.25,
        click_rate: 0.05,
        conversion_rate: 0.02,
        bounce_rate: 0.03
      }
    end

    context 'when successful' do
      before do
        allow_any_instance_of(RubyLlmService).to receive(:generate_content).and_return(
          instance_double('AiResponse',
            content: {
              performance_summary: 'Average open rate, below-average click-through rate',
              recommendations: [
                'Improve subject lines to increase open rates',
                'Add clearer call-to-action buttons',
                'Test different send times'
              ],
              insights: 'The low click-through rate indicates content may not be engaging enough'
            }.to_json,
            status: 'success',
            model: 'gpt-3.5-turbo',
            usage: { prompt_tokens: 300, completion_tokens: 200, total_tokens: 500 }
          )
        )
      end

      it 'returns performance analysis with recommendations' do
        result = service.analyze_email_performance(email_metrics)

        expect(result[:status]).to eq('success')
        expect(result[:performance_summary]).to include('Average open rate')
        expect(result[:recommendations]).to be_an(Array)
        expect(result[:recommendations].size).to eq(3)
      end
    end
  end
end
