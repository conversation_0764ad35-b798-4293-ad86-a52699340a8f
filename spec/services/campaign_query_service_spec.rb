# frozen_string_literal: true

require 'rails_helper'

RSpec.describe CampaignQueryService, type: :service do
  let(:tenant) { create(:tenant) }
  let(:user) { create(:user, tenant: tenant) }
  let(:service) { described_class.new(tenant) }

  before do
    ActsAsTenant.current_tenant = tenant
  end

  describe '#filtered_campaigns' do
    let!(:active_email_campaign) do
      create(:campaign, :email, :active, tenant: tenant, created_by: user, name: 'Email Campaign')
    end
    let!(:draft_social_campaign) do
      create(:campaign, :social, :draft, tenant: tenant, created_by: user, name: 'Social Campaign')
    end
    let!(:paused_seo_campaign) do
      create(:campaign, :seo, :paused, tenant: tenant, created_by: user, name: 'SEO Campaign')
    end

    it 'returns all campaigns without filters' do
      campaigns = service.filtered_campaigns
      expect(campaigns.count).to eq(3)
    end

    it 'filters by status' do
      campaigns = service.filtered_campaigns(status: 'active')
      expect(campaigns.count).to eq(1)
      expect(campaigns.first).to eq(active_email_campaign)
    end

    it 'filters by campaign type' do
      campaigns = service.filtered_campaigns(type: 'social')
      expect(campaigns.count).to eq(1)
      expect(campaigns.first).to eq(draft_social_campaign)
    end

    it 'filters by search term using full-text search' do
      campaigns = service.filtered_campaigns(search: 'Email')
      expect(campaigns.count).to eq(1)
      expect(campaigns.first).to eq(active_email_campaign)
    end

    it 'applies multiple filters' do
      campaigns = service.filtered_campaigns(status: 'draft', type: 'social')
      expect(campaigns.count).to eq(1)
      expect(campaigns.first).to eq(draft_social_campaign)
    end

    it 'applies sorting by name' do
      campaigns = service.filtered_campaigns(sort: 'name')
      expect(campaigns.pluck(:name)).to eq([ 'Email Campaign', 'SEO Campaign', 'Social Campaign' ])
    end

    it 'applies sorting by budget' do
      active_email_campaign.update!(budget_cents: 50000)
      draft_social_campaign.update!(budget_cents: 30000)
      paused_seo_campaign.update!(budget_cents: 40000)

      campaigns = service.filtered_campaigns(sort: 'budget')
      expect(campaigns.pluck(:budget_cents)).to eq([ 30000, 40000, 50000 ])
    end

    it 'applies pagination' do
      campaigns = service.filtered_campaigns(page: 1, per_page: 2)
      expect(campaigns.count).to eq(2)
    end

    it 'eager loads associations to prevent N+1 queries' do
      campaigns = service.filtered_campaigns

      # Verify associations are loaded
      campaigns.each do |campaign|
        expect(campaign.association(:email_campaign)).to be_loaded
        expect(campaign.association(:social_campaign)).to be_loaded
        expect(campaign.association(:seo_campaign)).to be_loaded
        expect(campaign.association(:created_by)).to be_loaded
      end
    end
  end

  describe '#campaign_stats' do
    let!(:active_campaign) { create(:campaign, :active, tenant: tenant, created_by: user) }
    let!(:draft_campaign) { create(:campaign, :draft, tenant: tenant, created_by: user) }
    let!(:paused_campaign) { create(:campaign, :paused, tenant: tenant, created_by: user) }

    it 'returns campaign statistics' do
      stats = service.campaign_stats

      expect(stats[:total]).to eq(3)
      expect(stats[:active]).to eq(1)
      expect(stats[:draft]).to eq(1)
      expect(stats[:paused]).to eq(1)
      expect(stats[:completed]).to eq(0)
      expect(stats[:cancelled]).to eq(0)
    end

    it 'caches the results' do
      expect(Rails.cache).to receive(:fetch).with(
        "campaign_stats_#{tenant.id}",
        expires_in: 5.minutes
      ).and_call_original

      service.campaign_stats
    end
  end

  describe '#campaign_with_associations' do
    let!(:campaign) { create(:campaign, :email, tenant: tenant, created_by: user) }

    it 'returns campaign with eager loaded associations' do
      result = service.campaign_with_associations(campaign.id)

      expect(result).to eq(campaign)
      expect(result.association(:email_campaign)).to be_loaded
      expect(result.association(:created_by)).to be_loaded
    end

    it 'caches the result' do
      expect(Rails.cache).to receive(:fetch).with(
        "campaign_detail_#{campaign.id}",
        expires_in: 15.minutes
      ).and_call_original

      service.campaign_with_associations(campaign.id)
    end
  end

  describe '#campaign_metrics_summary' do
    let!(:campaign) { create(:campaign, tenant: tenant, created_by: user) }
    let!(:metric1) do
      create(:campaign_metric,
             campaign: campaign,
             metric_date: 5.days.ago,
             impressions: 1000,
             clicks: 50,
             conversions: 5,
             revenue_cents: 5000,
             cost_cents: 2000)
    end
    let!(:metric2) do
      create(:campaign_metric,
             campaign: campaign,
             metric_date: 3.days.ago,
             impressions: 2000,
             clicks: 100,
             conversions: 10,
             revenue_cents: 10000,
             cost_cents: 4000)
    end

    it 'calculates metrics summary for date range' do
      summary = service.campaign_metrics_summary(campaign, 7.days.ago, Date.current)

      expect(summary[:total_impressions]).to eq(3000)
      expect(summary[:total_clicks]).to eq(150)
      expect(summary[:total_conversions]).to eq(15)
      expect(summary[:total_revenue]).to eq(150.0)
      expect(summary[:total_cost]).to eq(60.0)
      expect(summary[:roi]).to eq(150.0) # ((150-60)/60)*100
    end

    it 'returns empty hash when no metrics exist' do
      campaign_without_metrics = create(:campaign, tenant: tenant, created_by: user)
      summary = service.campaign_metrics_summary(campaign_without_metrics)

      expect(summary).to eq({})
    end

    it 'caches the results' do
      start_date = 30.days.ago.to_date
      end_date = Date.current
      cache_key = "campaign_metrics_#{campaign.id}_#{start_date}_#{end_date}"

      expect(Rails.cache).to receive(:fetch).with(
        cache_key,
        expires_in: 1.hour
      ).and_call_original

      service.campaign_metrics_summary(campaign, start_date, end_date)
    end
  end

  describe '#recent_campaigns_with_performance' do
    let!(:campaign1) { create(:campaign, tenant: tenant, created_by: user, created_at: 2.days.ago) }
    let!(:campaign2) { create(:campaign, tenant: tenant, created_by: user, created_at: 1.day.ago) }
    let!(:metric1) do
      create(:campaign_metric, campaign: campaign1, impressions: 1000, clicks: 50)
    end

    it 'returns recent campaigns with performance data' do
      campaigns = service.recent_campaigns_with_performance(5)

      expect(campaigns.length).to eq(2)
      expect(campaigns.first[:id]).to eq(campaign2.id) # Most recent first
      expect(campaigns.last[:id]).to eq(campaign1.id)
      expect(campaigns.last[:total_impressions]).to eq(1000)
    end

    it 'limits the number of campaigns returned' do
      campaigns = service.recent_campaigns_with_performance(1)
      expect(campaigns.length).to eq(1)
    end

    it 'caches the results' do
      expect(Rails.cache).to receive(:fetch).with(
        "recent_campaigns_performance_#{tenant.id}",
        expires_in: 10.minutes
      ).and_call_original

      service.recent_campaigns_with_performance
    end
  end

  describe '#bulk_update_status' do
    let!(:campaign1) { create(:campaign, :draft, tenant: tenant, created_by: user) }
    let!(:campaign2) { create(:campaign, :draft, tenant: tenant, created_by: user) }

    it 'updates multiple campaigns status' do
      updated_count = service.bulk_update_status([ campaign1.id, campaign2.id ], 'active')

      expect(updated_count).to eq(2)
      expect(campaign1.reload.status).to eq('active')
      expect(campaign2.reload.status).to eq('active')
    end

    it 'clears relevant caches' do
      cache_service = instance_double(CampaignCacheService)
      allow(CampaignCacheService).to receive(:new).and_return(cache_service)
      allow(cache_service).to receive(:invalidate_campaign_caches)
      allow(cache_service).to receive(:invalidate_tenant_aggregate_caches)

      service.bulk_update_status([ campaign1.id ], 'active')

      # Note: The current implementation doesn't actually call the cache service
      # This test documents the expected behavior for future implementation
    end
  end
end
