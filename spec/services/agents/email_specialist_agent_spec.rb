# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Agents::EmailSpecialistAgent, type: :service do
  subject(:agent) { described_class.new(tenant: tenant, ai_service: mock_ai_service) }

  let(:tenant) { create(:tenant) }
  let(:user) { create(:user, tenant: tenant) }
  let(:campaign) { create(:campaign, :with_email_campaign, tenant: tenant, created_by: user) }
  let(:mock_ai_service) { instance_double(RubyLlmService) }

  before do
    allow(mock_ai_service).to receive(:generate_content).and_return(ai_response)
  end

  let(:ai_response) do
    instance_double(AiResponse,
      successful?: true,
      content: {
        'subject_lines' => [
          'Transform Your Business Today',
          'Exclusive Opportunity Inside',
          'Limited Time: Special Offer'
        ],
        'email_content' => 'Personalized email content based on audience...',
        'call_to_action' => 'Get Started Now',
        'personalization_strategy' => 'demographic_based',
        'send_time_optimization' => '10:00 AM local time'
      }.to_json
    )
  end

  describe '#initialize' do
    it 'creates agent with tenant' do
      expect(agent.tenant).to eq(tenant)
      expect(agent.ai_client).to be_present
    end

    context 'when tenant is nil' do
      it 'raises argument error' do
        expect { described_class.new(tenant: nil) }.to raise_error(ArgumentError, 'Tenant is required')
      end
    end
  end

  describe '#execute_campaign' do
    let(:coordination_data) do
      {
        'priority' => 1,
        'timeline' => '24_hours',
        'focus_areas' => [ 'engagement', 'conversion' ]
      }
    end

    it 'generates email content using AI' do
      expect(agent).to receive(:generate_email_content).and_call_original
      agent.execute_campaign(campaign, coordination_data)
    end

    it 'optimizes send timing' do
      expect(agent).to receive(:optimize_send_timing).and_call_original
      agent.execute_campaign(campaign, coordination_data)
    end

    it 'creates email campaign record' do
      expect {
        agent.execute_campaign(campaign, coordination_data)
      }.to change(EmailCampaign, :count).by(1)
    end

    it 'returns execution result' do
      result = agent.execute_campaign(campaign, coordination_data)

      expect(result).to include(
        success: true,
        email_campaign_id: be_a(Integer),
        subject_lines: be_an(Array),
        optimization_applied: true
      )
    end
  end

  describe '#generate_email_content' do
    it 'calls AI service with appropriate prompt' do
      expect(mock_ai_service).to receive(:generate_content).with(
        include('email campaign'),
        hash_including(
          task_type: :creative_content,
          max_tokens: 4000,
          temperature: 0.7
        )
      )

      agent.send(:generate_email_content, campaign, {})
    end
  end
end
