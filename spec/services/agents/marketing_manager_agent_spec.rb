# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Agents::MarketingManagerAgent, type: :service do
  subject(:agent) { described_class.new(tenant: tenant, campaign: campaign, ai_service: mock_ai_service) }

  let(:tenant) { create(:tenant) }
  let(:user) { create(:user, tenant: tenant) }
  let(:campaign) { create(:campaign, tenant: tenant, created_by: user) }
  let(:mock_ai_service) { instance_double(RubyLlmService) }

  before do
    allow(mock_ai_service).to receive(:generate_content).and_return(ai_response)
  end

  let(:ai_response) do
    instance_double(AiResponse,
      successful?: true,
      content: {
        'strategy' => 'multi_channel_approach',
        'priority_agents' => [ 'email', 'social', 'seo' ],
        'coordination_plan' => {
          'email' => { 'priority': 1, 'timeline': '24_hours' },
          'social' => { 'priority': 2, 'timeline': '48_hours' },
          'seo' => { 'priority': 3, 'timeline': '72_hours' }
        },
        'success_metrics' => [ 'engagement_rate', 'conversion_rate', 'reach' ]
      }.to_json
    )
  end

  describe '#initialize' do
    it 'creates agent with required dependencies' do
      expect(agent.tenant).to eq(tenant)
      expect(agent.campaign).to eq(campaign)
      expect(agent.specialist_agents).to be_a(Hash)
    end

    context 'when tenant is nil' do
      it 'raises argument error' do
        expect { described_class.new(tenant: nil, campaign: campaign) }.to raise_error(ArgumentError, 'Tenant is required')
      end
    end

    context 'when campaign is nil' do
      it 'raises argument error' do
        expect { described_class.new(tenant: tenant, campaign: nil) }.to raise_error(ArgumentError, 'Campaign is required')
      end
    end
  end

  describe '#orchestrate_campaign' do
    let(:workflow) { create(:agent_workflow, campaign: campaign, tenant: tenant) }

    before do
      allow(AgentWorkflow).to receive(:create!).and_return(workflow)
      allow(workflow).to receive(:update!)
    end

    it 'creates an agent workflow' do
      expect(AgentWorkflow).to receive(:create!).with(
        campaign: campaign,
        tenant: tenant,
        workflow_type: 'marketing_orchestration',
        status: 'in_progress',
        context_data: hash_including(:campaign_data, :target_audience)
      )

      agent.orchestrate_campaign
    end

    it 'analyzes campaign context' do
      expect(agent).to receive(:analyze_campaign_context).and_call_original
      agent.orchestrate_campaign
    end

    it 'determines strategy using AI' do
      expect(agent).to receive(:determine_strategy).and_call_original
      agent.orchestrate_campaign
    end

    it 'delegates tasks to specialist agents' do
      expect(agent).to receive(:delegate_tasks).and_call_original
      agent.orchestrate_campaign
    end

    it 'returns orchestration result' do
      result = agent.orchestrate_campaign

      expect(result).to include(
        success: true,
        workflow_id: workflow.id,
        strategy: 'multi_channel_approach',
        delegated_agents: array_including('email', 'social', 'seo')
      )
    end

    context 'when AI service fails' do
      before do
        allow(mock_ai_service).to receive(:generate_content).and_raise(StandardError, 'AI service unavailable')
      end

      it 'handles error gracefully' do
        result = agent.orchestrate_campaign

        expect(result).to include(
          success: false,
          error: 'AI service unavailable'
        )
      end

      it 'logs error details' do
        expect(Rails.logger).to receive(:error).with(/Marketing Manager Agent failed/)
        agent.orchestrate_campaign
      end
    end
  end

  describe '#analyze_campaign_context' do
    it 'extracts relevant campaign data' do
      context = agent.send(:analyze_campaign_context)

      expect(context).to include(
        campaign_type: campaign.campaign_type,
        target_audience: campaign.target_audience,
        budget_cents: campaign.budget_cents,
        start_date: campaign.start_date,
        emotional_tone: campaign.emotional_tone
      )
    end

    context 'when campaign has emotional intelligence data' do
      let(:emotional_state) { create(:customer_emotional_state, tenant: tenant) }

      before do
        allow(tenant).to receive(:customer_emotional_states).and_return([ emotional_state ])
      end

      it 'includes emotional intelligence insights' do
        context = agent.send(:analyze_campaign_context)

        expect(context).to include(:emotional_insights)
        expect(context[:emotional_insights]).to be_present
      end
    end
  end

  describe '#determine_strategy' do
    let(:context) do
      {
        campaign_type: 'integrated',
        target_audience: 'tech_professionals',
        budget_cents: 100000,
        emotional_tone: 'confident'
      }
    end

    it 'calls AI service with appropriate prompt' do
      expect(mock_ai_service).to receive(:generate_content).with(
        include('marketing strategy'),
        hash_including(
          task_type: :creative_content,
          max_tokens: 4000,
          temperature: 0.7
        )
      )

      agent.send(:determine_strategy, context)
    end

    it 'returns parsed strategy from AI response' do
      strategy = agent.send(:determine_strategy, context)

      expect(strategy).to include(
        'strategy' => 'multi_channel_approach',
        'priority_agents' => [ 'email', 'social', 'seo' ],
        'coordination_plan' => be_a(Hash)
      )
    end
  end

  describe '#delegate_tasks' do
    let(:strategy) do
      {
        'priority_agents' => [ 'email', 'social' ],
        'coordination_plan' => {
          'email' => { 'priority' => 1, 'timeline' => '24_hours' },
          'social' => { 'priority' => 2, 'timeline' => '48_hours' }
        }
      }
    end

    it 'enqueues specialist agent jobs' do
      expect(Agents::EmailSpecialistJob).to receive(:perform_later).with(
        campaign_id: campaign.id,
        tenant_id: tenant.id,
        coordination_data: hash_including('priority' => 1)
      )

      expect(Agents::SocialMediaSpecialistJob).to receive(:perform_later).with(
        campaign_id: campaign.id,
        tenant_id: tenant.id,
        coordination_data: hash_including('priority' => 2)
      )

      agent.send(:delegate_tasks, strategy)
    end

    it 'creates delegation records for tracking' do
      expect {
        agent.send(:delegate_tasks, strategy)
      }.to change(AgentWorkflow, :count).by(2)
    end
  end

  describe '#monitor_execution' do
    let(:delegated_workflows) { create_list(:agent_workflow, 3, campaign: campaign, tenant: tenant) }

    before do
      allow(agent).to receive(:delegated_workflows).and_return(delegated_workflows)
    end

    it 'aggregates status from all delegated workflows' do
      delegated_workflows[0].update!(status: 'completed', progress_percent: 100)
      delegated_workflows[1].update!(status: 'in_progress', progress_percent: 50)
      delegated_workflows[2].update!(status: 'pending', progress_percent: 0)

      status = agent.monitor_execution

      expect(status).to include(
        overall_progress: 50, # (100 + 50 + 0) / 3
        completed_agents: 1,
        active_agents: 1,
        pending_agents: 1
      )
    end

    it 'identifies bottlenecks and issues' do
      # Simulate a failed workflow
      delegated_workflows[1].update!(
        status: 'failed',
        error_details: { error: 'API rate limit exceeded' }
      )

      status = agent.monitor_execution

      expect(status).to include(
        issues: array_including(
          hash_including(
            agent_type: 'failed',
            error: 'API rate limit exceeded'
          )
        )
      )
    end
  end

  describe 'integration with specialist agents' do
    it 'loads all required specialist agents' do
      expect(agent.specialist_agents).to include(
        'email' => be_a(Agents::EmailSpecialistAgent),
        'social' => be_a(Agents::SocialMediaSpecialistAgent),
        'seo' => be_a(Agents::SeoSpecialistAgent)
      )
    end
  end
end
