# frozen_string_literal: true

require 'rails_helper'

RSpec.describe EmotionalResonanceTool, type: :service do
  let(:tenant) { create(:tenant) }
  let(:campaign) { create(:campaign, tenant: tenant) }
  let(:tool) { described_class.new(campaign) }

  before do
    ActsAsTenant.current_tenant = tenant
  end

  describe '#initialize' do
    it 'initializes with a campaign' do
      expect(tool.campaign).to eq(campaign)
    end

    it 'raises error without campaign' do
      expect { described_class.new(nil) }.to raise_error(ArgumentError, 'Campaign is required')
    end
  end

  describe '#analyze_emotional_resonance' do
    it 'analyzes emotional resonance potential' do
      result = tool.analyze_emotional_resonance

      expect(result).to be_a(Hash)
      expect(result).to have_key(:resonance_score)
      expect(result).to have_key(:emotional_profile)
      expect(result).to have_key(:resonance_factors)
      expect(result).to have_key(:optimization_suggestions)
    end

    it 'calculates resonance score' do
      result = tool.analyze_emotional_resonance
      score = result[:resonance_score]

      expect(score).to be_a(Float)
      expect(score).to be_between(0, 10)
    end

    it 'creates emotional profile' do
      result = tool.analyze_emotional_resonance
      profile = result[:emotional_profile]

      expect(profile).to be_a(Hash)
      expect(profile).to have_key(:dominant_emotions)
      expect(profile).to have_key(:emotional_intensity)
      expect(profile).to have_key(:emotional_triggers)
    end
  end

  describe '#create_resonance_profile' do
    let(:profile_params) do
      {
        profile_name: 'Test Emotional Profile',
        emotional_patterns: { 'joy' => 8.5, 'excitement' => 7.0 },
        response_triggers: { 'urgency' => 6.0, 'social_proof' => 8.0 },
        engagement_factors: { 'visual_appeal' => 7.5, 'emotional_connection' => 8.5 }
      }
    end

    it 'creates emotional resonance profile' do
      expect { tool.create_resonance_profile(profile_params) }.to change { EmotionalResonanceProfile.count }.by(1)
    end

    it 'associates profile with campaign and tenant' do
      profile = tool.create_resonance_profile(profile_params)

      expect(profile.tenant).to eq(tenant)
    end

    it 'sets profile attributes correctly' do
      profile = tool.create_resonance_profile(profile_params)

      expect(profile.profile_name).to eq('Test Emotional Profile')
      expect(profile.emotional_patterns).to eq({ 'joy' => 8.5, 'excitement' => 7.0 })
      expect(profile.response_triggers).to eq({ 'urgency' => 6.0, 'social_proof' => 8.0 })
    end
  end

  describe '#emotional_mapping' do
    it 'creates emotional journey mapping' do
      mapping = tool.emotional_mapping

      expect(mapping).to be_a(Hash)
      expect(mapping).to have_key(:awareness_stage)
      expect(mapping).to have_key(:consideration_stage)
      expect(mapping).to have_key(:decision_stage)
      expect(mapping).to have_key(:retention_stage)
    end

    it 'includes emotional progression through stages' do
      mapping = tool.emotional_mapping

      mapping.each_value do |stage|
        expect(stage).to have_key(:target_emotions)
        expect(stage).to have_key(:emotional_triggers)
        expect(stage).to have_key(:content_recommendations)
      end
    end
  end

  describe '#emotion_optimization' do
    let(:target_emotions) { %w[joy excitement trust] }

    it 'optimizes content for target emotions' do
      optimization = tool.emotion_optimization(target_emotions)

      expect(optimization).to be_a(Hash)
      expect(optimization).to have_key(:content_adjustments)
      expect(optimization).to have_key(:messaging_recommendations)
      expect(optimization).to have_key(:visual_suggestions)
      expect(optimization).to have_key(:expected_impact)
    end

    it 'provides specific content adjustments' do
      optimization = tool.emotion_optimization(target_emotions)
      adjustments = optimization[:content_adjustments]

      expect(adjustments).to be_an(Array)
      adjustments.each do |adjustment|
        expect(adjustment).to have_key(:element_type)
        expect(adjustment).to have_key(:current_state)
        expect(adjustment).to have_key(:recommended_change)
        expect(adjustment).to have_key(:target_emotion)
      end
    end
  end

  describe '#resonance_testing' do
    let(:test_variants) do
      [
        { name: 'Variant A', content: 'Exciting new features await!' },
        { name: 'Variant B', content: 'Discover amazing possibilities.' }
      ]
    end

    it 'tests emotional resonance of content variants' do
      results = tool.resonance_testing(test_variants)

      expect(results).to be_a(Hash)
      expect(results).to have_key(:variant_scores)
      expect(results).to have_key(:best_performer)
      expect(results).to have_key(:emotional_impact_comparison)
    end

    it 'scores each variant' do
      results = tool.resonance_testing(test_variants)
      variant_scores = results[:variant_scores]

      expect(variant_scores).to be_a(Hash)
      expect(variant_scores.keys).to include('Variant A', 'Variant B')

      variant_scores.each_value do |score_data|
        expect(score_data).to have_key(:resonance_score)
        expect(score_data).to have_key(:emotional_breakdown)
      end
    end

    it 'identifies best performing variant' do
      results = tool.resonance_testing(test_variants)
      best_performer = results[:best_performer]

      expect(best_performer).to be_a(Hash)
      expect(best_performer).to have_key(:variant_name)
      expect(best_performer).to have_key(:score)
      expect(best_performer).to have_key(:key_strengths)
    end
  end

  describe '#emotional_triggers_analysis' do
    it 'analyzes emotional trigger effectiveness' do
      analysis = tool.emotional_triggers_analysis

      expect(analysis).to be_a(Hash)
      expect(analysis).to have_key(:trigger_effectiveness)
      expect(analysis).to have_key(:recommended_triggers)
      expect(analysis).to have_key(:trigger_combinations)
    end

    it 'evaluates individual trigger effectiveness' do
      analysis = tool.emotional_triggers_analysis
      effectiveness = analysis[:trigger_effectiveness]

      expect(effectiveness).to be_a(Hash)
      effectiveness.each do |trigger, data|
        expect(data).to have_key(:effectiveness_score)
        expect(data).to have_key(:target_emotions)
        expect(data).to have_key(:usage_recommendations)
      end
    end

    it 'recommends trigger combinations' do
      analysis = tool.emotional_triggers_analysis
      combinations = analysis[:trigger_combinations]

      expect(combinations).to be_an(Array)
      combinations.each do |combo|
        expect(combo).to have_key(:triggers)
        expect(combo).to have_key(:synergy_score)
        expect(combo).to have_key(:use_cases)
      end
    end
  end

  describe '#audience_emotional_profiling' do
    let(:audience_data) do
      {
        demographics: { age_range: '25-34', gender: 'mixed', location: 'urban' },
        interests: %w[technology lifestyle wellness],
        behaviors: %w[early_adopter social_sharer value_conscious]
      }
    end

    it 'creates emotional profile for target audience' do
      profile = tool.audience_emotional_profiling(audience_data)

      expect(profile).to be_a(Hash)
      expect(profile).to have_key(:emotional_characteristics)
      expect(profile).to have_key(:resonance_preferences)
      expect(profile).to have_key(:trigger_sensitivities)
      expect(profile).to have_key(:content_preferences)
    end

    it 'identifies emotional characteristics' do
      profile = tool.audience_emotional_profiling(audience_data)
      characteristics = profile[:emotional_characteristics]

      expect(characteristics).to be_a(Hash)
      expect(characteristics).to have_key(:dominant_emotions)
      expect(characteristics).to have_key(:emotional_drivers)
      expect(characteristics).to have_key(:response_patterns)
    end
  end

  describe '#resonance_metrics' do
    it 'calculates comprehensive resonance metrics' do
      metrics = tool.resonance_metrics

      expect(metrics).to be_a(Hash)
      expect(metrics).to have_key(:overall_resonance_score)
      expect(metrics).to have_key(:emotional_depth_score)
      expect(metrics).to have_key(:trigger_effectiveness_score)
      expect(metrics).to have_key(:audience_alignment_score)
    end

    it 'provides metric breakdowns' do
      metrics = tool.resonance_metrics

      metrics.each do |metric_name, metric_data|
        if metric_data.is_a?(Hash)
          expect(metric_data).to have_key(:score)
          expect(metric_data).to have_key(:contributing_factors)
        end
      end
    end
  end

  describe '#optimization_recommendations' do
    it 'provides emotional optimization recommendations' do
      recommendations = tool.optimization_recommendations

      expect(recommendations).to be_an(Array)
      recommendations.each do |rec|
        expect(rec).to have_key(:category)
        expect(rec).to have_key(:recommendation)
        expect(rec).to have_key(:expected_impact)
        expect(rec).to have_key(:implementation_effort)
        expect(rec).to have_key(:priority)
      end
    end

    it 'categorizes recommendations appropriately' do
      recommendations = tool.optimization_recommendations
      categories = recommendations.map { |r| r[:category] }.uniq

      expected_categories = %w[content_tone emotional_triggers visual_elements messaging_strategy]
      expect(categories.any? { |cat| expected_categories.include?(cat) }).to be true
    end
  end

  describe 'private methods' do
    describe '#extract_emotional_content' do
      it 'extracts emotional content from campaign' do
        content = tool.send(:extract_emotional_content)

        expect(content).to be_a(Hash)
        expect(content).to have_key(:text_elements)
        expect(content).to have_key(:emotional_cues)
        expect(content).to have_key(:tone_indicators)
      end
    end

    describe '#analyze_emotional_patterns' do
      let(:content) { { text_elements: [ 'Exciting news!', 'Join us today!' ] } }

      it 'analyzes emotional patterns in content' do
        patterns = tool.send(:analyze_emotional_patterns, content)

        expect(patterns).to be_a(Hash)
        expect(patterns).to have_key(:detected_emotions)
        expect(patterns).to have_key(:emotional_intensity)
        expect(patterns).to have_key(:emotional_progression)
      end
    end

    describe '#calculate_resonance_score' do
      let(:emotional_data) do
        {
          emotional_intensity: 7.5,
          trigger_effectiveness: 8.0,
          audience_alignment: 7.8,
          content_quality: 8.2
        }
      end

      it 'calculates weighted resonance score' do
        score = tool.send(:calculate_resonance_score, emotional_data)

        expect(score).to be_a(Float)
        expect(score).to be_between(0, 10)
      end
    end

    describe '#generate_emotional_recommendations' do
      let(:analysis_results) do
        {
          weak_areas: [ 'emotional_depth', 'trigger_variety' ],
          opportunities: [ 'joy_enhancement', 'trust_building' ],
          audience_preferences: { primary_emotions: %w[excitement trust] }
        }
      end

      it 'generates targeted recommendations' do
        recommendations = tool.send(:generate_emotional_recommendations, analysis_results)

        expect(recommendations).to be_an(Array)
        recommendations.each do |rec|
          expect(rec).to have_key(:category)
          expect(rec).to have_key(:recommendation)
          expect(rec).to have_key(:rationale)
        end
      end
    end
  end
end
