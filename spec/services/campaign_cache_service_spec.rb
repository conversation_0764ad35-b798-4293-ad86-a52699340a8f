# frozen_string_literal: true

require 'rails_helper'

RSpec.describe CampaignCacheService, type: :service do
  let(:tenant) { create(:tenant) }
  let(:user) { create(:user, tenant: tenant) }
  let(:service) { described_class.new(tenant) }

  before do
    ActsAsTenant.current_tenant = tenant
    Rails.cache.clear
  end

  describe '#cached_performance_summary' do
    let!(:campaign) { create(:campaign, tenant: tenant, created_by: user) }
    let!(:metric) do
      create(:campaign_metric,
             campaign: campaign,
             metric_date: 5.days.ago,
             impressions: 1000,
             clicks: 50,
             conversions: 5,
             revenue_cents: 5000,
             cost_cents: 2000)
    end

    it 'calculates and caches performance summary' do
      summary = service.cached_performance_summary(campaign)

      expect(summary[:total_impressions]).to eq(1000)
      expect(summary[:total_clicks]).to eq(50)
      expect(summary[:total_conversions]).to eq(5)
      expect(summary[:total_revenue]).to eq(50.0)
      expect(summary[:total_cost]).to eq(20.0)
      expect(summary[:roi]).to eq(150.0)
    end

    it 'uses cached result on subsequent calls' do
      # Use a fixed date range to avoid timestamp precision issues
      date_range = 7.days.ago.to_date..Date.current

      # Mock Rails.cache to simulate caching behavior
      allow(Rails.cache).to receive(:fetch).and_call_original

      first_result = service.cached_performance_summary(campaign, date_range)
      second_result = service.cached_performance_summary(campaign, date_range)

      expect(first_result).to eq(second_result)
      expect(Rails.cache).to have_received(:fetch).twice
    end

    it 'returns empty hash when no metrics exist' do
      campaign_without_metrics = create(:campaign, tenant: tenant, created_by: user)
      summary = service.cached_performance_summary(campaign_without_metrics)

      expect(summary).to eq({})
    end
  end

  describe '#cached_campaign_stats' do
    let!(:active_campaign) { create(:campaign, :active, tenant: tenant, created_by: user) }
    let!(:draft_campaign) { create(:campaign, :draft, tenant: tenant, created_by: user) }

    it 'calculates and caches campaign statistics' do
      stats = service.cached_campaign_stats

      expect(stats[:total]).to eq(2)
      expect(stats[:active]).to eq(1)
      expect(stats[:draft]).to eq(1)
    end

    it 'uses cached result on subsequent calls' do
      # Mock Rails.cache to simulate caching behavior
      allow(Rails.cache).to receive(:fetch).and_call_original

      first_result = service.cached_campaign_stats
      second_result = service.cached_campaign_stats

      expect(first_result).to eq(second_result)
      expect(Rails.cache).to have_received(:fetch).at_least(:twice)
    end
  end

  describe '#cached_dashboard_data' do
    let!(:campaign) { create(:campaign, :active, tenant: tenant, created_by: user) }

    it 'returns comprehensive dashboard data' do
      data = service.cached_dashboard_data

      expect(data).to have_key(:campaign_stats)
      expect(data).to have_key(:recent_campaigns)
      expect(data).to have_key(:performance_trends)
      expect(data).to have_key(:top_performing_campaigns)
    end

    it 'caches the dashboard data' do
      allow(Rails.cache).to receive(:fetch).and_call_original

      service.cached_dashboard_data

      expect(Rails.cache).to have_received(:fetch).with(
        "dashboard_data_#{tenant.id}",
        expires_in: described_class::CACHE_TTL[:dashboard_data]
      )
    end
  end

  describe '#cached_search_results' do
    let!(:campaign) { create(:campaign, tenant: tenant, created_by: user, name: 'Test Campaign') }

    it 'caches search results' do
      search_term = 'test'
      filters = { status: 'active' }

      # Mock the search method since it's a placeholder
      allow(service).to receive(:perform_search).and_return([ campaign ])

      results = service.cached_search_results(search_term, filters)
      expect(results).to eq([ campaign ])
    end

    it 'generates unique cache keys for different search terms and filters' do
      search_term1 = 'test'
      search_term2 = 'campaign'
      filters1 = { status: 'active' }
      filters2 = { status: 'draft' }

      key1 = service.send(:search_results_cache_key, search_term1, filters1)
      key2 = service.send(:search_results_cache_key, search_term2, filters2)

      expect(key1).not_to eq(key2)
    end
  end

  describe '#warm_cache_for_tenant' do
    let!(:campaign) { create(:campaign, tenant: tenant, created_by: user) }

    it 'warms cache in foreground by default' do
      expect(service).to receive(:perform_cache_warmup)
      service.warm_cache_for_tenant
    end

    it 'enqueues background job when requested' do
      expect(CacheWarmupJob).to receive(:perform_later).with(tenant.id)
      service.warm_cache_for_tenant(background: true)
    end
  end

  describe '#invalidate_campaign_caches' do
    let!(:campaign) { create(:campaign, tenant: tenant, created_by: user) }

    before do
      # Populate some caches
      service.cached_performance_summary(campaign)
      service.cached_campaign_stats
    end

    it 'clears campaign-specific caches' do
      allow(Rails.cache).to receive(:delete_matched)

      service.invalidate_campaign_caches(campaign)

      expect(Rails.cache).to have_received(:delete_matched).with("campaign_detail_#{campaign.id}*")
      expect(Rails.cache).to have_received(:delete_matched).with("performance_summary_#{campaign.id}*")
      expect(Rails.cache).to have_received(:delete_matched).with("campaign_metrics_#{campaign.id}*")
    end

    it 'clears tenant-wide aggregate caches' do
      expect(service).to receive(:invalidate_tenant_aggregate_caches)
      service.invalidate_campaign_caches(campaign)
    end

    it 'logs cache invalidation' do
      expect(Rails.logger).to receive(:info).with("Cache invalidated for campaign #{campaign.id} (#{campaign.name})")
      service.invalidate_campaign_caches(campaign)
    end
  end

  describe '#invalidate_tenant_aggregate_caches' do
    before do
      # Populate some caches
      service.cached_campaign_stats
      service.cached_dashboard_data
    end

    it 'clears tenant-wide caches' do
      expect(Rails.cache).to receive(:delete).with("campaign_stats_#{tenant.id}")
      expect(Rails.cache).to receive(:delete).with("dashboard_data_#{tenant.id}")
      expect(Rails.cache).to receive(:delete_matched).with("search_results_#{tenant.id}*")
      expect(Rails.cache).to receive(:delete_matched).with("recent_campaigns_#{tenant.id}*")

      service.invalidate_tenant_aggregate_caches
    end
  end

  describe '#preload_campaign_associations' do
    let!(:campaign1) { create(:campaign, :email, tenant: tenant, created_by: user) }
    let!(:campaign2) { create(:campaign, :social, tenant: tenant, created_by: user) }

    it 'preloads and caches campaign associations' do
      campaign_ids = [ campaign1.id, campaign2.id ]

      # Mock Rails.cache.write to verify it's called
      allow(Rails.cache).to receive(:write)

      service.preload_campaign_associations(campaign_ids)

      # Verify cache.write was called for each campaign
      expect(Rails.cache).to have_received(:write).with(
        "campaign_detail_#{campaign1.id}",
        anything,
        expires_in: described_class::CACHE_TTL[:campaign_detail]
      )
      expect(Rails.cache).to have_received(:write).with(
        "campaign_detail_#{campaign2.id}",
        anything,
        expires_in: described_class::CACHE_TTL[:campaign_detail]
      )
    end

    it 'eager loads associations to prevent N+1 queries' do
      campaign_ids = [ campaign1.id, campaign2.id ]

      # Mock the tenant.campaigns query to verify includes are used
      campaigns_relation = double('campaigns_relation')
      allow(tenant.campaigns).to receive(:includes).and_return(campaigns_relation)
      allow(campaigns_relation).to receive(:where).and_return([ campaign1, campaign2 ])
      allow(Rails.cache).to receive(:write)

      service.preload_campaign_associations(campaign_ids)

      # Verify that includes was called with the right associations
      expect(tenant.campaigns).to have_received(:includes).with(
        :email_campaign, :social_campaign, :seo_campaign, :created_by
      )
    end
  end

  describe 'cache TTL configuration' do
    it 'has appropriate TTL values for different cache types' do
      expect(described_class::CACHE_TTL[:metrics]).to eq(5.minutes)
      expect(described_class::CACHE_TTL[:performance_summary]).to eq(15.minutes)
      expect(described_class::CACHE_TTL[:campaign_stats]).to eq(5.minutes)
      expect(described_class::CACHE_TTL[:campaign_detail]).to eq(15.minutes)
      expect(described_class::CACHE_TTL[:dashboard_data]).to eq(10.minutes)
      expect(described_class::CACHE_TTL[:search_results]).to eq(30.minutes)
    end
  end

  describe 'private helper methods' do
    describe '#calculate_ctr' do
      it 'calculates click-through rate correctly' do
        ctr = service.send(:calculate_ctr, 50, 1000)
        expect(ctr).to eq(5.0)
      end

      it 'returns 0 when impressions is zero' do
        ctr = service.send(:calculate_ctr, 50, 0)
        expect(ctr).to eq(0.0)
      end
    end

    describe '#calculate_conversion_rate' do
      it 'calculates conversion rate correctly' do
        rate = service.send(:calculate_conversion_rate, 5, 50)
        expect(rate).to eq(10.0)
      end

      it 'returns 0 when clicks is zero' do
        rate = service.send(:calculate_conversion_rate, 5, 0)
        expect(rate).to eq(0.0)
      end
    end

    describe '#calculate_roi' do
      it 'calculates ROI correctly' do
        roi = service.send(:calculate_roi, 150.0, 100.0)
        expect(roi).to eq(50.0)
      end

      it 'returns 0 when cost is zero' do
        roi = service.send(:calculate_roi, 150.0, 0.0)
        expect(roi).to eq(0.0)
      end
    end
  end
end
