# frozen_string_literal: true

require 'rails_helper'

RSpec.describe CampaignContract, type: :contract do
  subject(:contract) { described_class.new }

  describe 'valid input' do
    let(:valid_params) do
      {
        name: 'Test Campaign',
        campaign_type: 'email',
        target_audience: 'Young professionals aged 25-35',
        budget_cents: 50000,
        start_date: Date.current + 1.day,
        end_date: Date.current + 30.days,
        description: 'A test campaign for validation'
      }
    end

    it 'passes validation with all required fields' do
      result = contract.call(valid_params)
      expect(result).to be_success
      expect(result.errors).to be_empty
    end

    it 'passes validation without optional fields' do
      minimal_params = valid_params.slice(:name, :campaign_type, :target_audience, :budget_cents)
      result = contract.call(minimal_params)
      expect(result).to be_success
    end
  end

  describe 'name validation' do
    let(:params) { valid_params.merge(name: invalid_name) }

    context 'when name is missing' do
      let(:invalid_name) { nil }

      it 'fails validation' do
        result = contract.call(params)
        expect(result).to be_failure
        expect(result.errors[:name]).to include('must be filled')
      end
    end

    context 'when name is empty' do
      let(:invalid_name) { '' }

      it 'fails validation' do
        result = contract.call(params)
        expect(result).to be_failure
        expect(result.errors[:name]).to include('must be filled')
      end
    end

    context 'when name is too long' do
      let(:invalid_name) { 'a' * 256 }

      it 'fails validation' do
        result = contract.call(params)
        expect(result).to be_failure
        expect(result.errors[:name]).to include('size cannot be greater than 255')
      end
    end
  end

  describe 'campaign_type validation' do
    let(:params) { valid_params.merge(campaign_type: invalid_type) }

    context 'when campaign_type is missing' do
      let(:invalid_type) { nil }

      it 'fails validation' do
        result = contract.call(params)
        expect(result).to be_failure
        expect(result.errors[:campaign_type]).to include('must be filled')
      end
    end

    context 'when campaign_type is invalid' do
      let(:invalid_type) { 'invalid_type' }

      it 'fails validation' do
        result = contract.call(params)
        expect(result).to be_failure
        expect(result.errors[:campaign_type]).to include('must be one of: email, social, seo, multi_channel')
      end
    end
  end

  describe 'budget validation' do
    let(:params) { valid_params.merge(budget_cents: invalid_budget) }

    context 'when budget is negative' do
      let(:invalid_budget) { -1000 }

      it 'fails validation' do
        result = contract.call(params)
        expect(result).to be_failure
        expect(result.errors[:budget_cents]).to include('must be greater than or equal to 0')
      end
    end

    context 'when budget is not an integer' do
      let(:invalid_budget) { 'not_a_number' }

      it 'fails validation' do
        result = contract.call(params)
        expect(result).to be_failure
        expect(result.errors[:budget_cents]).to include('must be an integer')
      end
    end
  end

  describe 'date validation' do
    context 'when end_date is before start_date' do
      let(:params) do
        valid_params.merge(
          start_date: Date.current + 30.days,
          end_date: Date.current + 1.day
        )
      end

      it 'fails validation' do
        result = contract.call(params)
        expect(result).to be_failure
        expect(result.errors[:end_date]).to include('must be after start date')
      end
    end

    context 'when start_date is in the past' do
      let(:params) do
        valid_params.merge(start_date: Date.current - 1.day)
      end

      it 'fails validation' do
        result = contract.call(params)
        expect(result).to be_failure
        expect(result.errors[:start_date]).to include('cannot be in the past')
      end
    end
  end

  describe 'target_audience validation' do
    let(:params) { valid_params.merge(target_audience: invalid_audience) }

    context 'when target_audience is missing' do
      let(:invalid_audience) { nil }

      it 'fails validation' do
        result = contract.call(params)
        expect(result).to be_failure
        expect(result.errors[:target_audience]).to include('must be filled')
      end
    end

    context 'when target_audience is too short' do
      let(:invalid_audience) { 'ab' }

      it 'fails validation' do
        result = contract.call(params)
        expect(result).to be_failure
        expect(result.errors[:target_audience]).to include('size cannot be less than 3')
      end
    end
  end

  private

  def valid_params
    {
      name: 'Test Campaign',
      campaign_type: 'email',
      target_audience: 'Young professionals aged 25-35',
      budget_cents: 50000,
      start_date: Date.current + 1.day,
      end_date: Date.current + 30.days,
      description: 'A test campaign for validation'
    }
  end
end
