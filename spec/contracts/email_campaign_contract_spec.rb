# frozen_string_literal: true

require 'rails_helper'

RSpec.describe EmailCampaignContract, type: :contract do
  subject(:contract) { described_class.new }

  describe 'valid input' do
    let(:valid_params) do
      {
        subject_line: 'Welcome to our newsletter!',
        content: 'Thank you for subscribing to our newsletter.',
        from_name: '<PERSON>',
        from_email: '<EMAIL>',
        preview_text: 'Get the latest updates...'
      }
    end

    it 'passes validation with all required fields' do
      result = contract.call(valid_params)
      expect(result).to be_success
      expect(result.errors).to be_empty
    end

    it 'passes validation without optional fields' do
      minimal_params = valid_params.slice(:subject_line, :content, :from_name, :from_email)
      result = contract.call(minimal_params)
      expect(result).to be_success
    end
  end

  describe 'subject_line validation' do
    let(:params) { valid_params.merge(subject_line: invalid_subject) }

    context 'when subject_line is missing' do
      let(:invalid_subject) { nil }

      it 'fails validation' do
        result = contract.call(params)
        expect(result).to be_failure
        expect(result.errors[:subject_line]).to include('must be filled')
      end
    end

    context 'when subject_line is empty' do
      let(:invalid_subject) { '' }

      it 'fails validation' do
        result = contract.call(params)
        expect(result).to be_failure
        expect(result.errors[:subject_line]).to include('must be filled')
      end
    end

    context 'when subject_line is too long' do
      let(:invalid_subject) { 'a' * 151 }

      it 'fails validation' do
        result = contract.call(params)
        expect(result).to be_failure
        expect(result.errors[:subject_line]).to include('size cannot be greater than 150')
      end
    end
  end

  describe 'content validation' do
    let(:params) { valid_params.merge(content: invalid_content) }

    context 'when content is missing' do
      let(:invalid_content) { nil }

      it 'fails validation' do
        result = contract.call(params)
        expect(result).to be_failure
        expect(result.errors[:content]).to include('must be filled')
      end
    end

    context 'when content is empty' do
      let(:invalid_content) { '' }

      it 'fails validation' do
        result = contract.call(params)
        expect(result).to be_failure
        expect(result.errors[:content]).to include('must be filled')
      end
    end
  end

  describe 'from_email validation' do
    let(:params) { valid_params.merge(from_email: invalid_email) }

    context 'when from_email is missing' do
      let(:invalid_email) { nil }

      it 'fails validation' do
        result = contract.call(params)
        expect(result).to be_failure
        expect(result.errors[:from_email]).to include('must be filled')
      end
    end

    context 'when from_email has invalid format' do
      let(:invalid_email) { 'invalid-email' }

      it 'fails validation' do
        result = contract.call(params)
        expect(result).to be_failure
        expect(result.errors[:from_email]).to include('must be a valid email address')
      end
    end

    context 'when from_email has invalid format with spaces' do
      let(:invalid_email) { 'invalid <EMAIL>' }

      it 'fails validation' do
        result = contract.call(params)
        expect(result).to be_failure
        expect(result.errors[:from_email]).to include('must be a valid email address')
      end
    end
  end

  describe 'from_name validation' do
    let(:params) { valid_params.merge(from_name: invalid_name) }

    context 'when from_name is missing' do
      let(:invalid_name) { nil }

      it 'fails validation' do
        result = contract.call(params)
        expect(result).to be_failure
        expect(result.errors[:from_name]).to include('must be filled')
      end
    end

    context 'when from_name is empty' do
      let(:invalid_name) { '' }

      it 'fails validation' do
        result = contract.call(params)
        expect(result).to be_failure
        expect(result.errors[:from_name]).to include('must be filled')
      end
    end
  end

  describe 'preview_text validation' do
    let(:params) { valid_params.merge(preview_text: invalid_preview) }

    context 'when preview_text is too long' do
      let(:invalid_preview) { 'a' * 201 }

      it 'fails validation' do
        result = contract.call(params)
        expect(result).to be_failure
        expect(result.errors[:preview_text]).to include('size cannot be greater than 200')
      end
    end

    context 'when preview_text is valid length' do
      let(:invalid_preview) { 'a' * 200 }

      it 'passes validation' do
        result = contract.call(params)
        expect(result).to be_success
      end
    end
  end

  private

  def valid_params
    {
      subject_line: 'Welcome to our newsletter!',
      content: 'Thank you for subscribing to our newsletter.',
      from_name: 'John Doe',
      from_email: '<EMAIL>',
      preview_text: 'Get the latest updates...'
    }
  end
end
