# frozen_string_literal: true

FactoryBot.define do
  factory :audience do
    sequence(:name) { |n| "Audience #{n}" }
    description { "A target audience segment for marketing campaigns" }
    target_demographics { "25-45 year old professionals" }
    cultural_context { "western" }
    primary_language { "en" }
    secondary_languages { [ "es", "fr" ] }
    geographic_regions { [ "North America", "Europe" ] }
    age_range_min { 25 }
    age_range_max { 45 }
    interests { [ "technology", "innovation", "business" ] }
    behavioral_traits { [ "early adopters", "tech-savvy", "career-focused" ] }
    communication_preferences { "Direct, professional communication with data-driven insights" }
    cultural_values { "Innovation, efficiency, work-life balance" }
    engagement_patterns { "Active on weekdays, prefers email and social media" }
    cultural_alignment_score { 85.5 }

    # Associations
    association :tenant
    association :created_by, factory: :user

    # Custom demographics and attributes
    demographics do
      {
        income_range: "$50,000-$100,000",
        education_level: "Bachelor's degree or higher",
        occupation: "Technology and business professionals",
        lifestyle: "Urban and suburban professionals"
      }
    end

    cultural_attributes do
      {
        communication_style: "direct",
        decision_making: "data-driven",
        social_values: [ "innovation", "efficiency", "collaboration" ],
        cultural_holidays: [ "New Year", "Independence Day", "Thanksgiving" ],
        preferred_content_types: [ "articles", "videos", "infographics" ]
      }
    end

    preferences do
      {
        content_format: [ "video", "article", "infographic" ],
        communication_frequency: "weekly",
        preferred_channels: [ "email", "social_media", "mobile_app" ],
        content_topics: [ "technology", "business", "innovation", "productivity" ]
      }
    end

    trait :western_culture do
      cultural_context { "western" }
      primary_language { "en" }
      cultural_values { "Individualism, innovation, efficiency" }
      communication_preferences { "Direct, concise communication" }
    end

    trait :eastern_culture do
      cultural_context { "eastern" }
      primary_language { "zh" }
      cultural_values { "Harmony, respect, tradition" }
      communication_preferences { "Respectful, context-aware communication" }
    end

    trait :latin_american_culture do
      cultural_context { "latin_american" }
      primary_language { "es" }
      cultural_values { "Family, community, celebration" }
      communication_preferences { "Warm, personal communication" }
    end

    trait :young_adults do
      age_range_min { 18 }
      age_range_max { 30 }
      interests { [ "social_media", "gaming", "music", "travel" ] }
      behavioral_traits { [ "digital_natives", "socially_conscious", "experience_seekers" ] }
      engagement_patterns { "Active on social media, mobile-first, evening engagement" }
    end

    trait :middle_aged_professionals do
      age_range_min { 35 }
      age_range_max { 55 }
      interests { [ "career", "family", "health", "finance" ] }
      behavioral_traits { [ "goal_oriented", "time_conscious", "value_quality" ] }
      engagement_patterns { "Email preferred, weekday mornings, research-oriented" }
    end

    trait :seniors do
      age_range_min { 55 }
      age_range_max { 75 }
      interests { [ "health", "family", "travel", "hobbies" ] }
      behavioral_traits { [ "cautious", "loyal", "value_personal_service" ] }
      engagement_patterns { "Traditional channels, phone and email, slower adoption" }
    end

    trait :tech_enthusiasts do
      name { "Tech Enthusiasts" }
      interests { [ "technology", "gadgets", "innovation", "startups" ] }
      behavioral_traits { [ "early_adopters", "tech_savvy", "influence_others" ] }
      cultural_values { "Innovation, disruption, efficiency" }
      engagement_patterns { "Multi-channel, high engagement, shares content" }
    end

    trait :health_conscious do
      name { "Health & Wellness Focused" }
      interests { [ "fitness", "nutrition", "wellness", "mindfulness" ] }
      behavioral_traits { [ "health_conscious", "active_lifestyle", "research_oriented" ] }
      cultural_values { "Health, balance, sustainability" }
      engagement_patterns { "Morning engagement, visual content, community-oriented" }
    end

    trait :budget_conscious do
      name { "Budget-Conscious Consumers" }
      interests { [ "savings", "deals", "value", "financial_planning" ] }
      behavioral_traits { [ "price_sensitive", "research_heavy", "comparison_shoppers" ] }
      cultural_values { "Frugality, value, practicality" }
      engagement_patterns { "Deal alerts, comparison content, weekend research" }
    end

    trait :high_cultural_alignment do
      cultural_alignment_score { 92.0 }
    end

    trait :medium_cultural_alignment do
      cultural_alignment_score { 75.0 }
    end

    trait :low_cultural_alignment do
      cultural_alignment_score { 55.0 }
    end

    trait :with_campaigns do
      after(:create) do |audience|
        create_list(:campaign, 3, tenant: audience.tenant) do |campaign|
          create(:campaign_audience, campaign: campaign, audience: audience, tenant: audience.tenant)
        end
      end
    end

    trait :with_insights do
      after(:create) do |audience|
        create(:audience_insight, :demographic, audience: audience)
        create(:audience_insight, :behavioral, audience: audience)
        create(:audience_insight, :cultural, audience: audience)
      end
    end

    trait :with_segments do
      after(:create) do |audience|
        create(:audience_segment, :demographic, audience: audience)
        create(:audience_segment, :behavioral, audience: audience)
        create(:audience_segment, :geographic, audience: audience)
      end
    end

    # Factory for testing edge cases
    trait :minimal do
      name { "Minimal Audience" }
      description { nil }
      target_demographics { nil }
      cultural_context { nil }
      secondary_languages { [] }
      geographic_regions { [] }
      age_range_min { nil }
      age_range_max { nil }
      interests { [] }
      behavioral_traits { [] }
      communication_preferences { nil }
      cultural_values { nil }
      engagement_patterns { nil }
      cultural_alignment_score { nil }
      demographics { {} }
      cultural_attributes { {} }
      preferences { {} }
    end

    # Factory for testing validation errors
    trait :invalid do
      name { "" }
      primary_language { "" }
      age_range_min { 150 }  # Invalid age
      age_range_max { 10 }   # Max less than min
    end
  end
end
