# frozen_string_literal: true

FactoryBot.define do
  factory :campaign_metric do
    association :campaign
    metric_date { Date.current }
    impressions { 1000 }
    clicks { 50 }
    conversions { 5 }
    revenue_cents { 5000 }
    cost_cents { 2000 }
    email_opens { 100 }
    email_clicks { 25 }
    email_bounces { 5 }
    social_engagements { 75 }
    social_shares { 15 }
    social_comments { 10 }
    seo_organic_traffic { 200 }
    seo_keyword_rankings { { 'keyword1' => 5, 'keyword2' => 12 } }
    custom_metrics { { 'custom_metric1' => 100 } }

    trait :high_performance do
      impressions { 5000 }
      clicks { 500 }
      conversions { 50 }
      revenue_cents { 50000 }
      cost_cents { 10000 }
    end

    trait :low_performance do
      impressions { 100 }
      clicks { 5 }
      conversions { 1 }
      revenue_cents { 500 }
      cost_cents { 1000 }
    end

    trait :recent do
      metric_date { 1.day.ago }
    end

    trait :old do
      metric_date { 30.days.ago }
    end
  end
end
