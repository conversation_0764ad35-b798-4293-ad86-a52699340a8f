# frozen_string_literal: true

FactoryBot.define do
  factory :agent_workflow do
    association :campaign
    association :tenant

    workflow_type { 'marketing_orchestration' }
    status { 'pending' }
    progress_percent { 0 }
    current_step { 'initialization' }
    total_steps { 5 }

    context_data do
      {
        campaign_data: {
          name: campaign&.name || 'Test Campaign',
          type: campaign&.campaign_type || 'email'
        },
        target_audience: campaign&.target_audience || 'test_audience',
        initiated_at: Time.current
      }
    end

    results { {} }
    error_details { {} }

    trait :running do
      status { 'running' }
      progress_percent { 50 }
      current_step { 'content_generation' }
      started_at { 1.hour.ago }
    end

    trait :pending do
      status { 'pending' }
      progress_percent { 0 }
      current_step { 'initialization' }
    end

    trait :completed do
      status { 'completed' }
      progress_percent { 100 }
      current_step { 'finalization' }
      started_at { 2.hours.ago }
      completed_at { 30.minutes.ago }
      
      results do
        {
          strategy: 'multi_channel_approach',
          delegated_agents: ['email', 'social', 'seo'],
          completion_metrics: {
            total_time: 90.minutes,
            success_rate: 100
          }
        }
      end
    end

    trait :failed do
      status { 'failed' }
      progress_percent { 25 }
      current_step { 'content_generation' }
      started_at { 1.hour.ago }
      
      error_details do
        {
          error: 'AI service unavailable',
          error_class: 'OpenAI::Error',
          failed_at: 30.minutes.ago,
          retry_count: 3
        }
      end
    end

    # Specialist agent workflow types
    trait :marketing_orchestration do
      workflow_type { 'marketing_orchestration' }
      context_data do
        {
          coordination_data: {
            priority: 0,
            timeline: '12_hours',
            focus_areas: ['strategy', 'coordination', 'optimization']
          },
          specialist_agents: ['email_specialist', 'social_specialist', 'seo_specialist'],
          started_at: Time.current
        }
      end
    end

    trait :email_specialist do
      workflow_type { 'email_specialist' }
      context_data do
        {
          coordination_data: {
            priority: 1,
            timeline: '24_hours',
            focus_areas: ['engagement', 'conversion']
          },
          job_id: SecureRandom.uuid,
          started_at: Time.current
        }
      end
    end

    trait :social_specialist do
      workflow_type { 'social_specialist' }
      context_data do
        {
          coordination_data: {
            priority: 2,
            timeline: '48_hours',
            focus_areas: ['awareness', 'engagement']
          },
          platforms: ['facebook', 'instagram', 'twitter'],
          started_at: Time.current
        }
      end
    end

    trait :seo_specialist do
      workflow_type { 'seo_specialist' }
      context_data do
        {
          coordination_data: {
            priority: 3,
            timeline: '72_hours',
            focus_areas: ['visibility', 'organic_traffic']
          },
          target_keywords: ['marketing automation', 'AI tools'],
          started_at: Time.current
        }
      end
    end

    trait :with_detailed_results do
      after(:build) do |workflow|
        case workflow.workflow_type
        when 'email_specialist'
          workflow.results = {
            email_campaign_id: rand(1..1000),
            subject_lines: [
              'Transform Your Business Today',
              'Exclusive Opportunity Inside',
              'Limited Time: Special Offer'
            ],
            selected_subject: 'Transform Your Business Today',
            optimal_send_time: 2.hours.from_now,
            personalization_applied: true,
            delivery_status: 'scheduled'
          }
        when 'social_specialist'
          workflow.results = {
            posts_created: 5,
            platforms: ['facebook', 'instagram', 'twitter'],
            engagement_prediction: 85.5,
            hashtags_generated: ['#marketing', '#AI', '#automation'],
            optimal_post_times: {
              'facebook' => '2:00 PM',
              'instagram' => '6:00 PM',
              'twitter' => '9:00 AM'
            }
          }
        when 'seo_specialist'
          workflow.results = {
            content_optimized: true,
            keywords_targeted: ['marketing automation', 'AI tools', 'business growth'],
            meta_tags_generated: true,
            seo_score: 92.3,
            estimated_traffic_increase: '35%'
          }
        end
      end
    end
  end
end
