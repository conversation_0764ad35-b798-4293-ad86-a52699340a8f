# frozen_string_literal: true

FactoryBot.define do
  factory :customer_emotional_state do
    association :tenant

    customer_identifier { Faker::Alphanumeric.alphanumeric(number: 10) }
    current_emotion { %w[joy anger fear sadness disgust surprise anticipation trust].sample }
    emotional_intensity { %w[subtle moderate strong intense].sample }
    confidence_score { rand(70.0..99.9).round(2) }
    last_interaction_at { rand(1.hour..7.days).ago }

    context_data do
      {
        interaction_type: %w[email_open website_visit purchase support_ticket].sample,
        page_visited: Faker::Internet.url,
        session_duration: rand(30..1800), # 30 seconds to 30 minutes
        device_type: %w[desktop mobile tablet].sample,
        location: {
          country: Faker::Address.country_code,
          timezone: 'UTC'
        }
      }
    end

    behavioral_signals do
      {
        click_rate: rand(0.0..10.0).round(2),
        time_on_page: rand(30..600),
        bounce_rate: rand(0.0..100.0).round(2),
        engagement_score: rand(1..10),
        purchase_intent: rand(0.0..100.0).round(2)
      }
    end

    interaction_history do
      Array.new(rand(1..5)) do
        {
          timestamp: rand(1.day..30.days).ago.iso8601,
          action: %w[email_open click purchase view_page].sample,
          emotion_detected: %w[positive neutral negative].sample,
          confidence: rand(60.0..95.0).round(2)
        }
      end
    end

    trait :excited do
      current_emotion { 'joy' }
      emotional_intensity { 'strong' }
      confidence_score { rand(85.0..99.9).round(2) }

      behavioral_signals do
        {
          click_rate: rand(8.0..10.0).round(2),
          time_on_page: rand(300..600),
          bounce_rate: rand(0.0..20.0).round(2),
          engagement_score: rand(8..10),
          purchase_intent: rand(80.0..100.0).round(2)
        }
      end
    end

    trait :frustrated do
      current_emotion { 'anger' }
      emotional_intensity { 'strong' }
      confidence_score { rand(80.0..95.0).round(2) }

      behavioral_signals do
        {
          click_rate: rand(0.0..2.0).round(2),
          time_on_page: rand(10..60),
          bounce_rate: rand(70.0..100.0).round(2),
          engagement_score: rand(1..3),
          purchase_intent: rand(0.0..20.0).round(2)
        }
      end
    end

    trait :neutral do
      current_emotion { 'trust' }
      emotional_intensity { 'moderate' }
      confidence_score { rand(70.0..85.0).round(2) }

      behavioral_signals do
        {
          click_rate: rand(3.0..6.0).round(2),
          time_on_page: rand(120..300),
          bounce_rate: rand(40.0..60.0).round(2),
          engagement_score: rand(4..6),
          purchase_intent: rand(40.0..60.0).round(2)
        }
      end
    end

    trait :recent_interaction do
      last_interaction_at { rand(1.minute..2.hours).ago }
    end

    trait :old_interaction do
      last_interaction_at { rand(7.days..30.days).ago }
    end

    trait :high_confidence do
      confidence_score { rand(90.0..99.9).round(2) }
    end

    trait :low_confidence do
      confidence_score { rand(50.0..70.0).round(2) }
    end

    trait :with_purchase_history do
      interaction_history do
        [
          {
            timestamp: 2.days.ago.iso8601,
            action: 'purchase',
            emotion_detected: 'positive',
            confidence: 92.5,
            purchase_amount: rand(50..500)
          },
          {
            timestamp: 1.week.ago.iso8601,
            action: 'email_open',
            emotion_detected: 'neutral',
            confidence: 78.3
          },
          {
            timestamp: 2.weeks.ago.iso8601,
            action: 'view_page',
            emotion_detected: 'positive',
            confidence: 85.7
          }
        ]
      end
    end

    trait :engaged_customer do
      current_emotion { %w[joy anticipation trust].sample }
      emotional_intensity { %w[moderate strong].sample }
      confidence_score { rand(80.0..99.9).round(2) }

      behavioral_signals do
        {
          click_rate: rand(6.0..10.0).round(2),
          time_on_page: rand(200..600),
          bounce_rate: rand(0.0..30.0).round(2),
          engagement_score: rand(7..10),
          purchase_intent: rand(70.0..100.0).round(2)
        }
      end
    end

    trait :at_risk_customer do
      current_emotion { %w[fear anger].sample }
      emotional_intensity { %w[moderate strong].sample }
      confidence_score { rand(75.0..90.0).round(2) }

      behavioral_signals do
        {
          click_rate: rand(0.0..3.0).round(2),
          time_on_page: rand(10..120),
          bounce_rate: rand(60.0..100.0).round(2),
          engagement_score: rand(1..4),
          purchase_intent: rand(0.0..30.0).round(2)
        }
      end
    end
  end
end
