# frozen_string_literal: true

FactoryBot.define do
  factory :vibe_analysis_record do
    # Use current tenant if available, otherwise create one
    tenant { ActsAsTenant.current_tenant || association(:tenant) }
    association :campaign

    analysis_type { 'emotional' }
    analysis_data do
      {
        overall_score: 7.5,
        primary_emotion: 'joy',
        secondary_emotions: [ 'trust', 'anticipation' ],
        emotional_intensity: 'moderate',
        resonance_strength: 7.5,
        confidence_score: 89.0,
        emotional_journey: {
          start_emotion: 'curiosity',
          peak_emotion: 'joy',
          end_emotion: 'trust'
        },
        audience_alignment: {
          target_alignment: 'high',
          demographic_match: 8.2,
          psychographic_match: 7.8
        }
      }
    end
    confidence_score { 85.0 }
    ai_reasoning { 'The campaign demonstrates strong emotional resonance with clear emotional journey progression.' }
    model_used { 'gpt-4' }
    metadata do
      {
        analysis_duration: 2.34,
        prompt_tokens: 150,
        completion_tokens: 300,
        created_by: 'VibeAnalysisService'
      }
    end

    trait :cultural do
      analysis_type { 'cultural' }
      analysis_data do
        {
          cultural_sensitivity_score: 8.5,
          trend_alignment: {
            alignment_strength: 7.8,
            trending_topics: [ 'sustainability', 'wellness' ],
            cultural_moments: [ 'Earth Day', 'Mental Health Awareness Month' ]
          },
          timing_optimization: {
            cultural_calendar_alignment: 8.0,
            optimal_timing: '2024-04-22T10:00:00Z',
            seasonal_relevance: 'high'
          }
        }
      end
      ai_reasoning { 'Strong cultural alignment with current sustainability trends.' }
    end

    trait :authenticity do
      analysis_type { 'authenticity' }
      analysis_data do
        {
          brand_voice_consistency: 8.7,
          cultural_sensitivity: 9.1,
          trust_factor: 8.3,
          authenticity_dimensions: {
            transparency: 8.5,
            consistency: 8.7,
            credibility: 8.9,
            genuineness: 8.1,
            relatability: 7.8,
            cultural_awareness: 9.1,
            ethical_alignment: 8.6,
            emotional_honesty: 8.2
          }
        }
      end
      ai_reasoning { 'Excellent authenticity across all dimensions with strong brand voice consistency.' }
    end

    trait :psychographic do
      analysis_type { 'psychographic' }
      analysis_data do
        {
          primary_segment: 'achievers',
          psychological_triggers: [ 'achievement', 'status', 'recognition' ],
          personality_alignment: {
            openness: 7.2,
            conscientiousness: 8.5,
            extraversion: 6.8,
            agreeableness: 7.9,
            neuroticism: 3.2
          },
          audience_segmentation: {
            primary_segment: 'achievers',
            secondary_segments: [ 'strivers', 'experiencers' ]
          }
        }
      end
      ai_reasoning { 'Strong alignment with achiever psychographic profile and relevant psychological triggers.' }
    end

    trait :low_confidence do
      confidence_score { 45.0 }
    end

    trait :high_performing do
      confidence_score { 95.0 }
      ai_reasoning { 'Exceptional vibe analysis with very high confidence across all metrics.' }
    end
  end
end
