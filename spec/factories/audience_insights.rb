# frozen_string_literal: true

FactoryBot.define do
  factory :audience_insight do
    association :audience
    insight_type { "demographic" }
    confidence { 85.5 }
    source { "analytics_engine" }

    data do
      {
        summary: "Key demographic insights for the audience",
        key_findings: [
          "Primary age group is 25-35 years old",
          "High concentration in urban areas",
          "Above-average income levels"
        ],
        recommendations: [
          "Focus on mobile-first content",
          "Emphasize convenience and efficiency",
          "Use professional tone in communications"
        ],
        metrics: {
          sample_size: 1000,
          confidence_interval: "95%",
          data_freshness: "7 days"
        },
        trends: {
          growth_rate: "12% YoY",
          engagement_trend: "increasing",
          seasonal_patterns: "higher engagement in Q4"
        }
      }
    end

    trait :demographic do
      insight_type { "demographic" }
      data do
        {
          summary: "Demographic profile analysis",
          key_findings: [
            "Average age: 32 years",
            "Gender split: 52% female, 48% male",
            "Education: 78% college-educated",
            "Income: $65,000 median household income"
          ],
          recommendations: [
            "Target content for college-educated professionals",
            "Use inclusive messaging for gender balance",
            "Price products for middle-to-upper income bracket"
          ],
          metrics: {
            age_distribution: { "18-24" => 15, "25-34" => 45, "35-44" => 30, "45+" => 10 },
            education_levels: { "high_school" => 22, "college" => 58, "graduate" => 20 },
            income_brackets: { "under_50k" => 25, "50k_75k" => 35, "75k_100k" => 25, "over_100k" => 15 }
          }
        }
      end
    end

    trait :behavioral do
      insight_type { "behavioral" }
      data do
        {
          summary: "Behavioral patterns and preferences",
          key_findings: [
            "High social media engagement",
            "Prefers video content over text",
            "Mobile-first browsing behavior",
            "Price-conscious but values quality"
          ],
          recommendations: [
            "Invest in video content creation",
            "Optimize for mobile experience",
            "Highlight value proposition clearly",
            "Use social proof and reviews"
          ],
          metrics: {
            content_preferences: { "video" => 45, "images" => 30, "text" => 20, "audio" => 5 },
            device_usage: { "mobile" => 65, "desktop" => 25, "tablet" => 10 },
            purchase_triggers: { "price" => 35, "quality" => 40, "convenience" => 15, "brand" => 10 }
          }
        }
      end
    end

    trait :cultural do
      insight_type { "cultural" }
      data do
        {
          summary: "Cultural values and communication preferences",
          key_findings: [
            "Values authenticity and transparency",
            "Responds well to inclusive messaging",
            "Prefers direct communication style",
            "Influenced by peer recommendations"
          ],
          recommendations: [
            "Use authentic brand storytelling",
            "Include diverse representation in content",
            "Be clear and direct in messaging",
            "Leverage user-generated content and testimonials"
          ],
          metrics: {
            cultural_values: { "authenticity" => 85, "inclusivity" => 78, "innovation" => 72, "tradition" => 45 },
            communication_style: { "direct" => 60, "storytelling" => 25, "humorous" => 10, "formal" => 5 },
            influence_sources: { "peers" => 40, "experts" => 30, "brands" => 20, "celebrities" => 10 }
          }
        }
      end
    end

    trait :engagement do
      insight_type { "engagement" }
      data do
        {
          summary: "Engagement patterns and optimal timing",
          key_findings: [
            "Peak engagement: Tuesday-Thursday 2-4 PM",
            "Weekend engagement drops 40%",
            "Email open rates: 25% above industry average",
            "Social media engagement highest on Instagram"
          ],
          recommendations: [
            "Schedule content for mid-week afternoons",
            "Reduce weekend posting frequency",
            "Prioritize email marketing campaigns",
            "Focus social media efforts on Instagram"
          ],
          metrics: {
            daily_engagement: { "monday" => 75, "tuesday" => 90, "wednesday" => 95, "thursday" => 88, "friday" => 70, "saturday" => 45, "sunday" => 40 },
            hourly_peaks: { "9am" => 60, "12pm" => 80, "2pm" => 95, "6pm" => 70, "8pm" => 55 },
            channel_performance: { "email" => 85, "instagram" => 78, "facebook" => 65, "twitter" => 45, "linkedin" => 55 }
          }
        }
      end
    end

    trait :performance do
      insight_type { "performance" }
      data do
        {
          summary: "Campaign performance analysis",
          key_findings: [
            "Video campaigns outperform static by 3x",
            "Personalized content increases CTR by 45%",
            "Mobile optimization critical for conversions",
            "Seasonal campaigns show 25% higher ROI"
          ],
          recommendations: [
            "Increase video content budget allocation",
            "Implement dynamic personalization",
            "Prioritize mobile-first design",
            "Plan seasonal campaign calendar"
          ],
          metrics: {
            content_performance: { "video" => 4.5, "image" => 2.8, "text" => 1.5, "carousel" => 3.2 },
            personalization_impact: { "personalized" => 3.8, "segmented" => 2.4, "generic" => 1.2 },
            device_conversion: { "mobile" => 2.8, "desktop" => 3.5, "tablet" => 2.1 }
          }
        }
      end
    end

    trait :preference do
      insight_type { "preference" }
      data do
        {
          summary: "Content and product preferences",
          key_findings: [
            "Prefers educational over promotional content",
            "Values sustainability and ethical practices",
            "Interested in behind-the-scenes content",
            "Responds to limited-time offers"
          ],
          recommendations: [
            "Create more educational content series",
            "Highlight sustainability initiatives",
            "Share company culture and process content",
            "Use scarcity marketing strategically"
          ],
          metrics: {
            content_types: { "educational" => 85, "entertainment" => 65, "promotional" => 45, "news" => 55 },
            brand_values: { "sustainability" => 80, "innovation" => 75, "quality" => 90, "affordability" => 60 },
            offer_types: { "discount" => 70, "free_shipping" => 65, "bundle" => 55, "limited_time" => 75 }
          }
        }
      end
    end

    trait :high_confidence do
      confidence { 92.0 }
    end

    trait :medium_confidence do
      confidence { 75.0 }
    end

    trait :low_confidence do
      confidence { 58.0 }
    end

    trait :recent do
      created_at { 2.days.ago }
    end

    trait :stale do
      created_at { 35.days.ago }
    end
  end
end
