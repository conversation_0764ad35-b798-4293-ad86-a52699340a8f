# frozen_string_literal: true

FactoryBot.define do
  factory :campaign do
    # Use current tenant if available, otherwise create one
    tenant { ActsAsTenant.current_tenant || association(:tenant) }
    association :created_by, factory: :user

    sequence(:name) { |n| "#{Faker::Marketing.buzzwords} Campaign #{n}" }
    description { Faker::Lorem.paragraph(sentence_count: 3) }
    campaign_type { 'email' }
    status { 'draft' }
    target_audience { Faker::Company.industry }

    start_date { 1.week.from_now }
    end_date { 3.weeks.from_now }

    budget_cents { rand(10000..100000) } # $100 to $1000 in cents

    settings do
      {
        goals: {
          primary: 'increase_engagement',
          metrics: [ 'open_rate', 'click_rate', 'conversion_rate' ]
        },
        targeting: {
          demographics: [ 'small_business_owners' ],
          interests: [ 'marketing', 'automation' ],
          geographic: [ 'north_america' ]
        }
      }
    end

    trait :active do
      status { 'active' }
      start_date { 1.day.ago }
    end

    trait :completed do
      status { 'completed' }
      start_date { 2.weeks.ago }
      end_date { 1.week.ago }
    end

    trait :multi_channel do
      campaign_type { 'multi_channel' }
    end

    trait :social do
      campaign_type { 'social' }
    end

    trait :seo do
      campaign_type { 'seo' }
    end

    trait :with_large_budget do
      budget_cents { rand(500000..1000000) } # $5000 to $10000
    end

    # Vibe Marketing traits
    trait :with_vibe_data do
      vibe_data do
        {
          emotional_profile: {
            primary_emotion: 'joy',
            secondary_emotions: [ 'trust', 'anticipation' ],
            intensity: 'moderate'
          },
          cultural_moment: 'Sustainability Week',
          psychographics: {
            primary_segment: 'achievers',
            psychological_triggers: [ 'achievement', 'recognition' ]
          },
          authenticity_metrics: {
            brand_voice_consistency: 8.5,
            cultural_sensitivity: 9.0
          }
        }
      end
      emotional_tone { 'optimistic' }
      cultural_relevance_score { 8.5 }
      authenticity_score { 8.7 }
      vibe_status { 'vibe_approved' }
    end

    trait :vibe_pending do
      vibe_status { 'pending' }
    end

    trait :vibe_flagged do
      vibe_status { 'vibe_flagged' }
      cultural_relevance_score { 4.2 }
      authenticity_score { 5.1 }
    end

    trait :cultural_validated do
      vibe_status { 'cultural_validated' }
      cultural_relevance_score { 9.2 }
    end

    trait :authenticity_verified do
      vibe_status { 'authenticity_verified' }
      authenticity_score { 9.5 }
    end

    trait :with_emotional_resonance do
      after(:create) do |campaign|
        create(:emotional_resonance_profile, campaign: campaign)
      end
    end

    trait :with_authenticity_check do
      after(:create) do |campaign|
        create(:authenticity_check, campaign: campaign)
      end
    end

    trait :with_vibe_analysis do
      after(:create) do |campaign|
        create(:vibe_analysis_record, :emotional, campaign: campaign)
        create(:vibe_analysis_record, :cultural, campaign: campaign)
      end
    end
  end
end
