# frozen_string_literal: true

FactoryBot.define do
  factory :tenant do
    sequence(:name) { |n| "#{Faker::Company.name} #{n}" }
    sequence(:subdomain) { |n| "#{Faker::Internet.domain_word.downcase}-#{n}" }
    status { 'active' }
    settings { {} }

    trait :suspended do
      status { 'suspended' }
    end

    trait :with_custom_settings do
      settings do
        {
          branding: {
            primary_color: '#007bff',
            logo_url: Faker::Internet.url
          },
          features: {
            ai_enabled: true,
            email_campaigns: true,
            social_media: true,
            seo_tools: true
          }
        }
      end
    end
  end
end
