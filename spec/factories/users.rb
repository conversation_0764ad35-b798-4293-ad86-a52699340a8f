# frozen_string_literal: true

FactoryBot.define do
  factory :user do
    # Use current tenant if available, otherwise create one
    tenant { ActsAsTenant.current_tenant || association(:tenant) }
    sequence(:email) { |n| "user#{n}@example.com" }
    password { 'password123' }
    password_confirmation { 'password123' }
    first_name { Faker::Name.first_name }
    last_name { Faker::Name.last_name }
    role { 0 } # member
    confirmed_at { Time.current }

    trait :admin do
      role { 1 } # admin
    end

    trait :owner do
      role { 2 } # owner
    end

    trait :unconfirmed do
      confirmed_at { nil }
    end
  end
end
