# frozen_string_literal: true

FactoryBot.define do
  factory :email_campaign do
    association :campaign

    subject_line { Faker::Marketing.buzzwords.titleize + " - Don't Miss Out!" }
    preview_text { Faker::Lorem.sentence(word_count: 10) }
    content { Faker::Lorem.paragraphs(number: 3).join("\n\n") }
    from_name { Faker::Company.name }
    from_email { Faker::Internet.email }

    settings do
      {
        delivery_options: {
          send_immediately: false,
          scheduled_at: nil,
          time_zone: 'UTC'
        },
        tracking: {
          opens: true,
          clicks: true,
          unsubscribes: true
        },
        recipient_count: rand(500..5000),
        a_b_test: {
          enabled: false,
          test_percentage: 10,
          variants: []
        }
      }
    end

    trait :ready_to_send do
      subject_line { "Ready to Send Campaign" }
      content { "This campaign is ready to be sent to recipients." }
      from_name { "Marketing Team" }
      from_email { "<EMAIL>" }

      # Create a campaign with active status for this email campaign
      association :campaign, :active
    end

    trait :with_a_b_test do
      settings do
        {
          delivery_options: {
            send_immediately: false,
            scheduled_at: nil,
            time_zone: 'UTC'
          },
          tracking: {
            opens: true,
            clicks: true,
            unsubscribes: true
          },
          recipient_count: 2000,
          a_b_test: {
            enabled: true,
            test_percentage: 20,
            variants: [
              {
                subject_line: "Variant A Subject",
                content: "Variant A content"
              },
              {
                subject_line: "Variant B Subject",
                content: "Variant B content"
              }
            ]
          }
        }
      end
    end

    trait :scheduled do
      settings do
        {
          delivery_options: {
            send_immediately: false,
            scheduled_at: 2.hours.from_now.iso8601,
            time_zone: 'UTC'
          },
          tracking: {
            opens: true,
            clicks: true,
            unsubscribes: true
          },
          recipient_count: 1000
        }
      end
    end

    trait :with_large_audience do
      settings do
        {
          delivery_options: {
            send_immediately: true,
            scheduled_at: nil,
            time_zone: 'UTC'
          },
          tracking: {
            opens: true,
            clicks: true,
            unsubscribes: true
          },
          recipient_count: 50000
        }
      end
    end
  end
end
