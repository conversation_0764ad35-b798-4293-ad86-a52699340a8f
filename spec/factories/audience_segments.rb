# frozen_string_literal: true

FactoryBot.define do
  factory :audience_segment do
    association :audience
    sequence(:name) { |n| "Segment #{n}" }
    segment_type { "demographic" }
    description { "A targeted segment of the audience" }
    size { 1000 }

    criteria do
      {
        age_range: "25-35",
        interests: [ "technology", "innovation" ],
        location: "urban"
      }
    end

    trait :demographic do
      segment_type { "demographic" }
      name { "Age-Based Segment" }
      description { "Segmented by age demographics" }
      criteria do
        {
          age_range: "25-35",
          gender: "all",
          income_level: "middle_to_high",
          education: "college_educated"
        }
      end
      size { 1500 }
    end

    trait :behavioral do
      segment_type { "behavioral" }
      name { "High Engagement Segment" }
      description { "Users with high engagement patterns" }
      criteria do
        {
          engagement_level: "high",
          purchase_frequency: "regular",
          content_interaction: "active",
          social_sharing: "frequent"
        }
      end
      size { 800 }
    end

    trait :cultural do
      segment_type { "cultural" }
      name { "Cultural Values Segment" }
      description { "Segmented by cultural values and preferences" }
      criteria do
        {
          cultural_context: "western",
          values: [ "innovation", "efficiency" ],
          communication_style: "direct",
          cultural_holidays: [ "christmas", "new_year" ]
        }
      end
      size { 1200 }
    end

    trait :geographic do
      segment_type { "geographic" }
      name { "Regional Segment" }
      description { "Segmented by geographic location" }
      criteria do
        {
          regions: [ "North America", "Europe" ],
          timezone: "UTC-5 to UTC+1",
          urban_rural: "urban",
          climate: "temperate"
        }
      end
      size { 2000 }
    end

    trait :psychographic do
      segment_type { "psychographic" }
      name { "Lifestyle Segment" }
      description { "Segmented by lifestyle and personality traits" }
      criteria do
        {
          lifestyle: "professional",
          personality_traits: [ "ambitious", "tech_savvy" ],
          interests: [ "career", "technology", "fitness" ],
          values: [ "success", "innovation", "work_life_balance" ]
        }
      end
      size { 900 }
    end

    trait :temporal do
      segment_type { "temporal" }
      name { "Time-Based Segment" }
      description { "Segmented by time-based behaviors" }
      criteria do
        {
          active_hours: "9am-6pm",
          peak_days: [ "tuesday", "wednesday", "thursday" ],
          seasonal_activity: "higher_in_q4",
          response_time: "within_24_hours"
        }
      end
      size { 1100 }
    end

    trait :large_segment do
      size { 5000 }
    end

    trait :medium_segment do
      size { 1500 }
    end

    trait :small_segment do
      size { 300 }
    end

    trait :tech_enthusiasts do
      name { "Tech Enthusiasts" }
      segment_type { "psychographic" }
      description { "Early adopters and technology enthusiasts" }
      criteria do
        {
          interests: [ "technology", "gadgets", "innovation", "startups" ],
          behavior: [ "early_adopter", "tech_influencer", "research_heavy" ],
          spending: "high_on_tech",
          content_preference: "technical_details"
        }
      end
      size { 750 }
    end

    trait :budget_conscious do
      name { "Budget-Conscious Shoppers" }
      segment_type { "behavioral" }
      description { "Price-sensitive consumers who research before buying" }
      criteria do
        {
          behavior: [ "price_comparison", "coupon_usage", "sale_waiting" ],
          purchase_triggers: [ "discount", "free_shipping", "bundle_deals" ],
          research_time: "extensive",
          brand_loyalty: "low_to_medium"
        }
      end
      size { 1800 }
    end

    trait :premium_customers do
      name { "Premium Customers" }
      segment_type { "behavioral" }
      description { "High-value customers who prefer quality over price" }
      criteria do
        {
          spending_level: "high",
          purchase_frequency: "regular",
          brand_loyalty: "high",
          service_expectations: "premium"
        }
      end
      size { 400 }
    end

    trait :mobile_first do
      name { "Mobile-First Users" }
      segment_type { "behavioral" }
      description { "Users who primarily engage via mobile devices" }
      criteria do
        {
          device_preference: "mobile",
          app_usage: "high",
          mobile_shopping: "frequent",
          notification_response: "high"
        }
      end
      size { 2200 }
    end

    trait :social_influencers do
      name { "Social Influencers" }
      segment_type { "behavioral" }
      description { "Users with high social media influence and sharing behavior" }
      criteria do
        {
          social_activity: "high",
          content_sharing: "frequent",
          follower_count: "above_average",
          engagement_rate: "high"
        }
      end
      size { 350 }
    end

    trait :with_performance_data do
      after(:create) do |segment|
        # Create some campaigns associated with this segment's audience
        campaigns = create_list(:campaign, 2, tenant: segment.audience.tenant)
        campaigns.each do |campaign|
          create(:campaign_audience, campaign: campaign, audience: segment.audience, tenant: segment.audience.tenant)
          create(:campaign_metric, campaign: campaign, engagement_rate: rand(2.0..6.0).round(2))
        end
      end
    end
  end
end
