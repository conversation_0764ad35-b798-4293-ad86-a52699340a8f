# frozen_string_literal: true

FactoryBot.define do
  factory :emotional_resonance_profile do
    # Use current tenant if available, otherwise create one
    tenant { ActsAsTenant.current_tenant || association(:tenant) }
    association :created_by, factory: :user

    name { "#{Faker::Emotion.adjective.capitalize} #{Faker::Marketing.buzzwords} Profile" }
    description { "Emotional resonance profile targeting #{primary_emotion} emotion" }
    primary_emotion { 'joy' }
    target_age_range { '25-34' }

    emotion_weights do
      {
        joy: 0.8,
        trust: 0.6,
        anticipation: 0.4,
        surprise: 0.2
      }
    end

    tone_preferences do
      {
        formality: 'casual',
        enthusiasm: 'high',
        empathy: 'strong',
        urgency: 'moderate'
      }
    end

    content_guidelines do
      {
        language_style: 'conversational',
        imagery_style: 'bright_optimistic',
        call_to_action_style: 'encouraging',
        storytelling_approach: 'aspirational'
      }
    end

    avoided_triggers do
      [ 'financial_stress', 'health_anxiety', 'social_isolation' ]
    end

    psychographic_traits do
      {
        values: [ 'authenticity', 'community', 'growth' ],
        interests: [ 'technology', 'wellness', 'creativity' ],
        lifestyle: 'urban_professional',
        communication_preference: 'visual_story'
      }
    end

    trait :high_intensity do
      emotion_weights do
        {
          joy: 0.95,
          trust: 0.8,
          anticipation: 0.7,
          surprise: 0.5
        }
      end
    end
  end
end
