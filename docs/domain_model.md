# Domain Model Design for AI Marketing Hub

## Core Bounded Contexts

### 1. Tenancy Context
- **Tenant** (Aggregate Root)
- **User** 
- **TenantConfiguration**

### 2. Marketing Context  
- **Campaign** (Aggregate Root)
- **CampaignTemplate**
- **CampaignMetrics**
- **EmailCampaign**
- **SocialCampaign** 
- **SeoCampaign**

### 3. Data Context
- **DataSource** (Aggregate Root)
- **DataIngestionJob**
- **Feature**
- **FeatureStore**

### 4. AI Orchestration Context
- **AgentWorkflow** (Aggregate Root)
- **AgentTask**
- **AgentResult**

## Domain Relationships

```
Tenant 1--* User
Tenant 1--* Campaign
Tenant 1--* DataSource
Tenant 1--* AgentWorkflow

Campaign 1--* CampaignMetrics
Campaign 1--1 EmailCampaign (optional)
Campaign 1--1 SocialCampaign (optional)
Campaign 1--1 SeoCampaign (optional)

DataSource 1--* Feature
AgentWorkflow 1--* AgentTask
AgentTask 1--1 AgentResult
```
