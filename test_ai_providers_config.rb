#!/usr/bin/env ruby

# Test script to verify AI Providers configuration
# Run with: ruby test_ai_providers_config.rb

require 'bundler/setup'
require 'ostruct'
require_relative 'config/environment'

puts "🔍 Testing AI Providers Configuration..."
puts "=" * 60

begin
  # Define all supported AI providers
  providers = {
    openai: {
      name: "OpenAI",
      credentials_key: :openai_api_key,
      env_key: "OPENAI_API_KEY"
    },
    anthropic: {
      name: "Anthropic Claude",
      credentials_key: :anthropic_api_key,
      env_key: "ANTHROPIC_API_KEY"
    },
    gemini: {
      name: "Google Gemini",
      credentials_key: :gemini_api_key,
      env_key: "GEMINI_API_KEY"
    },
    deepseek: {
      name: "DeepSeek",
      credentials_key: :deepseek_api_key,
      env_key: "DEEPSEEK_API_KEY"
    },
    openrouter: {
      name: "OpenRouter",
      credentials_key: :openrouter_api_key,
      env_key: "OPENROUTER_API_KEY"
    }
  }

  configured_providers = []
  placeholder_providers = []

  # Test 1: Check all provider configurations
  puts "1. Checking AI Provider Configurations..."
  providers.each do |provider_key, config|
    puts "\n   #{config[:name]}:"

    # Check credentials
    credentials_key = Rails.application.credentials.send(config[:credentials_key])
    if credentials_key
      if credentials_key.include?("placeholder") || credentials_key.include?("your_")
        puts "     📋 Rails credentials: ⚠️  Placeholder value"
        placeholder_providers << config[:name]
      else
        puts "     📋 Rails credentials: ✅ Configured (#{credentials_key[0..10]}...)"
        configured_providers << config[:name]
      end
    else
      puts "     📋 Rails credentials: ❌ Not set"
    end

    # Check environment variable
    env_key = ENV[config[:env_key]]
    if env_key
      if env_key.include?("placeholder") || env_key.include?("your_")
        puts "     🌍 Environment variable: ⚠️  Placeholder value"
      else
        puts "     🌍 Environment variable: ✅ Configured (#{env_key[0..10]}...)"
        configured_providers << config[:name] unless configured_providers.include?(config[:name])
      end
    else
      puts "     🌍 Environment variable: ❌ Not set"
    end

    # Check provider health
    health_status = RubyLLMExtensions.check_provider_health(provider_key.to_s)
    puts "     🏥 Provider health: #{health_status ? '✅ Available' : '❌ Unavailable'}"
  end

  configured_providers.uniq!

  # Test 2: Test RubyLlmService initialization
  puts "\n2. Testing Multi-Provider AI Service..."
  if configured_providers.any?
    begin
      # Create a mock tenant for testing
      tenant = OpenStruct.new(
        id: 1,
        name: "Test Tenant",
        settings: {}
      )

      service = RubyLlmService.new(
        tenant: tenant,
        context: { test: true },
        provider_strategy: :balanced
      )
      puts "   ✅ RubyLlmService initialized successfully!"
      puts "   🎯 Provider strategy: balanced"

      # Test provider health check
      health_check = service.provider_health_check
      puts "   🏥 Provider health status:"
      health_check.each do |provider, status|
        status_icon = status[:available] ? "✅" : "❌"
        puts "     #{provider}: #{status_icon} #{status[:available] ? 'Available' : 'Unavailable'}"
      end

    rescue => e
      puts "   ❌ RubyLlmService initialization failed: #{e.message}"
    end
  else
    puts "   ⚠️  No providers configured - skipping service test"
  end

  # Test 3: Test content generation (only if providers are available)
  if configured_providers.any? && !configured_providers.all? { |p| placeholder_providers.include?(p) }
    puts "\n3. Testing AI Content Generation..."
    begin
      tenant = OpenStruct.new(id: 1, name: "Test Tenant", settings: {})
      service = RubyLlmService.new(tenant: tenant, provider_strategy: :cost_sensitive)

      response = service.generate_content(
        "Say 'Hello from AI Marketing Hub!' in exactly 5 words.",
        task_type: :creative_content,
        max_tokens: 20
      )

      puts "   ✅ Content generation successful!"
      puts "   📝 Response: #{response.content}"
      puts "   🤖 Model used: #{response.model}"
      puts "   🏭 Provider: #{response.provider}"
      puts "   💰 Estimated cost: $#{response.cost}"

    rescue => e
      puts "   ⚠️  Content generation failed: #{e.message}"
      puts "   💡 This might be due to invalid API keys or network issues"
    end
  else
    puts "\n3. Skipping content generation test"
    puts "   💡 Replace placeholder keys with real API keys to test content generation"
  end

rescue RubyLlmService::ConfigurationError => e
  puts "   ❌ AI service configuration error:"
  puts e.message
rescue => e
  puts "   ❌ Unexpected error: #{e.message}"
  puts "   📍 #{e.backtrace.first}"
end

puts "\n" + "=" * 60
puts "🎯 Configuration Summary:"
puts "   • Configured providers: #{configured_providers.any? ? configured_providers.join(', ') : 'None'}"
puts "   • Placeholder providers: #{placeholder_providers.any? ? placeholder_providers.join(', ') : 'None'}"
puts "   • Total available: #{configured_providers.count} out of #{providers.count} providers"

puts "\n💡 Next steps:"
if configured_providers.empty?
  puts "   1. Configure at least one AI provider API key"
  puts "   2. For development: Add keys to .env file"
  puts "   3. For production: Add keys to Rails credentials"
  puts "   4. See updated setup documentation for all providers"
elsif placeholder_providers.any?
  puts "   1. Replace placeholder keys with real API keys"
  puts "   2. Test the configuration again"
else
  puts "   ✅ Configuration looks good!"
  puts "   🚀 Your AI Marketing Hub is ready to use multiple AI providers"
end

puts "\n📚 Provider Documentation:"
puts "   • OpenAI: https://platform.openai.com/api-keys"
puts "   • Anthropic: https://console.anthropic.com/"
puts "   • Google Gemini: https://makersuite.google.com/app/apikey"
puts "   • DeepSeek: https://platform.deepseek.com/api_keys"
puts "   • OpenRouter: https://openrouter.ai/keys"
