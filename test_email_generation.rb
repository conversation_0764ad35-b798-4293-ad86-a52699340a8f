#!/usr/bin/env ruby
# Test email content generation

puts "🧪 Testing Email Content Generation"
puts "=" * 40

begin
  # Load Rails environment
  require_relative 'config/environment'

  puts "✅ Rails environment loaded"

  # Check credentials
  puts "\n🔑 Checking API Keys:"
  credentials = Rails.application.credentials

  providers = {
    'OpenAI' => credentials.openai_api_key,
    'Anthropic' => credentials.anthropic_api_key,
    'Gemini' => credentials.gemini_api_key,
    'DeepSeek' => credentials.deepseek_api_key,
    'OpenRouter' => credentials.openrouter_api_key
  }

  configured_count = 0
  providers.each do |name, key|
    if key.present? && !key.include?('placeholder') && !key.include?('your_')
      puts "✅ #{name}: Configured"
      configured_count += 1
    else
      puts "❌ #{name}: Not configured"
    end
  end

  if configured_count == 0
    puts "\n❌ No API keys configured! Please add at least one API key."
    exit 1
  end

  # Test provider health
  puts "\n🏥 Testing Provider Health:"
  health_results = {}
  %w[openai anthropic gemini deepseek openrouter].each do |provider|
    begin
      health = RubyLLMExtensions.check_provider_health(provider)
      health_results[provider] = health
      puts "#{health ? '✅' : '❌'} #{provider.capitalize}: #{health ? 'Available' : 'Unavailable'}"
    rescue => e
      puts "❌ #{provider.capitalize}: Error - #{e.message}"
      health_results[provider] = false
    end
  end

  available_providers = health_results.select { |k, v| v }.keys
  if available_providers.empty?
    puts "\n❌ No providers are available!"
    exit 1
  end

  puts "\n✅ Available providers: #{available_providers.join(', ')}"

  # Get or create tenant and campaign
  puts "\n📋 Setting up test data:"
  tenant = Tenant.first
  if tenant.nil?
    tenant = Tenant.create!(name: 'Test Company', subdomain: 'test-company')
    puts "✅ Created test tenant: #{tenant.name}"
  else
    puts "✅ Using existing tenant: #{tenant.name}"
  end

  campaign = tenant.campaigns.first
  if campaign.nil?
    campaign = tenant.campaigns.create!(
      name: 'Test Email Campaign',
      campaign_type: 'email',
      target_audience: 'Small business owners',
      status: 'draft'
    )
    puts "✅ Created test campaign: #{campaign.name}"
  else
    puts "✅ Using existing campaign: #{campaign.name}"
  end

  # Test RubyLlmService directly
  puts "\n🤖 Testing RubyLlmService:"
  service = RubyLlmService.new(tenant: tenant)
  puts "✅ RubyLlmService initialized"

  # Test simple content generation
  puts "\n🎯 Testing simple content generation:"
  result = service.generate_content(
    "Write a short professional email subject line for a marketing campaign",
    task_type: :creative_content
  )

  if result.successful?
    puts "✅ Content generated successfully!"
    puts "📝 Generated content: #{result.content}"
    puts "🏷️  Model used: #{result.model}"
    puts "🏢 Provider: #{result.provider}"
  else
    puts "❌ Content generation failed"
  end

  # Test EmailSpecialistAgentService
  puts "\n📧 Testing EmailSpecialistAgentService:"
  email_service = EmailSpecialistAgentService.new(campaign: campaign)

  email_result = email_service.generate_campaign_content(
    brand_voice: 'professional',
    email_type: 'promotional',
    key_message: 'Boost your business with our AI-powered marketing tools'
  )

  puts "📊 Email generation result:"
  puts email_result.inspect

  if email_result[:status] == 'success'
    puts "\n🎉 SUCCESS! Email content generated successfully!"
    content = email_result[:content]
    puts "📧 Subject: #{content[:subject_line]}"
    puts "👀 Preview: #{content[:preview_text]}"
    puts "📄 Content length: #{content[:content]&.length || 0} characters"
  else
    puts "\n❌ Email generation failed: #{email_result[:message]}"
  end

rescue => e
  puts "\n❌ Error: #{e.message}"
  puts "📍 Backtrace:"
  puts e.backtrace.first(5).join("\n")
end
