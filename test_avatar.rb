#!/usr/bin/env ruby

# Simple test to verify avatar functionality
puts "Testing avatar functionality..."

# Test that the avatar component file exists
avatar_component_path = "app/views/shared/_user_avatar.html.erb"
if File.exist?(avatar_component_path)
  puts "✅ Avatar component exists at #{avatar_component_path}"
else
  puts "❌ Avatar component missing at #{avatar_component_path}"
end

# Test that User model has avatar attachment
user_model_path = "app/models/user.rb"
if File.exist?(user_model_path)
  user_content = File.read(user_model_path)
  if user_content.include?("has_one_attached :avatar")
    puts "✅ User model has avatar attachment"
  else
    puts "❌ User model missing avatar attachment"
  end
  
  if user_content.include?("validate :avatar_validation")
    puts "✅ User model has avatar validation"
  else
    puts "❌ User model missing avatar validation"
  end
else
  puts "❌ User model not found"
end

# Test that ProfilesController handles avatar
controller_path = "app/controllers/profiles_controller.rb"
if File.exist?(controller_path)
  controller_content = File.read(controller_path)
  if controller_content.include?(":avatar")
    puts "✅ ProfilesController handles avatar uploads"
  else
    puts "❌ ProfilesController missing avatar handling"
  end
else
  puts "❌ ProfilesController not found"
end

# Test that views use the avatar component
profile_show_path = "app/views/profiles/show.html.erb"
if File.exist?(profile_show_path)
  show_content = File.read(profile_show_path)
  if show_content.include?("render 'shared/user_avatar'")
    puts "✅ Profile show view uses avatar component"
  else
    puts "❌ Profile show view not using avatar component"
  end
else
  puts "❌ Profile show view not found"
end

profile_edit_path = "app/views/profiles/edit.html.erb"
if File.exist?(profile_edit_path)
  edit_content = File.read(profile_edit_path)
  if edit_content.include?("render 'shared/user_avatar'")
    puts "✅ Profile edit view uses avatar component"
  else
    puts "❌ Profile edit view not using avatar component"
  end
  
  if edit_content.include?("file_field :avatar")
    puts "✅ Profile edit view has avatar upload field"
  else
    puts "❌ Profile edit view missing avatar upload field"
  end
else
  puts "❌ Profile edit view not found"
end

navbar_path = "app/views/shared/_navbar.html.erb"
if File.exist?(navbar_path)
  navbar_content = File.read(navbar_path)
  if navbar_content.include?("render 'shared/user_avatar'")
    puts "✅ Navbar uses avatar component"
  else
    puts "❌ Navbar not using avatar component"
  end
else
  puts "❌ Navbar not found"
end

puts "\nAvatar functionality test completed!"
puts "The avatar system should now work with:"
puts "- Fallback to user initials when no avatar is uploaded"
puts "- Proper image display when avatar is uploaded"
puts "- File upload validation (type and size)"
puts "- Consistent avatar display across the application"
puts "- Responsive sizing (xs, sm, md, lg, xl, 2xl)"
