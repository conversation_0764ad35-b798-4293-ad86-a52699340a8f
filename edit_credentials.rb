#!/usr/bin/env ruby
# Script to help edit Rails credentials

require 'fileutils'
require 'tempfile'

puts "🔑 Rails Credentials Editor Helper"
puts "=" * 40

# Check if we're in a Rails app
unless File.exist?('config/credentials.yml.enc') && File.exist?('config/master.key')
  puts "❌ Error: Not in a Rails app directory or credentials not found"
  puts "Make sure you're in the Rails app root directory"
  exit 1
end

begin
  # Load Rails environment
  require_relative 'config/environment'

  puts "✅ Rails environment loaded"

  # Read current credentials
  current_content = Rails.application.credentials.config.to_yaml
  puts "\n📋 Current credentials content:"
  puts "-" * 30
  puts current_content
  puts "-" * 30

  puts "\n🎯 To edit credentials manually, you can:"
  puts "1. Run: EDITOR='code --wait' bundle exec rails credentials:edit"
  puts "2. Or run: EDITOR='nano' bundle exec rails credentials:edit"
  puts "3. Or use this script to update specific keys"

  puts "\n🔧 Would you like to update API keys now? (y/n)"
  response = gets.chomp.downcase

  if response == 'y' || response == 'yes'
    puts "\n🚀 Let's update your API keys!"

    # Get API keys from user
    api_keys = {}

    puts "\nEnter your API keys (press Enter to skip):"

    print "OpenAI API Key (sk-...): "
    openai_key = gets.chomp
    api_keys[:openai_api_key] = openai_key unless openai_key.empty?

    print "Anthropic API Key (sk-ant-...): "
    anthropic_key = gets.chomp
    api_keys[:anthropic_api_key] = anthropic_key unless anthropic_key.empty?

    print "Gemini API Key (AIza...): "
    gemini_key = gets.chomp
    api_keys[:gemini_api_key] = gemini_key unless gemini_key.empty?

    print "DeepSeek API Key: "
    deepseek_key = gets.chomp
    api_keys[:deepseek_api_key] = deepseek_key unless deepseek_key.empty?

    print "OpenRouter API Key: "
    openrouter_key = gets.chomp
    api_keys[:openrouter_api_key] = openrouter_key unless openrouter_key.empty?

    if api_keys.any?
      puts "\n💾 Updating credentials..."

      # Create new credentials content
      new_content = current_content.dup

      api_keys.each do |key, value|
        # Replace the line if it exists, or add it
        if new_content.include?("#{key}:")
          new_content.gsub!(/#{key}:.*/, "#{key}: #{value}")
        else
          new_content += "\n#{key}: #{value}"
        end
      end

      puts "\n📝 New credentials content:"
      puts "-" * 30
      puts new_content
      puts "-" * 30

      puts "\n⚠️  To apply these changes, you need to manually edit the credentials file:"
      puts "Run: EDITOR='nano' bundle exec rails credentials:edit"
      puts "Then copy and paste the content above."

    else
      puts "\n➡️  No API keys provided. Exiting."
    end
  end

  puts "\n✅ Done!"

rescue => e
  puts "❌ Error: #{e.message}"
  puts "\nTry running this from the Rails app directory:"
  puts "cd /Users/<USER>/Developer/ai_marketing_hub"
  puts "ruby edit_credentials.rb"
end
