# frozen_string_literal: true

# AI Providers Configuration Validator
# This initializer validates AI provider API key configuration on application startup

Rails.application.configure do
  config.after_initialize do
    # Only validate in development and production, skip in test
    next if Rails.env.test?

    begin
      # Define all supported AI providers
      providers = {
        openai: {
          name: "OpenAI",
          credentials_key: :openai_api_key,
          env_key: "OPENAI_API_KEY",
          url: "https://platform.openai.com/api-keys"
        },
        anthropic: {
          name: "Anthropic Claude",
          credentials_key: :anthropic_api_key,
          env_key: "ANTHROPIC_API_KEY",
          url: "https://console.anthropic.com/"
        },
        gemini: {
          name: "Google Gemini",
          credentials_key: :gemini_api_key,
          env_key: "GEMINI_API_KEY",
          url: "https://makersuite.google.com/app/apikey"
        },
        deepseek: {
          name: "DeepSeek",
          credentials_key: :deepseek_api_key,
          env_key: "DEEPSEEK_API_KEY",
          url: "https://platform.deepseek.com/api_keys"
        },
        openrouter: {
          name: "OpenRouter",
          credentials_key: :openrouter_api_key,
          env_key: "OPENROUTER_API_KEY",
          url: "https://openrouter.ai/keys"
        }
      }

      configured_providers = []
      placeholder_providers = []

      providers.each do |provider_key, config|
        # Check credentials first, then environment variable
        api_key = Rails.application.credentials.send(config[:credentials_key]) || ENV[config[:env_key]]

        if api_key.present?
          if api_key.include?("placeholder") || api_key.include?("your_") || api_key.include?("here")
            placeholder_providers << config[:name]
          else
            configured_providers << config[:name]
          end
        end
      end

      if configured_providers.any?
        Rails.logger.info "✅ AI Providers configured: #{configured_providers.join(', ')}"
        Rails.logger.info "🤖 Multi-provider AI system ready!"
      else
        Rails.logger.warn "⚠️  No AI providers configured. AI features will not work."
        Rails.logger.warn "   Configure at least one provider in Rails credentials or environment variables."
        Rails.logger.warn "   See OPENAI_SETUP.md for detailed setup instructions."
      end

      if placeholder_providers.any?
        Rails.logger.warn "⚠️  Placeholder API keys detected for: #{placeholder_providers.join(', ')}"
        Rails.logger.warn "   Replace with real API keys for these providers to work."
      end

      # Log provider strategy information
      if configured_providers.any?
        Rails.logger.info "💡 Provider selection strategies available:"
        Rails.logger.info "   • Creative content: #{RubyLLMExtensions::PROVIDER_STRATEGIES[:creative_content].join(', ')}"
        Rails.logger.info "   • Data analysis: #{RubyLLMExtensions::PROVIDER_STRATEGIES[:data_analysis].join(', ')}"
        Rails.logger.info "   • Cost sensitive: #{RubyLLMExtensions::PROVIDER_STRATEGIES[:cost_sensitive].join(', ')}"
      end

    rescue => e
      Rails.logger.error "❌ Error validating AI provider configuration: #{e.message}"
    end
  end
end
