# frozen_string_literal: true

# Flipper Configuration for Feature Flags
# Using database backend instead of Redis for better Rails integration

require 'flipper'
require 'flipper/adapters/active_record'

# Configure Flipper to use database backend
Flipper.configure do |config|
  config.adapter { Flipper::Adapters::ActiveRecord.new }
end

# Initialize default features if needed
Rails.application.config.after_initialize do
  # Add any default feature flags here
  # Example:
  # Flipper.enable(:new_dashboard_ui) if Rails.env.development?
end
