# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source <PERSON>s uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[8.0].define(version: 2025_06_07_100001) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "pg_catalog.plpgsql"

  create_table "agent_workflows", force: :cascade do |t|
    t.bigint "campaign_id", null: false
    t.integer "workflow_type", default: 0, null: false
    t.integer "status", default: 0, null: false
    t.integer "progress_percent", default: 0, null: false
    t.string "current_step"
    t.integer "total_steps", default: 0
    t.jsonb "context_data", default: {}, null: false
    t.jsonb "results", default: {}, null: false
    t.jsonb "error_details", default: {}, null: false
    t.datetime "started_at"
    t.datetime "completed_at"
    t.bigint "tenant_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["campaign_id"], name: "index_agent_workflows_on_campaign_id"
    t.index ["context_data"], name: "index_agent_workflows_on_context_data", using: :gin
    t.index ["results"], name: "index_agent_workflows_on_results", using: :gin
    t.index ["status"], name: "index_agent_workflows_on_status"
    t.index ["tenant_id"], name: "index_agent_workflows_on_tenant_id"
    t.index ["workflow_type"], name: "index_agent_workflows_on_workflow_type"
  end

  create_table "ai_usage_records", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.string "model"
    t.string "task_type"
    t.integer "input_tokens"
    t.integer "output_tokens"
    t.integer "total_tokens"
    t.float "duration_ms"
    t.decimal "cost"
    t.datetime "request_timestamp"
    t.jsonb "metadata"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["tenant_id"], name: "index_ai_usage_records_on_tenant_id"
  end

  create_table "alert_logs", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.string "alert_type", null: false
    t.string "severity", null: false
    t.text "message", null: false
    t.jsonb "metadata", default: {}, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["alert_type"], name: "index_alert_logs_on_alert_type"
    t.index ["created_at"], name: "index_alert_logs_on_created_at"
    t.index ["metadata"], name: "index_alert_logs_on_metadata", using: :gin
    t.index ["severity"], name: "index_alert_logs_on_severity"
    t.index ["tenant_id", "alert_type"], name: "index_alert_logs_on_tenant_and_alert_type"
    t.index ["tenant_id", "created_at"], name: "index_alert_logs_on_tenant_and_created_at"
    t.index ["tenant_id", "severity", "created_at"], name: "index_alert_logs_dashboard"
    t.index ["tenant_id", "severity"], name: "index_alert_logs_on_tenant_and_severity"
    t.index ["tenant_id"], name: "index_alert_logs_on_tenant_id"
  end

  create_table "audience_insights", force: :cascade do |t|
    t.bigint "audience_id", null: false
    t.string "insight_type", null: false
    t.jsonb "data", default: {}, null: false
    t.decimal "confidence", precision: 5, scale: 2
    t.string "source"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["audience_id", "insight_type"], name: "index_audience_insights_on_audience_id_and_insight_type"
    t.index ["audience_id"], name: "index_audience_insights_on_audience_id"
    t.index ["confidence"], name: "index_audience_insights_on_confidence"
    t.index ["data"], name: "index_audience_insights_on_data", using: :gin
    t.index ["insight_type"], name: "index_audience_insights_on_insight_type"
  end

  create_table "audience_segments", force: :cascade do |t|
    t.bigint "audience_id", null: false
    t.string "name", null: false
    t.string "segment_type", null: false
    t.jsonb "criteria", default: {}, null: false
    t.text "description"
    t.integer "size"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["audience_id", "name"], name: "index_audience_segments_on_audience_id_and_name", unique: true
    t.index ["audience_id"], name: "index_audience_segments_on_audience_id"
    t.index ["criteria"], name: "index_audience_segments_on_criteria", using: :gin
    t.index ["segment_type"], name: "index_audience_segments_on_segment_type"
    t.index ["size"], name: "index_audience_segments_on_size"
  end

  create_table "audiences", force: :cascade do |t|
    t.string "name"
    t.text "description"
    t.text "target_demographics"
    t.string "cultural_context"
    t.string "primary_language"
    t.string "secondary_languages"
    t.string "geographic_regions"
    t.integer "age_range_min"
    t.integer "age_range_max"
    t.text "interests"
    t.text "behavioral_traits"
    t.text "communication_preferences"
    t.text "cultural_values"
    t.text "engagement_patterns"
    t.decimal "cultural_alignment_score"
    t.jsonb "demographics"
    t.jsonb "cultural_attributes"
    t.jsonb "preferences"
    t.bigint "tenant_id", null: false
    t.bigint "created_by_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["created_by_id"], name: "index_audiences_on_created_by_id"
    t.index ["tenant_id"], name: "index_audiences_on_tenant_id"
  end

  create_table "authenticity_checks", force: :cascade do |t|
    t.bigint "campaign_id", null: false
    t.bigint "tenant_id", null: false
    t.string "check_type", null: false
    t.decimal "authenticity_score", precision: 5, scale: 2, null: false
    t.jsonb "findings", default: {}, null: false
    t.jsonb "recommendations", default: [], null: false
    t.string "status", default: "pending"
    t.text "reviewer_notes"
    t.bigint "reviewed_by_id"
    t.datetime "reviewed_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["authenticity_score"], name: "index_authenticity_checks_on_authenticity_score"
    t.index ["campaign_id", "check_type"], name: "index_authenticity_checks_on_campaign_id_and_check_type"
    t.index ["campaign_id"], name: "index_authenticity_checks_on_campaign_id"
    t.index ["findings"], name: "index_authenticity_checks_on_findings", using: :gin
    t.index ["reviewed_by_id"], name: "index_authenticity_checks_on_reviewed_by_id"
    t.index ["status"], name: "index_authenticity_checks_on_status"
    t.index ["tenant_id"], name: "index_authenticity_checks_on_tenant_id"
  end

  create_table "campaign_audiences", force: :cascade do |t|
    t.bigint "campaign_id", null: false
    t.bigint "audience_id", null: false
    t.bigint "tenant_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["audience_id"], name: "index_campaign_audiences_on_audience_id"
    t.index ["campaign_id", "audience_id"], name: "index_campaign_audiences_on_campaign_id_and_audience_id", unique: true
    t.index ["campaign_id"], name: "index_campaign_audiences_on_campaign_id"
    t.index ["tenant_id"], name: "index_campaign_audiences_on_tenant_id"
  end

  create_table "campaign_collection_items", force: :cascade do |t|
    t.bigint "campaign_collection_id", null: false
    t.bigint "campaign_id", null: false
    t.bigint "tenant_id", null: false
    t.bigint "added_by_id", null: false
    t.integer "sequence_order", null: false
    t.text "notes"
    t.text "execution_conditions"
    t.text "success_criteria"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["added_by_id"], name: "idx_campaign_collection_items_added_by"
    t.index ["added_by_id"], name: "index_campaign_collection_items_on_added_by_id"
    t.index ["campaign_collection_id", "campaign_id"], name: "idx_on_campaign_collection_id_campaign_id_985c92a9c4", unique: true
    t.index ["campaign_collection_id", "sequence_order"], name: "idx_on_campaign_collection_id_sequence_order_6890334913", unique: true
    t.index ["campaign_collection_id"], name: "index_campaign_collection_items_on_campaign_collection_id"
    t.index ["campaign_id"], name: "idx_campaign_collection_items_campaign"
    t.index ["campaign_id"], name: "index_campaign_collection_items_on_campaign_id"
    t.index ["tenant_id"], name: "idx_campaign_collection_items_tenant"
    t.index ["tenant_id"], name: "index_campaign_collection_items_on_tenant_id"
  end

  create_table "campaign_collections", force: :cascade do |t|
    t.string "name", limit: 100, null: false
    t.text "description"
    t.integer "status", default: 0, null: false
    t.integer "collection_type", default: 1, null: false
    t.bigint "tenant_id", null: false
    t.bigint "created_by_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "vibe_strategy", default: {}, null: false
    t.string "target_emotion"
    t.string "cultural_moment"
    t.index ["created_by_id"], name: "idx_campaign_collections_created_by"
    t.index ["created_by_id"], name: "index_campaign_collections_on_created_by_id"
    t.index ["target_emotion"], name: "index_campaign_collections_on_target_emotion"
    t.index ["tenant_id", "collection_type"], name: "index_campaign_collections_on_tenant_id_and_collection_type"
    t.index ["tenant_id", "name"], name: "index_campaign_collections_on_tenant_id_and_name", unique: true
    t.index ["tenant_id", "status"], name: "index_campaign_collections_on_tenant_id_and_status"
    t.index ["tenant_id"], name: "index_campaign_collections_on_tenant_id"
    t.index ["vibe_strategy"], name: "index_campaign_collections_on_vibe_strategy", using: :gin
  end

  create_table "campaign_libraries", force: :cascade do |t|
    t.string "name", limit: 100, null: false
    t.text "description"
    t.integer "visibility", default: 0, null: false
    t.integer "library_type", default: 0, null: false
    t.bigint "tenant_id", null: false
    t.bigint "created_by_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["created_by_id"], name: "idx_campaign_libraries_created_by"
    t.index ["created_by_id"], name: "index_campaign_libraries_on_created_by_id"
    t.index ["tenant_id", "library_type"], name: "index_campaign_libraries_on_tenant_id_and_library_type"
    t.index ["tenant_id", "name"], name: "index_campaign_libraries_on_tenant_id_and_name", unique: true
    t.index ["tenant_id", "visibility"], name: "index_campaign_libraries_on_tenant_id_and_visibility"
    t.index ["tenant_id"], name: "index_campaign_libraries_on_tenant_id"
  end

  create_table "campaign_library_items", force: :cascade do |t|
    t.bigint "campaign_library_id", null: false
    t.bigint "campaign_id", null: false
    t.bigint "tenant_id", null: false
    t.bigint "added_by_id", null: false
    t.text "notes"
    t.text "tags"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["added_by_id"], name: "idx_campaign_library_items_added_by"
    t.index ["added_by_id"], name: "index_campaign_library_items_on_added_by_id"
    t.index ["campaign_id"], name: "idx_campaign_library_items_campaign"
    t.index ["campaign_id"], name: "index_campaign_library_items_on_campaign_id"
    t.index ["campaign_library_id", "campaign_id"], name: "idx_on_campaign_library_id_campaign_id_b5088ec565", unique: true
    t.index ["campaign_library_id"], name: "index_campaign_library_items_on_campaign_library_id"
    t.index ["tenant_id"], name: "idx_campaign_library_items_tenant"
    t.index ["tenant_id"], name: "index_campaign_library_items_on_tenant_id"
  end

  create_table "campaign_metrics", force: :cascade do |t|
    t.bigint "campaign_id", null: false
    t.date "metric_date", null: false
    t.integer "impressions", default: 0
    t.integer "clicks", default: 0
    t.integer "conversions", default: 0
    t.integer "revenue_cents", default: 0
    t.integer "cost_cents", default: 0
    t.integer "email_opens", default: 0
    t.integer "email_clicks", default: 0
    t.integer "email_bounces", default: 0
    t.integer "social_engagements", default: 0
    t.integer "social_shares", default: 0
    t.integer "social_comments", default: 0
    t.integer "seo_organic_traffic", default: 0
    t.jsonb "seo_keyword_rankings", default: {}, null: false
    t.jsonb "custom_metrics", default: {}, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["campaign_id", "metric_date", "impressions"], name: "index_campaign_metrics_performance"
    t.index ["campaign_id", "metric_date"], name: "index_campaign_metrics_on_campaign_id_and_metric_date", unique: true
    t.index ["campaign_id"], name: "index_campaign_metrics_on_campaign_id"
    t.index ["custom_metrics"], name: "index_campaign_metrics_on_custom_metrics", using: :gin
    t.index ["metric_date", "campaign_id"], name: "index_campaign_metrics_date_campaign"
    t.index ["metric_date"], name: "index_campaign_metrics_on_metric_date"
    t.index ["seo_keyword_rankings"], name: "index_campaign_metrics_on_seo_keyword_rankings", using: :gin
  end

  create_table "campaigns", force: :cascade do |t|
    t.string "name", null: false
    t.text "description"
    t.integer "campaign_type", default: 0, null: false
    t.integer "status", default: 0, null: false
    t.string "target_audience", null: false
    t.date "start_date"
    t.date "end_date"
    t.integer "budget_cents", default: 0
    t.jsonb "settings", default: {}, null: false
    t.bigint "tenant_id", null: false
    t.bigint "created_by_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "vibe_data", default: {}, null: false
    t.string "emotional_tone"
    t.decimal "cultural_relevance_score", precision: 5, scale: 2
    t.decimal "authenticity_score", precision: 5, scale: 2
    t.string "vibe_status", default: "pending"
    t.index "to_tsvector('english'::regconfig, (((((COALESCE(name, ''::character varying))::text || ' '::text) || COALESCE(description, ''::text)) || ' '::text) || (COALESCE(target_audience, ''::character varying))::text))", name: "index_campaigns_on_search_text", using: :gin
    t.index ["campaign_type"], name: "index_campaigns_on_campaign_type"
    t.index ["created_by_id"], name: "index_campaigns_on_created_by_id"
    t.index ["emotional_tone"], name: "index_campaigns_on_emotional_tone"
    t.index ["name", "tenant_id"], name: "index_campaigns_on_name_and_tenant_id", unique: true
    t.index ["settings"], name: "index_campaigns_on_settings", using: :gin
    t.index ["start_date"], name: "index_campaigns_on_start_date"
    t.index ["status", "start_date"], name: "index_campaigns_on_status_and_start_date"
    t.index ["status"], name: "index_campaigns_on_status"
    t.index ["tenant_id", "campaign_type"], name: "index_campaigns_on_tenant_and_type"
    t.index ["tenant_id", "created_at"], name: "index_campaigns_on_tenant_and_created_at"
    t.index ["tenant_id", "status", "campaign_type"], name: "index_campaigns_on_tenant_status_type"
    t.index ["tenant_id", "status"], name: "index_campaigns_on_tenant_and_status"
    t.index ["tenant_id"], name: "index_campaigns_on_tenant_id"
    t.index ["vibe_data"], name: "index_campaigns_on_vibe_data", using: :gin
    t.index ["vibe_status"], name: "index_campaigns_on_vibe_status"
  end

  create_table "cultural_moments", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.string "title", null: false
    t.text "description"
    t.string "moment_type", null: false
    t.string "category"
    t.date "start_date"
    t.date "end_date"
    t.integer "relevance_score", default: 0
    t.jsonb "target_demographics", default: {}, null: false
    t.jsonb "engagement_metrics", default: {}, null: false
    t.jsonb "keywords", default: [], null: false
    t.string "status", default: "active"
    t.bigint "created_by_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["created_by_id"], name: "index_cultural_moments_on_created_by_id"
    t.index ["end_date"], name: "index_cultural_moments_on_end_date"
    t.index ["keywords"], name: "index_cultural_moments_on_keywords", using: :gin
    t.index ["relevance_score"], name: "index_cultural_moments_on_relevance_score"
    t.index ["start_date"], name: "index_cultural_moments_on_start_date"
    t.index ["target_demographics"], name: "index_cultural_moments_on_target_demographics", using: :gin
    t.index ["tenant_id", "moment_type"], name: "index_cultural_moments_on_tenant_id_and_moment_type"
    t.index ["tenant_id", "status"], name: "index_cultural_moments_on_tenant_id_and_status"
    t.index ["tenant_id"], name: "index_cultural_moments_on_tenant_id"
  end

  create_table "customer_emotional_states", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.string "customer_identifier", null: false
    t.string "current_emotion", null: false
    t.string "emotional_intensity", default: "moderate"
    t.decimal "confidence_score", precision: 5, scale: 2
    t.jsonb "context_data", default: {}, null: false
    t.jsonb "behavioral_signals", default: {}, null: false
    t.jsonb "interaction_history", default: [], null: false
    t.datetime "last_interaction_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["behavioral_signals"], name: "index_customer_emotional_states_on_behavioral_signals", using: :gin
    t.index ["confidence_score"], name: "index_customer_emotional_states_on_confidence_score"
    t.index ["context_data"], name: "index_customer_emotional_states_on_context_data", using: :gin
    t.index ["current_emotion", "confidence_score"], name: "index_customer_emotional_states_emotion_confidence"
    t.index ["current_emotion"], name: "index_customer_emotional_states_on_current_emotion"
    t.index ["customer_identifier"], name: "index_customer_emotional_states_on_customer_identifier"
    t.index ["emotional_intensity"], name: "index_customer_emotional_states_on_emotional_intensity"
    t.index ["interaction_history"], name: "index_customer_emotional_states_on_interaction_history", using: :gin
    t.index ["last_interaction_at"], name: "index_customer_emotional_states_on_last_interaction_at"
    t.index ["tenant_id", "current_emotion", "confidence_score"], name: "index_customer_emotional_states_tenant_emotion_conf"
    t.index ["tenant_id", "customer_identifier"], name: "index_customer_emotional_states_unique", unique: true
    t.index ["tenant_id", "last_interaction_at"], name: "index_customer_emotional_states_tenant_interaction"
    t.index ["tenant_id"], name: "index_customer_emotional_states_on_tenant_id"
  end

  create_table "email_campaigns", force: :cascade do |t|
    t.bigint "campaign_id", null: false
    t.string "subject_line", limit: 150, null: false
    t.string "preview_text", limit: 200
    t.text "content", null: false
    t.string "from_name", null: false
    t.string "from_email", null: false
    t.jsonb "settings", default: {}, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["campaign_id", "from_email"], name: "index_email_campaigns_campaign_from"
    t.index ["campaign_id"], name: "index_email_campaigns_on_campaign_id", unique: true
    t.index ["from_email"], name: "index_email_campaigns_on_from_email"
    t.index ["settings"], name: "index_email_campaigns_on_settings", using: :gin
  end

  create_table "emotional_resonance_profiles", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.string "name", null: false
    t.text "description"
    t.string "primary_emotion", null: false
    t.jsonb "emotion_weights", default: {}, null: false
    t.jsonb "tone_preferences", default: {}, null: false
    t.jsonb "content_guidelines", default: {}, null: false
    t.jsonb "avoided_triggers", default: [], null: false
    t.string "target_age_range"
    t.jsonb "psychographic_traits", default: {}, null: false
    t.bigint "created_by_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["created_by_id"], name: "index_emotional_resonance_profiles_on_created_by_id"
    t.index ["emotion_weights"], name: "index_emotional_resonance_profiles_on_emotion_weights", using: :gin
    t.index ["primary_emotion"], name: "index_emotional_resonance_profiles_on_primary_emotion"
    t.index ["psychographic_traits"], name: "index_emotional_resonance_profiles_on_psychographic_traits", using: :gin
    t.index ["tenant_id", "name"], name: "index_emotional_resonance_profiles_on_tenant_id_and_name", unique: true
    t.index ["tenant_id"], name: "index_emotional_resonance_profiles_on_tenant_id"
  end

  create_table "flipper_features", force: :cascade do |t|
    t.string "key", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["key"], name: "index_flipper_features_on_key", unique: true
  end

  create_table "flipper_gates", force: :cascade do |t|
    t.string "feature_key", null: false
    t.string "key", null: false
    t.text "value"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["feature_key", "key", "value"], name: "index_flipper_gates_on_feature_key_and_key_and_value", unique: true
  end

  create_table "oauth_tokens", force: :cascade do |t|
    t.bigint "platform_configuration_id", null: false
    t.string "access_token", null: false
    t.string "refresh_token"
    t.datetime "expires_at"
    t.string "token_type", default: "Bearer"
    t.text "scope"
    t.boolean "is_active", default: true, null: false
    t.bigint "tenant_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["expires_at"], name: "index_oauth_tokens_on_expires_at"
    t.index ["is_active"], name: "index_oauth_tokens_on_is_active"
    t.index ["platform_configuration_id"], name: "index_oauth_tokens_on_platform_configuration_id"
    t.index ["tenant_id"], name: "index_oauth_tokens_on_tenant_id"
  end

  create_table "platform_configurations", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.string "platform_name", null: false
    t.boolean "is_active", default: false, null: false
    t.string "connected_account_id"
    t.string "connected_account_name"
    t.datetime "last_sync_at"
    t.boolean "auto_sync_enabled", default: false, null: false
    t.boolean "posting_enabled", default: false, null: false
    t.boolean "analytics_enabled", default: false, null: false
    t.bigint "tenant_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["is_active"], name: "index_platform_configurations_on_is_active"
    t.index ["platform_name"], name: "index_platform_configurations_on_platform_name"
    t.index ["tenant_id"], name: "index_platform_configurations_on_tenant_id"
    t.index ["user_id", "platform_name"], name: "index_platform_configurations_on_user_id_and_platform_name", unique: true
    t.index ["user_id"], name: "index_platform_configurations_on_user_id"
  end

  create_table "seo_campaigns", force: :cascade do |t|
    t.bigint "campaign_id", null: false
    t.text "target_keywords"
    t.string "meta_title", limit: 60
    t.text "meta_description"
    t.jsonb "content_strategy", default: {}, null: false
    t.jsonb "technical_settings", default: {}, null: false
    t.jsonb "seo_settings", default: {}, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["campaign_id"], name: "index_seo_campaigns_on_campaign_id", unique: true
    t.index ["content_strategy"], name: "index_seo_campaigns_on_content_strategy", using: :gin
    t.index ["seo_settings"], name: "index_seo_campaigns_on_seo_settings", using: :gin
  end

  create_table "social_campaigns", force: :cascade do |t|
    t.bigint "campaign_id", null: false
    t.jsonb "platforms", default: [], null: false
    t.jsonb "content_variants", default: {}, null: false
    t.string "hashtags", default: ""
    t.jsonb "target_demographics", default: {}, null: false
    t.jsonb "post_schedule", default: {}, null: false
    t.jsonb "social_settings", default: {}, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["campaign_id"], name: "index_social_campaigns_on_campaign_id", unique: true
    t.index ["campaign_id"], name: "index_social_campaigns_with_platforms", where: "(jsonb_array_length(platforms) > 0)"
    t.index ["content_variants"], name: "index_social_campaigns_on_content_variants", using: :gin
    t.index ["platforms"], name: "index_social_campaigns_on_platforms", using: :gin
    t.index ["post_schedule"], name: "index_social_campaigns_on_post_schedule", using: :gin
    t.index ["social_settings"], name: "index_social_campaigns_on_social_settings", using: :gin
    t.index ["target_demographics"], name: "index_social_campaigns_on_target_demographics", using: :gin
  end

  create_table "tenants", force: :cascade do |t|
    t.string "name", null: false
    t.string "subdomain", null: false
    t.string "status", default: "active", null: false
    t.jsonb "settings", default: {}, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["name"], name: "index_tenants_on_name", unique: true
    t.index ["settings"], name: "index_tenants_on_settings", using: :gin
    t.index ["status"], name: "index_tenants_on_status"
    t.index ["subdomain"], name: "index_tenants_on_subdomain", unique: true
  end

  create_table "users", force: :cascade do |t|
    t.string "email", default: "", null: false
    t.string "encrypted_password", default: "", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at"
    t.datetime "remember_created_at"
    t.string "confirmation_token"
    t.datetime "confirmation_sent_at"
    t.datetime "confirmed_at"
    t.string "unconfirmed_email"
    t.bigint "tenant_id", null: false
    t.string "first_name", null: false
    t.string "last_name", null: false
    t.integer "role", default: 0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["confirmation_token"], name: "index_users_on_confirmation_token", unique: true
    t.index ["email", "tenant_id"], name: "index_users_on_email_and_tenant_id", unique: true
    t.index ["reset_password_token"], name: "index_users_on_reset_password_token", unique: true
    t.index ["role"], name: "index_users_on_role"
    t.index ["tenant_id", "created_at"], name: "index_users_on_tenant_and_created_at"
    t.index ["tenant_id", "role"], name: "index_users_on_tenant_and_role"
    t.index ["tenant_id"], name: "index_users_on_tenant_id"
  end

  create_table "vibe_analysis_records", force: :cascade do |t|
    t.bigint "campaign_id", null: false
    t.bigint "tenant_id", null: false
    t.string "analysis_type", null: false
    t.jsonb "analysis_data", default: {}, null: false
    t.decimal "confidence_score", precision: 5, scale: 2
    t.text "ai_reasoning"
    t.string "model_used"
    t.jsonb "metadata", default: {}, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["analysis_data"], name: "index_vibe_analysis_records_on_analysis_data", using: :gin
    t.index ["analysis_type"], name: "index_vibe_analysis_records_on_analysis_type"
    t.index ["campaign_id", "analysis_type"], name: "index_vibe_analysis_records_on_campaign_id_and_analysis_type", unique: true
    t.index ["campaign_id"], name: "index_vibe_analysis_records_on_campaign_id"
    t.index ["confidence_score"], name: "index_vibe_analysis_records_on_confidence_score"
    t.index ["tenant_id"], name: "index_vibe_analysis_records_on_tenant_id"
  end

  create_table "vibe_campaign_orchestrations", force: :cascade do |t|
    t.bigint "campaign_collection_id", null: false
    t.bigint "tenant_id", null: false
    t.string "orchestration_type", null: false
    t.jsonb "workflow_config", default: {}, null: false
    t.jsonb "current_state", default: {}, null: false
    t.integer "progress_percentage", default: 0
    t.string "status", default: "pending"
    t.datetime "started_at"
    t.datetime "completed_at"
    t.jsonb "results", default: {}, null: false
    t.bigint "created_by_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["campaign_collection_id", "orchestration_type"], name: "idx_on_campaign_collection_id_orchestration_type_f7a3fd8e1c", unique: true
    t.index ["campaign_collection_id"], name: "index_vibe_campaign_orchestrations_on_campaign_collection_id"
    t.index ["created_by_id"], name: "index_vibe_campaign_orchestrations_on_created_by_id"
    t.index ["current_state"], name: "index_vibe_campaign_orchestrations_on_current_state", using: :gin
    t.index ["orchestration_type"], name: "index_vibe_campaign_orchestrations_on_orchestration_type"
    t.index ["progress_percentage"], name: "index_vibe_campaign_orchestrations_on_progress_percentage"
    t.index ["status"], name: "index_vibe_campaign_orchestrations_on_status"
    t.index ["tenant_id"], name: "index_vibe_campaign_orchestrations_on_tenant_id"
    t.index ["workflow_config"], name: "index_vibe_campaign_orchestrations_on_workflow_config", using: :gin
  end

  add_foreign_key "agent_workflows", "campaigns"
  add_foreign_key "agent_workflows", "tenants"
  add_foreign_key "ai_usage_records", "tenants"
  add_foreign_key "alert_logs", "tenants"
  add_foreign_key "audience_insights", "audiences"
  add_foreign_key "audience_segments", "audiences"
  add_foreign_key "audiences", "tenants"
  add_foreign_key "audiences", "users", column: "created_by_id"
  add_foreign_key "authenticity_checks", "campaigns"
  add_foreign_key "authenticity_checks", "tenants"
  add_foreign_key "authenticity_checks", "users", column: "reviewed_by_id"
  add_foreign_key "campaign_audiences", "audiences"
  add_foreign_key "campaign_audiences", "campaigns"
  add_foreign_key "campaign_audiences", "tenants"
  add_foreign_key "campaign_collection_items", "campaign_collections"
  add_foreign_key "campaign_collection_items", "campaigns"
  add_foreign_key "campaign_collection_items", "tenants"
  add_foreign_key "campaign_collection_items", "users", column: "added_by_id"
  add_foreign_key "campaign_collections", "tenants"
  add_foreign_key "campaign_collections", "users", column: "created_by_id"
  add_foreign_key "campaign_libraries", "tenants"
  add_foreign_key "campaign_libraries", "users", column: "created_by_id"
  add_foreign_key "campaign_library_items", "campaign_libraries"
  add_foreign_key "campaign_library_items", "campaigns"
  add_foreign_key "campaign_library_items", "tenants"
  add_foreign_key "campaign_library_items", "users", column: "added_by_id"
  add_foreign_key "campaign_metrics", "campaigns"
  add_foreign_key "campaigns", "tenants"
  add_foreign_key "campaigns", "users", column: "created_by_id"
  add_foreign_key "cultural_moments", "tenants"
  add_foreign_key "cultural_moments", "users", column: "created_by_id"
  add_foreign_key "customer_emotional_states", "tenants"
  add_foreign_key "email_campaigns", "campaigns"
  add_foreign_key "emotional_resonance_profiles", "tenants"
  add_foreign_key "emotional_resonance_profiles", "users", column: "created_by_id"
  add_foreign_key "oauth_tokens", "platform_configurations"
  add_foreign_key "oauth_tokens", "tenants"
  add_foreign_key "platform_configurations", "tenants"
  add_foreign_key "platform_configurations", "users"
  add_foreign_key "seo_campaigns", "campaigns"
  add_foreign_key "social_campaigns", "campaigns"
  add_foreign_key "users", "tenants"
  add_foreign_key "vibe_analysis_records", "campaigns"
  add_foreign_key "vibe_analysis_records", "tenants"
  add_foreign_key "vibe_campaign_orchestrations", "campaign_collections"
  add_foreign_key "vibe_campaign_orchestrations", "tenants"
  add_foreign_key "vibe_campaign_orchestrations", "users", column: "created_by_id"
end
