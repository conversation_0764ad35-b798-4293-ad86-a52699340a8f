class CreateCampaignLibraryItems < ActiveRecord::Migration[8.0]
  def change
    create_table :campaign_library_items do |t|
      t.references :campaign_library, null: false, foreign_key: true
      t.references :campaign, null: false, foreign_key: true
      t.references :tenant, null: false, foreign_key: true
      t.references :added_by, null: false, foreign_key: { to_table: :users }
      t.text :notes
      t.text :tags

      t.timestamps
    end

    # Add unique constraint to prevent duplicate campaigns in same library
    add_index :campaign_library_items, [ :campaign_library_id, :campaign_id ],
              unique: true, name: 'idx_on_campaign_library_id_campaign_id_b5088ec565'

    # Add additional indexes for performance (references already creates basic indexes)
    add_index :campaign_library_items, :campaign_id, name: 'idx_campaign_library_items_campaign'
    add_index :campaign_library_items, :tenant_id, name: 'idx_campaign_library_items_tenant'
    add_index :campaign_library_items, :added_by_id, name: 'idx_campaign_library_items_added_by'
  end
end
