class CreateSocialCampaigns < ActiveRecord::Migration[8.0]
  def change
    create_table :social_campaigns do |t|
      t.references :campaign, null: false, foreign_key: true, index: { unique: true }
      t.jsonb :platforms, null: false, default: []
      t.jsonb :content_variants, null: false, default: {}
      t.string :hashtags, default: ''
      t.jsonb :target_demographics, null: false, default: {}
      t.jsonb :post_schedule, null: false, default: {}
      t.jsonb :social_settings, null: false, default: {}

      t.timestamps
    end

    # Indexes for performance
    add_index :social_campaigns, :platforms, using: :gin
    add_index :social_campaigns, :content_variants, using: :gin
    add_index :social_campaigns, :target_demographics, using: :gin
    add_index :social_campaigns, :post_schedule, using: :gin
    add_index :social_campaigns, :social_settings, using: :gin
  end
end
