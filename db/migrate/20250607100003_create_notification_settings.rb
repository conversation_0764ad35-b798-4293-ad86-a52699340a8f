class CreateNotificationSettings < ActiveRecord::Migration[8.0]
  def change
    create_table :notification_settings do |t|
      t.references :user, null: false, foreign_key: true
      t.references :tenant, null: false, foreign_key: true
      t.boolean :email_campaigns, default: true
      t.boolean :email_system, default: true
      t.boolean :email_marketing, default: true
      t.boolean :push_campaigns, default: true
      t.boolean :push_system, default: true
      t.boolean :push_marketing, default: false
      t.boolean :sms_campaigns, default: false
      t.boolean :sms_system, default: false
      t.boolean :sms_security, default: true
      t.string :digest_frequency, default: 'daily'
      t.time :quiet_hours_start
      t.time :quiet_hours_end

      t.timestamps
    end

    add_index :notification_settings, :user_id, unique: true
    add_index :notification_settings, :tenant_id
  end
end
