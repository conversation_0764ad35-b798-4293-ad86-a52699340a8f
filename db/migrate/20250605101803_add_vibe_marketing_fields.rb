class AddVibeMarketingFields < ActiveRecord::Migration[8.0]
  def change
    # Add vibe marketing fields to campaigns table
    add_column :campaigns, :vibe_data, :jsonb, default: {}, null: false
    add_column :campaigns, :emotional_tone, :string
    add_column :campaigns, :cultural_relevance_score, :decimal, precision: 5, scale: 2
    add_column :campaigns, :authenticity_score, :decimal, precision: 5, scale: 2
    add_column :campaigns, :vibe_status, :string, default: 'pending'

    # Add vibe marketing fields to campaign collections
    add_column :campaign_collections, :vibe_strategy, :jsonb, default: {}, null: false
    add_column :campaign_collections, :target_emotion, :string
    add_column :campaign_collections, :cultural_moment, :string

    # Add indexes for performance
    add_index :campaigns, :vibe_data, using: :gin
    add_index :campaigns, :emotional_tone
    add_index :campaigns, :vibe_status
    add_index :campaign_collections, :vibe_strategy, using: :gin
    add_index :campaign_collections, :target_emotion
  end
end
