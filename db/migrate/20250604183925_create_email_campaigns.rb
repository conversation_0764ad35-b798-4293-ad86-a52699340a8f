class CreateEmailCampaigns < ActiveRecord::Migration[8.0]
  def change
    create_table :email_campaigns do |t|
      t.references :campaign, null: false, foreign_key: true, index: { unique: true }
      t.string :subject_line, null: false, limit: 150
      t.string :preview_text, limit: 200
      t.text :content, null: false
      t.string :from_name, null: false
      t.string :from_email, null: false
      t.jsonb :settings, null: false, default: {}

      t.timestamps
    end

    # Additional indexes for performance
    add_index :email_campaigns, :from_email
    add_index :email_campaigns, :settings, using: :gin
  end
end
