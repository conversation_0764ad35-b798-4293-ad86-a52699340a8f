class CreateSupportTickets < ActiveRecord::Migration[8.0]
  def change
    create_table :support_tickets do |t|
      t.references :user, null: false, foreign_key: true
      t.references :tenant, null: false, foreign_key: true
      t.references :assigned_to, null: true, foreign_key: { to_table: :users }
      t.string :ticket_number, null: false
      t.string :subject, null: false
      t.text :description, null: false
      t.string :category, null: false
      t.string :priority, default: 'normal'
      t.string :status, default: 'open'
      t.datetime :closed_at

      t.timestamps
    end

    add_index :support_tickets, :ticket_number, unique: true
    add_index :support_tickets, :status
  end
end
