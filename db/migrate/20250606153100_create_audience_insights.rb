class CreateAudienceInsights < ActiveRecord::Migration[8.0]
  def change
    create_table :audience_insights do |t|
      t.references :audience, null: false, foreign_key: true
      t.string :insight_type, null: false
      t.jsonb :data, default: {}, null: false
      t.decimal :confidence, precision: 5, scale: 2
      t.string :source

      t.timestamps
    end

    # Add indexes for performance
    add_index :audience_insights, :insight_type
    add_index :audience_insights, :confidence
    add_index :audience_insights, :data, using: :gin
    add_index :audience_insights, [ :audience_id, :insight_type ]
  end
end
