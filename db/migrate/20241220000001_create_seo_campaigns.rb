# frozen_string_literal: true

class CreateSeoCampaigns < ActiveRecord::Migration[8.0]
  def change
    create_table :seo_campaigns do |t|
      t.references :campaign, null: false, foreign_key: true, index: { unique: true }
      t.text :target_keywords
      t.string :meta_title, limit: 60
      t.text :meta_description
      t.jsonb :content_strategy, null: false, default: {}
      t.jsonb :technical_settings, null: false, default: {}
      t.jsonb :seo_settings, null: false, default: {}

      t.timestamps
    end

    add_index :seo_campaigns, :content_strategy, using: :gin
    add_index :seo_campaigns, :seo_settings, using: :gin
  end
end
