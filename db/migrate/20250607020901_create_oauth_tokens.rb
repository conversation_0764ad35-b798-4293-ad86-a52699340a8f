class CreateOauthTokens < ActiveRecord::Migration[8.0]
  def change
    create_table :oauth_tokens do |t|
      t.references :platform_configuration, null: false, foreign_key: true
      t.string :access_token, null: false
      t.string :refresh_token
      t.datetime :expires_at
      t.string :token_type, default: 'Bearer'
      t.text :scope
      t.boolean :is_active, default: true, null: false
      t.references :tenant, null: false, foreign_key: true

      t.timestamps
    end

    add_index :oauth_tokens, :is_active
    add_index :oauth_tokens, :expires_at
  end
end
