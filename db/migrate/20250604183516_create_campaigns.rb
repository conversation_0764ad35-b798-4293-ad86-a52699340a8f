class CreateCampaigns < ActiveRecord::Migration[8.0]
  def change
    create_table :campaigns do |t|
      t.string :name, null: false
      t.text :description
      t.integer :campaign_type, null: false, default: 0
      t.integer :status, null: false, default: 0
      t.string :target_audience, null: false
      t.date :start_date
      t.date :end_date
      t.integer :budget_cents, default: 0
      t.jsonb :settings, null: false, default: {}

      # Multi-tenancy and user relationships
      t.references :tenant, null: false, foreign_key: true, index: true
      t.references :created_by, null: false, foreign_key: { to_table: :users }, index: true

      t.timestamps
    end

    # Additional indexes for performance (references already creates basic indexes)
    add_index :campaigns, [ :name, :tenant_id ], unique: true
    add_index :campaigns, :status
    add_index :campaigns, :campaign_type
    add_index :campaigns, :start_date
    add_index :campaigns, :settings, using: :gin
  end
end
