class CreateUserPreferences < ActiveRecord::Migration[8.0]
  def change
    create_table :user_preferences do |t|
      t.references :user, null: false, foreign_key: true
      t.references :tenant, null: false, foreign_key: true
      t.string :timezone, default: 'UTC'
      t.string :language, default: 'en'
      t.string :date_format, default: 'MM/DD/YYYY'
      t.string :theme, default: 'light'
      t.boolean :email_notifications, default: true
      t.boolean :push_notifications, default: true
      t.boolean :sms_notifications, default: false
      t.boolean :marketing_emails, default: true
      t.boolean :product_updates, default: true
      t.boolean :security_alerts, default: true

      t.timestamps
    end

    add_index :user_preferences, :user_id, unique: true
  end
end
