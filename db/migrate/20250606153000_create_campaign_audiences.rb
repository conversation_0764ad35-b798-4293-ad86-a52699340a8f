class CreateCampaignAudiences < ActiveRecord::Migration[8.0]
  def change
    create_table :campaign_audiences do |t|
      t.references :campaign, null: false, foreign_key: true
      t.references :audience, null: false, foreign_key: true
      t.references :tenant, null: false, foreign_key: true

      t.timestamps
    end

    # Add unique constraint to prevent duplicate audience assignments
    add_index :campaign_audiences, [ :campaign_id, :audience_id ], unique: true
  end
end
