class CreateCustomerEmotionalStates < ActiveRecord::Migration[8.0]
  def change
    create_table :customer_emotional_states do |t|
      t.references :tenant, null: false, foreign_key: true, index: true
      t.string :customer_identifier, null: false, index: true
      t.string :current_emotion, null: false
      t.string :emotional_intensity, default: 'moderate'
      t.decimal :confidence_score, precision: 5, scale: 2
      t.jsonb :context_data, null: false, default: {}
      t.jsonb :behavioral_signals, null: false, default: {}
      t.jsonb :interaction_history, null: false, default: []
      t.datetime :last_interaction_at
      
      t.timestamps
    end

    # Indexes for performance
    add_index :customer_emotional_states, [:tenant_id, :customer_identifier], 
              unique: true, name: 'index_customer_emotional_states_unique'
    add_index :customer_emotional_states, :current_emotion
    add_index :customer_emotional_states, :emotional_intensity  
    add_index :customer_emotional_states, :confidence_score
    add_index :customer_emotional_states, :last_interaction_at
    add_index :customer_emotional_states, :behavioral_signals, using: :gin
    add_index :customer_emotional_states, :context_data, using: :gin
    add_index :customer_emotional_states, :interaction_history, using: :gin
    
    # Composite indexes for common queries
    add_index :customer_emotional_states, [:current_emotion, :confidence_score], 
              name: 'index_customer_emotional_states_emotion_confidence'
    add_index :customer_emotional_states, [:tenant_id, :last_interaction_at], 
              name: 'index_customer_emotional_states_tenant_interaction'
    add_index :customer_emotional_states, [:tenant_id, :current_emotion, :confidence_score], 
              name: 'index_customer_emotional_states_tenant_emotion_conf'
  end
end
