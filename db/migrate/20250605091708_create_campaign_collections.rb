class CreateCampaignCollections < ActiveRecord::Migration[8.0]
  def change
    create_table :campaign_collections do |t|
      t.string :name, limit: 100, null: false
      t.text :description
      t.integer :status, default: 0, null: false
      t.integer :collection_type, default: 1, null: false
      t.references :tenant, null: false, foreign_key: true
      t.references :created_by, null: false, foreign_key: { to_table: :users }

      t.timestamps
    end

    # Add indexes for performance and constraints
    add_index :campaign_collections, [:tenant_id, :name], unique: true, name: 'index_campaign_collections_on_tenant_id_and_name'
    add_index :campaign_collections, [:tenant_id, :collection_type], name: 'index_campaign_collections_on_tenant_id_and_collection_type'
    add_index :campaign_collections, [:tenant_id, :status], name: 'index_campaign_collections_on_tenant_id_and_status'
    add_index :campaign_collections, :created_by_id, name: 'idx_campaign_collections_created_by'
  end
end
