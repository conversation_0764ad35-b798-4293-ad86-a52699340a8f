# frozen_string_literal: true

class CreateAgentWorkflows < ActiveRecord::Migration[8.0]
  def change
    create_table :agent_workflows do |t|
      t.references :campaign, null: false, foreign_key: true
      t.integer :workflow_type, default: 0, null: false
      t.integer :status, default: 0, null: false
      t.integer :progress_percent, default: 0, null: false
      t.string :current_step
      t.integer :total_steps, default: 0
      t.jsonb :context_data, default: {}, null: false
      t.jsonb :results, default: {}, null: false
      t.jsonb :error_details, default: {}, null: false
      t.datetime :started_at
      t.datetime :completed_at
      t.references :tenant, null: false, foreign_key: true

      t.timestamps
    end

    add_index :agent_workflows, :workflow_type
    add_index :agent_workflows, :status
    add_index :agent_workflows, :context_data, using: :gin
    add_index :agent_workflows, :results, using: :gin
  end
end
