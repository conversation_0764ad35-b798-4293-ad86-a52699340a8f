class CreateCampaignLibraries < ActiveRecord::Migration[8.0]
  def change
    create_table :campaign_libraries do |t|
      t.string :name, limit: 100, null: false
      t.text :description
      t.integer :visibility, default: 0, null: false
      t.integer :library_type, default: 0, null: false
      t.references :tenant, null: false, foreign_key: true
      t.references :created_by, null: false, foreign_key: { to_table: :users }

      t.timestamps
    end

    # Add indexes for performance and constraints
    add_index :campaign_libraries, [ :tenant_id, :name ], unique: true, name: 'index_campaign_libraries_on_tenant_id_and_name'
    add_index :campaign_libraries, [ :tenant_id, :library_type ], name: 'index_campaign_libraries_on_tenant_id_and_library_type'
    add_index :campaign_libraries, [ :tenant_id, :visibility ], name: 'index_campaign_libraries_on_tenant_id_and_visibility'
    add_index :campaign_libraries, :created_by_id, name: 'idx_campaign_libraries_created_by'
  end
end
