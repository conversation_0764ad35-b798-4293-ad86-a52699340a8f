class CreateAlertLogs < ActiveRecord::Migration[8.0]
  def change
    create_table :alert_logs do |t|
      t.references :tenant, null: false, foreign_key: true
      t.string :alert_type, null: false
      t.string :severity, null: false
      t.text :message, null: false
      t.jsonb :metadata, default: {}, null: false

      t.timestamps
    end

    # Indexes for performance
    add_index :alert_logs, [ :tenant_id, :created_at ],
              name: 'index_alert_logs_on_tenant_and_created_at'
    add_index :alert_logs, [ :tenant_id, :alert_type ],
              name: 'index_alert_logs_on_tenant_and_alert_type'
    add_index :alert_logs, [ :tenant_id, :severity ],
              name: 'index_alert_logs_on_tenant_and_severity'
    add_index :alert_logs, :alert_type,
              name: 'index_alert_logs_on_alert_type'
    add_index :alert_logs, :severity,
              name: 'index_alert_logs_on_severity'
    add_index :alert_logs, :created_at,
              name: 'index_alert_logs_on_created_at'
    add_index :alert_logs, :metadata, using: :gin,
              name: 'index_alert_logs_on_metadata'

    # Composite index for alerting dashboard queries
    add_index :alert_logs, [ :tenant_id, :severity, :created_at ],
              name: 'index_alert_logs_dashboard'
  end
end
