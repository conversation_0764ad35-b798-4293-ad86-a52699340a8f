class ChangeUserRoleToInteger < ActiveRecord::Migration[8.0]
  def up
    # Since we don't have data yet, we can simply change the column type
    remove_index :users, :role
    change_column :users, :role, :integer, default: 0, null: false, using: '0'
    add_index :users, :role
  end

  def down
    remove_index :users, :role
    change_column :users, :role, :string, default: 'member', null: false, using: "'member'"
    add_index :users, :role
  end
end
