class CreateVibeMarketingTables < ActiveRecord::Migration[8.0]
  def change
    # Vibe Analysis Records - Store AI-powered vibe analysis results
    create_table :vibe_analysis_records do |t|
      t.references :campaign, null: false, foreign_key: true
      t.references :tenant, null: false, foreign_key: true
      t.string :analysis_type, null: false # emotional, cultural, authenticity, psychographic
      t.jsonb :analysis_data, default: {}, null: false
      t.decimal :confidence_score, precision: 5, scale: 2
      t.text :ai_reasoning
      t.string :model_used
      t.jsonb :metadata, default: {}, null: false
      t.timestamps

      t.index [ :campaign_id, :analysis_type ], unique: true
      t.index :analysis_data, using: :gin
      t.index :analysis_type
      t.index :confidence_score
    end

    # Cultural Moments - Track trending cultural moments and events
    create_table :cultural_moments do |t|
      t.references :tenant, null: false, foreign_key: true
      t.string :title, null: false
      t.text :description
      t.string :moment_type, null: false # trend, event, holiday, movement, viral
      t.string :category # social_justice, entertainment, sports, technology, etc.
      t.date :start_date
      t.date :end_date
      t.integer :relevance_score, default: 0
      t.jsonb :target_demographics, default: {}, null: false
      t.jsonb :engagement_metrics, default: {}, null: false
      t.jsonb :keywords, default: [], null: false
      t.string :status, default: 'active' # active, expired, archived
      t.references :created_by, null: false, foreign_key: { to_table: :users }
      t.timestamps

      t.index [ :tenant_id, :moment_type ]
      t.index [ :tenant_id, :status ]
      t.index :start_date
      t.index :end_date
      t.index :relevance_score
      t.index :target_demographics, using: :gin
      t.index :keywords, using: :gin
    end

    # Emotional Resonance Profiles - Define emotional targeting profiles
    create_table :emotional_resonance_profiles do |t|
      t.references :tenant, null: false, foreign_key: true
      t.string :name, null: false
      t.text :description
      t.string :primary_emotion, null: false # joy, trust, fear, surprise, sadness, anger, anticipation, disgust
      t.jsonb :emotion_weights, default: {}, null: false # plutchik wheel weights
      t.jsonb :tone_preferences, default: {}, null: false
      t.jsonb :content_guidelines, default: {}, null: false
      t.jsonb :avoided_triggers, default: [], null: false
      t.string :target_age_range
      t.jsonb :psychographic_traits, default: {}, null: false
      t.references :created_by, null: false, foreign_key: { to_table: :users }
      t.timestamps

      t.index [ :tenant_id, :name ], unique: true
      t.index :primary_emotion
      t.index :emotion_weights, using: :gin
      t.index :psychographic_traits, using: :gin
    end

    # Authenticity Checks - Track authenticity validation results
    create_table :authenticity_checks do |t|
      t.references :campaign, null: false, foreign_key: true
      t.references :tenant, null: false, foreign_key: true
      t.string :check_type, null: false # brand_voice, cultural_appropriation, value_alignment, tone_consistency
      t.decimal :authenticity_score, precision: 5, scale: 2, null: false
      t.jsonb :findings, default: {}, null: false
      t.jsonb :recommendations, default: [], null: false
      t.string :status, default: 'pending' # pending, approved, flagged, rejected
      t.text :reviewer_notes
      t.references :reviewed_by, foreign_key: { to_table: :users }
      t.datetime :reviewed_at
      t.timestamps

      t.index [ :campaign_id, :check_type ]
      t.index :status
      t.index :authenticity_score
      t.index :findings, using: :gin
    end

    # Vibe Campaign Orchestrations - Track vibe-driven campaign workflows
    create_table :vibe_campaign_orchestrations do |t|
      t.references :campaign_collection, null: false, foreign_key: true
      t.references :tenant, null: false, foreign_key: true
      t.string :orchestration_type, null: false # emotional_journey, cultural_moment_capitalize, authenticity_boost
      t.jsonb :workflow_config, default: {}, null: false
      t.jsonb :current_state, default: {}, null: false
      t.integer :progress_percentage, default: 0
      t.string :status, default: 'pending' # pending, running, paused, completed, failed
      t.datetime :started_at
      t.datetime :completed_at
      t.jsonb :results, default: {}, null: false
      t.references :created_by, null: false, foreign_key: { to_table: :users }
      t.timestamps

      t.index [ :campaign_collection_id, :orchestration_type ], unique: true
      t.index :status
      t.index :orchestration_type
      t.index :progress_percentage
      t.index :workflow_config, using: :gin
      t.index :current_state, using: :gin
    end
  end
end
