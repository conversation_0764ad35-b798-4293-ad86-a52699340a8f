# frozen_string_literal: true

class CreateCampaignMetrics < ActiveRecord::Migration[8.0]
  def change
    create_table :campaign_metrics do |t|
      t.references :campaign, null: false, foreign_key: true
      t.date :metric_date, null: false

      # General metrics
      t.integer :impressions, default: 0
      t.integer :clicks, default: 0
      t.integer :conversions, default: 0
      t.integer :revenue_cents, default: 0
      t.integer :cost_cents, default: 0

      # Email specific metrics
      t.integer :email_opens, default: 0
      t.integer :email_clicks, default: 0
      t.integer :email_bounces, default: 0

      # Social media specific metrics
      t.integer :social_engagements, default: 0
      t.integer :social_shares, default: 0
      t.integer :social_comments, default: 0

      # SEO specific metrics
      t.integer :seo_organic_traffic, default: 0
      t.jsonb :seo_keyword_rankings, null: false, default: {}

      # Custom metrics for extensibility
      t.jsonb :custom_metrics, null: false, default: {}

      t.timestamps
    end

    add_index :campaign_metrics, [ :campaign_id, :metric_date ], unique: true
    add_index :campaign_metrics, :metric_date
    add_index :campaign_metrics, :custom_metrics, using: :gin
    add_index :campaign_metrics, :seo_keyword_rankings, using: :gin
  end
end
