class CreateAudienceSegments < ActiveRecord::Migration[8.0]
  def change
    create_table :audience_segments do |t|
      t.references :audience, null: false, foreign_key: true
      t.string :name, null: false
      t.string :segment_type, null: false
      t.jsonb :criteria, default: {}, null: false
      t.text :description
      t.integer :size

      t.timestamps
    end

    # Add unique constraint for segment names within an audience
    add_index :audience_segments, [ :audience_id, :name ], unique: true

    # Add indexes for performance
    add_index :audience_segments, :segment_type
    add_index :audience_segments, :criteria, using: :gin
    add_index :audience_segments, :size
  end
end
