class CreatePlatformConfigurations < ActiveRecord::Migration[8.0]
  def change
    create_table :platform_configurations do |t|
      t.references :user, null: false, foreign_key: true
      t.string :platform_name, null: false
      t.boolean :is_active, default: false, null: false
      t.string :connected_account_id
      t.string :connected_account_name
      t.datetime :last_sync_at
      t.boolean :auto_sync_enabled, default: false, null: false
      t.boolean :posting_enabled, default: false, null: false
      t.boolean :analytics_enabled, default: false, null: false
      t.references :tenant, null: false, foreign_key: true

      t.timestamps
    end

    add_index :platform_configurations, [ :user_id, :platform_name ], unique: true
    add_index :platform_configurations, :platform_name
    add_index :platform_configurations, :is_active
  end
end
