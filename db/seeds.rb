# frozen_string_literal: true

# Create seed data for AI Marketing Hub demo

puts "🌱 Seeding AI Marketing Hub..."

# Create demo tenant
demo_tenant = Tenant.find_or_create_by!(
  name: "Demo Corp",
  subdomain: "demo-corp"
) do |tenant|
  tenant.settings = {
    branding: {
      primary_color: "#3B82F6",
      logo_url: "https://via.placeholder.com/150x50/3B82F6/white?text=Demo+Corp"
    },
    features: {
      ai_enabled: true,
      email_campaigns: true,
      social_media: true,
      seo_tools: true
    }
  }
end

puts "✅ Created demo tenant: #{demo_tenant.name}"

# Create demo users
ActsAsTenant.with_tenant(demo_tenant) do
  # Create owner user
  owner_user = User.find_or_create_by!(email: "<EMAIL>") do |user|
    user.password = "password123"
    user.password_confirmation = "password123"
    user.first_name = "Sarah"
    user.last_name = "Johnson"
    user.role = "owner"
    user.confirmed_at = Time.current
    user.tenant = demo_tenant
  end

  # Create admin user
  admin_user = User.find_or_create_by!(email: "<EMAIL>") do |user|
    user.password = "password123"
    user.password_confirmation = "password123"
    user.first_name = "Mike"
    user.last_name = "<PERSON>"
    user.role = "admin"
    user.confirmed_at = Time.current
    user.tenant = demo_tenant
  end

  puts "✅ Created demo users: #{owner_user.full_name} (Owner), #{admin_user.full_name} (Admin)"

  # Create sample campaigns

  # 1. Active Email Campaign
  email_campaign = Campaign.find_or_create_by!(
    name: "Holiday Sale 2024",
    tenant: demo_tenant
  ) do |campaign|
    campaign.assign_attributes(
      description: "Black Friday and Cyber Monday promotional campaign targeting existing customers",
      campaign_type: "email",
      status: "active",
      target_audience: "Existing customers who purchased in last 12 months",
      start_date: 1.week.ago,
      end_date: 1.week.from_now,
      budget_cents: 50000, # $500
      created_by: owner_user,
      settings: {
        goals: {
          primary: "drive_sales",
          secondary: "increase_engagement",
          target_revenue: 25000
        }
      }
    )
  end

  EmailCampaign.create!(
    campaign: email_campaign,
    subject_line: "🎉 Exclusive Holiday Sale - Up to 50% Off Everything!",
    preview_text: "Don't miss our biggest sale of the year. Limited time only!",
    content: "Hi {{first_name}},\n\nOur biggest sale of the year is here! Get up to 50% off everything in our store.\n\nUse code HOLIDAY50 at checkout.\n\nSale ends December 2nd - don't wait!\n\nShop now: [Shop Button]",
    from_name: "Demo Corp Team",
    from_email: "<EMAIL>",
    settings: {
      delivery_options: {
        send_immediately: false,
        scheduled_at: 2.hours.from_now.iso8601
      },
      tracking: {
        opens: true,
        clicks: true,
        unsubscribes: true
      },
      recipient_count: 12500
    }
  )

  # 2. Active Social Campaign
  social_campaign = Campaign.create!(
    name: "Brand Awareness Q4",
    description: "Multi-platform social media campaign to increase brand awareness and engagement",
    campaign_type: "social",
    status: "active",
    target_audience: "Tech-savvy professionals aged 25-45",
    start_date: 2.weeks.ago,
    end_date: 2.weeks.from_now,
    budget_cents: 30000, # $300
    created_by: admin_user,
    tenant: demo_tenant,
    settings: {
      goals: {
        primary: "increase_brand_awareness",
        target_impressions: 100000,
        target_engagement_rate: 0.05
      }
    }
  )

  SocialCampaign.create!(
    campaign: social_campaign,
    platforms: [ "twitter", "linkedin", "facebook" ],
    content_variants: {
      "twitter" => "🚀 Exciting news! Our AI marketing platform is helping SMBs compete with enterprise tools. Join the revolution! #MarketingAI #SMB",
      "linkedin" => "We're proud to announce that our AI marketing automation platform is now helping small and medium businesses access enterprise-level marketing tools. See how we're democratizing advanced marketing technology.",
      "facebook" => "Great news for small business owners! 🎉 Our new AI marketing platform makes professional campaign management accessible and affordable. Check out how we're changing the game!"
    },
    hashtags: "#MarketingAI #SMB #Automation #BusinessGrowth",
    target_demographics: {
      age_range: "25-45",
      interests: [ "business", "technology", "marketing", "entrepreneurship" ],
      location: [ "United States", "Canada", "United Kingdom" ]
    },
    social_settings: {
      follower_counts: {
        twitter: 8500,
        linkedin: 5200,
        facebook: 12000
      },
      engagement_rate: 0.08
    }
  )

  # 3. Draft Multi-channel Campaign
  Campaign.create!(
    name: "New Product Launch",
    description: "Comprehensive launch campaign for our new AI analytics feature",
    campaign_type: "multi_channel",
    status: "draft",
    target_audience: "Current users and prospects interested in analytics",
    start_date: 1.month.from_now,
    end_date: 2.months.from_now,
    budget_cents: 100000, # $1000
    created_by: owner_user,
    tenant: demo_tenant,
    settings: {
      goals: {
        primary: "product_launch",
        target_signups: 500,
        target_revenue: 50000
      }
    }
  )

  # 4. Completed Campaign
  Campaign.create!(
    name: "Customer Onboarding Series",
    description: "Welcome email series for new customers",
    campaign_type: "email",
    status: "completed",
    target_audience: "New customers in first 30 days",
    start_date: 2.months.ago,
    end_date: 1.month.ago,
    budget_cents: 15000, # $150
    created_by: admin_user,
    tenant: demo_tenant,
    settings: {
      goals: {
        primary: "improve_onboarding",
        target_completion_rate: 0.75
      }
    }
  )

  # 5. Paused Campaign
  Campaign.create!(
    name: "Summer Promotion",
    description: "Summer sale campaign - paused due to inventory issues",
    campaign_type: "social",
    status: "paused",
    target_audience: "All customers and prospects",
    start_date: 1.month.ago,
    end_date: 2.weeks.from_now,
    budget_cents: 40000, # $400
    created_by: owner_user,
    tenant: demo_tenant,
    settings: {
      goals: {
        primary: "drive_summer_sales",
        target_revenue: 30000
      }
    }
  )

  puts "✅ Created 5 sample campaigns with various statuses"

  # Add Vibe Marketing data to existing campaigns
  puts "🎨 Adding Vibe Marketing data to campaigns..."

  Campaign.find_each do |campaign|
    # Add vibe status based on campaign type and status
    vibe_status = case campaign.status
    when 'active' then [ 'vibe_approved', 'analyzing' ].sample
    when 'completed' then 'vibe_approved'
    when 'paused' then [ 'vibe_flagged', 'analyzing' ].sample
    else 'pending'
    end

    # Add emotional tone based on campaign name/type
    emotional_tone = case campaign.name.downcase
    when /holiday|sale/ then 'excited'
    when /brand|awareness/ then 'confident'
    when /launch/ then 'anticipatory'
    when /onboarding/ then 'welcoming'
    when /summer|promotion/ then 'energetic'
    else 'positive'
    end

    # Add cultural relevance and authenticity scores
    cultural_score = rand(75.0..95.0).round(1)
    authenticity_score = rand(80.0..95.0).round(1)

    campaign.update!(
      vibe_status: vibe_status,
      emotional_tone: emotional_tone,
      cultural_relevance_score: cultural_score,
      authenticity_score: authenticity_score
    )
  end

  # Create sample audiences for Vibe Analytics
  puts "👥 Creating sample audiences..."

  audiences_data = [
    {
      name: "Tech Enthusiasts",
      description: "Early adopters and technology enthusiasts who love innovation",
      cultural_context: "western",
      primary_language: "en",
      age_range_min: 25,
      age_range_max: 45,
      interests: [ "technology", "gadgets", "innovation" ],
      cultural_alignment_score: 92.5
    },
    {
      name: "Budget-Conscious Shoppers",
      description: "Price-sensitive consumers who research before buying",
      cultural_context: "western",
      primary_language: "en",
      age_range_min: 30,
      age_range_max: 55,
      interests: [ "savings", "deals", "value" ],
      cultural_alignment_score: 78.0
    },
    {
      name: "Young Professionals",
      description: "Career-focused individuals in urban areas",
      cultural_context: "western",
      primary_language: "en",
      age_range_min: 22,
      age_range_max: 35,
      interests: [ "career", "networking", "productivity" ],
      cultural_alignment_score: 85.5
    }
  ]

  audiences = audiences_data.map do |audience_data|
    audience = Audience.find_or_create_by!(
      name: audience_data[:name],
      tenant: demo_tenant
    ) do |a|
      audience_data.each { |key, value| a.send("#{key}=", value) }
      a.created_by = owner_user
      a.target_demographics = "#{audience_data[:age_range_min]}-#{audience_data[:age_range_max]} year olds"
      a.geographic_regions = [ "North America", "Europe" ]
      a.behavioral_traits = [ "engaged", "research_oriented" ]
      a.communication_preferences = "Direct, value-focused communication"
      a.cultural_values = "Efficiency, value, innovation"
    end

    puts "  ✅ Created audience: #{audience.name}"
    audience
  end

  # Create campaign-audience associations
  puts "🔗 Creating campaign-audience associations..."

  Campaign.find_each.with_index do |campaign, index|
    audience = audiences[index % audiences.length]

    CampaignAudience.find_or_create_by!(
      campaign: campaign,
      audience: audience,
      tenant: demo_tenant
    )
  end

  # Create campaign metrics for analytics
  puts "📊 Creating campaign metrics..."

  Campaign.find_each do |campaign|
    CampaignMetric.find_or_create_by!(campaign: campaign) do |metric|
      metric.impressions = rand(5000..50000)
      metric.clicks = (metric.impressions * rand(0.01..0.05)).to_i
      metric.conversions = (metric.clicks * rand(0.02..0.08)).to_i
      metric.click_through_rate = (metric.clicks.to_f / metric.impressions * 100).round(2)
      metric.conversion_rate = (metric.conversions.to_f / metric.clicks * 100).round(2) if metric.clicks > 0
      metric.engagement_rate = rand(2.0..6.0).round(2)
      metric.cost_per_click = rand(0.50..3.00).round(2)
      metric.return_on_ad_spend = rand(2.0..8.0).round(2)
      metric.date = campaign.start_date || Date.current
    end
  end

  puts "✅ Created Vibe Marketing and Audiences data"
end

puts "🎉 Seeding completed! Demo data ready with Vibe Analytics."
puts ""
puts "📈 Summary:"
puts "  - #{Campaign.count} campaigns with vibe data"
puts "  - #{Audience.count} audiences created"
puts "  - #{CampaignAudience.count} campaign-audience associations"
puts "  - #{CampaignMetric.count} campaign metrics"
puts ""
puts "Demo login credentials:"
puts "Owner: <EMAIL> / password123"
puts "Admin: <EMAIL> / password123"
puts ""
puts "🌐 Available dashboards:"
puts "  - Main Dashboard: http://localhost:3000/dashboard"
puts "  - Vibe Analytics: http://localhost:3000/vibe_analytics"
puts "  - Audiences: http://localhost:3000/audiences"
puts "  - Campaigns: http://localhost:3000/campaigns"
