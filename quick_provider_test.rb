#!/usr/bin/env ruby
# Quick test for provider availability

puts "🔍 Quick Provider Test"
puts "=" * 30

# Test the provider extraction logic
def extract_provider_from_model(model)
  case model
  when /^gpt/, /^text-embedding/
    "openai"
  when /^claude/
    "anthropic"
  when /^gemini/
    "gemini"
  when /^deepseek/
    "deepseek"
  else
    model.split("-").first
  end
end

# Test models
test_models = [
  "gpt-4o-mini",
  "gemini-1.5-flash", 
  "deepseek-chat",
  "claude-3.5-sonnet"
]

puts "\n🧪 Model to Provider Mapping:"
test_models.each do |model|
  provider = extract_provider_from_model(model)
  puts "#{model} → #{provider}"
end

# Test provider strategies
PROVIDER_STRATEGIES = {
  general: %w[gpt-4o-mini gemini-1.5-flash deepseek-chat],
  creative_content: %w[gpt-4o claude-3.5-sonnet gemini-1.5-pro]
}

puts "\n📋 Provider Strategies:"
PROVIDER_STRATEGIES.each do |task, models|
  puts "#{task}: #{models.join(', ')}"
end

puts "\n✅ Provider extraction logic looks correct!"
puts "\n🎯 Next: Test in Rails app with 'Generate AI Content' button"
