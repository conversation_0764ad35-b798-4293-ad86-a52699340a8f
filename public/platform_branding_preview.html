<!DOCTYPE html>
<html>
<head>
  <title>Platform Branding Preview</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <style>
    /* Social Media Platform Brand Colors */
    .bg-facebook { background-color: #1877F2; }
    .bg-facebook-dark { background-color: #166FE5; }
    .border-facebook { border-color: #1877F2; }
    .text-facebook { color: #1877F2; }

    .bg-twitter { background-color: #000000; }
    .bg-twitter-dark { background-color: #1a1a1a; }
    .border-twitter { border-color: #000000; }
    .text-twitter { color: #000000; }

    .bg-instagram { background-color: #E4405F; }
    .bg-instagram-dark { background-color: #D73652; }
    .border-instagram { border-color: #E4405F; }
    .text-instagram { color: #E4405F; }

    .bg-linkedin { background-color: #0A66C2; }
    .bg-linkedin-dark { background-color: #004182; }
    .border-linkedin { border-color: #0A66C2; }
    .text-linkedin { color: #0A66C2; }

    .bg-youtube { background-color: #FF0000; }
    .bg-youtube-dark { background-color: #CC0000; }
    .border-youtube { border-color: #FF0000; }
    .text-youtube { color: #FF0000; }

    .bg-tiktok { background-color: #000000; }
    .bg-tiktok-dark { background-color: #1a1a1a; }
    .border-tiktok { border-color: #000000; }
    .text-tiktok { color: #000000; }

    .bg-instagram-gradient {
      background: linear-gradient(45deg, #f09433 0%,#e6683c 25%,#dc2743 50%,#cc2366 75%,#bc1888 100%);
    }

    .platform-icon-container {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      border-radius: 0.75rem;
      padding: 0.75rem;
      transition: all 0.2s ease-in-out;
    }

    .platform-icon-container:hover {
      transform: scale(1.05);
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }
  </style>
</head>
<body class="bg-gray-50 p-8">
  <div class="max-w-4xl mx-auto">
    <h1 class="text-3xl font-bold text-gray-900 mb-8">Platform Branding Preview</h1>
    
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
      <!-- Facebook -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <div class="flex items-center mb-4">
          <div class="w-1 h-6 bg-facebook rounded-full mr-3"></div>
          <h3 class="text-lg font-semibold text-gray-900">Facebook</h3>
        </div>
        <div class="platform-icon-container bg-facebook mb-4">
          <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
            <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
          </svg>
        </div>
        <button class="w-full bg-facebook hover:bg-facebook-dark text-white px-4 py-2 rounded-md transition-colors">
          Connect Facebook
        </button>
      </div>

      <!-- Twitter/X -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <div class="flex items-center mb-4">
          <div class="w-1 h-6 bg-twitter rounded-full mr-3"></div>
          <h3 class="text-lg font-semibold text-gray-900">Twitter/X</h3>
        </div>
        <div class="platform-icon-container bg-twitter mb-4">
          <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
            <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
          </svg>
        </div>
        <button class="w-full bg-twitter hover:bg-gray-800 text-white px-4 py-2 rounded-md transition-colors">
          Connect Twitter
        </button>
      </div>

      <!-- Instagram -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <div class="flex items-center mb-4">
          <div class="w-1 h-6 bg-instagram rounded-full mr-3"></div>
          <h3 class="text-lg font-semibold text-gray-900">Instagram</h3>
        </div>
        <div class="platform-icon-container bg-instagram-gradient mb-4">
          <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12.017 0C8.396 0 7.989.013 7.041.048 6.021.088 5.347.222 4.73.42a5.978 5.978 0 00-2.188 1.423A5.994 5.994 0 00.42 4.73C.222 5.347.087 6.021.048 7.041.013 7.989 0 8.396 0 12.017c0 3.624.013 4.09.048 5.014.04 1.017.174 1.693.372 2.31a5.993 5.993 0 001.423 2.188 6.007 6.007 0 002.188 1.423c.618.198 1.293.333 2.31.372C7.989 23.987 8.396 24 12.017 24c3.624 0 4.09-.013 5.014-.048 1.017-.04 1.693-.174 2.31-.372a5.994 5.994 0 002.188-1.423A6.007 6.007 0 0023.35 19.34c.198-.618.333-1.293.372-2.31C23.987 16.09 24 15.624 24 12.017c0-3.624-.013-4.09-.048-5.014-.04-1.017-.174-1.693-.372-2.31a5.994 5.994 0 00-1.423-2.188A6.007 6.007 0 0019.34.67c-.618-.198-1.293-.333-2.31-.372C16.09.013 15.624 0 12.017 0zm0 2.162c3.558 0 3.982.013 5.389.048.868.04 1.338.185 1.652.307.415.161.71.353 1.021.664s.503.606.664 1.021c.123.314.267.784.307 1.652.035 1.407.048 1.831.048 5.389s-.013 3.982-.048 5.389c-.04.868-.184 1.338-.307 1.652-.161.415-.353.71-.664 1.021a2.75 2.75 0 01-1.021.664c-.314.123-.784.267-1.652.307-1.407.035-1.831.048-5.389.048s-3.982-.013-5.389-.048c-.868-.04-1.338-.184-1.652-.307a2.744 2.744 0 01-1.021-.664 2.75 2.75 0 01-.664-1.021c-.123-.314-.267-.784-.307-1.652C2.175 15.999 2.162 15.575 2.162 12.017s.013-3.982.048-5.389c.04-.868.184-1.338.307-1.652.161-.415.353-.71.664-1.021a2.75 2.75 0 011.021-.664c.314-.123.784-.267 1.652-.307C8.035 2.175 8.459 2.162 12.017 2.162z"/>
            <path d="M12.017 5.838a6.179 6.179 0 100 12.358 6.179 6.179 0 000-12.358zm0 10.196a4.017 4.017 0 110-8.034 4.017 4.017 0 010 8.034z"/>
            <circle cx="18.406" cy="5.594" r="1.44"/>
          </svg>
        </div>
        <button class="w-full bg-instagram hover:bg-instagram-dark text-white px-4 py-2 rounded-md transition-colors">
          Connect Instagram
        </button>
      </div>

      <!-- LinkedIn -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <div class="flex items-center mb-4">
          <div class="w-1 h-6 bg-linkedin rounded-full mr-3"></div>
          <h3 class="text-lg font-semibold text-gray-900">LinkedIn</h3>
        </div>
        <div class="platform-icon-container bg-linkedin mb-4">
          <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
            <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
          </svg>
        </div>
        <button class="w-full bg-linkedin hover:bg-linkedin-dark text-white px-4 py-2 rounded-md transition-colors">
          Connect LinkedIn
        </button>
      </div>

      <!-- YouTube -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <div class="flex items-center mb-4">
          <div class="w-1 h-6 bg-youtube rounded-full mr-3"></div>
          <h3 class="text-lg font-semibold text-gray-900">YouTube</h3>
        </div>
        <div class="platform-icon-container bg-youtube mb-4">
          <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
            <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
          </svg>
        </div>
        <button class="w-full bg-youtube hover:bg-youtube-dark text-white px-4 py-2 rounded-md transition-colors">
          Connect YouTube
        </button>
      </div>

      <!-- TikTok -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <div class="flex items-center mb-4">
          <div class="w-1 h-6 bg-tiktok rounded-full mr-3"></div>
          <h3 class="text-lg font-semibold text-gray-900">TikTok</h3>
        </div>
        <div class="platform-icon-container bg-tiktok mb-4">
          <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12.525.02c1.31-.02 2.61-.01 3.91-.02.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z"/>
          </svg>
        </div>
        <button class="w-full bg-tiktok hover:bg-gray-800 text-white px-4 py-2 rounded-md transition-colors">
          Connect TikTok
        </button>
      </div>
    </div>

    <div class="bg-white rounded-xl shadow-sm p-6">
      <h2 class="text-xl font-semibold text-gray-900 mb-4">Brand Color Implementation</h2>
      <div class="text-gray-600 space-y-2">
        <p><strong>Facebook:</strong> #1877F2 (Official Facebook Blue)</p>
        <p><strong>Twitter/X:</strong> #000000 (New X Black Brand)</p>
        <p><strong>Instagram:</strong> #E4405F with gradient option (Official Instagram Pink)</p>
        <p><strong>LinkedIn:</strong> #0A66C2 (Official LinkedIn Blue)</p>
        <p><strong>YouTube:</strong> #FF0000 (Official YouTube Red)</p>
        <p><strong>TikTok:</strong> #000000 (Official TikTok Black)</p>
      </div>
    </div>
  </div>
</body>
</html>
