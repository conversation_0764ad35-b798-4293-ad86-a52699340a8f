<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Campaigns Layout Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Basic view toggle styles for testing */
        .campaign-view {
            transition: opacity 0.3s ease-in-out;
        }
        .campaign-view:not(.active) {
            display: none;
        }
        .campaign-view.active {
            display: block;
        }
        .campaigns-view-btn.active {
            background-color: rgba(59, 130, 246, 0.1);
            color: #2563eb;
        }
    </style>
</head>
<body class="bg-gray-50 p-8">
    <div class="max-w-7xl mx-auto">
        <h1 class="text-3xl font-bold mb-8">Campaigns Layout Test</h1>
        
        <!-- View Toggle -->
        <div class="mb-6">
            <div class="campaigns-view-toggle flex items-center bg-white rounded-xl p-1 border border-gray-200 w-fit">
                <button class="campaigns-view-btn active flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200" data-view="grid">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path>
                    </svg>
                    <span>Grid</span>
                </button>
                <button class="campaigns-view-btn flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200" data-view="table">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
                    </svg>
                    <span>Table</span>
                </button>
                <button class="campaigns-view-btn md:hidden flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200" data-view="mobile">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                    </svg>
                    <span>Cards</span>
                </button>
            </div>
        </div>

        <!-- Grid View -->
        <div class="campaign-view active" data-view="grid">
            <h2 class="text-xl font-semibold mb-4">Grid View</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                    <h3 class="text-lg font-semibold mb-2">Sample Campaign 1</h3>
                    <p class="text-gray-600 mb-4">This is a sample campaign description for testing the grid layout.</p>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-green-600 font-medium">Active</span>
                        <span class="text-sm text-gray-500">$5,000</span>
                    </div>
                </div>
                <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                    <h3 class="text-lg font-semibold mb-2">Sample Campaign 2</h3>
                    <p class="text-gray-600 mb-4">Another sample campaign for testing the responsive grid layout.</p>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-blue-600 font-medium">Draft</span>
                        <span class="text-sm text-gray-500">$3,000</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Table View -->
        <div class="campaign-view" data-view="table" style="display: none;">
            <h2 class="text-xl font-semibold mb-4">Table View</h2>
            <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Campaign</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Budget</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">Sample Campaign 1</div>
                                <div class="text-sm text-gray-500">Email Marketing</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Active</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">$5,000</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <a href="#" class="text-blue-600 hover:text-blue-900">View</a>
                            </td>
                        </tr>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">Sample Campaign 2</div>
                                <div class="text-sm text-gray-500">Social Media</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">Draft</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">$3,000</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <a href="#" class="text-blue-600 hover:text-blue-900">Edit</a>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Mobile View -->
        <div class="campaign-view md:hidden" data-view="mobile" style="display: none;">
            <h2 class="text-xl font-semibold mb-4">Mobile Cards View</h2>
            <div class="space-y-4">
                <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
                    <div class="flex items-start justify-between mb-3">
                        <h3 class="text-lg font-semibold">Sample Campaign 1</h3>
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Active</span>
                    </div>
                    <p class="text-gray-600 text-sm mb-3">Email marketing campaign targeting new customers.</p>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-500">Budget: $5,000</span>
                        <div class="flex gap-2">
                            <button class="text-blue-600 text-sm">View</button>
                            <button class="text-gray-600 text-sm">Edit</button>
                        </div>
                    </div>
                </div>
                <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
                    <div class="flex items-start justify-between mb-3">
                        <h3 class="text-lg font-semibold">Sample Campaign 2</h3>
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">Draft</span>
                    </div>
                    <p class="text-gray-600 text-sm mb-3">Social media campaign for brand awareness.</p>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-500">Budget: $3,000</span>
                        <div class="flex gap-2">
                            <button class="text-blue-600 text-sm">View</button>
                            <button class="text-gray-600 text-sm">Edit</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Simple view toggle functionality for testing
        document.addEventListener('DOMContentLoaded', function() {
            const viewButtons = document.querySelectorAll('.campaigns-view-btn');
            const viewContainers = document.querySelectorAll('.campaign-view');
            
            viewButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const viewType = this.getAttribute('data-view');
                    
                    // Update active button state
                    viewButtons.forEach(btn => {
                        btn.classList.remove('active');
                    });
                    this.classList.add('active');
                    
                    // Show corresponding view
                    viewContainers.forEach(container => {
                        const containerViewType = container.getAttribute('data-view');
                        
                        if (containerViewType === viewType) {
                            container.style.display = 'block';
                            container.classList.add('active');
                        } else {
                            container.style.display = 'none';
                            container.classList.remove('active');
                        }
                    });
                });
            });
        });
    </script>
</body>
</html>
