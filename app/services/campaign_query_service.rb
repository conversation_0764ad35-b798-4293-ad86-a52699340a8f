# frozen_string_literal: true

##
# Campaign Query Service
#
# Optimized query service for campaign-related database operations.
# Provides efficient, cached queries with proper eager loading to prevent N+1 queries.
#
# @example Basic usage
#   service = CampaignQueryService.new(tenant)
#   campaigns = service.filtered_campaigns(status: 'active', type: 'email')
#   stats = service.campaign_stats
#
class CampaignQueryService
  attr_reader :tenant

  def initialize(tenant)
    @tenant = tenant
  end

  ##
  # Efficiently fetch campaigns with filters and proper eager loading
  #
  # @param filters [Hash] Filter options
  # @option filters [String] :status Campaign status filter
  # @option filters [String] :type Campaign type filter
  # @option filters [String] :search Search term for name/description
  # @option filters [String] :sort Sort field (name, budget, start_date, status)
  # @option filters [Integer] :page Page number for pagination
  # @option filters [Integer] :per_page Items per page
  #
  # @return [ActiveRecord::Relation] Optimized campaign query
  #
  def filtered_campaigns(filters = {})
    campaigns = base_campaigns_query

    # Apply filters efficiently using indexes
    campaigns = campaigns.where(status: filters[:status]) if filters[:status].present?
    campaigns = campaigns.where(campaign_type: filters[:type]) if filters[:type].present?

    # Use full-text search for better performance
    if filters[:search].present?
      campaigns = campaigns.where(
        "to_tsvector('english', coalesce(name, '') || ' ' || coalesce(description, '') || ' ' || coalesce(target_audience, '')) @@ plainto_tsquery('english', ?)",
        filters[:search]
      )
    end

    # Apply sorting with proper indexes
    campaigns = apply_sorting(campaigns, filters[:sort])

    # Apply pagination if requested
    if filters[:page].present?
      per_page = filters[:per_page] || 12
      campaigns = campaigns.limit(per_page).offset((filters[:page].to_i - 1) * per_page)
    end

    campaigns
  end

  ##
  # Get campaign statistics efficiently with a single query
  #
  # @return [Hash] Campaign statistics
  #
  def campaign_stats
    Rails.cache.fetch("campaign_stats_#{tenant.id}", expires_in: 5.minutes) do
      stats_query = tenant.campaigns
                          .group(:status)
                          .count

      {
        total: stats_query.values.sum,
        active: stats_query["active"] || 0,
        draft: stats_query["draft"] || 0,
        paused: stats_query["paused"] || 0,
        completed: stats_query["completed"] || 0,
        cancelled: stats_query["cancelled"] || 0
      }
    end
  end

  ##
  # Get campaign with full associations for detail view
  #
  # @param campaign_id [Integer] Campaign ID
  # @return [Campaign] Campaign with eager loaded associations
  #
  def campaign_with_associations(campaign_id)
    Rails.cache.fetch("campaign_detail_#{campaign_id}", expires_in: 15.minutes) do
      tenant.campaigns
            .includes(:email_campaign, :social_campaign, :seo_campaign, :created_by)
            .find(campaign_id)
    end
  end

  ##
  # Get campaign metrics efficiently for a date range
  #
  # @param campaign [Campaign] Campaign object
  # @param start_date [Date] Start date for metrics
  # @param end_date [Date] End date for metrics
  # @return [Hash] Aggregated metrics
  #
  def campaign_metrics_summary(campaign, start_date = 30.days.ago.to_date, end_date = Date.current)
    cache_key = "campaign_metrics_#{campaign.id}_#{start_date}_#{end_date}"

    Rails.cache.fetch(cache_key, expires_in: 1.hour) do
      metrics_data = campaign.campaign_metrics
                            .where(metric_date: start_date..end_date)

      return {} if metrics_data.empty?

      # Calculate aggregates using Ruby to avoid SQL GROUP BY issues
      total_impressions = metrics_data.sum(:impressions)
      total_clicks = metrics_data.sum(:clicks)
      total_conversions = metrics_data.sum(:conversions)
      total_revenue_cents = metrics_data.sum(:revenue_cents)
      total_cost_cents = metrics_data.sum(:cost_cents)

      # Calculate averages
      avg_ctr = total_impressions > 0 ? (total_clicks.to_f / total_impressions * 100) : 0.0
      avg_conversion_rate = total_clicks > 0 ? (total_conversions.to_f / total_clicks * 100) : 0.0

      {
        total_impressions: total_impressions,
        total_clicks: total_clicks,
        total_conversions: total_conversions,
        total_revenue: total_revenue_cents / 100.0,
        total_cost: total_cost_cents / 100.0,
        average_ctr: avg_ctr.round(2),
        average_conversion_rate: avg_conversion_rate.round(2),
        roi: calculate_roi(total_revenue_cents, total_cost_cents)
      }
    end
  end

  ##
  # Get recent campaigns with performance data
  #
  # @param limit [Integer] Number of campaigns to return
  # @return [Array<Hash>] Campaigns with performance data
  #
  def recent_campaigns_with_performance(limit = 10)
    Rails.cache.fetch("recent_campaigns_performance_#{tenant.id}", expires_in: 10.minutes) do
      campaigns = tenant.campaigns
                        .includes(:email_campaign, :social_campaign, :seo_campaign)
                        .joins("LEFT JOIN campaign_metrics ON campaigns.id = campaign_metrics.campaign_id")
                        .select(
                          "campaigns.*",
                          "SUM(campaign_metrics.impressions) as total_impressions",
                          "SUM(campaign_metrics.clicks) as total_clicks",
                          "SUM(campaign_metrics.conversions) as total_conversions"
                        )
                        .group("campaigns.id")
                        .order(created_at: :desc)
                        .limit(limit)

      campaigns.map do |campaign|
        {
          id: campaign.id,
          name: campaign.name,
          status: campaign.status,
          campaign_type: campaign.campaign_type,
          total_impressions: campaign.total_impressions || 0,
          total_clicks: campaign.total_clicks || 0,
          total_conversions: campaign.total_conversions || 0,
          created_at: campaign.created_at
        }
      end
    end
  end

  ##
  # Bulk update campaign statuses efficiently
  #
  # @param campaign_ids [Array<Integer>] Campaign IDs to update
  # @param new_status [String] New status to set
  # @return [Integer] Number of updated campaigns
  #
  def bulk_update_status(campaign_ids, new_status)
    # Clear relevant caches
    clear_campaign_caches(campaign_ids)

    tenant.campaigns
          .where(id: campaign_ids)
          .update_all(
            status: new_status,
            updated_at: Time.current
          )
  end

  private

  ##
  # Base query for campaigns with proper eager loading
  #
  # @return [ActiveRecord::Relation] Base campaigns query
  #
  def base_campaigns_query
    tenant.campaigns
          .includes(:email_campaign, :social_campaign, :seo_campaign, :created_by)
  end

  ##
  # Apply sorting with proper index usage
  #
  # @param campaigns [ActiveRecord::Relation] Campaigns query
  # @param sort_field [String] Field to sort by
  # @return [ActiveRecord::Relation] Sorted campaigns query
  #
  def apply_sorting(campaigns, sort_field)
    case sort_field
    when "name"
      campaigns.order(:name)
    when "budget"
      campaigns.order(:budget_cents)
    when "start_date"
      campaigns.order(:start_date)
    when "status"
      campaigns.order(:status)
    else
      campaigns.order(created_at: :desc)
    end
  end

  ##
  # Calculate ROI from revenue and cost
  #
  # @param revenue_cents [Integer] Revenue in cents
  # @param cost_cents [Integer] Cost in cents
  # @return [Float] ROI percentage
  #
  def calculate_roi(revenue_cents, cost_cents)
    return 0.0 if cost_cents.nil? || cost_cents.zero?
    return 0.0 if revenue_cents.nil?

    ((revenue_cents - cost_cents).to_f / cost_cents * 100).round(2)
  end

  ##
  # Clear campaign-related caches
  #
  # @param campaign_ids [Array<Integer>] Campaign IDs to clear caches for
  #
  def clear_campaign_caches(campaign_ids)
    Rails.cache.delete("campaign_stats_#{tenant.id}")
    Rails.cache.delete("recent_campaigns_performance_#{tenant.id}")

    campaign_ids.each do |campaign_id|
      Rails.cache.delete_matched("campaign_detail_#{campaign_id}")
      Rails.cache.delete_matched("campaign_metrics_#{campaign_id}_*")
    end
  end
end
