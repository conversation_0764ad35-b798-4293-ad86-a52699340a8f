# frozen_string_literal: true

# EmotionalResonanceTool - AI-powered emotional resonance optimization tool
#
# This tool provides:
# - Real-time emotional analysis using Plutchik's emotion wheel
# - Emotional journey mapping and optimization
# - Audience emotional preference matching
# - Cross-campaign emotional consistency validation
class EmotionalResonanceTool
  include ActiveModel::Model
  include ActiveModel::Attributes

  attr_accessor :campaign, :target_emotion, :options

  def initialize(campaign:, target_emotion: nil, options: {})
    @campaign = campaign
    @target_emotion = target_emotion
    @options = options.with_indifferent_access
    @vibe_analysis_service = VibeAnalysisService.new(campaign: campaign, analysis_type: "emotional")
  end

  # Main emotional optimization method
  def optimize_emotional_resonance
    return error_result("Campaign not found") unless campaign.present?

    begin
      # Get or create emotional analysis
      emotional_analysis = get_emotional_analysis
      return emotional_analysis unless emotional_analysis[:success]

      # Generate optimization recommendations
      optimization_result = generate_optimization_recommendations(emotional_analysis)

      # Create or update emotional resonance profile
      profile_result = create_emotional_profile(emotional_analysis, optimization_result)

      {
        success: true,
        emotional_analysis: emotional_analysis,
        optimization_recommendations: optimization_result,
        emotional_profile: profile_result,
        resonance_score: calculate_overall_resonance_score(emotional_analysis)
      }
    rescue StandardError => e
      Rails.logger.error "EmotionalResonanceTool error: #{e.message}"
      error_result("Emotional optimization failed: #{e.message}")
    end
  end

  # Map emotional journey for campaign collection
  def map_emotional_journey(campaign_collection)
    return error_result("Campaign collection not found") unless campaign_collection.present?

    campaigns = campaign_collection.ordered_campaigns
    return error_result("No campaigns in collection") if campaigns.empty?

    journey_map = build_emotional_journey_map(campaigns)
    journey_analysis = analyze_journey_effectiveness(journey_map)
    journey_optimization = optimize_journey_flow(journey_map, journey_analysis)

    {
      success: true,
      journey_map: journey_map,
      journey_analysis: journey_analysis,
      optimization_suggestions: journey_optimization,
      overall_journey_score: calculate_journey_score(journey_map)
    }
  end

  # Validate emotional consistency across campaigns
  def validate_emotional_consistency(campaigns)
    return error_result("No campaigns provided") if campaigns.empty?

    consistency_analysis = analyze_emotional_consistency(campaigns)
    recommendations = generate_consistency_recommendations(consistency_analysis)

    {
      success: true,
      consistency_score: consistency_analysis[:score],
      consistency_analysis: consistency_analysis,
      recommendations: recommendations
    }
  end

  # Get emotional audience insights
  def analyze_audience_emotional_preferences
    return error_result("Campaign not found") unless campaign.present?

    # Get historical emotional data for this audience segment
    historical_data = get_historical_emotional_data
    audience_analysis = analyze_audience_emotional_patterns(historical_data)
    preference_mapping = map_emotional_preferences(audience_analysis)

    {
      success: true,
      audience_emotional_profile: audience_analysis,
      preference_mapping: preference_mapping,
      recommendations: generate_audience_alignment_recommendations(preference_mapping)
    }
  end

  # Real-time emotional tracking
  def track_emotional_performance
    return error_result("Campaign not found") unless campaign.present?

    current_metrics = get_current_emotional_metrics
    trend_analysis = analyze_emotional_trends(current_metrics)
    performance_insights = generate_performance_insights(trend_analysis)

    {
      success: true,
      current_metrics: current_metrics,
      trend_analysis: trend_analysis,
      performance_insights: performance_insights,
      action_items: generate_emotional_action_items(performance_insights)
    }
  end

  private

  def get_emotional_analysis
    # Check for existing recent analysis
    existing_analysis = campaign.vibe_analysis_records
                               .where(analysis_type: "emotional")
                               .where("created_at > ?", 1.hour.ago)
                               .order(created_at: :desc)
                               .first

    if existing_analysis
      format_existing_analysis(existing_analysis)
    else
      @vibe_analysis_service.analyze
    end
  end

  def format_existing_analysis(analysis_record)
    {
      success: true,
      analysis_data: analysis_record.analysis_data,
      confidence_score: analysis_record.confidence_score,
      vibe_score: analysis_record.vibe_score,
      cached: true
    }
  end

  def generate_optimization_recommendations(emotional_analysis)
    analysis_data = emotional_analysis[:analysis_data]
    current_score = emotional_analysis[:vibe_score]

    recommendations = []

    # Score-based recommendations
    if current_score < 6.0
      recommendations << {
        priority: "high",
        type: "emotional_intensity",
        action: "Increase emotional intensity",
        details: suggest_intensity_improvements(analysis_data)
      }
    end

    # Target emotion alignment
    if target_emotion.present?
      alignment_score = calculate_target_emotion_alignment(analysis_data)
      if alignment_score < 7.0
        recommendations << {
          priority: "medium",
          type: "target_alignment",
          action: "Better align with target emotion: #{target_emotion}",
          details: suggest_target_alignment_improvements(analysis_data, target_emotion)
        }
      end
    end

    # Audience alignment
    audience_fit = analysis_data.dig("audience_alignment", "demographic_fit") || 0.0
    if audience_fit < 7.0
      recommendations << {
        priority: "medium",
        type: "audience_alignment",
        action: "Improve audience emotional alignment",
        details: suggest_audience_alignment_improvements(analysis_data)
      }
    end

    # Journey optimization (if part of collection)
    if campaign.campaign_collections.any?
      journey_recommendations = suggest_journey_optimizations(analysis_data)
      recommendations.concat(journey_recommendations)
    end

    recommendations
  end

  def create_emotional_profile(emotional_analysis, optimization_result)
    profile_data = {
      emotion_wheel_data: {
        "primary_emotion" => emotional_analysis[:analysis_data]["primary_emotion"],
        "secondary_emotions" => emotional_analysis[:analysis_data]["secondary_emotions"],
        "journey_progression" => emotional_analysis[:analysis_data]["emotional_journey"]
      },
      demographic_data: {
        "target_group" => campaign.target_audience,
        "emotional_preferences" => extract_emotional_preferences(emotional_analysis),
        "segments" => build_demographic_segments(emotional_analysis)
      }
    }

    # Determine profile type
    profile_type = determine_profile_type
    emotional_intensity = emotional_analysis[:analysis_data]["emotional_intensity"] || "moderate"

    EmotionalResonanceProfile.create!(
      campaign: campaign,
      tenant: campaign.tenant,
      emotion_wheel_data: profile_data[:emotion_wheel_data],
      demographic_data: profile_data[:demographic_data],
      resonance_strength: emotional_analysis[:vibe_score],
      confidence_score: emotional_analysis[:confidence_score],
      profile_type: profile_type,
      emotional_intensity: emotional_intensity
    )
  end

  def calculate_overall_resonance_score(emotional_analysis)
    base_score = emotional_analysis[:vibe_score]
    confidence_factor = emotional_analysis[:confidence_score]

    # Adjust score based on confidence
    adjusted_score = base_score * (0.7 + (confidence_factor * 0.3))

    # Apply target emotion bonus if applicable
    if target_emotion.present?
      alignment_bonus = calculate_target_emotion_alignment(emotional_analysis[:analysis_data]) * 0.1
      adjusted_score += alignment_bonus
    end

    [ adjusted_score, 10.0 ].min.round(2)
  end

  def build_emotional_journey_map(campaigns)
    journey_steps = campaigns.map.with_index do |campaign, index|
      emotional_profile = campaign.emotional_resonance_profiles.last

      {
        step: index + 1,
        campaign_id: campaign.id,
        campaign_name: campaign.name,
        primary_emotion: emotional_profile&.primary_emotion || "unknown",
        secondary_emotions: emotional_profile&.secondary_emotions || [],
        resonance_strength: emotional_profile&.resonance_strength || 0.0,
        emotional_intensity: emotional_profile&.emotional_intensity || "moderate",
        transition_effectiveness: index > 0 ? calculate_transition_effectiveness(campaigns[index-1], campaign) : nil
      }
    end

    {
      total_steps: journey_steps.size,
      steps: journey_steps,
      journey_type: detect_journey_type(journey_steps),
      overall_coherence: calculate_journey_coherence(journey_steps)
    }
  end

  def analyze_journey_effectiveness(journey_map)
    steps = journey_map[:steps]

    {
      emotional_flow_score: calculate_emotional_flow_score(steps),
      transition_quality: analyze_transition_quality(steps),
      intensity_progression: analyze_intensity_progression(steps),
      audience_engagement_prediction: predict_audience_engagement(steps),
      weak_points: identify_journey_weak_points(steps),
      strengths: identify_journey_strengths(steps)
    }
  end

  def optimize_journey_flow(journey_map, journey_analysis)
    optimizations = []

    # Flow optimization
    if journey_analysis[:emotional_flow_score] < 7.0
      optimizations << {
        type: "flow_improvement",
        priority: "high",
        suggestions: suggest_flow_improvements(journey_map[:steps])
      }
    end

    # Transition optimization
    weak_transitions = journey_analysis[:weak_points].select { |wp| wp[:type] == "transition" }
    if weak_transitions.any?
      optimizations << {
        type: "transition_improvement",
        priority: "medium",
        suggestions: suggest_transition_improvements(weak_transitions)
      }
    end

    # Intensity optimization
    if journey_analysis[:intensity_progression][:score] < 6.0
      optimizations << {
        type: "intensity_optimization",
        priority: "medium",
        suggestions: suggest_intensity_optimization(journey_map[:steps])
      }
    end

    optimizations
  end

  def calculate_journey_score(journey_map)
    coherence_weight = 0.4
    flow_weight = 0.3
    transition_weight = 0.3

    coherence_score = journey_map[:overall_coherence]

    # Simplified flow and transition scoring for now
    flow_score = calculate_emotional_flow_score(journey_map[:steps])
    transition_score = calculate_average_transition_score(journey_map[:steps])

    total_score = (coherence_score * coherence_weight) +
                  (flow_score * flow_weight) +
                  (transition_score * transition_weight)

    total_score.round(2)
  end

  def analyze_emotional_consistency(campaigns)
    emotions_data = campaigns.map do |campaign|
      profile = campaign.emotional_resonance_profiles.last
      {
        campaign_id: campaign.id,
        primary_emotion: profile&.primary_emotion,
        resonance_strength: profile&.resonance_strength || 0.0
      }
    end.compact

    primary_emotions = emotions_data.map { |ed| ed[:primary_emotion] }.compact
    unique_emotions = primary_emotions.uniq

    consistency_score = case unique_emotions.size
    when 1 then 10.0 # Perfect consistency
    when 2 then 8.0  # Good consistency
    when 3 then 6.0  # Moderate consistency
    else 4.0         # Low consistency
    end

    {
      score: consistency_score,
      emotion_distribution: primary_emotions.tally,
      average_resonance: emotions_data.map { |ed| ed[:resonance_strength] }.sum / emotions_data.size.to_f,
      consistency_level: determine_consistency_level(consistency_score),
      dominant_emotion: primary_emotions.tally.max_by { |_, count| count }&.first
    }
  end

  def generate_consistency_recommendations(consistency_analysis)
    recommendations = []

    if consistency_analysis[:score] < 6.0
      recommendations << {
        priority: "high",
        action: "Improve emotional consistency across campaigns",
        details: [
          "Consider focusing on #{consistency_analysis[:dominant_emotion]} as primary emotion",
          "Review campaigns with divergent emotions",
          "Establish emotional guidelines for the campaign series"
        ]
      }
    end

    if consistency_analysis[:average_resonance] < 6.0
      recommendations << {
        priority: "medium",
        action: "Boost overall emotional resonance",
        details: [
          "Increase emotional intensity in weaker campaigns",
          "Review audience alignment for low-scoring campaigns",
          "Consider A/B testing different emotional approaches"
        ]
      }
    end

    recommendations
  end

  def get_historical_emotional_data
    # Get emotional data from similar campaigns for this tenant
    similar_campaigns = campaign.tenant.campaigns
                               .where(target_audience: campaign.target_audience)
                               .where.not(id: campaign.id)
                               .includes(:emotional_resonance_profiles)
                               .limit(10)

    similar_campaigns.map do |camp|
      profiles = camp.emotional_resonance_profiles
      next if profiles.empty?

      {
        campaign_id: camp.id,
        campaign_type: camp.campaign_type,
        emotional_data: profiles.map do |profile|
          {
            primary_emotion: profile.primary_emotion,
            resonance_strength: profile.resonance_strength,
            demographic_alignment: profile.demographic_alignment_score
          }
        end
      }
    end.compact
  end

  def analyze_audience_emotional_patterns(historical_data)
    return {} if historical_data.empty?

    all_emotional_data = historical_data.flat_map { |hd| hd[:emotional_data] }

    emotion_performance = all_emotional_data.group_by { |ed| ed[:primary_emotion] }
                                           .transform_values do |emotions|
                                             {
                                               count: emotions.size,
                                               avg_resonance: emotions.sum { |e| e[:resonance_strength] } / emotions.size.to_f,
                                               avg_alignment: emotions.sum { |e| e[:demographic_alignment] } / emotions.size.to_f
                                             }
                                           end

    best_performing_emotion = emotion_performance.max_by { |_, data| data[:avg_resonance] }&.first

    {
      emotion_performance: emotion_performance,
      best_performing_emotion: best_performing_emotion,
      sample_size: all_emotional_data.size,
      insights: generate_audience_insights(emotion_performance)
    }
  end

  def map_emotional_preferences(audience_analysis)
    return {} if audience_analysis.empty?

    performance_data = audience_analysis[:emotion_performance] || {}

    # Rank emotions by performance
    ranked_emotions = performance_data.sort_by { |_, data| -data[:avg_resonance] }

    {
      preferred_emotions: ranked_emotions.first(3).map(&:first),
      emotion_rankings: ranked_emotions.to_h,
      confidence_level: calculate_preference_confidence(audience_analysis[:sample_size])
    }
  end

  def generate_audience_alignment_recommendations(preference_mapping)
    return [] if preference_mapping.empty?

    recommendations = []
    preferred_emotions = preference_mapping[:preferred_emotions] || []

    if preferred_emotions.any? && !preferred_emotions.include?(target_emotion)
      recommendations << {
        priority: "high",
        action: "Consider targeting preferred emotions: #{preferred_emotions.join(', ')}",
        rationale: "Historical data shows better audience resonance with these emotions"
      }
    end

    recommendations
  end

  def get_current_emotional_metrics
    # This would typically pull from campaign metrics and analytics
    # For now, return simulated data based on existing analysis
    latest_analysis = campaign.vibe_analysis_records
                             .where(analysis_type: "emotional")
                             .order(created_at: :desc)
                             .first

    return {} unless latest_analysis

    {
      current_resonance_score: latest_analysis.vibe_score,
      confidence_level: latest_analysis.confidence_score,
      primary_emotion: latest_analysis.analysis_data["primary_emotion"],
      last_updated: latest_analysis.created_at,
      trend_direction: "stable" # Would be calculated from multiple data points
    }
  end

  def analyze_emotional_trends(metrics)
    # Simplified trend analysis - would be more sophisticated in production
    {
      trend_direction: metrics[:trend_direction] || "stable",
      trend_strength: "moderate",
      key_changes: [],
      projection: {
        expected_resonance: metrics[:current_resonance_score],
        confidence: 0.7
      }
    }
  end

  def generate_performance_insights(trend_analysis)
    insights = []

    case trend_analysis[:trend_direction]
    when "improving"
      insights << "Emotional resonance is trending upward - maintain current approach"
    when "declining"
      insights << "Emotional resonance is declining - consider optimization"
    else
      insights << "Emotional resonance is stable - explore opportunities for enhancement"
    end

    insights
  end

  def generate_emotional_action_items(insights)
    # Convert insights into actionable items
    insights.map.with_index do |insight, index|
      {
        id: index + 1,
        priority: "medium",
        action: insight,
        estimated_impact: "medium"
      }
    end
  end

  # Helper methods for various calculations

  def suggest_intensity_improvements(analysis_data)
    current_intensity = analysis_data["emotional_intensity"] || "moderate"

    case current_intensity
    when "subtle"
      [ "Add more emotional triggers", "Use stronger emotional language", "Include emotional imagery" ]
    when "moderate"
      [ "Amplify key emotional moments", "Add emotional storytelling elements" ]
    when "strong"
      [ "Ensure emotional intensity matches audience preferences" ]
    else
      [ "Review emotional intensity for appropriateness" ]
    end
  end

  def calculate_target_emotion_alignment(analysis_data)
    return 5.0 unless target_emotion.present?

    primary_emotion = analysis_data["primary_emotion"]
    return 10.0 if primary_emotion == target_emotion

    # Check if emotions are complementary
    complementary_emotions = get_complementary_emotions(target_emotion)
    return 7.0 if complementary_emotions.include?(primary_emotion)

    3.0 # Low alignment
  end

  def suggest_target_alignment_improvements(analysis_data, target_emotion)
    [
      "Adjust primary messaging to emphasize #{target_emotion}",
      "Review content tone to better match #{target_emotion}",
      "Consider emotional triggers that evoke #{target_emotion}"
    ]
  end

  def suggest_audience_alignment_improvements(analysis_data)
    [
      "Research audience emotional preferences more deeply",
      "Test different emotional approaches with audience segments",
      "Align emotional tone with audience demographics"
    ]
  end

  def suggest_journey_optimizations(analysis_data)
    [
      {
        priority: "low",
        type: "journey_optimization",
        action: "Optimize emotional journey flow",
        details: [ "Consider campaign's role in emotional journey", "Ensure smooth emotional transitions" ]
      }
    ]
  end

  def determine_profile_type
    if campaign.campaign_collections.any?
      collection = campaign.campaign_collections.first
      return "emotional_journey" if collection.vibe_collection_type == "emotional_journey"
    end

    return "demographic_specific" if campaign.target_audience.present?
    "primary_emotion"
  end

  def extract_emotional_preferences(emotional_analysis)
    {
      "preferred_emotions" => [ emotional_analysis[:analysis_data]["primary_emotion"] ],
      "intensity_preference" => emotional_analysis[:analysis_data]["emotional_intensity"],
      "resonance_threshold" => 6.0
    }
  end

  def build_demographic_segments(emotional_analysis)
    audience_data = emotional_analysis[:analysis_data]["audience_alignment"] || {}

    {
      "primary" => {
        "target_group" => campaign.target_audience,
        "expected_resonance" => audience_data["demographic_fit"] || 0.0,
        "cultural_alignment" => audience_data["cultural_sensitivity"] || 0.0
      }
    }
  end

  def calculate_transition_effectiveness(prev_campaign, current_campaign)
    prev_profile = prev_campaign.emotional_resonance_profiles.last
    current_profile = current_campaign.emotional_resonance_profiles.last

    return 5.0 unless prev_profile && current_profile

    prev_emotion = prev_profile.primary_emotion
    current_emotion = current_profile.primary_emotion

    # Use emotion wheel adjacency for transition effectiveness
    emotion_adjacency_score(prev_emotion, current_emotion)
  end

  def detect_journey_type(steps)
    emotions = steps.map { |step| step[:primary_emotion] }.compact

    return "unknown" if emotions.empty?
    return "single_emotion" if emotions.uniq.size == 1
    return "emotional_arc" if emotions.size > 2
    "simple_transition"
  end

  def calculate_journey_coherence(steps)
    return 10.0 if steps.size <= 1

    coherence_scores = []

    steps.each_cons(2) do |prev_step, current_step|
      transition_score = prev_step[:transition_effectiveness] || 5.0
      coherence_scores << transition_score
    end

    return 5.0 if coherence_scores.empty?

    coherence_scores.sum / coherence_scores.size.to_f
  end

  def calculate_emotional_flow_score(steps)
    return 10.0 if steps.size <= 1

    # Simplified flow calculation
    resonance_scores = steps.map { |step| step[:resonance_strength] }
    avg_resonance = resonance_scores.sum / resonance_scores.size.to_f

    # Flow is good if resonance is consistently high
    [ avg_resonance, 10.0 ].min
  end

  def analyze_transition_quality(steps)
    transitions = steps.select { |step| step[:transition_effectiveness] }

    return { average_quality: 0.0, quality_distribution: {} } if transitions.empty?

    transition_scores = transitions.map { |t| t[:transition_effectiveness] }
    avg_quality = transition_scores.sum / transition_scores.size.to_f

    {
      average_quality: avg_quality,
      quality_distribution: transition_scores.tally,
      weak_transitions: transitions.select { |t| t[:transition_effectiveness] < 6.0 }
    }
  end

  def analyze_intensity_progression(steps)
    intensity_levels = { "subtle" => 1, "moderate" => 2, "strong" => 3, "intense" => 4 }
    intensities = steps.map { |step| intensity_levels[step[:emotional_intensity]] || 2 }

    # Check for logical progression
    progression_score = calculate_intensity_progression_score(intensities)

    {
      score: progression_score,
      pattern: detect_intensity_pattern(intensities),
      recommendations: suggest_intensity_adjustments(intensities)
    }
  end

  def predict_audience_engagement(steps)
    # Simplified engagement prediction based on emotional resonance
    avg_resonance = steps.map { |step| step[:resonance_strength] }.sum / steps.size.to_f

    {
      predicted_engagement_score: avg_resonance,
      confidence: 0.7,
      engagement_factors: [ "emotional_resonance", "journey_coherence" ]
    }
  end

  def identify_journey_weak_points(steps)
    weak_points = []

    steps.each_with_index do |step, index|
      if step[:resonance_strength] < 6.0
        weak_points << {
          type: "low_resonance",
          step: index + 1,
          issue: "Low emotional resonance",
          score: step[:resonance_strength]
        }
      end

      if step[:transition_effectiveness] && step[:transition_effectiveness] < 6.0
        weak_points << {
          type: "transition",
          step: index + 1,
          issue: "Weak emotional transition",
          score: step[:transition_effectiveness]
        }
      end
    end

    weak_points
  end

  def identify_journey_strengths(steps)
    strengths = []

    high_resonance_steps = steps.select { |step| step[:resonance_strength] >= 8.0 }
    if high_resonance_steps.any?
      strengths << {
        type: "high_resonance",
        count: high_resonance_steps.size,
        description: "Strong emotional resonance in key steps"
      }
    end

    strengths
  end

  def suggest_flow_improvements(steps)
    [
      "Review emotional progression for logical flow",
      "Consider emotional buildup and resolution",
      "Ensure each step serves the overall emotional narrative"
    ]
  end

  def suggest_transition_improvements(weak_transitions)
    weak_transitions.map do |transition|
      "Improve transition at step #{transition[:step]} - consider bridging emotions"
    end
  end

  def suggest_intensity_optimization(steps)
    [
      "Review emotional intensity progression",
      "Consider building to emotional climax",
      "Ensure intensity matches campaign importance"
    ]
  end

  def calculate_average_transition_score(steps)
    transition_scores = steps.map { |step| step[:transition_effectiveness] }.compact
    return 5.0 if transition_scores.empty?

    transition_scores.sum / transition_scores.size.to_f
  end

  def determine_consistency_level(score)
    case score
    when 8.0..10.0 then "excellent"
    when 6.0...8.0 then "good"
    when 4.0...6.0 then "moderate"
    else "poor"
    end
  end

  def generate_audience_insights(emotion_performance)
    insights = []

    if emotion_performance.any?
      best_emotion = emotion_performance.max_by { |_, data| data[:avg_resonance] }&.first
      insights << "Audience responds best to #{best_emotion} with #{emotion_performance[best_emotion][:avg_resonance].round(2)} average resonance"
    end

    insights
  end

  def calculate_preference_confidence(sample_size)
    case sample_size
    when 0..2 then "low"
    when 3..7 then "medium"
    else "high"
    end
  end

  def get_complementary_emotions(emotion)
    # Plutchik's emotion wheel complementary pairs
    complementary_map = {
      "joy" => [ "trust", "anticipation" ],
      "trust" => [ "joy", "fear" ],
      "fear" => [ "trust", "surprise" ],
      "surprise" => [ "fear", "sadness" ],
      "sadness" => [ "surprise", "disgust" ],
      "disgust" => [ "sadness", "anger" ],
      "anger" => [ "disgust", "anticipation" ],
      "anticipation" => [ "anger", "joy" ]
    }

    complementary_map[emotion] || []
  end

  def emotion_adjacency_score(emotion1, emotion2)
    # Plutchik's emotion wheel positions
    wheel_positions = {
      "joy" => 0, "trust" => 1, "fear" => 2, "surprise" => 3,
      "sadness" => 4, "disgust" => 5, "anger" => 6, "anticipation" => 7
    }

    pos1 = wheel_positions[emotion1] || 0
    pos2 = wheel_positions[emotion2] || 0

    distance = [ (pos1 - pos2).abs, 8 - (pos1 - pos2).abs ].min

    case distance
    when 0 then 10.0 # Same emotion
    when 1 then 8.0  # Adjacent
    when 2 then 6.0  # Close
    when 3 then 4.0  # Moderate distance
    when 4 then 2.0  # Opposite emotions
    else 1.0
    end
  end

  def calculate_intensity_progression_score(intensities)
    return 10.0 if intensities.size <= 1

    # Look for logical progression patterns
    # Ascending progression gets high score
    if intensities == intensities.sort
      9.0
    # Descending progression
    elsif intensities == intensities.sort.reverse
      8.0
    # Bell curve progression
    elsif bell_curve_pattern?(intensities)
      8.5
    else
      5.0 # Random progression
    end
  end

  def detect_intensity_pattern(intensities)
    return "single" if intensities.size <= 1

    if intensities == intensities.sort
      "ascending"
    elsif intensities == intensities.sort.reverse
      "descending"
    elsif bell_curve_pattern?(intensities)
      "bell_curve"
    else
      "variable"
    end
  end

  def bell_curve_pattern?(intensities)
    return false if intensities.size < 3

    # Check if intensities build up then come down
    max_index = intensities.index(intensities.max)
    return false unless max_index > 0 && max_index < intensities.size - 1

    # Check ascending to max
    ascending_part = intensities[0..max_index]
    descending_part = intensities[max_index..-1]

    ascending_part == ascending_part.sort &&
    descending_part == descending_part.sort.reverse
  end

  def suggest_intensity_adjustments(intensities)
    pattern = detect_intensity_pattern(intensities)

    case pattern
    when "variable"
      [ "Consider a more structured intensity progression" ]
    when "ascending"
      [ "Good progression - consider adding resolution at the end" ]
    when "descending"
      [ "Consider building up before resolving" ]
    else
      []
    end
  end

  def error_result(message)
    {
      success: false,
      error: message
    }
  end
end
