# frozen_string_literal: true

class SocialPlatformOAuthService
  class AuthorizationError < StandardError; end
  class TokenError < StandardError; end
  class ApiError < StandardError; end

  PLATFORM_CONFIGS = {
    "twitter" => {
      name: "Twitter",
      oauth_version: "2.0",
      auth_url: "https://twitter.com/i/oauth2/authorize",
      token_url: "https://api.twitter.com/2/oauth2/token",
      scopes: [ "tweet.read", "tweet.write", "users.read", "offline.access" ],
      api_base_url: "https://api.twitter.com/2"
    },
    "facebook" => {
      name: "Facebook",
      oauth_version: "2.0",
      auth_url: "https://www.facebook.com/v18.0/dialog/oauth",
      token_url: "https://graph.facebook.com/v18.0/oauth/access_token",
      scopes: [ "pages_manage_posts", "pages_read_engagement", "pages_show_list", "business_management" ],
      api_base_url: "https://graph.facebook.com/v18.0"
    },
    "instagram" => {
      name: "Instagram",
      oauth_version: "2.0",
      auth_url: "https://api.instagram.com/oauth/authorize",
      token_url: "https://api.instagram.com/oauth/access_token",
      scopes: [ "user_profile", "user_media" ],
      api_base_url: "https://graph.instagram.com"
    },
    "linkedin" => {
      name: "LinkedIn",
      oauth_version: "2.0",
      auth_url: "https://www.linkedin.com/oauth/v2/authorization",
      token_url: "https://www.linkedin.com/oauth/v2/accessToken",
      scopes: [ "w_member_social", "r_liteprofile", "r_emailaddress" ],
      api_base_url: "https://api.linkedin.com/v2"
    },
    "tiktok" => {
      name: "TikTok",
      oauth_version: "2.0",
      auth_url: "https://www.tiktok.com/auth/authorize/",
      token_url: "https://open-api.tiktok.com/oauth/access_token/",
      scopes: [ "user.info.basic", "video.list", "video.upload" ],
      api_base_url: "https://open-api.tiktok.com"
    },
    "youtube" => {
      name: "YouTube",
      oauth_version: "2.0",
      auth_url: "https://accounts.google.com/o/oauth2/auth",
      token_url: "https://oauth2.googleapis.com/token",
      scopes: [ "https://www.googleapis.com/auth/youtube.upload", "https://www.googleapis.com/auth/youtube.readonly" ],
      api_base_url: "https://www.googleapis.com/youtube/v3"
    }
  }.freeze

  def initialize(platform_configuration)
    if platform_configuration.is_a?(String)
      # Legacy support for platform name only
      @platform = platform_configuration.to_s.downcase
      @platform_configuration = nil
    else
      # New approach with platform configuration object
      @platform_configuration = platform_configuration
      @platform = platform_configuration.platform_name.to_s.downcase
    end

    @config = PLATFORM_CONFIGS[@platform]

    raise ArgumentError, "Unsupported platform: #{@platform}" unless @config
  end

  def build_authorization_url(redirect_uri:, state: nil)
    params = {
      client_id: client_id,
      redirect_uri: redirect_uri,
      response_type: "code",
      scope: @config[:scopes].join(" "),
      state: state || SecureRandom.hex(16)
    }

    # Platform-specific parameter adjustments
    case @platform
    when "twitter"
      params[:code_challenge] = generate_pkce_challenge
      params[:code_challenge_method] = "S256"
    when "linkedin"
      params[:scope] = @config[:scopes].join("%20")
    end

    "#{@config[:auth_url]}?#{params.to_query}"
  end

  def exchange_code_for_token(code, redirect_uri, code_verifier: nil)
    params = {
      grant_type: "authorization_code",
      client_id: client_id,
      client_secret: client_secret,
      code: code,
      redirect_uri: redirect_uri
    }

    # Platform-specific parameter adjustments
    case @platform
    when "twitter"
      params[:code_verifier] = code_verifier if code_verifier
    when "instagram"
      params[:grant_type] = "authorization_code"
    end

    response = make_token_request(params)

    if response["error"]
      raise TokenError, "Token exchange failed: #{response['error_description'] || response['error']}"
    end

    {
      access_token: response["access_token"],
      refresh_token: response["refresh_token"],
      expires_at: response["expires_in"] ? Time.current + response["expires_in"].seconds : nil,
      token_type: response["token_type"] || "Bearer",
      scope: response["scope"]
    }
  end

  def refresh_token(refresh_token)
    return nil unless refresh_token

    params = {
      grant_type: "refresh_token",
      refresh_token: refresh_token,
      client_id: client_id,
      client_secret: client_secret
    }

    response = make_token_request(params)

    if response["error"]
      raise TokenError, "Token refresh failed: #{response['error_description'] || response['error']}"
    end

    {
      access_token: response["access_token"],
      refresh_token: response["refresh_token"] || refresh_token, # Some platforms don't return new refresh token
      expires_at: response["expires_in"] ? Time.current + response["expires_in"].seconds : nil,
      token_type: response["token_type"] || "Bearer"
    }
  end

  def test_platform_connection(access_token)
    case @platform
    when "twitter"
      test_twitter_connection(access_token)
    when "facebook"
      test_facebook_connection(access_token)
    when "instagram"
      test_instagram_connection(access_token)
    when "linkedin"
      test_linkedin_connection(access_token)
    when "tiktok"
      test_tiktok_connection(access_token)
    when "youtube"
      test_youtube_connection(access_token)
    else
      { success: false, error: "Unsupported platform" }
    end
  rescue => e
    { success: false, error: e.message }
  end

  def get_user_profile(access_token)
    case @platform
    when "twitter"
      get_twitter_profile(access_token)
    when "facebook"
      get_facebook_profile(access_token)
    when "instagram"
      get_instagram_profile(access_token)
    when "linkedin"
      get_linkedin_profile(access_token)
    when "tiktok"
      get_tiktok_profile(access_token)
    when "youtube"
      get_youtube_profile(access_token)
    else
      raise ApiError, "Unsupported platform"
    end
  end

  def refresh_access_token
    return unless @platform_configuration&.current_token&.refresh_token

    token_data = refresh_token(@platform_configuration.current_token.refresh_token)

    @platform_configuration.current_token.update!(
      access_token: token_data[:access_token],
      refresh_token: token_data[:refresh_token],
      expires_at: token_data[:expires_at],
      token_type: token_data[:token_type]
    )

    token_data
  end

  def test_connection
    return { success: false, error: "No active token" } unless @platform_configuration&.current_token

    test_platform_connection(@platform_configuration.current_token.access_token)
  end

  def fetch_recent_posts(limit: 10)
    return [] unless @platform_configuration&.current_token

    # This would be implemented per platform
    []
  end

  def fetch_account_metrics
    return {} unless @platform_configuration&.current_token

    # This would be implemented per platform
    {}
  end

  def post_content(access_token, content_data)
    case @platform
    when "twitter"
      post_to_twitter(access_token, content_data)
    when "facebook"
      post_to_facebook(access_token, content_data)
    when "instagram"
      post_to_instagram(access_token, content_data)
    when "linkedin"
      post_to_linkedin(access_token, content_data)
    when "tiktok"
      post_to_tiktok(access_token, content_data)
    when "youtube"
      post_to_youtube(access_token, content_data)
    else
      raise ApiError, "Unsupported platform"
    end
  end

  private

  def client_id
    ENV["#{@platform.upcase}_CLIENT_ID"] ||
    Rails.application.credentials.dig(@platform.to_sym, :client_id)
  end

  def client_secret
    ENV["#{@platform.upcase}_CLIENT_SECRET"] ||
    Rails.application.credentials.dig(@platform.to_sym, :client_secret)
  end

  def make_token_request(params)
    uri = URI(@config[:token_url])
    http = Net::HTTP.new(uri.host, uri.port)
    http.use_ssl = true

    request = Net::HTTP::Post.new(uri)
    request["Content-Type"] = "application/x-www-form-urlencoded"
    request.body = params.to_query

    response = http.request(request)
    JSON.parse(response.body) if response.body
  rescue JSON::ParserError
    { "error" => "Invalid response format" }
  end

  def make_api_request(url, access_token, method: :get, params: {})
    uri = URI(url)
    http = Net::HTTP.new(uri.host, uri.port)
    http.use_ssl = true

    case method
    when :get
      request = Net::HTTP::Get.new(uri)
    when :post
      request = Net::HTTP::Post.new(uri)
      request["Content-Type"] = "application/json"
      request.body = params.to_json
    end

    request["Authorization"] = "Bearer #{access_token}"
    response = http.request(request)

    JSON.parse(response.body) if response.body
  rescue JSON::ParserError
    { "error" => "Invalid response format" }
  end

  def generate_pkce_challenge
    verifier = SecureRandom.urlsafe_base64(32)
    challenge = Base64.urlsafe_encode64(Digest::SHA256.digest(verifier)).chomp("=")
    challenge
  end

  # Platform-specific connection tests
  def test_twitter_connection(access_token)
    response = make_api_request("#{@config[:api_base_url]}/users/me", access_token)

    if response["data"]
      { success: true, user: response["data"] }
    else
      { success: false, error: response["detail"] || "Connection failed" }
    end
  end

  def test_facebook_connection(access_token)
    response = make_api_request("#{@config[:api_base_url]}/me", access_token)

    if response["id"]
      { success: true, user: response }
    else
      { success: false, error: response["error"]["message"] || "Connection failed" }
    end
  end

  def test_instagram_connection(access_token)
    response = make_api_request("#{@config[:api_base_url]}/me", access_token)

    if response["id"]
      { success: true, user: response }
    else
      { success: false, error: response["error"]["message"] || "Connection failed" }
    end
  end

  def test_linkedin_connection(access_token)
    response = make_api_request("#{@config[:api_base_url]}/people/~", access_token)

    if response["id"]
      { success: true, user: response }
    else
      { success: false, error: response["message"] || "Connection failed" }
    end
  end

  def test_tiktok_connection(access_token)
    response = make_api_request("#{@config[:api_base_url]}/user/info/", access_token)

    if response["data"]
      { success: true, user: response["data"] }
    else
      { success: false, error: response["message"] || "Connection failed" }
    end
  end

  def test_youtube_connection(access_token)
    response = make_api_request("#{@config[:api_base_url]}/channels?part=snippet&mine=true", access_token)

    if response["items"]
      { success: true, user: response["items"].first }
    else
      { success: false, error: response["error"]["message"] || "Connection failed" }
    end
  end

  # Platform-specific profile fetching methods
  def get_twitter_profile(access_token)
    response = make_api_request("#{@config[:api_base_url]}/users/me?user.fields=public_metrics", access_token)

    if response["data"]
      {
        id: response["data"]["id"],
        name: response["data"]["name"],
        username: response["data"]["username"],
        avatar: response["data"]["profile_image_url"],
        followers: response["data"]["public_metrics"]["followers_count"],
        verified: response["data"]["verified"] || false
      }
    else
      raise ApiError, response["detail"] || "Failed to fetch profile"
    end
  end

  def get_facebook_profile(access_token)
    response = make_api_request("#{@config[:api_base_url]}/me?fields=id,name,picture", access_token)

    if response["id"]
      {
        id: response["id"],
        name: response["name"],
        username: response["name"],
        avatar: response["picture"]["data"]["url"],
        followers: nil,
        verified: false
      }
    else
      raise ApiError, response["error"]["message"] || "Failed to fetch profile"
    end
  end

  def get_instagram_profile(access_token)
    response = make_api_request("#{@config[:api_base_url]}/me?fields=id,username,media_count,followers_count", access_token)

    if response["id"]
      {
        id: response["id"],
        name: response["username"],
        username: response["username"],
        avatar: nil,
        followers: response["followers_count"],
        verified: false
      }
    else
      raise ApiError, response["error"]["message"] || "Failed to fetch profile"
    end
  end

  def get_linkedin_profile(access_token)
    response = make_api_request("#{@config[:api_base_url]}/people/~:(id,formattedName,pictureUrl)", access_token)

    if response["id"]
      {
        id: response["id"],
        name: response["formattedName"],
        username: response["formattedName"],
        avatar: response["pictureUrl"],
        followers: nil,
        verified: false
      }
    else
      raise ApiError, response["message"] || "Failed to fetch profile"
    end
  end

  def get_tiktok_profile(access_token)
    response = make_api_request("#{@config[:api_base_url]}/user/info/", access_token)

    if response["data"]
      {
        id: response["data"]["user"]["open_id"],
        name: response["data"]["user"]["display_name"],
        username: response["data"]["user"]["username"],
        avatar: response["data"]["user"]["avatar_url"],
        followers: response["data"]["user"]["follower_count"],
        verified: response["data"]["user"]["is_verified"] || false
      }
    else
      raise ApiError, response["message"] || "Failed to fetch profile"
    end
  end

  def get_youtube_profile(access_token)
    response = make_api_request("#{@config[:api_base_url]}/channels?part=snippet,statistics&mine=true", access_token)

    if response["items"] && response["items"].any?
      channel = response["items"].first
      {
        id: channel["id"],
        name: channel["snippet"]["title"],
        username: channel["snippet"]["customUrl"] || channel["snippet"]["title"],
        avatar: channel["snippet"]["thumbnails"]["default"]["url"],
        followers: channel["statistics"]["subscriberCount"].to_i,
        verified: false
      }
    else
      raise ApiError, response["error"]["message"] || "Failed to fetch profile"
    end
  end

  # Platform-specific posting methods
  def post_to_twitter(access_token, content_data)
    params = { text: content_data[:text] }
    params[:media] = { media_ids: content_data[:media_ids] } if content_data[:media_ids]

    response = make_api_request("#{@config[:api_base_url]}/tweets", access_token, method: :post, params: params)

    if response["data"]
      { success: true, post_id: response["data"]["id"], url: "https://twitter.com/i/status/#{response['data']['id']}" }
    else
      { success: false, error: response["detail"] || "Failed to post" }
    end
  end

  def post_to_facebook(access_token, content_data)
    # Implementation for Facebook posting
    { success: false, error: "Facebook posting not implemented yet" }
  end

  def post_to_instagram(access_token, content_data)
    # Implementation for Instagram posting
    { success: false, error: "Instagram posting not implemented yet" }
  end

  def post_to_linkedin(access_token, content_data)
    # Implementation for LinkedIn posting
    { success: false, error: "LinkedIn posting not implemented yet" }
  end

  def post_to_tiktok(access_token, content_data)
    # Implementation for TikTok posting
    { success: false, error: "TikTok posting not implemented yet" }
  end

  def post_to_youtube(access_token, content_data)
    # Implementation for YouTube posting
    { success: false, error: "YouTube posting not implemented yet" }
  end
end
