# frozen_string_literal: true

# Centralized service for handling errors across the application
# This service provides consistent error handling, logging, and user-friendly messages
class ErrorHandlingService
  include ActiveModel::Model
  
  # Error severity levels
  SEVERITY = {
    info: 0,      # Informational, non-critical
    warning: 1,   # Warning, may require attention
    error: 2,     # Error, requires attention
    critical: 3   # Critical, requires immediate attention
  }
  
  # Default retry strategies
  RETRY_STRATEGIES = {
    none: { max_retries: 0, backoff: 0 },
    immediate: { max_retries: 3, backoff: 0 },
    linear_backoff: { max_retries: 3, backoff: 5 },
    exponential_backoff: { max_retries: 5, backoff: :exponential }
  }
  
  # Error code prefixes for different services
  ERROR_CODE_PREFIXES = {
    'EmailSpecialistAgent' => 'EMAIL',
    'EmailContentGenerator' => 'EMAIL',
    'SocialMediaAgent' => 'SOCIAL',
    'AnalyticsAgent' => 'ANALYTICS',
    'CampaignOrchestration' => 'CAMPAIGN',
    'AuthenticityChecker' => 'AUTH',
    'EmotionalResonance' => 'EMOTION',
    'MarketingManager' => 'MARKETING',
    'SeoSpecialist' => 'SEO',
    'CustomerExperience' => 'CX',
    'RubyLlm' => 'LLM',
    'OpenAi' => 'OPENAI',
    'default' => 'SYSTEM'
  }
  
  class << self
    # Log and process an error, returning structured information
    def log_and_process(error:, context: {}, error_code: nil, retry_strategy: :none)
      # Generate error code if not provided
      error_code ||= generate_error_code(error, context)
      
      # Determine severity
      severity = determine_severity(error)
      
      # Normalize context data
      normalized_context = normalize_context(context)
      
      # Prepare error metadata
      metadata = {
        error_class: error.class.name,
        message: error.message,
        backtrace: error.backtrace&.first(10),
        context: normalized_context,
        retry_strategy: retry_strategy
      }
      
      # Log the error
      log_error(error_code, severity, metadata)
      
      # Create alert log for higher severity errors
      if severity >= SEVERITY[:error]
        create_alert_log(error_code, severity, metadata, normalized_context[:tenant_id])
      end
      
      # Return structured error information
      {
        error_code: error_code,
        error_class: error.class.name,
        message: error.message,
        severity: severity_name(severity),
        recovery_options: determine_recovery_options(error, context),
        retry_strategy: retry_strategy
      }
    end
    
    # Generate a user-friendly error message
    def user_friendly_message(error, custom_message = nil)
      return custom_message if custom_message.present?
      
      # Map common errors to user-friendly messages
      case error
      when RubyLlmService::RateLimitError
        'Our AI service is experiencing high demand. Please try again in a few minutes.'
      when RubyLlmService::TokenLimitError
        'The content is too large for our AI to process. Please try with a smaller amount of text.'
      when RubyLlmService::ApiTimeoutError
        'The request took too long to process. Please try again or with a smaller request.'
      when RubyLlmService::InvalidRequestError
        'There was an issue with the request format. Our team has been notified.'
      when RubyLlmService::ServiceUnavailableError
        'The AI service is temporarily unavailable. Please try again later.'
      when RubyLlmService::AuthenticationError
        'There was an authentication issue with our AI provider. Our team has been notified.'
      when Net::OpenTimeout, Net::ReadTimeout
        'The request timed out. Please check your internet connection and try again.'
      when StandardError
        'An unexpected error occurred. Our team has been notified and is working on a fix.'
      else
        'Something went wrong. Please try again or contact support if the issue persists.'
      end
    end
    
    # Calculate retry delay based on attempt number and strategy
    def calculate_retry_delay(attempt, strategy_name)
      strategy = RETRY_STRATEGIES[strategy_name.to_sym] || RETRY_STRATEGIES[:none]
      
      case strategy[:backoff]
      when 0
        0 # No delay
      when :exponential
        # Exponential backoff with jitter: 2^attempt + random(0..1)
        (2**attempt) + rand
      else
        # Linear backoff: attempt * backoff_seconds
        attempt * strategy[:backoff]
      end
    end
    
    # Check if retry is allowed based on attempt number and strategy
    def retry_allowed?(attempt, strategy_name)
      strategy = RETRY_STRATEGIES[strategy_name.to_sym] || RETRY_STRATEGIES[:none]
      attempt < strategy[:max_retries]
    end
    
    private
    
    # Generate a unique error code
    def generate_error_code(error, context)
      # Extract service name from context
      service_name = context[:service] || 'default'
      
      # Get the prefix for this service
      prefix = ERROR_CODE_PREFIXES[service_name] || ERROR_CODE_PREFIXES['default']
      
      # Generate an error code based on error class
      case error
      when RubyLlmService::RateLimitError
        "#{prefix}_RATE_001"
      when RubyLlmService::TokenLimitError
        "#{prefix}_TOKEN_001"
      when RubyLlmService::ApiTimeoutError
        "#{prefix}_TIMEOUT_001"
      when RubyLlmService::InvalidRequestError
        "#{prefix}_REQ_001"
      when RubyLlmService::ServiceUnavailableError
        "#{prefix}_SVC_001"
      when RubyLlmService::AuthenticationError
        "#{prefix}_AUTH_001"
      when Net::OpenTimeout, Net::ReadTimeout
        "#{prefix}_NET_001"
      else
        # Generate a unique error code for unexpected errors
        "#{prefix}_#{error.class.name.underscore.upcase.gsub('::', '_').first(10)}_001"
      end
    end
    
    # Determine error severity based on error type
    def determine_severity(error)
      case error
      when RubyLlmService::RateLimitError,
           RubyLlmService::TokenLimitError,
           Net::OpenTimeout, Net::ReadTimeout
        SEVERITY[:warning]
      when RubyLlmService::ApiTimeoutError,
           RubyLlmService::InvalidRequestError
        SEVERITY[:error]
      when RubyLlmService::ServiceUnavailableError,
           RubyLlmService::AuthenticationError
        SEVERITY[:critical]
      else
        SEVERITY[:error]
      end
    end
    
    # Get severity name from level
    def severity_name(level)
      SEVERITY.key(level) || :error
    end
    
    # Normalize context data for consistent logging
    def normalize_context(context)
      # Convert context to hash if it's not already
      context = {} unless context.is_a?(Hash)
      
      # Ensure all keys are symbols
      context = context.deep_symbolize_keys
      
      # Remove any sensitive data
      sanitized_context = context.except(:password, :token, :api_key, :secret)
      
      # Ensure tenant_id is present if tenant is provided
      if context[:tenant].present? && context[:tenant].respond_to?(:id)
        sanitized_context[:tenant_id] = context[:tenant].id
        sanitized_context.delete(:tenant)
      end
      
      sanitized_context
    end
    
    # Log error to Rails logger and potentially external services
    def log_error(error_code, severity, metadata)
      severity_name = severity_name(severity)
      
      # Log to Rails logger
      Rails.logger.public_send(
        severity_name,
        "[#{error_code}] #{metadata[:error_class]}: #{metadata[:message]}"
      )
      
      # Additional logging could be added here, such as:
      # - DataDog
      # - Sentry
      # - CloudWatch
      # - Etc.
    end
    
    # Create an alert log entry in the database
    def create_alert_log(error_code, severity, metadata, tenant_id)
      AlertLog.create!(
        tenant_id: tenant_id,
        severity: severity_name(severity).to_s,
        message: "#{metadata[:error_class]}: #{metadata[:message]}",
        source: metadata[:context][:service] || 'System',
        error_code: error_code,
        metadata: metadata.slice(:context, :error_class, :backtrace)
      )
    rescue => e
      # If we can't create an alert log, just log the error
      Rails.logger.error("Failed to create alert log: #{e.message}")
    end
    
    # Determine recovery options based on error type
    def determine_recovery_options(error, context)
      case error
      when RubyLlmService::RateLimitError
        ['retry_later', 'use_template', 'reduce_complexity']
      when RubyLlmService::TokenLimitError
        ['reduce_content', 'split_request', 'use_template']
      when RubyLlmService::ApiTimeoutError
        ['retry', 'reduce_complexity', 'use_template']
      when RubyLlmService::ServiceUnavailableError
        ['retry_later', 'use_alternate_service', 'use_template']
      when RubyLlmService::InvalidRequestError
        ['check_input', 'use_template', 'contact_support']
      else
        ['retry', 'contact_support']
      end
    end
  end
end
