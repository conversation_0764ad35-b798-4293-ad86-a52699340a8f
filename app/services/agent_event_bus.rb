# frozen_string_literal: true

# Event-driven communication system for multi-agent coordination
class AgentEventBus
  include Singleton
  
  class AgentEvent
    attr_reader :event_type, :source_agent, :target_agents, :payload, :timestamp, :correlation_id
    
    def initialize(event_type:, source_agent:, payload:, target_agents: [], correlation_id: nil)
      @event_type = event_type
      @source_agent = source_agent
      @target_agents = target_agents
      @payload = payload
      @timestamp = Time.current
      @correlation_id = correlation_id || SecureRandom.uuid
    end
  end
  
  def initialize
    @subscribers = Hash.new { |hash, key| hash[key] = [] }
    @event_queue = Queue.new
    @event_history = []
    @processing_thread = nil
  end
  
  # Subscribe an agent to specific event types
  def subscribe(agent, event_types)
    Array(event_types).each do |event_type|
      @subscribers[event_type] << agent unless @subscribers[event_type].include?(agent)
    end
  end
  
  # Unsubscribe an agent from event types
  def unsubscribe(agent, event_types = nil)
    if event_types
      Array(event_types).each do |event_type|
        @subscribers[event_type].delete(agent)
      end
    else
      @subscribers.each_value { |agents| agents.delete(agent) }
    end
  end
  
  # Publish an event to the bus
  def publish(event_type:, source_agent:, payload:, target_agents: [])
    event = AgentEvent.new(
      event_type: event_type,
      source_agent: source_agent,
      payload: payload,
      target_agents: target_agents
    )
    
    @event_queue << event
    @event_history << event
    
    # Start processing thread if not running
    start_processing_thread unless @processing_thread&.alive?
    
    event.correlation_id
  end
  
  # Get event history for debugging/analysis
  def event_history(limit: 100)
    @event_history.last(limit)
  end
  
  # Clear event history
  def clear_history
    @event_history.clear
  end
  
  private
  
  def start_processing_thread
    @processing_thread = Thread.new do
      loop do
        event = @event_queue.pop
        process_event(event)
      rescue => e
        Rails.logger.error "AgentEventBus error: #{e.message}"
        Rails.logger.error e.backtrace.join("\n")
      end
    end
  end
  
  def process_event(event)
    # Determine target agents
    target_agents = if event.target_agents.any?
      event.target_agents
    else
      @subscribers[event.event_type]
    end
    
    # Deliver event to each target agent
    target_agents.each do |agent|
      begin
        if agent.respond_to?(:handle_event)
          agent.handle_event(event)
        else
          Rails.logger.warn "Agent #{agent.class.name} does not implement handle_event method"
        end
      rescue => e
        Rails.logger.error "Error delivering event to #{agent.class.name}: #{e.message}"
      end
    end
    
    # Log event processing
    Rails.logger.info "Processed event: #{event.event_type} from #{event.source_agent} to #{target_agents.size} agents"
  end
end
