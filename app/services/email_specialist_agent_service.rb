# frozen_string_literal: true

# Email Specialist Agent - Handles AI-powered email campaign generation and optimization
class EmailSpecialistAgentService
  include ActiveModel::Model
  include ActiveModel::Validations

  attr_reader :campaign, :tenant, :ai_service, :email_campaign

  def initialize(campaign:, ai_service: nil)
    @campaign = campaign
    @tenant = campaign.tenant
    @ai_service = ai_service || build_ai_service
    @email_campaign = campaign.email_campaign
  end

  # Generate comprehensive email campaign content
  def generate_campaign_content(context = {})
    validate_campaign_for_email!

    begin
      # Extract campaign requirements from context or parameters
      campaign_requirements = extract_campaign_requirements(context)

      # Generate email content using AI
      email_prompt = build_email_campaign_prompt(
        campaign_name: campaign.name,
        target_audience: campaign.target_audience,
        campaign_goals: campaign_requirements[:goals],
        brand_voice: campaign_requirements[:brand_voice],
        email_type: campaign_requirements[:email_type],
        key_message: campaign_requirements[:key_message]
      )

      Rails.logger.info "Generating email content with prompt length: #{email_prompt.length}"

      # Try creative_content first, fallback to general if needed
      ai_response = begin
        @ai_service.generate_content(
          email_prompt,
          task_type: :creative_content,
          temperature: 0.8
        )
      rescue RubyLlmService::ConfigurationError => e
        Rails.logger.warn "Creative content task failed, trying general: #{e.message}"
        @ai_service.generate_content(
          email_prompt,
          task_type: :general,
          temperature: 0.8
        )
      end

      Rails.logger.info "AI response received: #{ai_response.class}"

      ai_content = ai_response.content
      Rails.logger.info "Raw AI content: #{ai_content.inspect}"

      # Process AI content but don't auto-save (let user review first)
      email_campaign_data = process_ai_content(ai_content)
      Rails.logger.info "Processed email campaign data: #{email_campaign_data.inspect}"

      # Don't auto-save - let the user review and save manually
      # create_or_update_email_campaign(email_campaign_data)

      # Return results for orchestrator
      result = {
        status: "success",
        content_generated: true,
        ai_content: ai_content,
        campaign_data: email_campaign_data,
        ready_for_review: true
      }

      Rails.logger.info "Final service result: #{result.inspect}"
      result
    rescue => error
      handle_generation_error(error)
    end
  end

  # Optimize existing email campaign content
  def optimize_campaign_content(optimization_context = {})
    optimize_email_content(optimization_context)
  end

  def optimize_email_content(optimization_context = {})
    return { status: "error", message: "No email campaign found" } unless @email_campaign

    begin
      current_content = {
        subject_line: @email_campaign.subject_line,
        content: @email_campaign.content,
        from_name: @email_campaign.from_name
      }

      optimization_prompt = build_optimization_prompt(current_content, optimization_context)

      ai_response = @ai_service.generate_content(
        optimization_prompt,
        task_type: :creative_content,
        temperature: 0.7
      )

      optimized_content = ai_response.content

      optimized_data = process_optimization_results(optimized_content)
      update_email_campaign_with_optimizations(optimized_data)

      {
        status: "success",
        optimizations_applied: true,
        original_content: current_content,
        optimized_content: optimized_data
      }
    rescue => error
      handle_optimization_error(error)
    end
  end

  # Generate A/B test variants for email content
  def generate_ab_test_variants(variant_count: 3)
    return { status: "error", message: "No email campaign found" } unless @email_campaign

    begin
      base_content = {
        subject_line: @email_campaign.subject_line,
        content: @email_campaign.content
      }

      variants = []

      (1..variant_count).each do |i|
        variant_prompt = build_variant_prompt(base_content, i)
        ai_response = @ai_service.generate_content(
          variant_prompt,
          task_type: :creative_content,
          temperature: 0.8
        )

        variant_content = ai_response.content

        variants << process_variant_content(variant_content, i)
      end

      store_ab_test_variants(variants)

      {
        status: "success",
        variants_generated: variant_count,
        base_campaign: base_content,
        variants: variants
      }
    rescue => error
      handle_variant_generation_error(error)
    end
  end

  # Analyze email performance and provide recommendations
  def analyze_email_performance
    return { status: "error", message: "No email campaign found" } unless @email_campaign

    begin
      performance_data = collect_email_metrics

      analysis_prompt = build_performance_analysis_prompt(performance_data)
      ai_response = @ai_service.generate_content(
        analysis_prompt,
        task_type: :data_analysis,
        temperature: 0.5
      )

      ai_analysis = ai_response.content

      recommendations = extract_recommendations_from_analysis(ai_analysis)

      {
        status: "success",
        performance_data: performance_data,
        ai_analysis: ai_analysis,
        recommendations: recommendations,
        next_actions: generate_next_actions(recommendations)
      }
    rescue => error
      handle_analysis_error(error)
    end
  end

  private

  # Build enterprise AI service with tenant-specific configuration
  def build_ai_service
    RubyLlmService.new(
      tenant: @tenant,
      context: {
        campaign_id: @campaign.id,
        task_type: :creative_content,
        brand_guidelines: @tenant.settings.dig("brand_guidelines"),
        target_audience: @campaign.target_audience,
        email_type: determine_email_type
      },
      provider_strategy: determine_provider_strategy
    )
  end

  # Determine optimal provider strategy based on campaign requirements
  def determine_provider_strategy
    case @campaign.budget_cents
    when 0..15000 # Low budget campaigns
      :cost_sensitive
    when 15000..75000 # Medium budget campaigns
      :balanced
    else # High budget campaigns
      :creative_content
    end
  end

  # Determine email type for AI context
  def determine_email_type
    campaign.settings.dig("email", "type") || "promotional"
  end

  # Build email campaign generation prompt
  def build_email_campaign_prompt(campaign_name:, target_audience:, campaign_goals:, brand_voice:, email_type: nil, key_message: nil)
    key_message_section = key_message.present? ? "\nKey Message: #{key_message}" : ""

    <<~PROMPT
      Create a comprehensive email campaign for the following requirements:

      Campaign Name: #{campaign_name}
      Target Audience: #{target_audience}
      Campaign Goals: #{campaign_goals}
      Brand Voice: #{brand_voice}
      Email Type: #{email_type || determine_email_type}#{key_message_section}

      Please generate:
      1. A compelling subject line that drives opens
      2. Preview text that complements the subject
      3. Email content that achieves the campaign goals
      4. Clear call-to-action (CTA)
      5. Appropriate tone matching the brand voice

      Format the response as JSON with these keys:
      - subject_line: The email subject line
      - preview_text: Preview text for email clients
      - content: Full email content in HTML format
      - cta: Primary call-to-action text
      - tone: Tone used in the content

      Make the content engaging, personalized, and optimized for conversions.
    PROMPT
  end

  def validate_campaign_for_email!
    raise ArgumentError, "Campaign must be email or multi-channel type" unless email_campaign_type?
    raise ArgumentError, "Campaign must belong to a tenant" unless @tenant
  end

  def email_campaign_type?
    campaign.email? || campaign.multi_channel?
  end

  def extract_campaign_requirements(context)
    {
      goals: context.dig(:campaign_analysis, :settings, :goals) || default_email_goals,
      brand_voice: context[:brand_voice] || context.dig(:campaign_analysis, :settings, :brand_voice) || "professional",
      email_type: context[:email_type] || context.dig(:campaign_analysis, :settings, :email_type) || determine_email_type,
      key_message: context[:key_message] || context.dig(:campaign_analysis, :settings, :key_message) || "",
      urgency_level: context.dig(:campaign_analysis, :settings, :urgency) || "medium",
      personalization_level: context.dig(:campaign_analysis, :settings, :personalization) || "basic"
    }
  end

  def default_email_goals
    case campaign.target_audience.downcase
    when /business|b2b|professional/
      "Generate qualified leads and nurture business relationships"
    when /customer|consumer|b2c/
      "Drive conversions and increase customer engagement"
    else
      "Increase brand awareness and drive meaningful actions"
    end
  end

  def process_ai_content(ai_content)
    Rails.logger.info "Processing AI content: #{ai_content.class} - #{ai_content.inspect}"

    # Try to parse JSON if it's a string
    parsed_content = if ai_content.is_a?(String)
      begin
        JSON.parse(ai_content)
      rescue JSON::ParserError => e
        Rails.logger.warn "Failed to parse AI content as JSON: #{e.message}"
        Rails.logger.warn "Raw content: #{ai_content}"
        nil
      end
    elsif ai_content.is_a?(Hash)
      ai_content
    else
      nil
    end

    if parsed_content.is_a?(Hash)
      result = {
        subject_line: parsed_content["subject_line"] || "#{campaign.name} - Important Update",
        preview_text: parsed_content["preview_text"] || "Open to learn more",
        content: parsed_content["content"] || parsed_content.to_s,
        from_name: determine_from_name,
        from_email: determine_from_email,
        settings: extract_email_settings(parsed_content)
      }
    else
      # Fallback for non-JSON content
      result = {
        subject_line: "#{campaign.name} - AI Generated Campaign",
        preview_text: "Powered by AI marketing automation",
        content: ai_content.to_s,
        from_name: determine_from_name,
        from_email: determine_from_email,
        settings: default_email_settings
      }
    end

    Rails.logger.info "Processed result: #{result.inspect}"
    result
  end

  def create_or_update_email_campaign(email_data)
    if @email_campaign
      @email_campaign.update!(email_data.except(:settings))
      update_email_settings(email_data[:settings])
    else
      @email_campaign = campaign.create_email_campaign!(email_data.except(:settings))
      update_email_settings(email_data[:settings])
    end
  end

  def determine_from_name
    campaign.settings.dig("email", "from_name") ||
      @tenant.settings.dig("email", "default_from_name") ||
      @tenant.name
  end

  def determine_from_email
    campaign.settings.dig("email", "from_email") ||
      @tenant.settings.dig("email", "default_from_email") ||
      "noreply@#{@tenant.subdomain}.com"
  end

  def extract_email_settings(ai_content)
    base_settings = default_email_settings

    if ai_content.is_a?(Hash)
      base_settings.merge({
        ai_generated: true,
        generation_timestamp: Time.current,
        cta: ai_content["cta"],
        tone: ai_content["tone"] || "professional",
        personalization_tokens: extract_personalization_tokens(ai_content["content"] || "")
      })
    else
      base_settings.merge({
        ai_generated: true,
        generation_timestamp: Time.current
      })
    end
  end

  def default_email_settings
    {
      track_opens: true,
      track_clicks: true,
      auto_responder: false,
      send_time_optimization: true,
      ai_generated: true
    }
  end

  def update_email_settings(settings)
    return unless settings && @email_campaign

    current_settings = @email_campaign.settings || {}
    @email_campaign.update!(settings: current_settings.merge(settings))
  end

  def extract_personalization_tokens(content)
    # Extract personalization tokens like {{first_name}}, {{company}}, etc.
    content.scan(/\{\{([^}]+)\}\}/).flatten.uniq
  end

  def build_optimization_prompt(current_content, optimization_context)
    performance_hints = optimization_context[:performance_data] ?
      "Current performance: #{optimization_context[:performance_data]}" :
      "No performance data available yet"

    <<~PROMPT
      Optimize this email campaign for better performance:

      Current Email:
      Subject: #{current_content[:subject_line]}
      From: #{current_content[:from_name]}
      Content: #{current_content[:content][0..500]}...

      Campaign Context:
      - Target Audience: #{campaign.target_audience}
      - Campaign Goals: #{campaign.settings.dig('goals') || 'Increase engagement'}
      - #{performance_hints}

      Please provide optimized versions focusing on:
      1. Subject line optimization (A/B test worthy)
      2. Content structure improvements
      3. Call-to-action optimization
      4. Personalization enhancements

      Format as JSON with keys: subject_line, content, cta, optimization_notes
    PROMPT
  end

  def process_optimization_results(optimized_content)
    begin
      parsed_content = JSON.parse(optimized_content)
      {
        subject_line: parsed_content["subject_line"],
        content: parsed_content["content"],
        cta: parsed_content["cta"],
        optimization_notes: parsed_content["optimization_notes"]
      }
    rescue JSON::ParserError
      # Fallback if AI doesn't return valid JSON
      {
        subject_line: @email_campaign.subject_line,
        content: optimized_content,
        optimization_notes: "AI optimization applied to content"
      }
    end
  end

  def update_email_campaign_with_optimizations(optimized_data)
    @email_campaign.update!(
      subject_line: optimized_data[:subject_line] || @email_campaign.subject_line,
      content: optimized_data[:content] || @email_campaign.content
    )

    # Store optimization history in settings
    optimization_history = @email_campaign.settings["optimization_history"] || []
    optimization_history << {
      timestamp: Time.current,
      changes: optimized_data,
      ai_generated: true
    }

    @email_campaign.update!(
      settings: @email_campaign.settings.merge({
        "optimization_history" => optimization_history,
        "last_optimized_at" => Time.current
      })
    )
  end

  def build_variant_prompt(base_content, variant_number)
    <<~PROMPT
      Create variant ##{variant_number} for A/B testing this email campaign:

      Original:
      Subject: #{base_content[:subject_line]}
      Content: #{base_content[:content][0..300]}...

      Campaign: #{campaign.name}
      Audience: #{campaign.target_audience}

      Create a significantly different variant that:
      1. Uses a different subject line approach
      2. Varies the opening hook
      3. Tests different persuasion techniques
      4. Maintains the core message

      Format as JSON: subject_line, content, variant_strategy
    PROMPT
  end

  def process_variant_content(variant_content, variant_number)
    begin
      parsed_variant = JSON.parse(variant_content)
      {
        variant_id: variant_number,
        subject_line: parsed_variant["subject_line"],
        content: parsed_variant["content"],
        strategy: parsed_variant["variant_strategy"],
        generated_at: Time.current
      }
    rescue JSON::ParserError
      {
        variant_id: variant_number,
        subject_line: "#{@email_campaign.subject_line} - Variant #{variant_number}",
        content: variant_content,
        strategy: "AI-generated alternative approach",
        generated_at: Time.current
      }
    end
  end

  def store_ab_test_variants(variants)
    ab_test_data = {
      base_campaign_id: @email_campaign.id,
      variants: variants,
      created_at: Time.current,
      status: "ready_for_testing"
    }

    @email_campaign.update!(
      settings: @email_campaign.settings.merge({
        "ab_test_variants" => ab_test_data
      })
    )
  end

  def collect_email_metrics
    campaign_metrics = campaign.campaign_metrics.recent(30)

    {
      total_sent: calculate_total_sent,
      open_rate: calculate_open_rate(campaign_metrics),
      click_rate: calculate_click_rate(campaign_metrics),
      conversion_rate: campaign.conversion_rate,
      unsubscribe_rate: calculate_unsubscribe_rate,
      bounce_rate: calculate_bounce_rate(campaign_metrics),
      revenue_per_email: calculate_revenue_per_email
    }
  end

  def calculate_total_sent
    # This would integrate with email service provider API
    campaign.campaign_metrics.sum(:email_opens) * 4 # Estimate based on typical open rates
  end

  def calculate_open_rate(metrics)
    total_opens = metrics.sum(:email_opens)
    total_sent = calculate_total_sent
    return 0.0 if total_sent.zero?

    (total_opens.to_f / total_sent * 100).round(2)
  end

  def calculate_click_rate(metrics)
    total_clicks = metrics.sum(:email_clicks)
    total_opens = metrics.sum(:email_opens)
    return 0.0 if total_opens.zero?

    (total_clicks.to_f / total_opens * 100).round(2)
  end

  def calculate_unsubscribe_rate
    # This would integrate with email service provider API
    0.5 # Placeholder - typical unsubscribe rate
  end

  def calculate_bounce_rate(metrics)
    total_bounces = metrics.sum(:email_bounces)
    total_sent = calculate_total_sent
    return 0.0 if total_sent.zero?

    (total_bounces.to_f / total_sent * 100).round(2)
  end

  def calculate_revenue_per_email
    total_revenue = campaign.total_revenue
    total_sent = calculate_total_sent
    return 0.0 if total_sent.zero?

    (total_revenue / total_sent).round(2)
  end

  def build_performance_analysis_prompt(performance_data)
    <<~PROMPT
      Analyze this email campaign performance and provide actionable recommendations:

      Campaign: #{campaign.name}
      Target Audience: #{campaign.target_audience}
      Campaign Duration: #{campaign.duration_in_days} days

      Performance Metrics:
      #{performance_data.to_json}

      Email Details:
      Subject: #{@email_campaign.subject_line}
      From: #{@email_campaign.from_name}
      Content Length: #{@email_campaign.content.length} characters

      Provide analysis on:
      1. Performance vs industry benchmarks
      2. Areas for improvement
      3. Specific optimization recommendations
      4. A/B test suggestions

      Format as JSON with detailed insights.
    PROMPT
  end

  def extract_recommendations_from_analysis(ai_analysis)
    begin
      parsed_analysis = JSON.parse(ai_analysis)
      parsed_analysis["recommendations"] || []
    rescue JSON::ParserError
      # Extract recommendations from text
      ai_analysis.split(/\d+\.|\n-|\n\*/).map(&:strip).reject(&:empty?)[1..5] || []
    end
  end

  def generate_next_actions(recommendations)
    base_actions = [
      "Review performance metrics weekly",
      "Test subject line variations",
      "Optimize send times based on audience engagement"
    ]

    base_actions + recommendations.map { |rec| "Implement: #{rec}" }[0..2]
  end

  # Error handling methods
  def handle_generation_error(error)
    error_message = "Email content generation failed: #{error.message}"
    Rails.logger.error error_message
    Rails.logger.error error.backtrace.join("\n")

    {
      status: "error",
      message: error_message,
      error_type: error.class.name
    }
  end

  def handle_optimization_error(error)
    {
      status: "error",
      message: "Email optimization failed: #{error.message}",
      error_type: error.class.name
    }
  end

  def handle_variant_generation_error(error)
    {
      status: "error",
      message: "A/B test variant generation failed: #{error.message}",
      error_type: error.class.name
    }
  end

  def handle_analysis_error(error)
    {
      status: "error",
      message: "Email performance analysis failed: #{error.message}",
      error_type: error.class.name
    }
  end
end
