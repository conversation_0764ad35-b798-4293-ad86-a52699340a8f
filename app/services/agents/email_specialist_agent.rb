# frozen_string_literal: true

module Agents
  # Email Specialist AI Agent - Handles all email marketing automation
  # Generates personalized email content, optimizes timing, and manages delivery
  #
  # Responsibilities:
  # - Generate AI-powered email content and subject lines
  # - Optimize send timing based on audience behavior
  # - Personalize content for different audience segments
  # - Manage email delivery and tracking
  # - Analyze performance and optimize future sends
  #
  class EmailSpecialistAgent
    include ActiveModel::Model
    include ActiveModel::Validations

    attr_reader :tenant, :ai_service

    validates :tenant, presence: true

    def initialize(tenant:, ai_service: nil)
      raise ArgumentError, "Tenant is required" if tenant.nil?

      @tenant = tenant
      @ai_service = ai_service || RubyLlmService.new(
        tenant: tenant,
        context: {
          task_type: "email_content_generation",
          brand_guidelines: tenant.settings.dig("brand_guidelines")
        },
        provider_strategy: :creative_content
      )
      @errors = ActiveModel::Errors.new(self)
    end

    # Execute email campaign with AI optimization
    def execute_campaign(campaign, coordination_data = {})
      return failure_result("Invalid agent configuration") unless valid?

      begin
        # Generate AI-powered email content
        email_content_data = generate_email_content(campaign, coordination_data)

        # Optimize send timing based on audience behavior
        optimal_timing = optimize_send_timing(campaign, coordination_data)

        # Create or update email campaign
        email_campaign = create_or_update_email_campaign(campaign, email_content_data, optimal_timing)

        # Apply personalization strategies
        personalization_result = apply_personalization(campaign, email_campaign, coordination_data)

        # Schedule delivery if ready
        delivery_result = schedule_delivery(email_campaign, optimal_timing)

        success_result(email_campaign, email_content_data, optimal_timing, personalization_result, delivery_result)

      rescue StandardError => e
        handle_execution_error(e, campaign)
        failure_result(e.message)
      end
    end

    # Generate performance insights for email campaigns
    def analyze_performance(campaign)
      email_campaign = campaign.email_campaign
      return { error: "No email campaign found" } unless email_campaign

      metrics = get_email_metrics(campaign)
      ai_insights = generate_ai_insights(campaign, metrics)

      {
        metrics: metrics,
        ai_insights: ai_insights,
        optimization_suggestions: generate_optimization_suggestions(metrics, ai_insights)
      }
    end

    private

    # Generate AI-powered email content
    def generate_email_content(campaign, coordination_data)
      context = build_email_context(campaign, coordination_data)
      prompt = build_email_content_prompt(context)

      response = @ai_service.generate_content(
        prompt,
        task_type: :creative_content,
        max_tokens: 4000,
        temperature: 0.7
      )

      if response.successful?
        JSON.parse(response.content)
      else
        Rails.logger.error "AI email content generation failed"
        fallback_email_content(campaign)
      end

    rescue JSON::ParserError => e
      Rails.logger.error "Failed to parse AI email content response: #{e.message}"
      fallback_email_content(campaign)
    end

    # Optimize send timing based on audience behavior and data
    def optimize_send_timing(campaign, coordination_data)
      # Get historical performance data
      historical_data = get_historical_send_data

      # Analyze audience timezone and behavior patterns
      audience_patterns = analyze_audience_patterns(campaign)

      # Use AI to determine optimal timing
      timing_context = {
        historical_data: historical_data,
        audience_patterns: audience_patterns,
        campaign_type: campaign.campaign_type,
        urgency: coordination_data["timeline"],
        target_timezone: determine_primary_timezone(campaign)
      }

      optimal_time = determine_optimal_send_time(timing_context)

      {
        recommended_send_time: optimal_time,
        timezone: timing_context[:target_timezone],
        confidence_score: calculate_timing_confidence(timing_context),
        reasoning: generate_timing_reasoning(timing_context, optimal_time)
      }
    end

    # Create or update email campaign record
    def create_or_update_email_campaign(campaign, content_data, timing_data)
      email_campaign = campaign.email_campaign || campaign.build_email_campaign

      email_campaign.assign_attributes(
        subject_line: select_best_subject_line(content_data["subject_lines"]),
        preview_text: content_data["preview_text"] || generate_preview_text(content_data["email_content"]),
        content: content_data["email_content"],
        from_name: determine_from_name(campaign),
        from_email: determine_from_email(campaign),
        settings: build_email_settings(content_data, timing_data)
      )

      email_campaign.save!
      email_campaign
    end

    # Apply personalization strategies
    def apply_personalization(campaign, email_campaign, coordination_data)
      personalization_strategy = coordination_data["personalization_strategy"] || "basic"

      case personalization_strategy
      when "demographic_based"
        apply_demographic_personalization(campaign, email_campaign)
      when "behavioral_based"
        apply_behavioral_personalization(campaign, email_campaign)
      when "emotional_based"
        apply_emotional_personalization(campaign, email_campaign)
      else
        apply_basic_personalization(campaign, email_campaign)
      end
    end

    # Schedule email delivery
    def schedule_delivery(email_campaign, timing_data)
      send_time = timing_data[:recommended_send_time]

      if send_time.future?
        # Schedule for future delivery
        EmailDeliveryJob.set(wait_until: send_time).perform_later(
          email_campaign_id: email_campaign.id,
          tenant_id: tenant.id
        )

        { status: "scheduled", send_time: send_time }
      else
        # Send immediately
        EmailDeliveryJob.perform_later(
          email_campaign_id: email_campaign.id,
          tenant_id: tenant.id
        )

        { status: "immediate", send_time: Time.current }
      end
    end

    # Build context for email content generation
    def build_email_context(campaign, coordination_data)
      base_context = {
        campaign_name: campaign.name,
        campaign_type: campaign.campaign_type,
        target_audience: campaign.target_audience,
        emotional_tone: campaign.emotional_tone,
        budget_cents: campaign.budget_cents,
        focus_areas: coordination_data["focus_areas"] || [],
        timeline: coordination_data["timeline"]
      }

      # Add audience insights
      if campaign.audiences.any?
        base_context[:audience_insights] = campaign.audiences.map do |audience|
          {
            name: audience.name,
            demographics: audience.demographics,
            preferences: audience.preferences,
            cultural_attributes: audience.cultural_attributes
          }
        end
      end

      # Add emotional intelligence data
      emotional_data = get_emotional_intelligence_insights
      base_context[:emotional_insights] = emotional_data if emotional_data.present?

      base_context
    end

    # Build AI prompt for email content generation
    def build_email_content_prompt(context)
      <<~PROMPT
        You are an expert Email Marketing AI Specialist creating a highly effective email campaign.

        Campaign Context:
        #{context.to_json}

        Generate a comprehensive email campaign as JSON with this structure:
        {
          "subject_lines": [
            "Primary subject line",
            "Alternative subject line 1",#{' '}
            "Alternative subject line 2"
          ],
          "preview_text": "Engaging preview text that complements the subject line",
          "email_content": "Full HTML email content with personalization placeholders",
          "call_to_action": "Primary CTA text",
          "secondary_cta": "Optional secondary CTA",
          "personalization_strategy": "demographic_based|behavioral_based|emotional_based",
          "content_sections": {
            "header": "Header content",
            "main_message": "Primary message content",
            "benefits": ["Benefit 1", "Benefit 2", "Benefit 3"],
            "social_proof": "Testimonial or social proof content",
            "footer": "Footer content"
          },
          "design_recommendations": {
            "color_scheme": "primary_color_hex",
            "layout_style": "clean|modern|classic",
            "image_suggestions": ["image_description_1", "image_description_2"]
          }
        }

        Requirements:
        - Subject lines should be 6-10 words, compelling and relevant
        - Email content should be scannable with clear hierarchy
        - Include emotional triggers based on the emotional tone
        - Respect cultural context and audience preferences
        - Include clear, action-oriented CTAs
        - Optimize for mobile readability
        - Include personalization placeholders like {{first_name}}, {{company}}
      PROMPT
    end

    # Select the best subject line using AI scoring
    def select_best_subject_line(subject_lines)
      return subject_lines.first if subject_lines.length <= 1

      # Score each subject line based on various factors
      scored_lines = subject_lines.map do |line|
        {
          text: line,
          score: calculate_subject_line_score(line)
        }
      end

      scored_lines.max_by { |line| line[:score] }[:text]
    end

    # Calculate subject line effectiveness score
    def calculate_subject_line_score(subject_line)
      score = 0

      # Length optimization (6-10 words is optimal)
      word_count = subject_line.split.length
      score += case word_count
      when 6..10 then 10
      when 4..12 then 7
      else 3
      end

      # Emotional words boost
      emotional_words = %w[exclusive limited free new amazing transform unlock discover]
      score += subject_line.downcase.split.count { |word| emotional_words.include?(word) } * 2

      # Urgency indicators
      urgency_words = %w[today now limited hurry deadline expires]
      score += subject_line.downcase.split.count { |word| urgency_words.include?(word) } * 1.5

      # Avoid spam triggers (negative score)
      spam_words = %w[buy cheap deal sale money cash prize winner]
      score -= subject_line.downcase.split.count { |word| spam_words.include?(word) } * 3

      score
    end

    # Generate preview text from email content
    def generate_preview_text(email_content)
      # Extract first meaningful sentence from email content
      text_content = ActionView::Base.full_sanitizer.sanitize(email_content)
      first_sentence = text_content.split(".").first&.strip

      return first_sentence if first_sentence && first_sentence.length.between?(30, 90)

      # Fallback to truncated content
      text_content.truncate(85)
    end

    # Determine optimal send time using AI and data analysis
    def determine_optimal_send_time(timing_context)
      # Base optimal times by industry/audience
      base_times = {
        "business" => [ 9, 14, 16 ], # 9 AM, 2 PM, 4 PM
        "consumer" => [ 8, 12, 19 ], # 8 AM, 12 PM, 7 PM
        "international" => [ 10, 15 ] # 10 AM, 3 PM
      }

      audience_type = determine_audience_type(timing_context)
      optimal_hours = base_times[audience_type] || base_times["business"]

      # Factor in historical data if available
      if timing_context[:historical_data].any?
        historical_optimal = analyze_historical_optimal_times(timing_context[:historical_data])
        optimal_hours = blend_optimal_times(optimal_hours, historical_optimal)
      end

      # Choose the best hour and add some minutes for natural timing
      best_hour = optimal_hours.first
      random_minutes = [ 0, 15, 30, 45 ].sample

      # Calculate next occurrence of this time
      target_time = Time.current.in_time_zone(timing_context[:target_timezone])
                        .beginning_of_day + best_hour.hours + random_minutes.minutes

      # If time has passed today, schedule for tomorrow
      target_time += 1.day if target_time < Time.current

      target_time
    end

    # Get emotional intelligence insights for email optimization
    def get_emotional_intelligence_insights
      return {} unless tenant.customer_emotional_states.any?

      recent_states = tenant.customer_emotional_states
                            .where(last_interaction_at: 7.days.ago..)
                            .limit(500)

      return {} if recent_states.empty?

      {
        dominant_emotion: recent_states.group(:current_emotion).count.max_by(&:last).first,
        average_confidence: recent_states.average(:confidence_score),
        emotional_distribution: recent_states.group(:current_emotion).count,
        trending_emotions: analyze_trending_emotions(recent_states)
      }
    end

    # Apply demographic-based personalization
    def apply_demographic_personalization(campaign, email_campaign)
      audiences = campaign.audiences
      return { strategy: "none" } if audiences.empty?

      personalization_rules = audiences.map do |audience|
        {
          audience_id: audience.id,
          name_personalization: true,
          demographic_content: generate_demographic_content(audience),
          cultural_adaptations: generate_cultural_adaptations(audience)
        }
      end

      { strategy: "demographic", rules: personalization_rules }
    end

    # Apply emotional-based personalization using emotional intelligence data
    def apply_emotional_personalization(campaign, email_campaign)
      emotional_data = get_emotional_intelligence_insights
      return { strategy: "none" } if emotional_data.empty?

      dominant_emotion = emotional_data[:dominant_emotion]

      emotional_adaptations = {
        emotion: dominant_emotion,
        tone_adjustments: generate_tone_adjustments(dominant_emotion),
        content_modifications: generate_emotional_content_modifications(dominant_emotion),
        cta_optimization: generate_emotional_cta_optimization(dominant_emotion)
      }

      { strategy: "emotional", adaptations: emotional_adaptations }
    end

    # Helper methods for various calculations and data analysis
    def get_historical_send_data
      # Get last 30 days of email performance data
      CampaignMetric.joins(campaign: :email_campaign)
                    .where(campaign: { tenant: tenant })
                    .where(metric_date: 30.days.ago..Date.current)
                    .group_by_hour_of_day(:created_at)
                    .average(:email_opens)
    end

    def analyze_audience_patterns(campaign)
      # Analyze when the audience is most active based on available data
      {
        primary_timezone: determine_primary_timezone(campaign),
        activity_patterns: get_audience_activity_patterns(campaign),
        device_preferences: get_device_preferences(campaign)
      }
    end

    def determine_primary_timezone(campaign)
      # Default to tenant timezone or UTC
      tenant.settings.dig("timezone") || "UTC"
    end

    def fallback_email_content(campaign)
      {
        "subject_lines" => [ "Important Update from #{tenant.name}" ],
        "email_content" => generate_basic_email_template(campaign),
        "call_to_action" => "Learn More",
        "personalization_strategy" => "basic"
      }
    end

    # Result helper methods
    def success_result(email_campaign, content_data, timing_data, personalization_result, delivery_result)
      {
        success: true,
        email_campaign_id: email_campaign.id,
        subject_lines: content_data["subject_lines"],
        selected_subject: email_campaign.subject_line,
        optimal_send_time: timing_data[:recommended_send_time],
        personalization_applied: personalization_result[:strategy] != "none",
        delivery_status: delivery_result[:status],
        optimization_applied: true,
        timestamp: Time.current
      }
    end

    def failure_result(error_message)
      {
        success: false,
        error: error_message,
        timestamp: Time.current
      }
    end

    def handle_execution_error(error, campaign)
      Rails.logger.error "Email Specialist Agent failed for campaign #{campaign.id}: #{error.message}"
      Rails.logger.error error.backtrace.join("\n")

      # Create error record for monitoring
      AlertLog.create!(
        tenant: tenant,
        alert_type: "agent_failure",
        severity: "high",
        message: "Email Specialist Agent failed: #{error.message}",
        metadata: {
          campaign_id: campaign.id,
          error_class: error.class.name,
          backtrace: error.backtrace.first(5)
        }
      )
    end

    # Additional helper methods would continue here...
    # (Truncated for brevity, but would include all the referenced private methods)
  end
end
