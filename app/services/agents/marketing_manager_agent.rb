# frozen_string_literal: true

module Agents
  # Marketing Manager AI Agent - Central orchestrator for all marketing AI agents
  # Coordinates specialist agents (SEO, Email, Social) for comprehensive campaign execution
  #
  # Responsibilities:
  # - Analyze campaign context and requirements
  # - Determine optimal marketing strategy using AI
  # - Delegate tasks to specialist agents based on strategy
  # - Monitor and coordinate execution across all agents
  # - Aggregate results and optimize performance
  #
  # Usage:
  #   agent = Agents::MarketingManagerAgent.new(tenant: tenant, campaign: campaign)
  #   result = agent.orchestrate_campaign
  #
  class MarketingManagerAgent
    include ActiveModel::Model
    include ActiveModel::Validations

    attr_reader :tenant, :campaign, :specialist_agents, :ai_service, :workflow

    validates :tenant, presence: true
    validates :campaign, presence: true

    def initialize(tenant:, campaign:, ai_service: nil)
      raise ArgumentError, "Tenant is required" if tenant.nil?
      raise ArgumentError, "Campaign is required" if campaign.nil?

      @tenant = tenant
      @campaign = campaign
      @ai_service = ai_service || RubyLlmService.new(
        tenant: tenant,
        context: build_ai_context,
        provider_strategy: :balanced
      )
      @specialist_agents = load_specialist_agents
      @errors = ActiveModel::Errors.new(self)
    end

    # Main orchestration method - coordinates entire campaign execution
    def orchestrate_campaign
      return failure_result("Invalid agent configuration") unless valid?

      create_orchestration_workflow

      begin
        context = analyze_campaign_context
        strategy = determine_strategy(context)
        delegated_agents = delegate_tasks(strategy)

        update_workflow_success(strategy, delegated_agents)
        success_result(strategy, delegated_agents)

      rescue StandardError => e
        handle_orchestration_error(e)
        failure_result(e.message)
      end
    end

    # Monitor execution of all delegated specialist agents
    def monitor_execution
      workflows = delegated_workflows
      return { status: "no_delegated_agents" } if workflows.empty?

      total_progress = workflows.sum(&:progress_percent)
      average_progress = total_progress / workflows.count

      status_counts = workflows.group_by(&:status).transform_values(&:count)
      issues = identify_issues(workflows)

      {
        overall_progress: average_progress,
        completed_agents: status_counts["completed"] || 0,
        active_agents: status_counts["in_progress"] || 0,
        pending_agents: status_counts["pending"] || 0,
        failed_agents: status_counts["failed"] || 0,
        total_agents: workflows.count,
        issues: issues,
        last_updated: workflows.maximum(:updated_at)
      }
    end

    # Get real-time insights and recommendations
    def get_insights
      execution_status = monitor_execution
      emotional_data = get_emotional_intelligence_data
      performance_metrics = get_performance_metrics

      {
        execution_status: execution_status,
        emotional_insights: emotional_data,
        performance_metrics: performance_metrics,
        recommendations: generate_recommendations(execution_status, emotional_data)
      }
    end

    private

    # Build AI context for the service
    def build_ai_context
      {
        brand_guidelines: campaign.settings.dig("brand_guidelines"),
        target_audience: campaign.target_audience,
        task_type: "marketing_orchestration"
      }
    end

    # Create the main orchestration workflow record
    def create_orchestration_workflow
      @workflow = AgentWorkflow.create!(
        campaign: campaign,
        tenant: tenant,
        workflow_type: "marketing_orchestration",
        status: "in_progress",
        context_data: {
          campaign_data: campaign_summary,
          target_audience: campaign.target_audience,
          initiated_at: Time.current
        }
      )
    end

    # Analyze campaign context to inform AI strategy decisions
    def analyze_campaign_context
      base_context = {
        campaign_type: campaign.campaign_type,
        target_audience: campaign.target_audience,
        budget_cents: campaign.budget_cents,
        start_date: campaign.start_date,
        end_date: campaign.end_date,
        emotional_tone: campaign.emotional_tone,
        cultural_relevance_score: campaign.cultural_relevance_score,
        settings: campaign.settings
      }

      # Enrich with emotional intelligence data if available
      emotional_insights = get_emotional_intelligence_data
      base_context[:emotional_insights] = emotional_insights if emotional_insights.present?

      # Add audience insights if available
      if campaign.audiences.any?
        base_context[:audience_insights] = campaign.audiences.map do |audience|
          {
            name: audience.name,
            demographics: audience.demographics,
            cultural_attributes: audience.cultural_attributes,
            preferences: audience.preferences
          }
        end
      end

      base_context
    end

    # Use AI to determine optimal marketing strategy
    def determine_strategy(context)
      prompt = build_strategy_prompt(context)

      response = @ai_service.generate_content(
        prompt,
        task_type: :creative_content,
        max_tokens: 4000,
        temperature: 0.7
      )

      if response.successful?
        JSON.parse(response.content)
      else
        Rails.logger.error "AI strategy generation failed"
        fallback_strategy(context)
      end

    rescue JSON::ParserError => e
      Rails.logger.error "Failed to parse AI strategy response: #{e.message}"
      fallback_strategy(context)
    end

    # Delegate tasks to specialist agents based on strategy
    def delegate_tasks(strategy)
      priority_agents = strategy["priority_agents"] || []
      coordination_plan = strategy["coordination_plan"] || {}
      delegated_agents = []

      priority_agents.each do |agent_type|
        coordination_data = coordination_plan[agent_type] || {}

        case agent_type
        when "email"
          Agents::EmailSpecialistJob.perform_later(
            campaign_id: campaign.id,
            tenant_id: tenant.id,
            coordination_data: coordination_data
          )
        when "social"
          Agents::SocialMediaSpecialistJob.perform_later(
            campaign_id: campaign.id,
            tenant_id: tenant.id,
            coordination_data: coordination_data
          )
        when "seo"
          Agents::SeoSpecialistJob.perform_later(
            campaign_id: campaign.id,
            tenant_id: tenant.id,
            coordination_data: coordination_data
          )
        end

        delegated_agents << agent_type
        create_delegation_workflow(agent_type, coordination_data)
      end

      delegated_agents
    end

    # Load all specialist agents for this tenant
    def load_specialist_agents
      {
        "email" => Agents::EmailSpecialistAgent.new(tenant: tenant),
        "social" => Agents::SocialMediaSpecialistAgent.new(tenant: tenant),
        "seo" => Agents::SeoSpecialistAgent.new(tenant: tenant)
      }
    end

    # Build AI prompt for strategy determination
    def build_strategy_prompt(context)
      <<~PROMPT
        You are an expert Marketing Manager AI coordinating a comprehensive marketing campaign.

        Campaign Context:
        #{context.to_json}

        Please analyze this campaign and provide a strategic coordination plan as JSON with this structure:
        {
          "strategy": "strategy_name",
          "priority_agents": ["agent1", "agent2", "agent3"],
          "coordination_plan": {
            "email": {
              "priority": 1,
              "timeline": "24_hours",
              "focus_areas": ["engagement", "conversion"],
              "specific_tasks": ["content_generation", "list_segmentation"]
            },
            "social": {
              "priority": 2,
              "timeline": "48_hours",#{' '}
              "focus_areas": ["awareness", "engagement"],
              "specific_tasks": ["content_creation", "community_management"]
            },
            "seo": {
              "priority": 3,
              "timeline": "72_hours",
              "focus_areas": ["visibility", "organic_traffic"],
              "specific_tasks": ["keyword_optimization", "content_strategy"]
            }
          },
          "success_metrics": ["metric1", "metric2"],
          "optimization_triggers": ["condition1", "condition2"],
          "budget_allocation": {
            "email": 30,
            "social": 40,
            "seo": 30
          }
        }

        Consider the emotional intelligence data, audience insights, and cultural relevance when making decisions.
        Prioritize agents based on campaign type, target audience, and available budget.
      PROMPT
    end

    # Create workflow record for delegated agent
    def create_delegation_workflow(agent_type, coordination_data)
      AgentWorkflow.create!(
        campaign: campaign,
        tenant: tenant,
        workflow_type: "#{agent_type}_specialist",
        status: "pending",
        context_data: {
          coordination_data: coordination_data,
          delegated_by: workflow.id,
          delegated_at: Time.current
        }
      )
    end

    # Get emotional intelligence data for the campaign
    def get_emotional_intelligence_data
      return {} unless tenant.customer_emotional_states.any?

      emotional_states = tenant.customer_emotional_states
                               .where(last_interaction_at: 30.days.ago..)
                               .limit(1000)

      return {} if emotional_states.empty?

      {
        dominant_emotions: emotional_states.group(:current_emotion).count,
        average_confidence: emotional_states.average(:confidence_score),
        emotional_trends: analyze_emotional_trends(emotional_states),
        audience_mood: determine_audience_mood(emotional_states)
      }
    end

    # Analyze emotional trends over time
    def analyze_emotional_trends(emotional_states)
      emotional_states
        .group_by_day(:last_interaction_at, last: 7)
        .group(:current_emotion)
        .count
    end

    # Determine overall audience mood
    def determine_audience_mood(emotional_states)
      recent_emotions = emotional_states.where(last_interaction_at: 7.days.ago..)

      return "neutral" if recent_emotions.empty?

      emotion_weights = {
        "excited" => 5, "happy" => 4, "satisfied" => 3, "neutral" => 2,
        "concerned" => 1, "frustrated" => 0, "angry" => -1
      }

      total_weight = recent_emotions.sum { |state| emotion_weights[state.current_emotion] || 2 }
      average_weight = total_weight.to_f / recent_emotions.count

      case average_weight
      when 4..5 then "very_positive"
      when 3..4 then "positive"
      when 2..3 then "neutral"
      when 1..2 then "slightly_negative"
      else "negative"
      end
    end

    # Get current performance metrics
    def get_performance_metrics
      recent_metrics = CampaignMetric.where(
        campaign: campaign,
        metric_date: 30.days.ago..Date.current
      )

      return {} if recent_metrics.empty?

      {
        total_impressions: recent_metrics.sum(:impressions),
        total_clicks: recent_metrics.sum(:clicks),
        total_conversions: recent_metrics.sum(:conversions),
        average_ctr: calculate_ctr(recent_metrics),
        average_conversion_rate: calculate_conversion_rate(recent_metrics),
        total_revenue: recent_metrics.sum(:revenue_cents),
        roi: calculate_roi(recent_metrics)
      }
    end

    # Generate AI-powered recommendations
    def generate_recommendations(execution_status, emotional_data)
      recommendations = []

      # Performance-based recommendations
      if execution_status[:failed_agents] > 0
        recommendations << {
          type: "agent_failure",
          priority: "high",
          message: "Some specialist agents have failed. Consider retry or alternative approaches.",
          action: "investigate_failures"
        }
      end

      # Emotional intelligence recommendations
      if emotional_data[:audience_mood] == "negative"
        recommendations << {
          type: "emotional_adjustment",
          priority: "high",
          message: "Audience mood is negative. Consider adjusting tone and messaging.",
          action: "adjust_emotional_tone"
        }
      end

      recommendations
    end

    # Get all workflows delegated by this orchestration
    def delegated_workflows
      AgentWorkflow.where(
        campaign: campaign,
        tenant: tenant
      ).where("context_data->>'delegated_by' = ?", workflow&.id&.to_s)
    end

    # Identify issues in delegated workflows
    def identify_issues(workflows)
      issues = []

      workflows.each do |workflow|
        if workflow.status == "failed"
          issues << {
            agent_type: workflow.workflow_type,
            error: workflow.error_details["error"] || "Unknown error",
            workflow_id: workflow.id
          }
        elsif workflow.status == "in_progress" && workflow.created_at < 24.hours.ago
          issues << {
            agent_type: workflow.workflow_type,
            error: "Agent execution taking longer than expected",
            workflow_id: workflow.id
          }
        end
      end

      issues
    end

    # Fallback strategy when AI fails
    def fallback_strategy(context)
      {
        "strategy" => "sequential_execution",
        "priority_agents" => determine_fallback_agents(context),
        "coordination_plan" => build_fallback_coordination_plan,
        "success_metrics" => [ "engagement_rate", "conversion_rate" ],
        "budget_allocation" => { "email" => 40, "social" => 35, "seo" => 25 }
      }
    end

    def determine_fallback_agents(context)
      agents = []
      agents << "email" if context[:campaign_type].in?([ "email", "integrated" ])
      agents << "social" if context[:campaign_type].in?([ "social", "integrated" ])
      agents << "seo" if context[:campaign_type].in?([ "seo", "integrated" ])
      agents.presence || [ "email", "social", "seo" ]
    end

    def build_fallback_coordination_plan
      {
        "email" => { "priority" => 1, "timeline" => "24_hours" },
        "social" => { "priority" => 2, "timeline" => "48_hours" },
        "seo" => { "priority" => 3, "timeline" => "72_hours" }
      }
    end

    # Helper methods for metrics calculation
    def calculate_ctr(metrics)
      total_clicks = metrics.sum(:clicks)
      total_impressions = metrics.sum(:impressions)
      return 0 if total_impressions.zero?

      (total_clicks.to_f / total_impressions * 100).round(2)
    end

    def calculate_conversion_rate(metrics)
      total_conversions = metrics.sum(:conversions)
      total_clicks = metrics.sum(:clicks)
      return 0 if total_clicks.zero?

      (total_conversions.to_f / total_clicks * 100).round(2)
    end

    def calculate_roi(metrics)
      total_revenue = metrics.sum(:revenue_cents)
      total_cost = metrics.sum(:cost_cents)
      return 0 if total_cost.zero?

      ((total_revenue - total_cost).to_f / total_cost * 100).round(2)
    end

    # Result helper methods
    def success_result(strategy, delegated_agents)
      {
        success: true,
        workflow_id: workflow.id,
        strategy: strategy["strategy"],
        delegated_agents: delegated_agents,
        coordination_plan: strategy["coordination_plan"],
        timestamp: Time.current
      }
    end

    def failure_result(error_message)
      {
        success: false,
        error: error_message,
        workflow_id: workflow&.id,
        timestamp: Time.current
      }
    end

    def campaign_summary
      {
        id: campaign.id,
        name: campaign.name,
        type: campaign.campaign_type,
        status: campaign.status
      }
    end

    def update_workflow_success(strategy, delegated_agents)
      workflow.update!(
        status: "completed",
        progress_percent: 100,
        results: {
          strategy: strategy,
          delegated_agents: delegated_agents,
          completed_at: Time.current
        }
      )
    end

    def handle_orchestration_error(error)
      Rails.logger.error "Marketing Manager Agent failed: #{error.message}"
      Rails.logger.error error.backtrace.join("\n")

      workflow&.update!(
        status: "failed",
        error_details: {
          error: error.message,
          backtrace: error.backtrace.first(10),
          failed_at: Time.current
        }
      )
    end
  end
end
