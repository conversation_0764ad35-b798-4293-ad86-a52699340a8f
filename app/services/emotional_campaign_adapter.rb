# frozen_string_literal: true

# EmotionalCampaignAdapter - Adapts marketing campaigns based on customer emotional states
#
# This service implements the core "Marketing Therapist AI" functionality by:
# - Automatically adjusting campaign content, timing, and approach based on emotional state
# - Providing real-time personalization that responds to customer emotions
# - Maintaining empathetic marketing that builds genuine connections
# - Preventing emotional mismatch that could damage customer relationships
class EmotionalCampaignAdapter
  include ActiveModel::Model
  include ActiveModel::Attributes

  attr_accessor :emotional_state, :tenant, :campaign, :options

  # Campaign adjustment types
  ADJUSTMENT_TYPES = %w[
    content_tone messaging_style timing_delay channel_selection
    emotional_intensity personalization_level interaction_approach
  ].freeze

  # Emotional timing strategies
  TIMING_STRATEGIES = {
    immediate: { delay: 0.minutes, description: "Send immediately - customer is receptive" },
    short_delay: { delay: 30.minutes, description: "Brief pause to avoid oversaturation" },
    processing_time: { delay: 2.hours, description: "Allow emotional processing time" },
    cooldown_period: { delay: 6.hours, description: "Wait for emotional state to stabilize" },
    significant_delay: { delay: 24.hours, description: "Significant pause due to negative emotion" },
    hold_indefinitely: { delay: nil, description: "Do not send - customer not receptive" }
  }.freeze

  def initialize(emotional_state:, tenant:, campaign: nil, options: {})
    @emotional_state = emotional_state
    @tenant = tenant
    @campaign = campaign
    @options = options.with_indifferent_access
  end

  # Generate comprehensive campaign adjustments based on emotional state
  def generate_adjustments
    return error_result("Missing emotional state") unless emotional_state.present?

    begin
      adjustments = {
        timing_adjustment: calculate_timing_adjustment,
        content_adjustments: generate_content_adjustments,
        channel_adjustments: generate_channel_adjustments,
        personalization_adjustments: generate_personalization_adjustments,
        risk_mitigation: identify_risk_mitigation_strategies,
        success_optimization: identify_success_optimization_opportunities,
        emotional_journey: plan_emotional_journey_adjustments
      }

      {
        success: true,
        customer_emotional_state: format_emotional_state,
        adjustments: adjustments,
        implementation_priority: calculate_implementation_priority(adjustments),
        expected_impact: predict_adjustment_impact(adjustments),
        monitoring_recommendations: generate_monitoring_recommendations
      }
    rescue StandardError => e
      Rails.logger.error "EmotionalCampaignAdapter error: #{e.message}"
      error_result("Adjustment generation failed: #{e.message}")
    end
  end

  # Apply adjustments to a specific campaign
  def apply_to_campaign(campaign)
    return error_result("No campaign provided") unless campaign.present?

    @campaign = campaign
    adjustments = generate_adjustments

    return adjustments unless adjustments[:success]

    begin
      applied_adjustments = {}

      # Apply timing adjustments
      if adjustments[:adjustments][:timing_adjustment][:requires_delay]
        applied_adjustments[:timing] = apply_timing_adjustments(campaign, adjustments[:adjustments][:timing_adjustment])
      end

      # Apply content adjustments
      if adjustments[:adjustments][:content_adjustments][:modifications].any?
        applied_adjustments[:content] = apply_content_adjustments(campaign, adjustments[:adjustments][:content_adjustments])
      end

      # Apply channel adjustments
      if adjustments[:adjustments][:channel_adjustments][:channel_changes].any?
        applied_adjustments[:channels] = apply_channel_adjustments(campaign, adjustments[:adjustments][:channel_adjustments])
      end

      # Apply personalization adjustments
      if adjustments[:adjustments][:personalization_adjustments][:personalization_changes].any?
        applied_adjustments[:personalization] = apply_personalization_adjustments(campaign, adjustments[:adjustments][:personalization_adjustments])
      end

      # Log the emotional adaptation
      log_emotional_adaptation(campaign, adjustments, applied_adjustments)

      {
        success: true,
        campaign_id: campaign.id,
        applied_adjustments: applied_adjustments,
        original_adjustments: adjustments,
        adaptation_timestamp: Time.current,
        next_review_at: calculate_next_review_time
      }
    rescue StandardError => e
      Rails.logger.error "Failed to apply emotional adjustments to campaign #{campaign.id}: #{e.message}"
      error_result("Failed to apply adjustments: #{e.message}")
    end
  end

  # Generate real-time campaign recommendations
  def real_time_recommendations
    return error_result("Missing emotional state") unless emotional_state.present?

    recommendations = []

    # Receptivity recommendations
    if emotional_state.receptive_to_marketing?
      recommendations << create_recommendation(
        type: "opportunity",
        priority: "high",
        action: "Customer is receptive - optimal time for engagement",
        details: "Current emotional state (#{emotional_state.current_emotion}) is favorable for marketing"
      )
    else
      recommendations << create_recommendation(
        type: "caution",
        priority: "high", 
        action: "Customer not receptive - avoid marketing contact",
        details: "Current emotional state (#{emotional_state.current_emotion}) requires sensitive approach"
      )
    end

    # Confidence-based recommendations
    if emotional_state.confidence_score < 60.0
      recommendations << create_recommendation(
        type: "data_collection",
        priority: "medium",
        action: "Collect more behavioral data for better emotional insights",
        details: "Low confidence (#{emotional_state.confidence_score}%) in emotional state detection"
      )
    end

    # Emotional journey recommendations
    if emotional_state.significant_emotional_change?
      recommendations << create_recommendation(
        type: "adaptation",
        priority: "high",
        action: "Adapt campaigns due to significant emotional change",
        details: "Customer emotional state has changed significantly - review all active campaigns"
      )
    end

    # Timing recommendations
    timing_rec = generate_timing_recommendation
    recommendations << timing_rec if timing_rec

    {
      success: true,
      recommendations: recommendations,
      emotional_context: format_emotional_state,
      generated_at: Time.current
    }
  end

  private

  def calculate_timing_adjustment
    emotion = emotional_state.current_emotion
    intensity = emotional_state.emotional_intensity
    confidence = emotional_state.confidence_score

    # Determine base timing strategy
    strategy = determine_timing_strategy(emotion, intensity)
    
    # Adjust based on confidence
    adjusted_strategy = adjust_for_confidence(strategy, confidence)

    {
      original_emotion: emotion,
      intensity: intensity,
      confidence: confidence,
      recommended_strategy: adjusted_strategy[:strategy],
      delay_duration: adjusted_strategy[:delay],
      reasoning: adjusted_strategy[:reasoning],
      requires_delay: adjusted_strategy[:delay] && adjusted_strategy[:delay] > 0,
      send_after: adjusted_strategy[:delay] ? Time.current + adjusted_strategy[:delay] : nil
    }
  end

  def determine_timing_strategy(emotion, intensity)
    case emotion
    when "joy"
      case intensity
      when "intense" then { strategy: :short_delay, reasoning: "High joy but avoid overwhelming" }
      else { strategy: :immediate, reasoning: "Customer is happy and receptive" }
      end
    when "trust"
      { strategy: :immediate, reasoning: "Customer trusts brand - excellent timing" }
    when "anticipation"
      case intensity
      when "intense" then { strategy: :immediate, reasoning: "Strike while anticipation is high" }
      else { strategy: :short_delay, reasoning: "Build anticipation slightly more" }
      end
    when "surprise"
      { strategy: :processing_time, reasoning: "Allow time to process surprising information" }
    when "fear"
      case intensity
      when "subtle" then { strategy: :cooldown_period, reasoning: "Mild fear - wait for calming" }
      else { strategy: :significant_delay, reasoning: "Strong fear - extended waiting period" }
      end
    when "sadness"
      { strategy: :significant_delay, reasoning: "Respect emotional state - avoid marketing during sadness" }
    when "anger"
      case intensity
      when "subtle" then { strategy: :cooldown_period, reasoning: "Wait for anger to subside" }
      else { strategy: :hold_indefinitely, reasoning: "Strong anger - do not engage until resolved" }
      end
    when "disgust"
      { strategy: :hold_indefinitely, reasoning: "Customer has negative brand association - focus on relationship repair" }
    else
      { strategy: :short_delay, reasoning: "Unknown emotion - conservative approach" }
    end
  end

  def adjust_for_confidence(base_strategy, confidence)
    strategy_config = TIMING_STRATEGIES[base_strategy[:strategy]]
    
    # If confidence is low, be more conservative
    if confidence < 60.0
      if base_strategy[:strategy] == :immediate
        adjusted_strategy = :short_delay
        adjusted_reasoning = "#{base_strategy[:reasoning]} (reduced confidence - adding safety delay)"
      elsif base_strategy[:strategy] == :short_delay
        adjusted_strategy = :processing_time
        adjusted_reasoning = "#{base_strategy[:reasoning]} (reduced confidence - extending delay)"
      else
        adjusted_strategy = base_strategy[:strategy]
        adjusted_reasoning = base_strategy[:reasoning]
      end
    else
      adjusted_strategy = base_strategy[:strategy]
      adjusted_reasoning = base_strategy[:reasoning]
    end

    adjusted_config = TIMING_STRATEGIES[adjusted_strategy]
    
    {
      strategy: adjusted_strategy,
      delay: adjusted_config[:delay],
      reasoning: adjusted_reasoning,
      confidence_adjusted: confidence < 60.0
    }
  end

  def generate_content_adjustments
    emotion = emotional_state.current_emotion
    intensity = emotional_state.emotional_intensity
    approach = emotional_state.recommended_interaction_approach

    modifications = []

    # Tone adjustments
    modifications << create_content_modification(
      type: "tone",
      current: "default",
      recommended: approach[:tone],
      reasoning: "Adjust tone to match customer's emotional state (#{emotion})"
    )

    # Messaging style adjustments
    modifications << create_content_modification(
      type: "messaging_style",
      current: "standard",
      recommended: approach[:messaging_style],
      reasoning: "Adapt messaging style for emotional resonance"
    )

    # Content type adjustments
    modifications << create_content_modification(
      type: "content_type",
      current: "promotional",
      recommended: approach[:content_type],
      reasoning: "Match content type to emotional receptivity"
    )

    # Emotional intensity adjustments
    if needs_emotional_intensity_adjustment?(emotion, intensity)
      modifications << create_emotional_intensity_adjustment(emotion, intensity)
    end

    # Empathy adjustments
    if requires_empathy_enhancement?(emotion)
      modifications << create_empathy_adjustment(emotion)
    end

    {
      emotional_context: { emotion: emotion, intensity: intensity },
      modifications: modifications,
      implementation_notes: generate_content_implementation_notes(modifications)
    }
  end

  def create_content_modification(type:, current:, recommended:, reasoning:)
    {
      modification_type: type,
      current_value: current,
      recommended_value: recommended,
      reasoning: reasoning,
      priority: calculate_modification_priority(type, current, recommended),
      implementation: generate_implementation_guidance(type, recommended)
    }
  end

  def needs_emotional_intensity_adjustment?(emotion, intensity)
    # High intensity negative emotions need softer approach
    %w[fear anger sadness disgust].include?(emotion) && %w[strong intense].include?(intensity)
  end

  def create_emotional_intensity_adjustment(emotion, intensity)
    {
      modification_type: "emotional_intensity",
      current_value: "standard",
      recommended_value: "reduced",
      reasoning: "Reduce emotional intensity due to customer's #{emotion} state at #{intensity} level",
      priority: "high",
      implementation: "Use gentler language, avoid exclamation marks, reduce urgency"
    }
  end

  def requires_empathy_enhancement?(emotion)
    %w[fear sadness anger].include?(emotion)
  end

  def create_empathy_adjustment(emotion)
    empathy_guidance = {
      "fear" => "Acknowledge concerns, provide reassurance, focus on security and support",
      "sadness" => "Use compassionate language, avoid overly cheerful tone, offer genuine support",
      "anger" => "Acknowledge frustration, focus on solutions, avoid defensive language"
    }

    {
      modification_type: "empathy_enhancement",
      current_value: "standard",
      recommended_value: "high_empathy",
      reasoning: "Enhance empathy due to customer's #{emotion} state",
      priority: "high",
      implementation: empathy_guidance[emotion]
    }
  end

  def calculate_modification_priority(type, current, recommended)
    # High priority for major changes that affect emotional safety
    empathy_types = %w[tone messaging_style empathy_enhancement]
    
    if empathy_types.include?(type) && current != recommended
      "high"
    elsif current != recommended
      "medium"
    else
      "low"
    end
  end

  def generate_implementation_guidance(type, recommended_value)
    guidance_map = {
      "tone" => {
        "enthusiastic" => "Use exclamation marks, positive language, energy words",
        "confident" => "Use authoritative language, strong statements, expert positioning",
        "reassuring" => "Use comforting words, acknowledge concerns, provide security",
        "compassionate" => "Use understanding language, avoid sales pressure, show care",
        "apologetic" => "Acknowledge issues, take responsibility, focus on solutions",
        "respectful_distance" => "Maintain professional tone, avoid personal language"
      },
      "messaging_style" => {
        "celebratory" => "Focus on achievements, success, positive outcomes",
        "authoritative_friendly" => "Expert advice delivered in approachable way",
        "build_excitement" => "Create anticipation, tease benefits, use preview language",
        "helpful_context" => "Provide explanations, background information, clarity",
        "empathetic_helpful" => "Combine understanding with practical assistance",
        "understanding" => "Acknowledge feelings, validate concerns, show support",
        "problem_solving" => "Focus on solutions, actionable steps, resolution",
        "authentic_transparent" => "Be honest, admit mistakes, show genuine intent"
      },
      "content_type" => {
        "promotional" => "Product benefits, offers, calls to action",
        "educational_or_promotional" => "Mix education with soft promotion",
        "preview_or_teaser" => "Sneak peeks, coming soon, anticipation building",
        "clarifying" => "Explanations, FAQs, detailed information",
        "supportive" => "Help resources, assistance, guidance",
        "supportive_only" => "Pure support with no sales elements",
        "solution_focused" => "Problem resolution, fixes, remedies",
        "value_rebuilding" => "Trust rebuilding, value demonstration, authenticity"
      }
    }

    guidance_map.dig(type, recommended_value) || "Adjust #{type} to #{recommended_value}"
  end

  def generate_content_implementation_notes(modifications)
    notes = []

    high_priority_mods = modifications.select { |mod| mod[:priority] == "high" }
    if high_priority_mods.any?
      notes << "HIGH PRIORITY: #{high_priority_mods.size} critical emotional adjustments required"
    end

    empathy_mods = modifications.select { |mod| mod[:modification_type] == "empathy_enhancement" }
    if empathy_mods.any?
      notes << "EMPATHY REQUIRED: Customer is in vulnerable emotional state - use extra care"
    end

    notes << "Review all content against emotional state before sending"
    notes << "Consider A/B testing emotional variations for future optimization"

    notes
  end

  def generate_channel_adjustments
    emotion = emotional_state.current_emotion
    confidence = emotional_state.confidence_score

    channel_changes = []

    # Email channel adjustments
    email_changes = generate_email_channel_adjustments(emotion, confidence)
    channel_changes.concat(email_changes) if email_changes.any?

    # Social media channel adjustments
    social_changes = generate_social_channel_adjustments(emotion, confidence)
    channel_changes.concat(social_changes) if social_changes.any?

    # SMS/Direct channel adjustments
    direct_changes = generate_direct_channel_adjustments(emotion, confidence)
    channel_changes.concat(direct_changes) if direct_changes.any?

    {
      emotional_context: { emotion: emotion, confidence: confidence },
      channel_changes: channel_changes,
      channel_priorities: calculate_channel_priorities(emotion),
      implementation_notes: generate_channel_implementation_notes(channel_changes)
    }
  end

  def generate_email_channel_adjustments(emotion, confidence)
    changes = []

    case emotion
    when "fear", "sadness", "anger"
      changes << {
        channel: "email",
        adjustment_type: "subject_line",
        recommendation: "Use gentle, non-aggressive subject lines",
        reasoning: "Negative emotional state requires careful approach"
      }
      
      changes << {
        channel: "email",
        adjustment_type: "sender_name",
        recommendation: "Use personal name rather than company name",
        reasoning: "Personal touch needed for emotional support"
      }
    when "joy", "anticipation"
      changes << {
        channel: "email",
        adjustment_type: "send_frequency", 
        recommendation: "Increase frequency while receptive",
        reasoning: "Customer is in positive state - capitalize on receptivity"
      }
    when "disgust"
      changes << {
        channel: "email",
        adjustment_type: "pause_channel",
        recommendation: "Temporarily halt email communications",
        reasoning: "Customer has negative association - risk of unsubscribe"
      }
    end

    changes
  end

  def generate_social_channel_adjustments(emotion, confidence)
    changes = []

    case emotion
    when "anger", "disgust"
      changes << {
        channel: "social_media",
        adjustment_type: "pause_channel",
        recommendation: "Avoid social media targeting",
        reasoning: "High risk of negative public response"
      }
    when "joy", "trust"
      changes << {
        channel: "social_media",
        adjustment_type: "engagement_approach",
        recommendation: "Encourage sharing and engagement",
        reasoning: "Positive emotional state ideal for social amplification"
      }
    when "surprise"
      changes << {
        channel: "social_media",
        adjustment_type: "content_pacing",
        recommendation: "Space out social content",
        reasoning: "Give time to process surprising information"
      }
    end

    changes
  end

  def generate_direct_channel_adjustments(emotion, confidence)
    changes = []

    case emotion
    when "fear", "anger"
      changes << {
        channel: "sms_direct",
        adjustment_type: "pause_channel",
        recommendation: "Avoid direct/SMS communications",
        reasoning: "Direct channels feel intrusive during negative emotional states"
      }
    when "trust", "anticipation"
      changes << {
        channel: "sms_direct",
        adjustment_type: "opt_in_only",
        recommendation: "Use for time-sensitive, valuable information only",
        reasoning: "High trust allows for direct communication if valuable"
      }
    end

    changes
  end

  def calculate_channel_priorities(emotion)
    case emotion
    when "joy", "trust", "anticipation"
      { email: "high", social_media: "high", sms_direct: "medium" }
    when "surprise"
      { email: "medium", social_media: "low", sms_direct: "low" }
    when "fear", "sadness"
      { email: "low", social_media: "none", sms_direct: "none" }
    when "anger", "disgust"
      { email: "none", social_media: "none", sms_direct: "none" }
    else
      { email: "medium", social_media: "medium", sms_direct: "low" }
    end
  end

  def generate_channel_implementation_notes(channel_changes)
    notes = []

    pause_channels = channel_changes.select { |change| change[:adjustment_type] == "pause_channel" }
    if pause_channels.any?
      paused_channel_names = pause_channels.map { |change| change[:channel] }.join(", ")
      notes << "PAUSE REQUIRED: Temporarily halt #{paused_channel_names} due to emotional state"
    end

    high_risk_channels = channel_changes.select { |change| change[:reasoning].include?("risk") }
    if high_risk_channels.any?
      notes << "HIGH RISK: Some channels pose risk of negative response - proceed with caution"
    end

    notes << "Monitor channel performance closely during emotional adaptation"
    notes << "Be prepared to pause additional channels if emotional state worsens"

    notes
  end

  def generate_personalization_adjustments
    emotion = emotional_state.current_emotion
    intensity = emotional_state.emotional_intensity
    
    personalization_changes = []

    # Name usage adjustments
    name_adjustment = generate_name_usage_adjustment(emotion)
    personalization_changes << name_adjustment if name_adjustment

    # Content personalization adjustments
    content_personalization = generate_content_personalization_adjustment(emotion, intensity)
    personalization_changes << content_personalization if content_personalization

    # Recommendation engine adjustments
    recommendation_adjustment = generate_recommendation_adjustment(emotion)
    personalization_changes << recommendation_adjustment if recommendation_adjustment

    # Behavioral trigger adjustments
    trigger_adjustment = generate_behavioral_trigger_adjustment(emotion)
    personalization_changes << trigger_adjustment if trigger_adjustment

    {
      emotional_context: { emotion: emotion, intensity: intensity },
      personalization_changes: personalization_changes,
      personalization_strategy: determine_personalization_strategy(emotion),
      implementation_notes: generate_personalization_implementation_notes(personalization_changes)
    }
  end

  def generate_name_usage_adjustment(emotion)
    case emotion
    when "fear", "sadness", "anger"
      {
        personalization_type: "name_usage",
        current_approach: "standard",
        recommended_approach: "careful_personal",
        reasoning: "Use first name carefully - personal but not overly familiar",
        implementation: "Use first name with respectful context, avoid casual nicknames"
      }
    when "disgust"
      {
        personalization_type: "name_usage",
        current_approach: "standard", 
        recommended_approach: "formal_only",
        reasoning: "Maintain distance - use formal address only",
        implementation: "Use formal titles (Mr./Ms.) or no name, avoid personal familiarity"
      }
    when "joy", "trust"
      {
        personalization_type: "name_usage",
        current_approach: "standard",
        recommended_approach: "warm_personal",
        reasoning: "Positive state allows for warmer personalization",
        implementation: "Use first name warmly, consider friendly variations if appropriate"
      }
    else
      nil # No adjustment needed
    end
  end

  def generate_content_personalization_adjustment(emotion, intensity)
    case emotion
    when "fear"
      {
        personalization_type: "content_focus",
        current_approach: "product_focused",
        recommended_approach: "security_focused",
        reasoning: "Address security concerns and provide reassurance",
        implementation: "Highlight security features, guarantees, risk mitigation"
      }
    when "trust"
      {
        personalization_type: "content_focus",
        current_approach: "standard",
        recommended_approach: "authority_focused",
        reasoning: "Leverage trust for authority-based messaging",
        implementation: "Use expert recommendations, social proof, authoritative content"
      }
    when "anticipation"
      {
        personalization_type: "content_focus",
        current_approach: "standard",
        recommended_approach: "excitement_building",
        reasoning: "Build on existing anticipation",
        implementation: "Use preview content, exclusive access, behind-the-scenes"
      }
    else
      nil
    end
  end

  def generate_recommendation_adjustment(emotion)
    case emotion
    when "fear", "sadness"
      {
        personalization_type: "recommendation_engine",
        current_approach: "performance_optimized",
        recommended_approach: "comfort_optimized",
        reasoning: "Prioritize familiar, comforting options over new/challenging ones",
        implementation: "Show familiar brands, proven solutions, low-risk options"
      }
    when "joy", "anticipation"
      {
        personalization_type: "recommendation_engine",
        current_approach: "standard",
        recommended_approach: "discovery_optimized",
        reasoning: "Positive state good for introducing new options",
        implementation: "Show new products, innovative solutions, premium options"
      }
    else
      nil
    end
  end

  def generate_behavioral_trigger_adjustment(emotion)
    case emotion
    when "anger", "disgust"
      {
        personalization_type: "behavioral_triggers",
        current_approach: "standard_triggers",
        recommended_approach: "minimal_triggers",
        reasoning: "Reduce automated triggers that might feel intrusive",
        implementation: "Disable cart abandonment, browse abandonment, frequency-based triggers"
      }
    when "fear"
      {
        personalization_type: "behavioral_triggers",
        current_approach: "standard_triggers",
        recommended_approach: "supportive_triggers",
        reasoning: "Use triggers that provide help rather than sales pressure",
        implementation: "Focus on help desk offers, FAQ suggestions, support triggers"
      }
    else
      nil
    end
  end

  def determine_personalization_strategy(emotion)
    case emotion
    when "joy", "trust", "anticipation"
      "high_personalization"
    when "surprise"
      "moderate_personalization"
    when "fear", "sadness"
      "careful_personalization"
    when "anger", "disgust"
      "minimal_personalization"
    else
      "standard_personalization"
    end
  end

  def generate_personalization_implementation_notes(personalization_changes)
    notes = []

    if personalization_changes.any? { |change| change[:personalization_type] == "behavioral_triggers" }
      notes << "TRIGGER ADJUSTMENT: Modify automated behavioral triggers based on emotional state"
    end

    if personalization_changes.any? { |change| change[:recommended_approach].include?("minimal") }
      notes << "REDUCED PERSONALIZATION: Scale back personalization to avoid intrusion"
    end

    notes << "Test personalization changes against emotional state predictions"
    notes << "Monitor for signs of emotional state changes that require re-adjustment"

    notes
  end

  def identify_risk_mitigation_strategies
    emotion = emotional_state.current_emotion
    confidence = emotional_state.confidence_score

    strategies = []

    # Emotional mismatch risks
    if %w[anger disgust].include?(emotion)
      strategies << {
        risk_type: "brand_damage",
        severity: "high",
        mitigation: "Pause all marketing communications immediately",
        monitoring: "Track for signs of emotional state improvement before re-engaging"
      }
    end

    if %w[fear sadness].include?(emotion)
      strategies << {
        risk_type: "emotional_harm",
        severity: "medium",
        mitigation: "Switch to supportive communication only",
        monitoring: "Monitor for receptivity signals before returning to promotional content"
      }
    end

    # Low confidence risks
    if confidence < 50.0
      strategies << {
        risk_type: "misinterpretation",
        severity: "medium",
        mitigation: "Use conservative approach until more data available",
        monitoring: "Collect additional behavioral signals to improve confidence"
      }
    end

    # Emotional volatility risks
    if emotional_state.significant_emotional_change?
      strategies << {
        risk_type: "emotional_volatility",
        severity: "medium",
        mitigation: "Use stable, non-reactive messaging until emotions stabilize",
        monitoring: "Track emotional stability before implementing major changes"
      }
    end

    strategies
  end

  def identify_success_optimization_opportunities
    emotion = emotional_state.current_emotion
    intensity = emotional_state.emotional_intensity
    confidence = emotional_state.confidence_score

    opportunities = []

    # High-receptivity optimization
    if emotional_state.receptive_to_marketing? && confidence > 70.0
      opportunities << {
        opportunity_type: "high_receptivity",
        potential_impact: "high",
        optimization: "Increase engagement frequency and promotional intensity",
        implementation: "Send additional targeted offers, cross-sells, upsells"
      }
    end

    # Trust-building optimization
    if emotion == "trust" && intensity.in?(%w[moderate strong])
      opportunities << {
        opportunity_type: "trust_leverage",
        potential_impact: "high",
        optimization: "Leverage trust for relationship deepening",
        implementation: "Introduce premium products, loyalty programs, referral requests"
      }
    end

    # Anticipation optimization
    if emotion == "anticipation"
      opportunities << {
        opportunity_type: "anticipation_building",
        potential_impact: "medium",
        optimization: "Build anticipation for future launches or announcements",
        implementation: "Use preview content, exclusive access, waitlist signups"
      }
    end

    # Joy optimization
    if emotion == "joy" && intensity == "strong"
      opportunities << {
        opportunity_type: "viral_potential",
        potential_impact: "high",
        optimization: "Encourage sharing and referrals during peak positive emotion",
        implementation: "Social sharing incentives, referral programs, user-generated content"
      }
    end

    opportunities
  end

  def plan_emotional_journey_adjustments
    current_emotion = emotional_state.current_emotion
    emotional_journey = emotional_state.emotional_journey
    
    journey_adjustments = {
      current_position: {
        emotion: current_emotion,
        intensity: emotional_state.emotional_intensity,
        stability: emotional_journey[:emotional_stability]
      },
      target_emotion: determine_target_emotion(current_emotion),
      journey_strategy: determine_journey_strategy(current_emotion, emotional_journey),
      milestone_campaigns: plan_milestone_campaigns(current_emotion),
      emotional_triggers_to_avoid: identify_triggers_to_avoid(current_emotion),
      emotional_triggers_to_leverage: identify_triggers_to_leverage(current_emotion)
    }

    journey_adjustments
  end

  def determine_target_emotion(current_emotion)
    # Map current emotions to ideal target emotions
    emotion_targets = {
      "anger" => "trust",
      "fear" => "trust", 
      "sadness" => "joy",
      "disgust" => "trust",
      "surprise" => "joy",
      "joy" => "joy", # Maintain
      "trust" => "joy", # Enhance
      "anticipation" => "joy" # Fulfill
    }

    emotion_targets[current_emotion] || "trust"
  end

  def determine_journey_strategy(current_emotion, emotional_journey)
    stability = emotional_journey[:emotional_stability]

    if stability < 0.3
      "stabilization_first" # Focus on emotional stability before progression
    elsif %w[anger fear sadness disgust].include?(current_emotion)
      "recovery_journey" # Focus on emotional recovery
    elsif %w[surprise].include?(current_emotion)
      "clarification_journey" # Focus on providing clarity and understanding
    else
      "enhancement_journey" # Build on positive emotions
    end
  end

  def plan_milestone_campaigns(current_emotion)
    case current_emotion
    when "anger", "disgust"
      [
        { milestone: "acknowledgment", campaign_type: "apology_support", timing: "immediate" },
        { milestone: "solution", campaign_type: "problem_resolution", timing: "24_hours" },
        { milestone: "rebuilding", campaign_type: "trust_rebuilding", timing: "1_week" }
      ]
    when "fear", "sadness"
      [
        { milestone: "support", campaign_type: "emotional_support", timing: "immediate" },
        { milestone: "reassurance", campaign_type: "security_focused", timing: "48_hours" },
        { milestone: "engagement", campaign_type: "gentle_promotional", timing: "1_week" }
      ]
    when "surprise"
      [
        { milestone: "clarification", campaign_type: "explanatory", timing: "2_hours" },
        { milestone: "education", campaign_type: "educational", timing: "24_hours" },
        { milestone: "engagement", campaign_type: "promotional", timing: "3_days" }
      ]
    else
      [
        { milestone: "enhancement", campaign_type: "promotional", timing: "immediate" },
        { milestone: "deepening", campaign_type: "relationship_building", timing: "3_days" }
      ]
    end
  end

  def identify_triggers_to_avoid(current_emotion)
    triggers_to_avoid = {
      "anger" => ["urgency", "pressure", "competitive_comparison", "problem_reminder"],
      "fear" => ["uncertainty", "risk_highlighting", "change_emphasis", "urgency"],
      "sadness" => ["cheerful_overwhelm", "pressure", "celebration", "urgency"],
      "disgust" => ["overfamiliarity", "sales_pressure", "brand_self_praise", "frequency_increase"],
      "surprise" => ["additional_surprises", "complexity", "rapid_changes", "information_overload"]
    }

    triggers_to_avoid[current_emotion] || []
  end

  def identify_triggers_to_leverage(current_emotion)
    triggers_to_leverage = {
      "joy" => ["celebration", "sharing", "social_proof", "premium_offers"],
      "trust" => ["authority", "recommendations", "exclusive_access", "relationship_deepening"],
      "anticipation" => ["previews", "exclusivity", "countdown", "behind_scenes"],
      "surprise" => ["clarity", "explanation", "education", "context_providing"]
    }

    triggers_to_leverage[current_emotion] || []
  end

  def calculate_implementation_priority(adjustments)
    priorities = []

    # Timing adjustments - always high priority if delay required
    if adjustments[:timing_adjustment][:requires_delay]
      priorities << { area: "timing", priority: "critical", reason: "Prevents emotional mismatch" }
    end

    # Content adjustments - high priority for negative emotions
    if adjustments[:content_adjustments][:modifications].any? { |mod| mod[:priority] == "high" }
      priorities << { area: "content", priority: "high", reason: "Emotional safety required" }
    end

    # Channel adjustments - critical if channels need pausing
    paused_channels = adjustments[:channel_adjustments][:channel_changes].select { |change| 
      change[:adjustment_type] == "pause_channel" 
    }
    if paused_channels.any?
      priorities << { area: "channels", priority: "critical", reason: "Prevent brand damage" }
    end

    # Risk mitigation - always high priority
    if adjustments[:risk_mitigation].any? { |risk| risk[:severity] == "high" }
      priorities << { area: "risk_mitigation", priority: "critical", reason: "High-severity risks identified" }
    end

    priorities
  end

  def predict_adjustment_impact(adjustments)
    # Predict the impact of implementing the adjustments
    base_receptivity = emotional_state.receptive_to_marketing? ? 0.7 : 0.3
    
    # Timing impact
    timing_impact = adjustments[:timing_adjustment][:requires_delay] ? 0.2 : 0.0
    
    # Content impact
    content_high_priority = adjustments[:content_adjustments][:modifications].count { |mod| mod[:priority] == "high" }
    content_impact = content_high_priority * 0.15
    
    # Channel impact
    channel_pauses = adjustments[:channel_adjustments][:channel_changes].count { |change| 
      change[:adjustment_type] == "pause_channel" 
    }
    channel_impact = channel_pauses * 0.1
    
    # Success optimization impact
    optimization_opportunities = adjustments[:success_optimization].count { |opp| opp[:potential_impact] == "high" }
    optimization_impact = optimization_opportunities * 0.2
    
    total_positive_impact = timing_impact + content_impact + optimization_impact
    total_negative_impact = channel_impact
    
    predicted_engagement_improvement = [(base_receptivity + total_positive_impact - total_negative_impact), 1.0].min
    
    {
      baseline_receptivity: base_receptivity,
      predicted_improvement: total_positive_impact - total_negative_impact,
      final_predicted_engagement: predicted_engagement_improvement,
      confidence_level: emotional_state.confidence_score > 70.0 ? "high" : "medium",
      key_impact_drivers: identify_key_impact_drivers(adjustments)
    }
  end

  def identify_key_impact_drivers(adjustments)
    drivers = []

    if adjustments[:timing_adjustment][:requires_delay]
      drivers << "Timing adjustment prevents emotional mismatch"
    end

    if adjustments[:success_optimization].any?
      drivers << "Success optimization opportunities identified"
    end

    if adjustments[:risk_mitigation].any? { |risk| risk[:severity] == "high" }
      drivers << "Risk mitigation prevents negative outcomes"
    end

    drivers
  end

  def generate_monitoring_recommendations
    emotion = emotional_state.current_emotion
    confidence = emotional_state.confidence_score

    recommendations = []

    # Standard monitoring
    recommendations << {
      metric: "emotional_state_changes",
      frequency: "every_2_hours",
      threshold: "significant_emotion_change",
      action: "Re-evaluate all campaign adjustments"
    }

    # Confidence-based monitoring
    if confidence < 60.0
      recommendations << {
        metric: "behavioral_signals",
        frequency: "every_30_minutes",
        threshold: "new_signal_data",
        action: "Update emotional state analysis"
      }
    end

    # Emotion-specific monitoring
    if %w[anger disgust].include?(emotion)
      recommendations << {
        metric: "negative_feedback_signals",
        frequency: "continuous",
        threshold: "any_negative_response",
        action: "Immediate campaign pause and review"
      }
    end

    if %w[fear sadness].include?(emotion)
      recommendations << {
        metric: "engagement_improvement_signals",
        frequency: "every_6_hours",
        threshold: "positive_engagement_increase",
        action: "Consider gradual re-engagement"
      }
    end

    recommendations
  end

  def apply_timing_adjustments(campaign, timing_adjustment)
    if timing_adjustment[:requires_delay] && timing_adjustment[:send_after]
      # Schedule the campaign for later
      campaign.update!(
        start_date: timing_adjustment[:send_after].to_date,
        settings: campaign.settings.merge({
          emotional_timing_adjustment: {
            original_timing: Time.current,
            adjusted_timing: timing_adjustment[:send_after],
            reason: timing_adjustment[:reasoning],
            emotion_context: emotional_state.current_emotion
          }
        })
      )

      { status: "delayed", send_after: timing_adjustment[:send_after], reason: timing_adjustment[:reasoning] }
    else
      { status: "no_change", reason: "No timing adjustment required" }
    end
  end

  def apply_content_adjustments(campaign, content_adjustments)
    applied_changes = []

    content_adjustments[:modifications].each do |modification|
      case modification[:modification_type]
      when "tone"
        apply_tone_adjustment(campaign, modification)
        applied_changes << "tone_adjusted"
      when "messaging_style"
        apply_messaging_style_adjustment(campaign, modification)
        applied_changes << "messaging_style_adjusted"
      when "empathy_enhancement"
        apply_empathy_adjustment(campaign, modification)
        applied_changes << "empathy_enhanced"
      end
    end

    { changes_applied: applied_changes, total_modifications: content_adjustments[:modifications].size }
  end

  def apply_tone_adjustment(campaign, modification)
    # Update campaign settings with tone adjustment
    campaign.settings = campaign.settings.merge({
      emotional_adaptations: (campaign.settings["emotional_adaptations"] || {}).merge({
        tone_adjustment: {
          original_tone: modification[:current_value],
          adapted_tone: modification[:recommended_value],
          reasoning: modification[:reasoning],
          applied_at: Time.current
        }
      })
    })
    campaign.save!
  end

  def apply_messaging_style_adjustment(campaign, modification)
    campaign.settings = campaign.settings.merge({
      emotional_adaptations: (campaign.settings["emotional_adaptations"] || {}).merge({
        messaging_style_adjustment: {
          original_style: modification[:current_value],
          adapted_style: modification[:recommended_value],
          reasoning: modification[:reasoning],
          applied_at: Time.current
        }
      })
    })
    campaign.save!
  end

  def apply_empathy_adjustment(campaign, modification)
    campaign.settings = campaign.settings.merge({
      emotional_adaptations: (campaign.settings["emotional_adaptations"] || {}).merge({
        empathy_enhancement: {
          level: modification[:recommended_value],
          reasoning: modification[:reasoning],
          implementation_guide: modification[:implementation],
          applied_at: Time.current
        }
      })
    })
    campaign.save!
  end

  def apply_channel_adjustments(campaign, channel_adjustments)
    applied_changes = []

    channel_adjustments[:channel_changes].each do |change|
      case change[:adjustment_type]
      when "pause_channel"
        pause_channel(campaign, change[:channel])
        applied_changes << "#{change[:channel]}_paused"
      when "send_frequency"
        adjust_send_frequency(campaign, change)
        applied_changes << "#{change[:channel]}_frequency_adjusted"
      end
    end

    { changes_applied: applied_changes, channel_priorities: channel_adjustments[:channel_priorities] }
  end

  def pause_channel(campaign, channel)
    paused_channels = campaign.settings.dig("paused_channels") || []
    paused_channels << {
      channel: channel,
      paused_at: Time.current,
      reason: "emotional_state_adaptation",
      emotion_context: emotional_state.current_emotion
    }

    campaign.settings = campaign.settings.merge({ paused_channels: paused_channels })
    campaign.save!
  end

  def adjust_send_frequency(campaign, change)
    campaign.settings = campaign.settings.merge({
      emotional_adaptations: (campaign.settings["emotional_adaptations"] || {}).merge({
        frequency_adjustment: {
          channel: change[:channel],
          adjustment: change[:recommendation],
          reasoning: change[:reasoning],
          applied_at: Time.current
        }
      })
    })
    campaign.save!
  end

  def apply_personalization_adjustments(campaign, personalization_adjustments)
    applied_changes = []

    personalization_adjustments[:personalization_changes].each do |change|
      case change[:personalization_type]
      when "name_usage"
        apply_name_usage_adjustment(campaign, change)
        applied_changes << "name_usage_adjusted"
      when "content_focus"
        apply_content_focus_adjustment(campaign, change)
        applied_changes << "content_focus_adjusted"
      when "behavioral_triggers"
        apply_behavioral_trigger_adjustment(campaign, change)
        applied_changes << "behavioral_triggers_adjusted"
      end
    end

    { 
      changes_applied: applied_changes, 
      personalization_strategy: personalization_adjustments[:personalization_strategy] 
    }
  end

  def apply_name_usage_adjustment(campaign, change)
    campaign.settings = campaign.settings.merge({
      emotional_adaptations: (campaign.settings["emotional_adaptations"] || {}).merge({
        name_usage_adjustment: {
          approach: change[:recommended_approach],
          reasoning: change[:reasoning],
          implementation: change[:implementation],
          applied_at: Time.current
        }
      })
    })
    campaign.save!
  end

  def apply_content_focus_adjustment(campaign, change)
    campaign.settings = campaign.settings.merge({
      emotional_adaptations: (campaign.settings["emotional_adaptations"] || {}).merge({
        content_focus_adjustment: {
          focus: change[:recommended_approach],
          reasoning: change[:reasoning],
          implementation: change[:implementation],
          applied_at: Time.current
        }
      })
    })
    campaign.save!
  end

  def apply_behavioral_trigger_adjustment(campaign, change)
    campaign.settings = campaign.settings.merge({
      emotional_adaptations: (campaign.settings["emotional_adaptations"] || {}).merge({
        behavioral_trigger_adjustment: {
          approach: change[:recommended_approach],
          reasoning: change[:reasoning],
          implementation: change[:implementation],
          applied_at: Time.current
        }
      })
    })
    campaign.save!
  end

  def log_emotional_adaptation(campaign, adjustments, applied_adjustments)
    Rails.logger.info "Emotional adaptation applied to campaign #{campaign.id}:"
    Rails.logger.info "  Customer emotion: #{emotional_state.current_emotion} (#{emotional_state.confidence_score}% confidence)"
    Rails.logger.info "  Adjustments applied: #{applied_adjustments.keys.join(', ')}"
    Rails.logger.info "  Expected impact: #{adjustments[:expected_impact][:final_predicted_engagement]}"
  end

  def calculate_next_review_time
    # Determine when to next review emotional state and adjustments
    case emotional_state.current_emotion
    when "anger", "disgust"
      24.hours.from_now # Check daily for negative emotions
    when "fear", "sadness"
      12.hours.from_now # Check twice daily
    when "surprise"
      4.hours.from_now # Check frequently during surprise
    else
      8.hours.from_now # Standard review frequency
    end
  end

  def generate_timing_recommendation
    approach = emotional_state.recommended_interaction_approach
    
    case approach[:timing]
    when "immediate"
      create_recommendation(
        type: "timing_opportunity",
        priority: "medium",
        action: "Optimal timing - send campaigns now",
        details: "Customer emotional state is receptive to immediate engagement"
      )
    when "significantly_delayed"
      create_recommendation(
        type: "timing_caution",
        priority: "high",
        action: "Delay campaigns by 24+ hours",
        details: "Customer emotional state requires significant cooling-off period"
      )
    else
      nil
    end
  end

  def create_recommendation(type:, priority:, action:, details:)
    {
      type: type,
      priority: priority,
      action: action,
      details: details,
      emotional_context: emotional_state.current_emotion,
      confidence: emotional_state.confidence_score,
      timestamp: Time.current
    }
  end

  def format_emotional_state
    {
      customer_identifier: emotional_state.customer_identifier,
      current_emotion: emotional_state.current_emotion,
      intensity: emotional_state.emotional_intensity,
      confidence: emotional_state.confidence_score,
      receptive_to_marketing: emotional_state.receptive_to_marketing?,
      last_interaction: emotional_state.last_interaction_at,
      significant_change: emotional_state.significant_emotional_change?
    }
  end

  def error_result(message)
    {
      success: false,
      error: message
    }
  end
end
