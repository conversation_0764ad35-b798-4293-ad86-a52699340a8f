# frozen_string_literal: true

##
# Campaign Cache Service
#
# Intelligent caching service for campaign-related data with automatic cache warming,
# invalidation strategies, and multi-layer caching support.
#
# @example Basic usage
#   cache_service = CampaignCacheService.new(tenant)
#   performance_data = cache_service.cached_performance_summary(campaign)
#   cache_service.warm_cache_for_tenant
#
class CampaignCacheService
  # Cache TTL configuration
  CACHE_TTL = {
    metrics: 5.minutes,
    performance_summary: 15.minutes,
    campaign_stats: 5.minutes,
    campaign_detail: 15.minutes,
    dashboard_data: 10.minutes,
    search_results: 30.minutes
  }.freeze

  attr_reader :tenant

  def initialize(tenant)
    @tenant = tenant
  end

  ##
  # Get cached performance summary for a campaign
  #
  # @param campaign [Campaign] Campaign object
  # @param date_range [Range] Date range for metrics (default: last 30 days)
  # @return [Hash] Performance summary data
  #
  def cached_performance_summary(campaign, date_range = 30.days.ago..Date.current)
    cache_key = performance_summary_cache_key(campaign, date_range)

    Rails.cache.fetch(cache_key, expires_in: CACHE_TTL[:performance_summary]) do
      calculate_performance_summary(campaign, date_range)
    end
  end

  ##
  # Get cached campaign statistics for tenant
  #
  # @return [Hash] Campaign statistics
  #
  def cached_campaign_stats
    cache_key = "campaign_stats_#{tenant.id}"

    Rails.cache.fetch(cache_key, expires_in: CACHE_TTL[:campaign_stats]) do
      calculate_campaign_stats
    end
  end

  ##
  # Get cached dashboard data for tenant
  #
  # @return [Hash] Dashboard data including recent campaigns and metrics
  #
  def cached_dashboard_data
    cache_key = "dashboard_data_#{tenant.id}"

    Rails.cache.fetch(cache_key, expires_in: CACHE_TTL[:dashboard_data]) do
      {
        campaign_stats: cached_campaign_stats,
        recent_campaigns: recent_campaigns_summary,
        performance_trends: performance_trends_summary,
        top_performing_campaigns: top_performing_campaigns
      }
    end
  end

  ##
  # Get cached search results
  #
  # @param search_term [String] Search term
  # @param filters [Hash] Additional filters
  # @return [Array] Search results
  #
  def cached_search_results(search_term, filters = {})
    cache_key = search_results_cache_key(search_term, filters)

    Rails.cache.fetch(cache_key, expires_in: CACHE_TTL[:search_results]) do
      perform_search(search_term, filters)
    end
  end

  ##
  # Warm cache for tenant - preload frequently accessed data
  #
  # @param background [Boolean] Whether to run in background job
  #
  def warm_cache_for_tenant(background: false)
    if background
      CacheWarmupJob.perform_later(tenant.id)
    else
      perform_cache_warmup
    end
  end

  ##
  # Invalidate all caches for a specific campaign
  #
  # @param campaign [Campaign] Campaign to invalidate caches for
  #
  def invalidate_campaign_caches(campaign)
    # Clear campaign-specific caches
    Rails.cache.delete_matched("campaign_detail_#{campaign.id}*")
    Rails.cache.delete_matched("performance_summary_#{campaign.id}*")
    Rails.cache.delete_matched("campaign_metrics_#{campaign.id}*")

    # Clear tenant-wide caches that include this campaign
    invalidate_tenant_aggregate_caches

    # Log cache invalidation for monitoring
    Rails.logger.info "Cache invalidated for campaign #{campaign.id} (#{campaign.name})"
  end

  ##
  # Invalidate tenant-wide aggregate caches
  #
  def invalidate_tenant_aggregate_caches
    Rails.cache.delete("campaign_stats_#{tenant.id}")
    Rails.cache.delete("dashboard_data_#{tenant.id}")
    Rails.cache.delete_matched("search_results_#{tenant.id}*")
    Rails.cache.delete_matched("recent_campaigns_#{tenant.id}*")
  end

  ##
  # Get cache statistics for monitoring
  #
  # @return [Hash] Cache hit/miss statistics
  #
  def cache_statistics
    {
      tenant_id: tenant.id,
      cache_keys: active_cache_keys,
      estimated_memory_usage: estimated_cache_memory_usage,
      last_warmup: Rails.cache.read("cache_warmup_#{tenant.id}")
    }
  end

  ##
  # Preload and cache campaign associations
  #
  # @param campaign_ids [Array<Integer>] Campaign IDs to preload
  #
  def preload_campaign_associations(campaign_ids)
    campaigns = tenant.campaigns
                     .includes(:email_campaign, :social_campaign, :seo_campaign, :created_by)
                     .where(id: campaign_ids)

    campaigns.each do |campaign|
      cache_key = "campaign_detail_#{campaign.id}"
      Rails.cache.write(cache_key, campaign, expires_in: CACHE_TTL[:campaign_detail])
    end
  end

  private

  ##
  # Calculate performance summary for a campaign
  #
  # @param campaign [Campaign] Campaign object
  # @param date_range [Range] Date range for calculation
  # @return [Hash] Performance summary
  #
  def calculate_performance_summary(campaign, date_range)
    metrics = campaign.campaign_metrics.where(metric_date: date_range)

    return {} if metrics.empty?

    total_impressions = metrics.sum(:impressions)
    total_clicks = metrics.sum(:clicks)
    total_conversions = metrics.sum(:conversions)
    total_revenue = metrics.sum(:revenue_cents) / 100.0
    total_cost = metrics.sum(:cost_cents) / 100.0

    {
      total_impressions: total_impressions,
      total_clicks: total_clicks,
      total_conversions: total_conversions,
      total_revenue: total_revenue,
      total_cost: total_cost,
      average_ctr: calculate_ctr(total_clicks, total_impressions),
      average_conversion_rate: calculate_conversion_rate(total_conversions, total_clicks),
      roi: calculate_roi(total_revenue, total_cost),
      date_range: {
        start_date: date_range.begin,
        end_date: date_range.end
      }
    }
  end

  ##
  # Calculate campaign statistics for tenant
  #
  # @return [Hash] Campaign statistics
  #
  def calculate_campaign_stats
    stats = tenant.campaigns.group(:status).count

    {
      total: stats.values.sum,
      active: stats["active"] || 0,
      draft: stats["draft"] || 0,
      paused: stats["paused"] || 0,
      completed: stats["completed"] || 0,
      cancelled: stats["cancelled"] || 0
    }
  end

  ##
  # Get recent campaigns summary
  #
  # @return [Array<Hash>] Recent campaigns data
  #
  def recent_campaigns_summary
    tenant.campaigns
          .includes(:email_campaign, :social_campaign, :seo_campaign)
          .order(created_at: :desc)
          .limit(5)
          .map do |campaign|
      {
        id: campaign.id,
        name: campaign.name,
        status: campaign.status,
        campaign_type: campaign.campaign_type,
        created_at: campaign.created_at,
        budget: campaign.budget_in_dollars
      }
    end
  end

  ##
  # Get performance trends summary
  #
  # @return [Hash] Performance trends data
  #
  def performance_trends_summary
    # Calculate trends for the last 7 days vs previous 7 days
    current_period = 7.days.ago..Date.current
    previous_period = 14.days.ago..8.days.ago

    current_metrics = aggregate_metrics_for_period(current_period)
    previous_metrics = aggregate_metrics_for_period(previous_period)

    {
      current_period: current_metrics,
      previous_period: previous_metrics,
      trends: calculate_trends(current_metrics, previous_metrics)
    }
  end

  ##
  # Get top performing campaigns
  #
  # @return [Array<Hash>] Top performing campaigns
  #
  def top_performing_campaigns
    tenant.campaigns
          .joins(:campaign_metrics)
          .where(campaign_metrics: { metric_date: 30.days.ago..Date.current })
          .group("campaigns.id, campaigns.name")
          .order("SUM(campaign_metrics.revenue_cents) DESC")
          .limit(5)
          .pluck(
            "campaigns.id",
            "campaigns.name",
            "SUM(campaign_metrics.revenue_cents)",
            "SUM(campaign_metrics.cost_cents)"
          )
          .map do |id, name, revenue_cents, cost_cents|
      {
        id: id,
        name: name,
        revenue: revenue_cents / 100.0,
        cost: cost_cents / 100.0,
        roi: calculate_roi(revenue_cents / 100.0, cost_cents / 100.0)
      }
    end
  end

  ##
  # Perform cache warmup
  #
  def perform_cache_warmup
    # Warm up campaign stats
    cached_campaign_stats

    # Warm up dashboard data
    cached_dashboard_data

    # Preload recent campaigns
    recent_campaign_ids = tenant.campaigns.order(created_at: :desc).limit(10).pluck(:id)
    preload_campaign_associations(recent_campaign_ids)

    # Mark warmup completion
    Rails.cache.write("cache_warmup_#{tenant.id}", Time.current, expires_in: 1.day)

    Rails.logger.info "Cache warmed up for tenant #{tenant.id}"
  end

  # Helper methods for cache key generation and calculations

  def performance_summary_cache_key(campaign, date_range)
    "performance_summary_#{campaign.id}_#{date_range.begin}_#{date_range.end}"
  end

  def search_results_cache_key(search_term, filters)
    filter_hash = Digest::SHA256.hexdigest(filters.to_json)
    "search_results_#{tenant.id}_#{search_term}_#{filter_hash}"
  end

  def calculate_ctr(clicks, impressions)
    return 0.0 if impressions.zero?
    (clicks.to_f / impressions * 100).round(2)
  end

  def calculate_conversion_rate(conversions, clicks)
    return 0.0 if clicks.zero?
    (conversions.to_f / clicks * 100).round(2)
  end

  def calculate_roi(revenue, cost)
    return 0.0 if cost.zero?
    ((revenue - cost) / cost * 100).round(2)
  end

  def active_cache_keys
    # This would require Redis introspection in a real implementation
    # For now, return estimated count
    10 # Placeholder
  end

  def estimated_cache_memory_usage
    # This would require actual memory usage calculation
    # For now, return estimated size in KB
    "~500KB" # Placeholder
  end

  def aggregate_metrics_for_period(period)
    tenant.campaigns
          .joins(:campaign_metrics)
          .where(campaign_metrics: { metric_date: period })
          .sum("campaign_metrics.revenue_cents") / 100.0
  end

  def calculate_trends(current, previous)
    return 0.0 if previous.zero?
    ((current - previous) / previous * 100).round(2)
  end

  def perform_search(search_term, filters)
    # Implement optimized search logic here
    # This is a placeholder for the actual search implementation
    []
  end
end
