# frozen_string_literal: true

# Social Media Specialist Agent - Handles AI-powered social media campaign generation and optimization
class SocialMediaAgentService
  include ActiveModel::Model
  include ActiveModel::Validations

  attr_reader :campaign, :tenant, :ai_service, :social_campaign

  SUPPORTED_PLATFORMS = %w[twitter facebook instagram linkedin tiktok youtube].freeze
  PLATFORM_LIMITS = {
    "twitter" => { character_limit: 280, hashtag_limit: 2 },
    "facebook" => { character_limit: 2200, hashtag_limit: 5 },
    "instagram" => { character_limit: 2200, hashtag_limit: 30 },
    "linkedin" => { character_limit: 3000, hashtag_limit: 3 },
    "tiktok" => { character_limit: 150, hashtag_limit: 5 },
    "youtube" => { character_limit: 5000, hashtag_limit: 15 }
  }.freeze

  def initialize(campaign:, ai_service: nil)
    @campaign = campaign
    @tenant = campaign.tenant
    @ai_service = ai_service || build_ai_service
    @social_campaign = campaign.social_campaign
  end

  # Generate comprehensive social media campaign content
  def generate_campaign_content(context = {})
    validate_campaign_for_social!

    begin
      # Extract campaign requirements
      campaign_requirements = extract_campaign_requirements(context)
      platforms = determine_target_platforms(campaign_requirements)

      # Generate platform-specific content using AI
      social_prompt = build_social_content_prompt(
        campaign_name: campaign.name,
        platforms: platforms,
        target_audience: campaign.target_audience,
        key_message: campaign_requirements[:key_message]
      )

      # Pass through for real content generation
      ai_response = @ai_service.generate_content(
        social_prompt,
        task_type: :creative_content,
        temperature: 0.9,
        use_real_response: true  # Force real response, but don't specify model to allow multi-model support
      )

      ai_content = ai_response.content

      # Process and structure the content for each platform
      social_campaign_data = process_ai_content(ai_content, platforms)
      create_or_update_social_campaign(social_campaign_data)

      # Generate posting schedule
      posting_schedule = generate_posting_schedule(platforms, context)
      update_posting_schedule(posting_schedule)

      # Return results for orchestrator
      {
        status: "success",
        content_generated: true,
        social_campaign_id: @social_campaign.id,
        platforms: platforms,
        ai_content: ai_content,
        posting_schedule: posting_schedule
      }
    rescue => error
      handle_generation_error(error)
    end
  end

  # Optimize existing social media content for better engagement
  def optimize_social_content(optimization_context = {})
    return { status: "error", message: "No social campaign found" } unless @social_campaign

    begin
      current_content = @social_campaign.content_variants
      platforms = @social_campaign.platforms

      optimized_content = {}

      platforms.each do |platform|
        optimization_prompt = build_platform_optimization_prompt(
          platform,
          current_content[platform],
          optimization_context
        )

        ai_response = @ai_service.generate_content(
          optimization_prompt,
          task_type: :creative_content,
          temperature: 0.8
        )

        optimized_platform_content = ai_response.content

        optimized_content[platform] = process_platform_optimization(
          optimized_platform_content,
          platform
        )
      end

      update_social_campaign_content(optimized_content)

      {
        status: "success",
        optimizations_applied: true,
        platforms_optimized: platforms,
        original_content: current_content,
        optimized_content: optimized_content
      }
    rescue => error
      handle_optimization_error(error)
    end
  end

  # Generate content calendar for social media posts
  def generate_content_calendar(duration_weeks: 4)
    return { status: "error", message: "No social campaign found" } unless @social_campaign

    begin
      platforms = @social_campaign.platforms
      base_content = @social_campaign.content_variants

      calendar_prompt = build_content_calendar_prompt(platforms, base_content, duration_weeks)
      ai_response = @ai_service.generate_content(
        calendar_prompt,
        task_type: :creative_content,
        temperature: 0.7
      )

      ai_calendar = ai_response.content

      content_calendar = process_content_calendar(ai_calendar, duration_weeks)
      store_content_calendar(content_calendar)

      {
        status: "success",
        calendar_generated: true,
        duration_weeks: duration_weeks,
        platforms: platforms,
        calendar: content_calendar
      }
    rescue => error
      handle_calendar_generation_error(error)
    end
  end

  # Analyze social media performance and engagement
  def analyze_social_performance
    return { status: "error", message: "No social campaign found" } unless @social_campaign

    begin
      performance_data = collect_social_metrics

      analysis_prompt = build_social_performance_analysis_prompt(performance_data)
      ai_response = @ai_service.generate_content(
        analysis_prompt,
        task_type: :data_analysis,
        temperature: 0.5
      )

      ai_analysis = ai_response.content

      recommendations = extract_social_recommendations(ai_analysis)
      engagement_insights = analyze_engagement_patterns(performance_data)

      {
        status: "success",
        performance_data: performance_data,
        ai_analysis: ai_analysis,
        recommendations: recommendations,
        engagement_insights: engagement_insights,
        next_actions: generate_social_next_actions(recommendations)
      }
    rescue => error
      handle_analysis_error(error)
    end
  end

  # Generate hashtag strategies for different platforms
  def generate_hashtag_strategy
    return { status: "error", message: "No social campaign found" } unless @social_campaign

    begin
      platforms = @social_campaign.platforms
      hashtag_strategies = {}

      platforms.each do |platform|
        hashtag_prompt = build_hashtag_prompt(platform)
        ai_response = @ai_service.generate_content(
          hashtag_prompt,
          task_type: :creative_content,
          temperature: 0.6
        )

        ai_hashtags = ai_response.content

        hashtag_strategies[platform] = process_hashtag_response(ai_hashtags, platform)
      end

      update_hashtag_strategies(hashtag_strategies)

      {
        status: "success",
        hashtag_strategies: hashtag_strategies,
        platforms: platforms
      }
    rescue => error
      handle_hashtag_generation_error(error)
    end
  end

  private

  # Build enterprise AI service with tenant-specific configuration
  def build_ai_service
    RubyLlmService.new(
      tenant: @tenant,
      context: {
        campaign_id: @campaign.id,
        task_type: :creative_content,
        brand_guidelines: @tenant.settings.dig("brand_guidelines"),
        target_audience: @campaign.target_audience,
        social_platforms: determine_target_platforms({})
      },
      provider_strategy: determine_provider_strategy
    )
  end

  # Determine optimal provider strategy based on campaign requirements
  def determine_provider_strategy
    case @campaign.budget_cents
    when 0..15000 # Low budget campaigns
      :cost_sensitive
    when 15000..75000 # Medium budget campaigns
      :balanced
    else # High budget campaigns
      :creative_content
    end
  end

  # Build social content generation prompt
  def build_social_content_prompt(campaign_name:, platforms:, target_audience:, key_message:)
    platform_specs = platforms.map do |platform|
      limit = PLATFORM_LIMITS.dig(platform, :character_limit) || 280
      "#{platform.capitalize}: #{limit} characters max"
    end.join(", ")

    <<~PROMPT
      Create engaging social media content for multiple platforms:

      Campaign: #{campaign_name}
      Target Audience: #{target_audience}
      Key Message: #{key_message}
      Platforms: #{platforms.join(", ")}
      Platform Limits: #{platform_specs}

      For each platform, generate:
      1. Platform-optimized content that fits character limits
      2. Relevant hashtags (following platform best practices)
      3. Optimal posting time suggestion
      4. Engagement strategy

      Format as JSON with platform names as keys:
      {
        "twitter": {
          "content": "Tweet content here",
          "hashtags": "#hashtag1 #hashtag2",
          "posting_time": "optimal_time",
          "engagement_strategy": "strategy description"
        },
        "linkedin": { ... },
        etc.
      }

      Make content engaging, platform-appropriate, and optimized for each audience.
    PROMPT
  end

  def validate_campaign_for_social!
    raise ArgumentError, "Campaign must be social or multi-channel type" unless social_campaign_type?
    raise ArgumentError, "Campaign must belong to a tenant" unless @tenant
  end

  def social_campaign_type?
    campaign.social? || campaign.multi_channel?
  end

  def extract_campaign_requirements(context)
    {
      key_message: extract_key_message(context),
      brand_voice: context.dig(:campaign_analysis, :settings, :brand_voice) || "engaging",
      content_pillars: context.dig(:campaign_analysis, :settings, :content_pillars) || default_content_pillars,
      target_platforms: context.dig(:campaign_analysis, :settings, :social_platforms) || [ "twitter", "linkedin" ]
    }
  end

  def extract_key_message(context)
    campaign.description.present? ?
      campaign.description[0..200] :
      "Discover #{campaign.name} - designed for #{campaign.target_audience}"
  end

  def default_content_pillars
    case campaign.target_audience.downcase
    when /business|b2b|professional/
      [ "industry_insights", "thought_leadership", "case_studies" ]
    when /customer|consumer|b2c/
      [ "lifestyle", "entertainment", "user_generated_content" ]
    else
      [ "educational", "behind_the_scenes", "community" ]
    end
  end

  def determine_target_platforms(requirements)
    target_platforms = requirements[:target_platforms]

    # Ensure target_platforms is an array and filter by supported platforms
    if target_platforms.is_a?(Array) && target_platforms.any?
      specified_platforms = target_platforms & SUPPORTED_PLATFORMS
      return specified_platforms if specified_platforms.any?
    end

    # Default platform selection based on audience
    case campaign.target_audience.downcase
    when /business|b2b|professional/
      [ "linkedin", "twitter" ]
    when /young|teen|gen/
      [ "tiktok", "instagram" ]
    when /visual|creative|design/
      [ "instagram", "youtube" ]
    else
      [ "twitter", "facebook" ]
    end
  end

  def process_ai_content(ai_content, platforms)
    if ai_content.is_a?(Hash)
      content_variants = platforms.each_with_object({}) do |platform, variants|
        platform_content = ai_content[platform] || ai_content[platform.to_sym] || {}
        variants[platform] = process_platform_content(platform_content, platform)
      end
    else
      # Fallback for non-structured AI response
      content_variants = platforms.each_with_object({}) do |platform, variants|
        variants[platform] = {
          content: truncate_for_platform(ai_content.to_s, platform),
          hashtags: generate_fallback_hashtags(platform),
          posting_time: "optimal_time",
          engagement_strategy: "standard_engagement"
        }
      end
    end

    {
      platforms: platforms,
      content_variants: content_variants,
      hashtags: extract_global_hashtags(ai_content),
      target_demographics: build_target_demographics,
      social_settings: default_social_settings
    }
  end

  def process_platform_content(platform_content, platform)
    if platform_content.is_a?(Hash)
      {
        content: truncate_for_platform(platform_content["content"] || platform_content["post"] || "", platform),
        hashtags: platform_content["hashtags"] || generate_fallback_hashtags(platform),
        posting_time: platform_content["posting_time"] || "optimal_time",
        engagement_strategy: platform_content["engagement_strategy"] || "standard_engagement"
      }
    else
      {
        content: truncate_for_platform(platform_content.to_s, platform),
        hashtags: generate_fallback_hashtags(platform),
        posting_time: "optimal_time",
        engagement_strategy: "standard_engagement"
      }
    end
  end

  def truncate_for_platform(content, platform)
    limit = PLATFORM_LIMITS.dig(platform, :character_limit) || 280
    content.length > limit ? "#{content[0..limit-4]}..." : content
  end

  def generate_fallback_hashtags(platform)
    base_hashtags = [ "##{campaign.name.gsub(/\s+/, '')}", "#Marketing", "##{campaign.target_audience.gsub(/\s+/, '')}" ]
    hashtag_limit = PLATFORM_LIMITS.dig(platform, :hashtag_limit) || 3
    base_hashtags.first(hashtag_limit).join(" ")
  end

  def extract_global_hashtags(ai_content)
    if ai_content.is_a?(Hash)
      all_hashtags = ai_content.values.flat_map do |platform_data|
        next [] unless platform_data.is_a?(Hash)
        (platform_data["hashtags"] || "").split(/\s+/)
      end
      all_hashtags.uniq.join(" ")
    else
      generate_fallback_hashtags("twitter")
    end
  end

  def build_target_demographics
    {
      primary_audience: campaign.target_audience,
      age_range: extract_age_range,
      interests: extract_interests,
      geographic_focus: extract_geographic_focus
    }
  end

  def extract_age_range
    audience = campaign.target_audience.downcase
    case audience
    when /teen|young|student/
      "16-24"
    when /professional|business|executive/
      "25-45"
    when /senior|mature|experienced/
      "45-65"
    else
      "25-54"
    end
  end

  def extract_interests
    audience = campaign.target_audience.downcase
    case audience
    when /tech|startup|innovation/
      [ "technology", "startups", "innovation" ]
    when /business|professional/
      [ "business", "networking", "career_development" ]
    when /creative|design|art/
      [ "design", "creativity", "arts" ]
    else
      [ "general_interest", "lifestyle", "community" ]
    end
  end

  def extract_geographic_focus
    # This would be configured based on campaign settings
    campaign.settings.dig("geographic_focus") || "global"
  end

  def default_social_settings
    {
      auto_post: false,
      engagement_tracking: true,
      hashtag_tracking: true,
      mention_monitoring: true,
      ai_generated: true,
      generation_timestamp: Time.current
    }
  end

  def create_or_update_social_campaign(social_data)
    if @social_campaign
      @social_campaign.update!(social_data.except(:social_settings))
      update_social_settings(social_data[:social_settings])
    else
      @social_campaign = campaign.create_social_campaign!(social_data.except(:social_settings))
      update_social_settings(social_data[:social_settings])
    end
  end

  def update_social_settings(settings)
    return unless settings && @social_campaign

    current_settings = @social_campaign.social_settings || {}
    @social_campaign.update!(social_settings: current_settings.merge(settings))
  end

  def generate_posting_schedule(platforms, context)
    duration_days = context.dig(:campaign_analysis, :duration_days) || 30
    posts_per_week = calculate_posts_per_week(platforms.count)

    schedule = {}
    platforms.each do |platform|
      schedule[platform] = generate_platform_schedule(platform, posts_per_week, duration_days)
    end

    schedule
  end

  def calculate_posts_per_week(platform_count)
    # Adjust posting frequency based on number of platforms
    case platform_count
    when 1
      7 # Daily posting for single platform
    when 2..3
      5 # 5 times per week for multiple platforms
    else
      3 # 3 times per week for many platforms
    end
  end

  def generate_platform_schedule(platform, posts_per_week, duration_days)
    optimal_times = get_optimal_posting_times(platform)

    weeks = (duration_days / 7.0).ceil
    schedule = []

    weeks.times do |week|
      posts_per_week.times do |post|
        day_offset = (post * 7.0 / posts_per_week).round
        post_date = Date.current + (week * 7) + day_offset

        schedule << {
          date: post_date,
          time: optimal_times.sample,
          content_type: determine_content_type(post, platform),
          priority: calculate_post_priority(week, post)
        }
      end
    end

    schedule
  end

  def get_optimal_posting_times(platform)
    case platform
    when "twitter"
      [ "9:00 AM", "1:00 PM", "3:00 PM", "5:00 PM" ]
    when "linkedin"
      [ "8:00 AM", "12:00 PM", "5:00 PM", "6:00 PM" ]
    when "instagram"
      [ "11:00 AM", "2:00 PM", "5:00 PM", "7:00 PM" ]
    when "facebook"
      [ "9:00 AM", "1:00 PM", "3:00 PM", "4:00 PM" ]
    when "tiktok"
      [ "6:00 AM", "10:00 AM", "7:00 PM", "9:00 PM" ]
    when "youtube"
      [ "2:00 PM", "4:00 PM", "6:00 PM", "8:00 PM" ]
    else
      [ "10:00 AM", "2:00 PM", "4:00 PM", "6:00 PM" ]
    end
  end

  def determine_content_type(post_index, platform)
    content_types = get_platform_content_types(platform)
    content_types[post_index % content_types.length]
  end

  def get_platform_content_types(platform)
    case platform
    when "twitter"
      [ "thread", "single_tweet", "quote_tweet", "poll" ]
    when "linkedin"
      [ "article", "post", "video", "carousel" ]
    when "instagram"
      [ "photo", "carousel", "reel", "story" ]
    when "facebook"
      [ "post", "video", "photo", "event" ]
    when "tiktok"
      [ "video", "trend", "challenge", "duet" ]
    when "youtube"
      [ "video", "short", "community_post", "livestream" ]
    else
      [ "post", "photo", "video", "text" ]
    end
  end

  def calculate_post_priority(week, post)
    # Higher priority for launch week and key posts
    if week == 0
      "high"
    elsif post % 3 == 0
      "medium"
    else
      "low"
    end
  end

  def update_posting_schedule(schedule)
    @social_campaign.update!(post_schedule: schedule)
  end

  # Additional helper methods for optimization, calendar generation, etc.
  def build_platform_optimization_prompt(platform, current_content, context)
    performance_data = context[:performance_data] || {}

    <<~PROMPT
      Optimize this #{platform} content for better engagement:

      Current Content: #{current_content}
      Platform: #{platform} (limit: #{PLATFORM_LIMITS.dig(platform, :character_limit)} characters)

      Campaign: #{campaign.name}
      Audience: #{campaign.target_audience}
      Performance Data: #{performance_data}

      Optimize for:
      1. Platform-specific best practices
      2. Higher engagement rates
      3. Better hashtag usage
      4. Trending topics integration

      Return optimized content with hashtags and engagement strategy.
    PROMPT
  end

  def collect_social_metrics
    campaign_metrics = campaign.campaign_metrics.recent(30)

    {
      total_impressions: campaign_metrics.sum(:impressions),
      total_engagements: campaign_metrics.sum(:social_engagements),
      total_shares: campaign_metrics.sum(:social_shares),
      total_comments: campaign_metrics.sum(:social_comments),
      engagement_rate: calculate_engagement_rate(campaign_metrics),
      reach_growth: calculate_reach_growth,
      hashtag_performance: analyze_hashtag_performance
    }
  end

  def calculate_engagement_rate(metrics)
    total_impressions = metrics.sum(:impressions)
    total_engagements = metrics.sum(:social_engagements)
    return 0.0 if total_impressions.zero?

    (total_engagements.to_f / total_impressions * 100).round(2)
  end

  def calculate_reach_growth
    # This would integrate with social media APIs for actual data
    rand(5.0..15.0).round(2) # Placeholder
  end

  def analyze_hashtag_performance
    # This would analyze which hashtags perform best
    {
      top_performing: @social_campaign&.hashtags&.split&.first(3) || [],
      trending_relevant: [ "#trending", "#viral", "#engagement" ],
      recommended_new: [ "#innovation", "#growth", "#success" ]
    }
  end

  def build_hashtag_prompt(platform)
    limit = PLATFORM_LIMITS.dig(platform, :hashtag_limit) || 5

    <<~PROMPT
      Generate optimal hashtag strategy for #{platform}:

      Campaign: #{campaign.name}
      Audience: #{campaign.target_audience}
      Platform Limit: #{limit} hashtags

      Provide:
      1. Primary hashtags (brand/campaign specific)
      2. Secondary hashtags (industry/audience)
      3. Trending hashtags (current trends)
      4. Community hashtags (engagement focused)

      Format as JSON with strategy explanations.
    PROMPT
  end

  # Error handling methods (similar to email agent)
  def handle_generation_error(error)
    {
      status: "error",
      message: "Social media content generation failed: #{error.message}",
      error_type: error.class.name
    }
  end

  def handle_optimization_error(error)
    {
      status: "error",
      message: "Social media optimization failed: #{error.message}",
      error_type: error.class.name
    }
  end

  def handle_calendar_generation_error(error)
    {
      status: "error",
      message: "Content calendar generation failed: #{error.message}",
      error_type: error.class.name
    }
  end

  def handle_analysis_error(error)
    {
      status: "error",
      message: "Social media analysis failed: #{error.message}",
      error_type: error.class.name
    }
  end

  def handle_hashtag_generation_error(error)
    {
      status: "error",
      message: "Hashtag strategy generation failed: #{error.message}",
      error_type: error.class.name
    }
  end

  # Additional methods for calendar processing, recommendations, etc.
  def process_content_calendar(ai_calendar, duration_weeks)
    # Process AI-generated calendar into structured format
    # Implementation would parse AI response and create weekly schedules
    {}
  end

  def store_content_calendar(calendar)
    # Store calendar in social campaign settings
    @social_campaign.update!(
      social_settings: @social_campaign.social_settings.merge({
        "content_calendar" => calendar,
        "calendar_generated_at" => Time.current
      })
    )
  end

  def extract_social_recommendations(ai_analysis)
    # Extract actionable recommendations from AI analysis
    []
  end

  def analyze_engagement_patterns(performance_data)
    # Analyze when audience is most engaged
    {}
  end

  def generate_social_next_actions(recommendations)
    # Generate next action items based on recommendations
    []
  end

  def process_hashtag_response(ai_hashtags, platform)
    # Process AI hashtag response into structured format
    {}
  end

  def update_hashtag_strategies(strategies)
    # Update social campaign with new hashtag strategies
    @social_campaign.update!(
      social_settings: @social_campaign.social_settings.merge({
        "hashtag_strategies" => strategies,
        "hashtags_updated_at" => Time.current
      })
    )
  end

  def build_content_calendar_prompt(platforms, base_content, duration_weeks)
    <<~PROMPT
      Create a #{duration_weeks}-week content calendar for social media campaign:

      Campaign: #{campaign.name}
      Platforms: #{platforms.join(', ')}
      Audience: #{campaign.target_audience}
      Base Content: #{base_content}

      Generate weekly themes and daily post ideas with:
      1. Content variety (text, images, videos, polls)
      2. Platform-specific optimization
      3. Trending topic integration
      4. Community engagement focus

      Format as structured weekly calendar.
    PROMPT
  end

  def build_social_performance_analysis_prompt(performance_data)
    <<~PROMPT
      Analyze social media campaign performance:

      Campaign: #{campaign.name}
      Platforms: #{@social_campaign.platforms.join(', ')}
      Performance Data: #{performance_data.to_json}

      Provide insights on:
      1. Engagement patterns and trends
      2. Platform-specific performance
      3. Content type effectiveness
      4. Audience behavior analysis
      5. Optimization opportunities

      Include specific, actionable recommendations.
    PROMPT
  end
end
