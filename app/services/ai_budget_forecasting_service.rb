# frozen_string_literal: true

# Service for forecasting AI usage costs and managing budget constraints
class AiBudgetForecastingService
  include ActiveModel::Model
  
  # Default budget alert thresholds
  ALERT_THRESHOLDS = [0.5, 0.75, 0.9, 0.95]
  
  # Sample model costs per 1K tokens (these would be actual values in production)
  MODEL_COSTS = {
    'gpt-3.5-turbo' => {
      input: 0.0005,   # $0.0005 per 1K input tokens
      output: 0.0015   # $0.0015 per 1K output tokens
    },
    'gpt-4-turbo' => {
      input: 0.01,     # $0.01 per 1K input tokens
      output: 0.03     # $0.03 per 1K output tokens
    },
    'anthropic-claude-3-sonnet' => {
      input: 0.008,    # $0.008 per 1K input tokens
      output: 0.024    # $0.024 per 1K output tokens
    },
    'anthropic-claude-3-haiku' => {
      input: 0.00025,  # $0.00025 per 1K input tokens
      output: 0.00125  # $0.00125 per 1K output tokens
    }
  }
  
  class << self
    # Calculate the cost of a specific AI request
    def calculate_request_cost(model:, input_tokens:, output_tokens:)
      return 0 unless MODEL_COSTS[model]
      
      input_cost = MODEL_COSTS[model][:input] * (input_tokens / 1000.0)
      output_cost = MODEL_COSTS[model][:output] * (output_tokens / 1000.0)
      
      input_cost + output_cost
    end
    
    # Forecast monthly usage based on current trends
    def forecast_monthly_usage(tenant:, days_to_forecast: 30)
      tenant_id = tenant.is_a?(Tenant) ? tenant.id : tenant
      
      # Get historical usage data for the past 30 days
      history = get_usage_history(tenant_id)
      
      # Calculate daily average usage
      daily_average = calculate_daily_average(history)
      
      # Project forward
      projected_usage = project_usage(daily_average, days_to_forecast)
      
      # Calculate cost projections
      cost_projection = calculate_cost_projection(projected_usage)
      
      # Determine if we'll exceed budget
      budget_analysis = analyze_budget(tenant_id, cost_projection[:total_cost])
      
      {
        daily_average_tokens: daily_average,
        projected_tokens: projected_usage,
        cost_projection: cost_projection,
        budget_analysis: budget_analysis
      }
    end
    
    # Check if a proposed operation would exceed budget limits
    def check_budget_impact(tenant:, proposed_operation:)
      tenant_id = tenant.is_a?(Tenant) ? tenant.id : tenant
      
      # Get the tenant's current usage and budget
      current_usage = get_current_usage(tenant_id)
      budget_limit = get_budget_limit(tenant_id)
      
      # Calculate the estimated cost of the proposed operation
      operation_cost = estimate_operation_cost(proposed_operation)
      
      # Calculate the projected total with this operation
      projected_total = current_usage[:current_cost] + operation_cost
      
      # Check if this would exceed the budget
      would_exceed = projected_total > budget_limit
      
      # Calculate how much of the budget would be used
      budget_percentage = budget_limit > 0 ? (projected_total / budget_limit) * 100 : 0
      
      {
        current_usage: current_usage[:current_cost],
        operation_cost: operation_cost,
        projected_total: projected_total,
        budget_limit: budget_limit,
        budget_percentage: budget_percentage,
        would_exceed_budget: would_exceed,
        remaining_budget: budget_limit - current_usage[:current_cost]
      }
    end
    
    # Generate usage reports for a specific time period
    def generate_usage_report(tenant:, start_date: 30.days.ago, end_date: Time.current, group_by: :day)
      tenant_id = tenant.is_a?(Tenant) ? tenant.id : tenant
      
      # Get usage records for the period
      records = AiUsageRecord.where(
        tenant_id: tenant_id,
        created_at: start_date..end_date
      )
      
      # Group records by the specified interval
      grouped_records = group_records(records, group_by)
      
      # Calculate costs and usage statistics
      report_data = calculate_report_data(grouped_records)
      
      # Generate summary statistics
      summary = generate_summary(report_data)
      
      {
        period: {
          start_date: start_date,
          end_date: end_date,
          days: (end_date.to_date - start_date.to_date).to_i
        },
        grouping: group_by,
        data: report_data,
        summary: summary
      }
    end
    
    # Check if tenant has exceeded any budget thresholds and create alerts
    def check_budget_thresholds(tenant:)
      tenant_id = tenant.is_a?(Tenant) ? tenant.id : tenant
      
      # Get current usage and budget
      current_usage = get_current_usage(tenant_id)
      budget_limit = get_budget_limit(tenant_id)
      
      return if budget_limit <= 0
      
      # Calculate current percentage of budget used
      current_percentage = (current_usage[:current_cost] / budget_limit)
      
      # Check which thresholds have been crossed
      crossed_thresholds = []
      
      ALERT_THRESHOLDS.each do |threshold|
        # Get the last recorded alert for this threshold
        last_alert = AlertLog.where(
          tenant_id: tenant_id,
          source: 'BudgetForecasting',
          metadata: { threshold: threshold }
        ).order(created_at: :desc).first
        
        # Check if we've crossed this threshold and haven't alerted recently
        if current_percentage >= threshold && (last_alert.nil? || last_alert.created_at < 24.hours.ago)
          # Create a new alert
          AlertLog.create!(
            tenant_id: tenant_id,
            severity: threshold >= 0.9 ? 'critical' : 'warning',
            message: "AI usage budget #{(threshold * 100).to_i}% threshold exceeded",
            source: 'BudgetForecasting',
            metadata: {
              threshold: threshold,
              current_usage: current_usage[:current_cost],
              budget_limit: budget_limit,
              percentage: current_percentage
            }
          )
          
          crossed_thresholds << threshold
        end
      end
      
      {
        current_percentage: current_percentage,
        crossed_thresholds: crossed_thresholds,
        current_usage: current_usage[:current_cost],
        budget_limit: budget_limit
      }
    end
    
    # Update a model's cost configuration
    def update_model_cost(model:, input_cost:, output_cost:)
      MODEL_COSTS[model] = {
        input: input_cost,
        output: output_cost
      }
    end
    
    private
    
    # Get historical usage data for a tenant
    def get_usage_history(tenant_id)
      AiUsageRecord.where(
        tenant_id: tenant_id,
        created_at: 30.days.ago..Time.current
      ).order(created_at: :asc)
    end
    
    # Calculate the daily average usage
    def calculate_daily_average(history)
      # Group by day
      daily_usage = history.group_by { |record| record.created_at.to_date }
      
      # Calculate totals per day
      daily_totals = daily_usage.transform_values do |records|
        {
          total_tokens: records.sum { |r| r.input_tokens + r.output_tokens },
          input_tokens: records.sum(&:input_tokens),
          output_tokens: records.sum(&:output_tokens),
          cost: records.sum do |r|
            calculate_request_cost(
              model: r.model,
              input_tokens: r.input_tokens,
              output_tokens: r.output_tokens
            )
          end
        }
      end
      
      # Calculate averages
      days = daily_totals.keys.count
      
      if days > 0
        {
          total_tokens: daily_totals.values.sum { |v| v[:total_tokens] } / days,
          input_tokens: daily_totals.values.sum { |v| v[:input_tokens] } / days,
          output_tokens: daily_totals.values.sum { |v| v[:output_tokens] } / days,
          cost: daily_totals.values.sum { |v| v[:cost] } / days
        }
      else
        { total_tokens: 0, input_tokens: 0, output_tokens: 0, cost: 0 }
      end
    end
    
    # Project usage forward
    def project_usage(daily_average, days)
      {
        total_tokens: daily_average[:total_tokens] * days,
        input_tokens: daily_average[:input_tokens] * days,
        output_tokens: daily_average[:output_tokens] * days
      }
    end
    
    # Calculate cost projection
    def calculate_cost_projection(projected_usage)
      # Simplified approach using average costs
      # In a real implementation, this would use a more sophisticated model
      # based on the mix of models used
      avg_input_cost = 0.003 # Average cost per 1K input tokens
      avg_output_cost = 0.006 # Average cost per 1K output tokens
      
      input_cost = projected_usage[:input_tokens] / 1000.0 * avg_input_cost
      output_cost = projected_usage[:output_tokens] / 1000.0 * avg_output_cost
      total_cost = input_cost + output_cost
      
      {
        input_cost: input_cost,
        output_cost: output_cost,
        total_cost: total_cost
      }
    end
    
    # Analyze budget impact
    def analyze_budget(tenant_id, projected_cost)
      budget_limit = get_budget_limit(tenant_id)
      current_usage = get_current_usage(tenant_id)
      
      if budget_limit <= 0
        return {
          has_budget: false,
          message: "No budget limit set"
        }
      end
      
      projected_percentage = projected_cost / budget_limit * 100
      current_percentage = current_usage[:current_cost] / budget_limit * 100
      
      {
        has_budget: true,
        budget_limit: budget_limit,
        current_usage: current_usage[:current_cost],
        current_percentage: current_percentage,
        projected_cost: projected_cost,
        projected_percentage: projected_percentage,
        will_exceed: projected_cost > budget_limit,
        remaining_budget: budget_limit - current_usage[:current_cost]
      }
    end
    
    # Get the current month's usage for a tenant
    def get_current_usage(tenant_id)
      # Get current month's records
      start_of_month = Time.current.beginning_of_month
      
      records = AiUsageRecord.where(
        tenant_id: tenant_id,
        created_at: start_of_month..Time.current
      )
      
      # Calculate total tokens and cost
      total_input_tokens = records.sum(&:input_tokens)
      total_output_tokens = records.sum(&:output_tokens)
      
      total_cost = records.sum do |record|
        calculate_request_cost(
          model: record.model,
          input_tokens: record.input_tokens,
          output_tokens: record.output_tokens
        )
      end
      
      {
        start_date: start_of_month,
        current_date: Time.current,
        days_elapsed: (Time.current.to_date - start_of_month.to_date).to_i,
        total_input_tokens: total_input_tokens,
        total_output_tokens: total_output_tokens,
        total_tokens: total_input_tokens + total_output_tokens,
        current_cost: total_cost
      }
    end
    
    # Get the budget limit for a tenant
    def get_budget_limit(tenant_id)
      # In a real implementation, this would fetch from the tenant's settings
      # For now, return a default value
      tenant = Tenant.find_by(id: tenant_id)
      tenant&.ai_budget_limit || 100.0 # Default $100 if not set
    end
    
    # Estimate the cost of a proposed operation
    def estimate_operation_cost(operation)
      model = operation[:model] || 'gpt-3.5-turbo'
      
      # Estimate tokens based on operation type if not provided
      input_tokens = operation[:input_tokens]
      output_tokens = operation[:output_tokens]
      
      unless input_tokens && output_tokens
        # Rough estimates based on operation type
        case operation[:type]
        when :email_generation
          input_tokens ||= 1000
          output_tokens ||= 2000
        when :social_post_generation
          input_tokens ||= 500
          output_tokens ||= 1000
        when :seo_analysis
          input_tokens ||= 2000
          output_tokens ||= 3000
        when :audience_analysis
          input_tokens ||= 1500
          output_tokens ||= 2500
        else
          input_tokens ||= 1000
          output_tokens ||= 1000
        end
      end
      
      calculate_request_cost(
        model: model,
        input_tokens: input_tokens,
        output_tokens: output_tokens
      )
    end
    
    # Group usage records by the specified interval
    def group_records(records, grouping)
      case grouping
      when :day
        records.group_by { |r| r.created_at.to_date }
      when :week
        records.group_by { |r| r.created_at.beginning_of_week.to_date }
      when :month
        records.group_by { |r| r.created_at.beginning_of_month.to_date }
      when :service
        records.group_by(&:service)
      when :model
        records.group_by(&:model)
      else
        records.group_by { |r| r.created_at.to_date }
      end
    end
    
    # Calculate report data from grouped records
    def calculate_report_data(grouped_records)
      grouped_records.transform_values do |records|
        {
          count: records.count,
          total_tokens: records.sum { |r| r.input_tokens + r.output_tokens },
          input_tokens: records.sum(&:input_tokens),
          output_tokens: records.sum(&:output_tokens),
          cost: records.sum do |r|
            calculate_request_cost(
              model: r.model,
              input_tokens: r.input_tokens,
              output_tokens: r.output_tokens
            )
          end
        }
      end
    end
    
    # Generate summary statistics
    def generate_summary(report_data)
      total_requests = report_data.values.sum { |v| v[:count] }
      total_tokens = report_data.values.sum { |v| v[:total_tokens] }
      total_cost = report_data.values.sum { |v| v[:cost] }
      
      {
        total_requests: total_requests,
        total_tokens: total_tokens,
        average_tokens_per_request: total_requests > 0 ? (total_tokens.to_f / total_requests).round : 0,
        total_cost: total_cost,
        average_cost_per_request: total_requests > 0 ? (total_cost / total_requests) : 0,
        average_cost_per_1k_tokens: total_tokens > 0 ? (total_cost / (total_tokens / 1000.0)) : 0
      }
    end
  end
end
