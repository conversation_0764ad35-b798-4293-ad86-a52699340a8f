# frozen_string_literal: true

module EmailContent
  # Focused service responsible for generating email content
  # This is an example of refactoring the EmailSpecialistAgentService
  # into smaller, more focused components
  class ContentGenerator
    include ActiveModel::Model
    
    attr_reader :campaign, :options
    
    def initialize(campaign:, options: {})
      @campaign = campaign
      @options = options
      @tenant = campaign.tenant
    end
    
    # Generate complete email content with subject line and body
    def generate
      begin
        # Try to get cached response first
        cache_key = cache_key_for_campaign
        cached_response = AiModelOptimizationService.get_cached_response(key: cache_key)
        
        if cached_response
          process_ai_response(cached_response, true)
        else
          # Generate new content
          model = select_optimal_model
          response = generate_content_with_model(model)
          
          # Cache the response
          AiModelOptimizationService.cache_response(key: cache_key, response: response)
          
          process_ai_response(response, false)
        end
      rescue StandardError => e
        handle_generation_error(e)
      end
    end
    
    # Generate variations of a subject line
    def generate_subject_variations(original_subject, count = 3)
      validate_subject(original_subject)
      
      begin
        prompt = build_subject_variation_prompt(original_subject, count)
        
        response = llm_service.generate_content(
          prompt,
          task_type: :email_subject_optimization,
          model: select_optimal_model(task: :email_subject_optimization),
          temperature: 0.7
        )
        
        process_subject_variations(response)
      rescue StandardError => e
        handle_generation_error(e)
      end
    end
    
    private
    
    def llm_service
      @llm_service ||= RubyLlmService.new(tenant: @tenant)
    end
    
    def build_generation_prompt
      audience = campaign.audience
      
      # Utilize the PromptBuilder utility to construct a structured prompt
      PromptBuilder.new
        .add_system_context("You are an expert email marketer specializing in creating engaging email content.")
        .add_campaign_context(campaign)
        .add_audience_context(audience)
        .add_brand_voice(options[:brand_voice] || campaign.brand_voice || "professional")
        .add_email_type(options[:email_type] || "promotional")
        .add_key_message(options[:key_message])
        .add_structure_requirements
        .build
    end
    
    def build_subject_variation_prompt(original_subject, count)
      PromptBuilder.new
        .add_system_context("You are an expert in email marketing subject lines.")
        .add_task("Create #{count} variations of the following subject line. Make them more engaging and likely to increase open rates.")
        .add_input(original_subject)
        .add_output_format(
          {
            optimized_subject: "The best variation",
            reasoning: "Why this variation is effective",
            variations: ["Array of all subject line variations"]
          }
        )
        .build
    end
    
    def select_optimal_model(task: :email_generation)
      importance = determine_task_importance
      audience_size = campaign.audience&.subscriber_count || 0
      
      AiModelOptimizationService.select_optimal_model(
        tenant: @tenant,
        task_type: task,
        importance: importance,
        options: {
          audience_size: audience_size,
          campaign_type: campaign.type,
          content_length: options[:content_length] || :medium
        }
      )
    end
    
    def determine_task_importance
      if campaign.priority == "high" || campaign.urgent?
        :high
      elsif campaign.draft?
        :low
      else
        :normal
      end
    end
    
    def generate_content_with_model(model)
      prompt = build_generation_prompt
      
      llm_service.generate_content(
        prompt,
        task_type: :email_generation,
        model: model,
        temperature: options[:creativity] || 0.7,
        max_tokens: options[:max_tokens]
      )
    end
    
    def process_ai_response(response, from_cache = false)
      begin
        content = JSON.parse(response.content)
        
        # Track usage for billing/monitoring (skip if from cache)
        unless from_cache
          AiUsageTracker.track_usage(
            tenant: @tenant,
            service: 'EmailSpecialistAgent',
            campaign_id: campaign.id,
            model: response.model,
            tokens: response.usage&.dig(:total_tokens) || 0
          )
        end
        
        # Structure the successful response
        {
          status: 'success',
          campaign_data: {
            subject_line: content['subject_line'],
            content: content['content'],
            cta: content['cta'],
            tone: content['tone']
          },
          model_used: response.model,
          source: from_cache ? 'cache' : 'generation'
        }
      rescue JSON::ParserError => e
        # Handle malformed response specifically
        handle_parsing_error(e, response.content)
      end
    end
    
    def process_subject_variations(response)
      begin
        content = JSON.parse(response.content)
        
        AiUsageTracker.track_usage(
          tenant: @tenant,
          service: 'EmailSpecialistAgent',
          campaign_id: campaign.id,
          model: response.model,
          tokens: response.usage&.dig(:total_tokens) || 0,
          operation: 'subject_optimization'
        )
        
        {
          status: 'success',
          optimized_subject: content['optimized_subject'],
          variations: content['variations'],
          reasoning: content['reasoning']
        }
      rescue JSON::ParserError => e
        handle_parsing_error(e, response.content)
      end
    end
    
    def handle_generation_error(error)
      result = ErrorHandlingService.log_and_process(
        error: error,
        context: {
          service: 'EmailContentGenerator',
          campaign_id: campaign.id,
          tenant_id: @tenant.id,
          operation: 'generate_content'
        },
        error_code: 'EMAIL_GEN_001',
        retry_strategy: :exponential_backoff
      )
      
      {
        status: 'error',
        message: ErrorHandlingService.user_friendly_message(error),
        error_type: result[:error_class],
        error_code: result[:error_code],
        recovery_options: result[:recovery_options]
      }
    end
    
    def handle_parsing_error(error, content)
      ErrorHandlingService.log_and_process(
        error: error,
        context: {
          service: 'EmailContentGenerator',
          campaign_id: campaign.id,
          tenant_id: @tenant.id,
          operation: 'parse_response',
          response_content: content.truncate(200)
        },
        error_code: 'EMAIL_GEN_002'
      )
      
      {
        status: 'error',
        message: 'We had trouble processing the AI response. Our team has been notified.',
        error_type: 'ParseError',
        error_code: 'EMAIL_GEN_002',
        recovery_options: ['retry', 'use_template']
      }
    end
    
    def validate_subject(subject)
      if subject.blank?
        raise ArgumentError, 'A subject line is required'
      end
      
      if subject.length > 150
        raise ArgumentError, 'Subject line is too long (maximum is 150 characters)'
      end
    end
    
    def cache_key_for_campaign
      campaign_attributes = {
        id: campaign.id,
        updated_at: campaign.updated_at.to_i,
        brand_voice: options[:brand_voice] || campaign.brand_voice,
        email_type: options[:email_type],
        key_message: options[:key_message]
      }
      
      serialized = campaign_attributes.to_json
      
      AiModelOptimizationService.cache_key_for(
        prompt: serialized,
        task_type: :email_generation,
        temperature: options[:creativity] || 0.7
      )
    end
  end
end
