# frozen_string_literal: true

# Service for intelligently selecting and optimizing AI models based on various factors
# This service helps reduce costs while maintaining quality by selecting appropriate models
# for different tasks based on importance, budget constraints, and performance requirements
class AiModelOptimizationService
  include ActiveModel::Model

  # Model tiers with corresponding models and their characteristics
  MODEL_TIERS = {
    economy: {
      models: [ "gpt-3.5-turbo", "claude-instant-1", "text-bison" ],
      cost_per_1k_tokens: 0.0015,
      max_tokens: 4096,
      strengths: [ "speed", "cost-efficiency" ],
      weaknesses: [ "complex reasoning", "creativity" ],
      suitable_for: [ "drafts", "simple content", "routine tasks" ]
    },
    standard: {
      models: [ "gpt-3.5-turbo-16k", "claude-1", "gemini-pro" ],
      cost_per_1k_tokens: 0.005,
      max_tokens: 16384,
      strengths: [ "balance of quality and cost", "longer context" ],
      weaknesses: [ "advanced reasoning", "nuanced understanding" ],
      suitable_for: [ "most marketing content", "medium complexity tasks" ]
    },
    premium: {
      models: [ "gpt-4", "claude-2", "gemini-pro-vision" ],
      cost_per_1k_tokens: 0.03,
      max_tokens: 8192,
      strengths: [ "high quality", "complex reasoning", "creativity" ],
      weaknesses: [ "cost", "speed" ],
      suitable_for: [ "high-stakes content", "complex campaigns", "creative tasks" ]
    },
    enterprise: {
      models: [ "gpt-4-turbo", "claude-2.1", "gemini-ultra" ],
      cost_per_1k_tokens: 0.06,
      max_tokens: 128000,
      strengths: [ "highest quality", "very long context", "advanced capabilities" ],
      weaknesses: [ "highest cost" ],
      suitable_for: [ "critical campaigns", "executive content", "complex analysis" ]
    }
  }

  # Task types and their default importance levels
  TASK_TYPES = {
    email_generation: {
      default_importance: :standard,
      economy_suitable: true,
      factors: [ :audience_size, :campaign_type, :content_length ]
    },
    social_post_generation: {
      default_importance: :standard,
      economy_suitable: true,
      factors: [ :platform, :campaign_type, :content_length ]
    },
    campaign_analysis: {
      default_importance: :premium,
      economy_suitable: false,
      factors: [ :data_complexity, :campaign_importance ]
    },
    audience_insights: {
      default_importance: :premium,
      economy_suitable: false,
      factors: [ :audience_size, :data_sources ]
    },
    content_optimization: {
      default_importance: :standard,
      economy_suitable: true,
      factors: [ :content_type, :optimization_goals ]
    },
    subject_line_generation: {
      default_importance: :standard,
      economy_suitable: true,
      factors: [ :campaign_type, :audience_size ]
    }
  }

  # Subscription tiers and their model access levels
  SUBSCRIPTION_TIERS = {
    basic: {
      allowed_tiers: [ :economy ],
      monthly_token_budget: 1_000_000, # 1M tokens
      model_options: {
        can_use_premium_models: false,
        economy_model_discount: 0
      }
    },
    professional: {
      allowed_tiers: [ :economy, :standard ],
      monthly_token_budget: 5_000_000, # 5M tokens
      model_options: {
        can_use_premium_models: true,
        premium_model_usage_limit: 100_000, # 100K premium tokens
        economy_model_discount: 0.1 # 10% discount
      }
    },
    business: {
      allowed_tiers: [ :economy, :standard, :premium ],
      monthly_token_budget: 20_000_000, # 20M tokens
      model_options: {
        can_use_premium_models: true,
        premium_model_usage_limit: 1_000_000, # 1M premium tokens
        economy_model_discount: 0.2 # 20% discount
      }
    },
    enterprise: {
      allowed_tiers: [ :economy, :standard, :premium, :enterprise ],
      monthly_token_budget: 100_000_000, # 100M tokens
      model_options: {
        can_use_premium_models: true,
        premium_model_usage_limit: 10_000_000, # 10M premium tokens
        economy_model_discount: 0.3 # 30% discount
      }
    }
  }

  # Cache TTL (time to live) settings for different content types
  CACHE_TTL = {
    email_generation: 1.hour,
    social_post_generation: 1.hour,
    campaign_analysis: 6.hours,
    audience_insights: 12.hours,
    content_optimization: 2.hours,
    subject_line_generation: 30.minutes
  }

  class << self
    # Select the optimal model based on task, importance, and tenant constraints
    def select_optimal_model(tenant:, task_type:, importance: nil, options: {})
      # Get tenant's subscription tier
      subscription_tier = tenant.subscription_tier.to_sym rescue :basic
      subscription_options = SUBSCRIPTION_TIERS[subscription_tier] || SUBSCRIPTION_TIERS[:basic]

      # Determine task importance
      task_config = TASK_TYPES[task_type.to_sym] || TASK_TYPES[:email_generation]
      importance ||= task_config[:default_importance]

      # Map importance to model tier
      tier = importance_to_tier(importance, subscription_options[:allowed_tiers], task_config)

      # Check budget constraints
      if budget_constrained?(tenant)
        # Downgrade to a lower tier if budget is constrained
        tier = downgrade_tier(tier, subscription_options[:allowed_tiers], task_config)
      end

      # Check if special handling is needed based on options
      tier = apply_special_handling(tier, options, task_type, tenant)

      # Select a specific model from the tier
      select_model_from_tier(tier, tenant, task_type)
    end

    # Generate a cache key for storing and retrieving cached responses
    def cache_key_for(prompt:, task_type:, temperature: nil, model: nil)
      # Generate a hash of the prompt to use as part of the cache key
      prompt_hash = Digest::MD5.hexdigest(prompt.to_s)

      # Build the cache key
      key_parts = [
        "ai_response",
        task_type.to_s,
        prompt_hash
      ]

      # Add optional components if present
      key_parts << "temp_#{temperature}" if temperature
      key_parts << "model_#{model}" if model

      # Join with colons to form the complete cache key
      key_parts.join(":")
    end

    # Retrieve a cached response if available
    def get_cached_response(key:)
      return nil unless key

      # Try to get from Rails cache
      cached = Rails.cache.read(key)

      if cached
        # Track cache hit
        track_cache_hit(key)
        return cached
      end

      nil
    end

    # Cache a response for future use
    def cache_response(key:, response:, ttl: nil)
      return unless key && response

      # Determine appropriate TTL if not specified
      unless ttl
        # Extract task type from cache key if possible
        task_type = key.split(":")[1]&.to_sym
        ttl = CACHE_TTL[task_type] if task_type && CACHE_TTL[task_type]
        ttl ||= 1.hour # Default TTL
      end

      # Store in Rails cache
      Rails.cache.write(key, response, expires_in: ttl)

      # Track cache storage
      track_cache_storage(key, ttl)

      response
    end

    # Check if caching is appropriate for this request
    def should_cache?(task_type:, options: {})
      # Don't cache if explicitly disabled
      return false if options[:skip_cache]

      # Check if this task type should be cached
      case task_type.to_sym
      when :email_generation, :social_post_generation, :subject_line_generation
        # Cache these types by default
        true
      when :audience_insights, :campaign_analysis
        # Only cache these if they're not real-time dependent
        !options[:real_time_data]
      when :content_optimization
        # Only cache if not A/B test dependent
        !options[:ab_test_dependent]
      else
        # Default to caching enabled for unknown types
        true
      end
    end

    # Calculate the estimated token count for a prompt
    def estimate_token_count(text)
      return 0 if text.blank?

      # Estimate based on words (rough approximation)
      words = text.split(/\s+/).size
      (words * 1.3).to_i # Roughly 1.3 tokens per word
    end

    # Calculate cost for a potential operation
    def calculate_operation_cost(model:, input_tokens:, output_tokens:)
      tier = model_to_tier(model)
      return 0 unless tier

      cost_per_1k = MODEL_TIERS[tier][:cost_per_1k_tokens]
      total_tokens = input_tokens + output_tokens

      (total_tokens.to_f / 1000) * cost_per_1k
    end

    private

    # Map importance level to model tier
    def importance_to_tier(importance, allowed_tiers, task_config)
      case importance
      when :high, :critical, :premium
        select_highest_allowed_tier([ :premium, :enterprise ], allowed_tiers)
      when :medium, :standard
        select_highest_allowed_tier([ :standard ], allowed_tiers)
      when :low, :draft, :economy
        if task_config[:economy_suitable]
          select_highest_allowed_tier([ :economy ], allowed_tiers)
        else
          select_highest_allowed_tier([ :standard ], allowed_tiers)
        end
      else
        # Default to the middle of allowed tiers
        allowed_tiers[allowed_tiers.size / 2]
      end
    end

    # Select the highest tier from desired_tiers that is also in allowed_tiers
    def select_highest_allowed_tier(desired_tiers, allowed_tiers)
      # Get all tiers in order from highest to lowest
      all_tiers = [ :enterprise, :premium, :standard, :economy ]

      # Find the highest desired tier that's also allowed
      all_tiers.each do |tier|
        if desired_tiers.include?(tier) && allowed_tiers.include?(tier)
          return tier
        end
      end

      # If no match, return the highest allowed tier
      allowed_tiers.first
    end

    # Downgrade to a lower tier if needed
    def downgrade_tier(current_tier, allowed_tiers, task_config)
      all_tiers = [ :enterprise, :premium, :standard, :economy ]
      current_index = all_tiers.index(current_tier)

      # Can't downgrade if already at lowest tier
      return current_tier if current_index == all_tiers.size - 1

      # Try the next lower tier
      next_lower_tier = all_tiers[current_index + 1]

      # If next lower tier is economy, check if suitable for this task
      if next_lower_tier == :economy && !task_config[:economy_suitable]
        return current_tier
      end

      # Only downgrade if the lower tier is allowed
      allowed_tiers.include?(next_lower_tier) ? next_lower_tier : current_tier
    end

    # Apply special handling based on options
    def apply_special_handling(tier, options, task_type, tenant)
      case task_type.to_sym
      when :email_generation
        # Upgrade for large audiences
        if options[:audience_size].to_i > 100_000 && can_use_higher_tier?(tier, tenant)
          upgrade_tier(tier, tenant)
        elsif options[:campaign_type] == "high_priority" && can_use_higher_tier?(tier, tenant)
          upgrade_tier(tier, tenant)
        else
          tier
        end
      when :audience_insights
        # Always use at least standard for audience insights
        tier = :standard if tier == :economy
        tier
      else
        tier
      end
    end

    # Check if a higher tier can be used
    def can_use_higher_tier?(current_tier, tenant)
      all_tiers = [ :economy, :standard, :premium, :enterprise ]
      current_index = all_tiers.index(current_tier)

      # Can't upgrade if already at highest tier
      return false if current_index == 0

      # Get tenant's subscription tier
      subscription_tier = tenant.subscription_tier.to_sym rescue :basic
      subscription_options = SUBSCRIPTION_TIERS[subscription_tier] || SUBSCRIPTION_TIERS[:basic]

      # Check if the next higher tier is allowed
      next_higher_tier = all_tiers[current_index - 1]
      subscription_options[:allowed_tiers].include?(next_higher_tier)
    end

    # Upgrade to a higher tier if possible
    def upgrade_tier(current_tier, tenant)
      all_tiers = [ :economy, :standard, :premium, :enterprise ]
      current_index = all_tiers.index(current_tier)

      # Can't upgrade if already at highest tier
      return current_tier if current_index == 0

      # Get tenant's subscription tier
      subscription_tier = tenant.subscription_tier.to_sym rescue :basic
      subscription_options = SUBSCRIPTION_TIERS[subscription_tier] || SUBSCRIPTION_TIERS[:basic]

      # Check if the next higher tier is allowed
      next_higher_tier = all_tiers[current_index - 1]
      subscription_options[:allowed_tiers].include?(next_higher_tier) ? next_higher_tier : current_tier
    end

    # Check if tenant is budget constrained
    def budget_constrained?(tenant)
      # Get usage statistics
      usage_stats = AiBudgetForecastingService.get_usage_statistics(tenant: tenant)

      # Consider budget constrained if over 80% of monthly budget
      usage_stats[:percent_of_budget_used] > 80
    rescue
      # If we can't get usage stats, assume not constrained
      false
    end

    # Select a specific model from the tier
    def select_model_from_tier(tier, tenant, task_type)
      models = MODEL_TIERS[tier][:models]

      # Default to first model in the tier
      models.first
    end

    # Map a model name to its tier
    def model_to_tier(model)
      MODEL_TIERS.each do |tier, config|
        return tier if config[:models].include?(model)
      end

      # Default to standard if model not found
      :standard
    end

    # Track cache hit for analytics
    def track_cache_hit(key)
      # Could be implemented to track cache performance
      # For now, just log
      Rails.logger.debug("AI cache hit: #{key}")
    end

    # Track cache storage for analytics
    def track_cache_storage(key, ttl)
      # Could be implemented to track cache usage
      # For now, just log
      Rails.logger.debug("AI cache store: #{key}, TTL: #{ttl}")
    end
  end
end
