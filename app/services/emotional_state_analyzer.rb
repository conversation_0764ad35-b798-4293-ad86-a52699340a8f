# frozen_string_literal: true

# EmotionalStateAnalyzer - AI-powered emotional state detection from behavioral signals
#
# This service implements "The Marketing Therapist AI" core intelligence by:
# - Analyzing customer behavioral signals to determine emotional state
# - Using machine learning patterns to detect emotional shifts
# - Providing confidence scoring for emotional state predictions
# - Tracking emotional triggers and context for better understanding
class EmotionalStateAnalyzer
  include ActiveModel::Model
  include ActiveModel::Attributes

  attr_accessor :customer_identifier, :behavioral_signals, :interaction_history, :tenant

  # Behavioral signal weights for emotional analysis
  SIGNAL_WEIGHTS = {
    email_engagement: {
      open_rate: 0.15,
      click_rate: 0.20,
      time_to_open: 0.10,
      time_spent_reading: 0.15,
      forward_rate: 0.10,
      unsubscribe_action: 0.30
    },
    website_behavior: {
      session_duration: 0.20,
      pages_per_session: 0.15,
      bounce_rate: 0.25,
      scroll_depth: 0.10,
      click_patterns: 0.15,
      exit_behavior: 0.15
    },
    purchase_patterns: {
      purchase_frequency: 0.25,
      cart_abandonment: 0.30,
      time_to_purchase: 0.20,
      price_sensitivity: 0.15,
      product_category_shifts: 0.10
    },
    social_interaction: {
      engagement_rate: 0.30,
      sentiment_of_comments: 0.35,
      sharing_behavior: 0.20,
      response_time: 0.15
    },
    support_interaction: {
      ticket_frequency: 0.25,
      issue_complexity: 0.20,
      resolution_satisfaction: 0.30,
      communication_tone: 0.25
    },
    timing_patterns: {
      interaction_frequency: 0.30,
      time_of_day_patterns: 0.25,
      response_speed: 0.25,
      seasonal_variations: 0.20
    }
  }.freeze

  # Emotional indicators based on behavioral patterns
  EMOTIONAL_INDICATORS = {
    joy: {
      email_engagement: { open_rate: :high, click_rate: :high, time_spent_reading: :high },
      website_behavior: { session_duration: :high, pages_per_session: :high },
      social_interaction: { engagement_rate: :high, sentiment_of_comments: :positive },
      purchase_patterns: { purchase_frequency: :increased, time_to_purchase: :decreased }
    },
    trust: {
      email_engagement: { open_rate: :consistent, forward_rate: :high },
      website_behavior: { return_visits: :high, scroll_depth: :deep },
      purchase_patterns: { purchase_frequency: :consistent, cart_abandonment: :low },
      support_interaction: { resolution_satisfaction: :high }
    },
    anticipation: {
      email_engagement: { time_to_open: :very_fast, click_rate: :high },
      website_behavior: { pages_per_session: :high, product_page_views: :increased },
      social_interaction: { engagement_rate: :increased, sharing_behavior: :high },
      timing_patterns: { interaction_frequency: :increased }
    },
    surprise: {
      website_behavior: { session_duration: :sudden_change, bounce_rate: :changed },
      timing_patterns: { interaction_patterns: :disrupted, response_speed: :varied },
      email_engagement: { open_behavior: :different_than_usual }
    },
    fear: {
      email_engagement: { open_rate: :decreased, time_to_open: :delayed },
      website_behavior: { session_duration: :decreased, exit_behavior: :quick },
      purchase_patterns: { cart_abandonment: :increased, time_to_purchase: :increased },
      support_interaction: { ticket_frequency: :increased, issue_complexity: :increased }
    },
    sadness: {
      email_engagement: { engagement: :decreased_overall },
      website_behavior: { session_duration: :decreased, interaction: :minimal },
      social_interaction: { engagement_rate: :low, response_time: :slow },
      timing_patterns: { interaction_frequency: :decreased }
    },
    anger: {
      support_interaction: { ticket_frequency: :high, communication_tone: :negative },
      social_interaction: { sentiment_of_comments: :negative, engagement_type: :confrontational },
      email_engagement: { unsubscribe_rate: :increased },
      website_behavior: { exit_behavior: :abrupt }
    },
    disgust: {
      email_engagement: { unsubscribe_action: :immediate, engagement: :minimal },
      website_behavior: { bounce_rate: :very_high, pages_per_session: :very_low },
      purchase_patterns: { purchase_frequency: :stopped, cart_abandonment: :complete },
      social_interaction: { engagement_rate: :none, sentiment: :very_negative }
    }
  }.freeze

  def initialize(customer_identifier:, behavioral_signals:, interaction_history: [], tenant:)
    @customer_identifier = customer_identifier
    @behavioral_signals = behavioral_signals || {}
    @interaction_history = interaction_history || []
    @tenant = tenant
    @ai_service = RubyLlmService.new(tenant: tenant)
  end

  # Main analysis method - determines emotional state from behavioral signals
  def analyze
    return error_result("Missing required data") unless valid_inputs?

    begin
      # Step 1: Analyze individual behavioral signals
      signal_analysis = analyze_behavioral_signals

      # Step 2: Calculate emotional scores using pattern matching
      emotion_scores = calculate_emotion_scores(signal_analysis)

      # Step 3: Determine primary emotion and confidence
      primary_analysis = determine_primary_emotion(emotion_scores)

      # Step 4: Analyze emotional context and triggers
      context_analysis = analyze_emotional_context(signal_analysis)

      # Step 5: Generate AI-enhanced insights
      ai_insights = generate_ai_insights(signal_analysis, primary_analysis, context_analysis)

      # Step 6: Calculate final confidence score
      final_confidence = calculate_confidence_score(primary_analysis, signal_analysis, ai_insights)

      {
        success: true,
        primary_emotion: primary_analysis[:emotion],
        intensity: determine_emotional_intensity(primary_analysis, signal_analysis),
        confidence: final_confidence,
        emotion_scores: emotion_scores,
        context: context_analysis,
        trigger_event: identify_trigger_event(signal_analysis),
        signals_summary: summarize_key_signals(signal_analysis),
        ai_insights: ai_insights,
        recommendations: generate_recommendations(primary_analysis, context_analysis)
      }
    rescue StandardError => e
      Rails.logger.error "EmotionalStateAnalyzer error: #{e.message}"
      error_result("Analysis failed: #{e.message}")
    end
  end

  # Analyze changes in emotional state over time
  def analyze_emotional_trajectory
    return {} if interaction_history.empty?

    recent_emotions = interaction_history.last(10).map { |interaction| interaction["emotion"] }.compact
    return {} if recent_emotions.empty?

    {
      trend: detect_emotional_trend(recent_emotions),
      volatility: calculate_emotional_volatility(recent_emotions),
      stability_score: calculate_stability_score(recent_emotions),
      dominant_emotion: recent_emotions.tally.max_by { |_, count| count }&.first,
      trajectory_insights: generate_trajectory_insights(recent_emotions)
    }
  end

  # Quick emotional state check for real-time decisions
  def quick_emotional_check
    return error_result("Insufficient data") if behavioral_signals.empty?

    # Simplified analysis for real-time use
    key_signals = extract_key_signals
    emotion_indicators = EMOTIONAL_INDICATORS.map do |emotion, patterns|
      score = calculate_quick_emotion_score(emotion, patterns, key_signals)
      [emotion, score]
    end.to_h

    primary_emotion = emotion_indicators.max_by { |_, score| score }&.first
    confidence = calculate_quick_confidence(emotion_indicators, key_signals)

    {
      success: true,
      primary_emotion: primary_emotion,
      confidence: confidence,
      quick_analysis: true,
      receptive_to_marketing: quick_receptivity_check(primary_emotion, confidence)
    }
  end

  private

  def valid_inputs?
    customer_identifier.present? && tenant.present? && behavioral_signals.is_a?(Hash)
  end

  def analyze_behavioral_signals
    analysis = {}

    SIGNAL_WEIGHTS.each do |category, weights|
      category_signals = behavioral_signals[category.to_s] || {}
      next if category_signals.empty?

      analysis[category] = analyze_signal_category(category, category_signals, weights)
    end

    analysis
  end

  def analyze_signal_category(category, signals, weights)
    category_analysis = {
      raw_signals: signals,
      normalized_scores: {},
      category_emotion_indicators: {},
      weighted_score: 0.0
    }

    weights.each do |signal_type, weight|
      raw_value = signals[signal_type.to_s]
      next unless raw_value.present?

      normalized_score = normalize_signal_value(signal_type, raw_value, category)
      category_analysis[:normalized_scores][signal_type] = normalized_score
      category_analysis[:weighted_score] += normalized_score * weight
    end

    # Analyze emotional indicators for this category
    EMOTIONAL_INDICATORS.each do |emotion, patterns|
      category_patterns = patterns[category] || {}
      next if category_patterns.empty?

      indicator_score = calculate_category_emotion_score(category_analysis[:normalized_scores], category_patterns)
      category_analysis[:category_emotion_indicators][emotion] = indicator_score
    end

    category_analysis
  end

  def normalize_signal_value(signal_type, raw_value, category)
    # Normalize signals to 0-1 scale based on signal type and historical context
    case signal_type
    when :open_rate, :click_rate, :engagement_rate
      # Percentage values
      [raw_value.to_f / 100.0, 1.0].min
    when :session_duration
      # Normalize session duration (assume good session is 5+ minutes)
      [raw_value.to_f / 300.0, 1.0].min
    when :bounce_rate
      # Bounce rate (lower is better, so invert)
      [1.0 - (raw_value.to_f / 100.0), 1.0].min
    when :purchase_frequency
      # Frequency data (normalize against historical average)
      historical_avg = get_historical_average(signal_type, category)
      return 0.5 if historical_avg.zero?
      
      [(raw_value.to_f / historical_avg), 2.0].min / 2.0
    when :response_time
      # Response time (faster is generally better, but context matters)
      # Normalize to 0-1 where 1 is very fast response
      max_response_time = 24 * 60 * 60 # 24 hours in seconds
      [1.0 - (raw_value.to_f / max_response_time), 1.0].max
    else
      # Default normalization for unknown signal types
      case raw_value
      when Numeric
        [raw_value.to_f / 100.0, 1.0].min
      when String
        convert_string_to_score(raw_value)
      else
        0.5 # Neutral score for unknown types
      end
    end
  end

  def convert_string_to_score(value)
    case value.to_s.downcase
    when "very_high", "excellent", "positive" then 0.9
    when "high", "good" then 0.75
    when "medium", "moderate", "neutral" then 0.5
    when "low", "poor" then 0.25
    when "very_low", "terrible", "negative" then 0.1
    else 0.5
    end
  end

  def get_historical_average(signal_type, category)
    # In production, this would query historical data
    # For now, return reasonable defaults based on signal type
    case signal_type
    when :purchase_frequency then 2.0  # purchases per month
    when :session_duration then 180.0  # 3 minutes
    when :open_rate then 25.0          # 25%
    when :click_rate then 3.0          # 3%
    else 1.0
    end
  end

  def calculate_category_emotion_score(normalized_scores, patterns)
    return 0.0 if patterns.empty? || normalized_scores.empty?

    total_score = 0.0
    pattern_count = 0

    patterns.each do |signal, expected_pattern|
      signal_score = normalized_scores[signal]
      next unless signal_score.present?

      pattern_score = score_against_pattern(signal_score, expected_pattern)
      total_score += pattern_score
      pattern_count += 1
    end

    return 0.0 if pattern_count.zero?
    total_score / pattern_count
  end

  def score_against_pattern(signal_score, expected_pattern)
    case expected_pattern
    when :high, :increased, :positive
      signal_score # Higher values match better
    when :low, :decreased, :negative, :minimal
      1.0 - signal_score # Lower values match better
    when :very_high
      signal_score > 0.8 ? signal_score : signal_score * 0.5
    when :very_low, :none
      signal_score < 0.2 ? (1.0 - signal_score) : (1.0 - signal_score) * 0.5
    when :consistent
      # Score based on how close to historical average
      historical_consistency_score(signal_score)
    when :sudden_change, :disrupted, :different_than_usual
      # Score based on deviation from patterns
      calculate_deviation_score(signal_score)
    else
      0.5 # Neutral score for unknown patterns
    end
  end

  def historical_consistency_score(signal_score)
    # Assume consistency is good around 0.4-0.7 range
    if signal_score.between?(0.4, 0.7)
      1.0
    else
      1.0 - (signal_score - 0.55).abs * 2
    end.clamp(0.0, 1.0)
  end

  def calculate_deviation_score(signal_score)
    # Higher deviation from normal (0.5) indicates pattern disruption
    deviation = (signal_score - 0.5).abs
    [deviation * 2, 1.0].min
  end

  def calculate_emotion_scores(signal_analysis)
    emotion_scores = Hash.new(0.0)

    # Aggregate emotion scores across all signal categories
    signal_analysis.each do |category, analysis|
      category_weight = get_category_weight(category)
      
      analysis[:category_emotion_indicators].each do |emotion, score|
        emotion_scores[emotion] += score * category_weight
      end
    end

    # Normalize scores
    max_possible_score = SIGNAL_WEIGHTS.values.map { |weights| weights.values.sum }.max
    emotion_scores.transform_values { |score| score / max_possible_score }
  end

  def get_category_weight(category)
    # Weight categories based on reliability for emotional detection
    case category
    when :email_engagement then 0.25
    when :website_behavior then 0.20
    when :social_interaction then 0.20
    when :support_interaction then 0.15
    when :purchase_patterns then 0.15
    when :timing_patterns then 0.05
    else 0.10
    end
  end

  def determine_primary_emotion(emotion_scores)
    return { emotion: "neutral", confidence: 0.0 } if emotion_scores.empty?

    # Find emotion with highest score
    primary_emotion, primary_score = emotion_scores.max_by { |_, score| score }
    
    # Calculate confidence based on score separation
    scores_array = emotion_scores.values.sort.reverse
    score_separation = scores_array[0] - (scores_array[1] || 0.0)
    
    base_confidence = primary_score * 100
    separation_bonus = score_separation * 20
    
    final_confidence = [base_confidence + separation_bonus, 100.0].min

    {
      emotion: primary_emotion.to_s,
      confidence: final_confidence.round(2),
      score: primary_score,
      score_separation: score_separation
    }
  end

  def determine_emotional_intensity(primary_analysis, signal_analysis)
    base_score = primary_analysis[:score]
    
    # Calculate intensity based on signal strength and consistency
    signal_strength = calculate_overall_signal_strength(signal_analysis)
    consistency = calculate_signal_consistency(signal_analysis)
    
    combined_intensity = (base_score + signal_strength + consistency) / 3.0
    
    case combined_intensity
    when 0.0..0.3 then "subtle"
    when 0.3..0.6 then "moderate"
    when 0.6..0.8 then "strong"
    when 0.8..1.0 then "intense"
    else "moderate"
    end
  end

  def calculate_overall_signal_strength(signal_analysis)
    return 0.0 if signal_analysis.empty?

    total_strength = signal_analysis.values.sum { |analysis| analysis[:weighted_score] }
    category_count = signal_analysis.size
    
    total_strength / category_count
  end

  def calculate_signal_consistency(signal_analysis)
    return 1.0 if signal_analysis.size < 2

    scores = signal_analysis.values.map { |analysis| analysis[:weighted_score] }
    mean = scores.sum / scores.size.to_f
    variance = scores.sum { |score| (score - mean) ** 2 } / scores.size.to_f
    
    # Convert variance to consistency score (lower variance = higher consistency)
    [1.0 - variance, 0.0].max
  end

  def analyze_emotional_context(signal_analysis)
    context = {
      primary_drivers: identify_primary_drivers(signal_analysis),
      emotional_triggers: identify_emotional_triggers(signal_analysis),
      contextual_factors: extract_contextual_factors,
      temporal_context: analyze_temporal_context,
      interaction_context: analyze_interaction_context
    }

    context
  end

  def identify_primary_drivers(signal_analysis)
    drivers = []

    signal_analysis.each do |category, analysis|
      # Find signals with strong emotional indicators
      analysis[:category_emotion_indicators].each do |emotion, score|
        if score > 0.7
          drivers << {
            category: category,
            emotion: emotion,
            strength: score,
            contributing_signals: find_contributing_signals(analysis, emotion)
          }
        end
      end
    end

    drivers.sort_by { |driver| -driver[:strength] }.first(3)
  end

  def find_contributing_signals(analysis, emotion)
    contributing = []

    analysis[:normalized_scores].each do |signal, score|
      # Check if this signal contributes to the emotion
      if signal_contributes_to_emotion?(signal, score, emotion)
        contributing << { signal: signal, score: score }
      end
    end

    contributing
  end

  def signal_contributes_to_emotion?(signal, score, emotion)
    # Simplified logic - in production this would be more sophisticated
    case emotion
    when :joy
      score > 0.6
    when :fear, :sadness, :anger, :disgust
      score < 0.4
    when :trust
      score.between?(0.5, 0.8)
    else
      score > 0.5
    end
  end

  def identify_emotional_triggers(signal_analysis)
    triggers = []

    # Look for sudden changes or extreme values that might trigger emotions
    signal_analysis.each do |category, analysis|
      analysis[:normalized_scores].each do |signal, score|
        if extreme_value?(score) || sudden_change?(signal, score)
          triggers << {
            type: :behavioral_trigger,
            category: category,
            signal: signal,
            value: score,
            trigger_strength: calculate_trigger_strength(score)
          }
        end
      end
    end

    triggers
  end

  def extreme_value?(score)
    score < 0.1 || score > 0.9
  end

  def sudden_change?(signal, current_score)
    # Compare with historical data if available
    # For now, assume change if score is significantly different from neutral
    (current_score - 0.5).abs > 0.3
  end

  def calculate_trigger_strength(score)
    # Calculate how strong the trigger is based on deviation from normal
    deviation = (score - 0.5).abs
    [deviation * 2, 1.0].min
  end

  def extract_contextual_factors
    {
      time_of_day: Time.current.hour,
      day_of_week: Time.current.wday,
      season: determine_season,
      recent_campaigns: get_recent_campaign_exposure,
      interaction_recency: calculate_interaction_recency
    }
  end

  def determine_season
    month = Date.current.month
    case month
    when 12, 1, 2 then "winter"
    when 3, 4, 5 then "spring"
    when 6, 7, 8 then "summer"
    when 9, 10, 11 then "autumn"
    end
  end

  def get_recent_campaign_exposure
    # This would query recent campaign interactions for the customer
    # For now, return placeholder data
    {
      campaigns_last_7_days: 0,
      last_campaign_type: nil,
      last_interaction_type: nil
    }
  end

  def calculate_interaction_recency
    return 0 if interaction_history.empty?

    last_interaction = interaction_history.last
    return 0 unless last_interaction["timestamp"]

    begin
      last_time = Time.parse(last_interaction["timestamp"])
      hours_since = (Time.current - last_time) / 1.hour
      
      # Convert to recency score (0-1, where 1 is very recent)
      case hours_since
      when 0..1 then 1.0
      when 1..6 then 0.8
      when 6..24 then 0.6
      when 24..72 then 0.4
      when 72..168 then 0.2
      else 0.1
      end
    rescue ArgumentError
      0.5
    end
  end

  def analyze_temporal_context
    return {} if interaction_history.empty?

    recent_interactions = interaction_history.last(5)
    
    {
      interaction_frequency: calculate_interaction_frequency(recent_interactions),
      temporal_patterns: identify_temporal_patterns(recent_interactions),
      emotional_progression: track_emotional_progression(recent_interactions)
    }
  end

  def calculate_interaction_frequency(interactions)
    return 0.0 if interactions.size < 2

    time_diffs = []
    interactions.each_cons(2) do |prev, current|
      begin
        prev_time = Time.parse(prev["timestamp"])
        current_time = Time.parse(current["timestamp"])
        time_diffs << (current_time - prev_time) / 1.hour
      rescue ArgumentError
        next
      end
    end

    return 0.0 if time_diffs.empty?

    avg_hours_between = time_diffs.sum / time_diffs.size
    
    # Convert to frequency score
    case avg_hours_between
    when 0..6 then 1.0      # Very frequent
    when 6..24 then 0.8     # Daily
    when 24..72 then 0.6    # Every few days
    when 72..168 then 0.4   # Weekly
    else 0.2                # Infrequent
    end
  end

  def identify_temporal_patterns(interactions)
    return {} if interactions.empty?

    times_of_day = interactions.map do |interaction|
      begin
        Time.parse(interaction["timestamp"]).hour
      rescue ArgumentError
        12 # Default to noon
      end
    end.compact

    {
      preferred_hours: times_of_day.tally.max_by { |_, count| count }&.first,
      interaction_spread: calculate_time_spread(times_of_day),
      consistency_score: calculate_temporal_consistency(times_of_day)
    }
  end

  def calculate_time_spread(hours)
    return 0 if hours.empty?

    hours.uniq.size / 24.0
  end

  def calculate_temporal_consistency(hours)
    return 1.0 if hours.size < 2

    # Calculate standard deviation of interaction times
    mean = hours.sum / hours.size.to_f
    variance = hours.sum { |hour| (hour - mean) ** 2 } / hours.size.to_f
    std_dev = Math.sqrt(variance)
    
    # Convert to consistency score (lower std_dev = higher consistency)
    [1.0 - (std_dev / 12.0), 0.0].max
  end

  def track_emotional_progression(interactions)
    emotions = interactions.map { |interaction| interaction["emotion"] }.compact
    return {} if emotions.size < 2

    {
      emotional_trend: detect_emotional_trend(emotions),
      stability: calculate_emotional_stability(emotions),
      progression_pattern: identify_progression_pattern(emotions)
    }
  end

  def detect_emotional_trend(emotions)
    return "stable" if emotions.size < 3

    # Simplified trend detection
    positive_emotions = %w[joy trust anticipation]
    negative_emotions = %w[fear anger sadness disgust]
    
    recent_half = emotions.last(emotions.size / 2)
    earlier_half = emotions.first(emotions.size / 2)
    
    recent_positive = recent_half.count { |e| positive_emotions.include?(e) }
    earlier_positive = earlier_half.count { |e| positive_emotions.include?(e) }
    
    if recent_positive > earlier_positive
      "improving"
    elsif recent_positive < earlier_positive
      "declining"
    else
      "stable"
    end
  end

  def calculate_emotional_stability(emotions)
    return 1.0 if emotions.size < 2

    unique_emotions = emotions.uniq.size
    total_emotions = emotions.size
    
    # Higher variety = lower stability
    stability = 1.0 - ((unique_emotions - 1) / [total_emotions, 8].min.to_f)
    [stability, 0.0].max
  end

  def identify_progression_pattern(emotions)
    return "insufficient_data" if emotions.size < 3

    # Look for common emotional patterns
    pattern_string = emotions.join("->")
    
    case pattern_string
    when /joy.*trust.*anticipation/
      "positive_building"
    when /fear.*anger.*disgust/
      "negative_spiral"
    when /(joy|trust|anticipation).*(fear|anger|sadness)/
      "positive_to_negative"
    when /(fear|anger|sadness).*(joy|trust|anticipation)/
      "recovery_pattern"
    else
      "mixed_pattern"
    end
  end

  def analyze_interaction_context
    return {} unless interaction_history.any?

    last_interaction = interaction_history.last
    
    {
      last_emotion: last_interaction["emotion"],
      last_intensity: last_interaction["intensity"],
      last_trigger: last_interaction["trigger_event"],
      interaction_quality: assess_interaction_quality(last_interaction),
      context_carryover: calculate_context_carryover
    }
  end

  def assess_interaction_quality(interaction)
    confidence = interaction["confidence"] || 50.0
    
    case confidence
    when 80..100 then "high_quality"
    when 60..79 then "moderate_quality"
    when 40..59 then "fair_quality"
    else "low_quality"
    end
  end

  def calculate_context_carryover
    return 0.0 if interaction_history.empty?

    last_interaction_time = interaction_history.last["timestamp"]
    return 0.0 unless last_interaction_time

    begin
      hours_since = (Time.current - Time.parse(last_interaction_time)) / 1.hour
      
      # Context carryover decreases over time
      case hours_since
      when 0..2 then 1.0
      when 2..6 then 0.8
      when 6..24 then 0.6
      when 24..72 then 0.4
      else 0.2
      end
    rescue ArgumentError
      0.5
    end
  end

  def identify_trigger_event(signal_analysis)
    # Find the most significant trigger event
    triggers = identify_emotional_triggers(signal_analysis)
    return "unknown" if triggers.empty?

    strongest_trigger = triggers.max_by { |trigger| trigger[:trigger_strength] }
    "#{strongest_trigger[:category]}_#{strongest_trigger[:signal]}"
  end

  def summarize_key_signals(signal_analysis)
    summary = {}

    signal_analysis.each do |category, analysis|
      # Find the most significant signals in each category
      significant_signals = analysis[:normalized_scores].select { |_, score| score > 0.7 || score < 0.3 }
      summary[category] = significant_signals unless significant_signals.empty?
    end

    summary
  end

  def generate_ai_insights(signal_analysis, primary_analysis, context_analysis)
    # Use AI to generate additional insights
    prompt = build_ai_analysis_prompt(signal_analysis, primary_analysis, context_analysis)
    
    ai_response = @ai_service.generate_content(
      prompt: prompt,
      model: "gpt-4",
      temperature: 0.3,
      max_tokens: 500
    )

    if ai_response[:success]
      parse_ai_insights(ai_response[:content])
    else
      { insights: [], confidence_adjustment: 0.0 }
    end
  end

  def build_ai_analysis_prompt(signal_analysis, primary_analysis, context_analysis)
    <<~PROMPT
      Analyze this customer's emotional state data and provide insights:

      Customer: #{customer_identifier}
      Primary Emotion: #{primary_analysis[:emotion]} (#{primary_analysis[:confidence]}% confidence)
      
      Behavioral Signal Summary:
      #{signal_analysis.to_json}
      
      Context:
      #{context_analysis.to_json}

      Please provide:
      1. Key insights about the customer's emotional state
      2. Confidence adjustment recommendation (-10 to +10)
      3. Marketing approach recommendations
      4. Risk factors to consider

      Format as JSON:
      {
        "insights": ["insight 1", "insight 2"],
        "confidence_adjustment": 5.0,
        "marketing_approach": "recommended approach",
        "risk_factors": ["risk 1", "risk 2"]
      }
    PROMPT
  end

  def parse_ai_insights(ai_content)
    begin
      JSON.parse(ai_content)
    rescue JSON::ParserError
      {
        insights: ["AI analysis available but unparseable"],
        confidence_adjustment: 0.0,
        marketing_approach: "standard",
        risk_factors: []
      }
    end
  end

  def calculate_confidence_score(primary_analysis, signal_analysis, ai_insights)
    base_confidence = primary_analysis[:confidence]
    
    # Adjust based on signal quality
    signal_quality_adjustment = calculate_signal_quality_adjustment(signal_analysis)
    
    # Adjust based on AI insights
    ai_adjustment = ai_insights["confidence_adjustment"] || 0.0
    
    # Adjust based on historical consistency
    historical_adjustment = calculate_historical_consistency_adjustment
    
    final_confidence = base_confidence + signal_quality_adjustment + ai_adjustment + historical_adjustment
    [final_confidence, 100.0].min.round(2)
  end

  def calculate_signal_quality_adjustment(signal_analysis)
    return 0.0 if signal_analysis.empty?

    # More signal categories = higher confidence
    category_bonus = [signal_analysis.size * 2, 10].min
    
    # Strong signals = higher confidence
    strong_signals = signal_analysis.values.count { |analysis| analysis[:weighted_score] > 0.7 }
    signal_strength_bonus = strong_signals * 3
    
    category_bonus + signal_strength_bonus
  end

  def calculate_historical_consistency_adjustment
    return 0.0 if interaction_history.size < 3

    # Check if current analysis is consistent with recent history
    recent_emotions = interaction_history.last(3).map { |i| i["emotion"] }.compact
    return 0.0 if recent_emotions.empty?

    # If emotions are consistent, increase confidence
    unique_recent = recent_emotions.uniq.size
    case unique_recent
    when 1 then 5.0  # Very consistent
    when 2 then 2.0  # Mostly consistent
    else 0.0         # Inconsistent
    end
  end

  def generate_recommendations(primary_analysis, context_analysis)
    recommendations = []

    emotion = primary_analysis[:emotion]
    confidence = primary_analysis[:confidence]

    # Confidence-based recommendations
    if confidence < 60.0
      recommendations << "Collect more behavioral data before making marketing decisions"
      recommendations << "Use conservative approach due to low confidence in emotional state"
    end

    # Emotion-specific recommendations
    case emotion
    when "joy", "trust", "anticipation"
      recommendations << "Customer is in receptive state - good time for promotional content"
      recommendations << "Consider upselling or cross-selling opportunities"
    when "surprise"
      recommendations << "Give customer time to process - avoid immediate sales pressure"
      recommendations << "Provide clarifying information or context"
    when "fear", "sadness"
      recommendations << "Use supportive and reassuring messaging"
      recommendations << "Avoid promotional content - focus on value and support"
    when "anger", "disgust"
      recommendations << "Avoid all marketing contact temporarily"
      recommendations << "Focus on customer service and issue resolution"
    end

    # Context-based recommendations
    if context_analysis[:interaction_context][:context_carryover] < 0.3
      recommendations << "Previous emotional context has faded - safe to re-engage"
    end

    recommendations
  end

  def extract_key_signals
    # Extract the most important signals for quick analysis
    key_signals = {}

    %w[email_engagement website_behavior social_interaction].each do |category|
      category_data = behavioral_signals[category] || {}
      next if category_data.empty?

      # Get most important signals for each category
      case category
      when "email_engagement"
        key_signals[:email_open_rate] = category_data["open_rate"]
        key_signals[:email_click_rate] = category_data["click_rate"]
      when "website_behavior"
        key_signals[:session_duration] = category_data["session_duration"]
        key_signals[:bounce_rate] = category_data["bounce_rate"]
      when "social_interaction"
        key_signals[:engagement_rate] = category_data["engagement_rate"]
        key_signals[:sentiment] = category_data["sentiment_of_comments"]
      end
    end

    key_signals.compact
  end

  def calculate_quick_emotion_score(emotion, patterns, key_signals)
    return 0.0 if patterns.empty? || key_signals.empty?

    total_score = 0.0
    pattern_count = 0

    patterns.each do |category, category_patterns|
      category_patterns.each do |signal, expected_pattern|
        signal_key = "#{category}_#{signal}".to_sym
        signal_value = key_signals[signal_key]
        next unless signal_value.present?

        normalized_value = normalize_signal_value(signal, signal_value, category)
        pattern_score = score_against_pattern(normalized_value, expected_pattern)
        
        total_score += pattern_score
        pattern_count += 1
      end
    end

    return 0.0 if pattern_count.zero?
    total_score / pattern_count
  end

  def calculate_quick_confidence(emotion_indicators, key_signals)
    return 30.0 if emotion_indicators.empty?

    # Calculate confidence based on signal strength and separation
    scores = emotion_indicators.values.sort.reverse
    top_score = scores[0] || 0.0
    second_score = scores[1] || 0.0
    
    base_confidence = top_score * 60 # Max 60% from score strength
    separation_bonus = (top_score - second_score) * 30 # Max 30% from separation
    signal_bonus = [key_signals.size * 2, 10].min # Max 10% from signal quantity
    
    [base_confidence + separation_bonus + signal_bonus, 95.0].min.round(1)
  end

  def quick_receptivity_check(emotion, confidence)
    return false if confidence < 50.0

    %w[joy trust anticipation].include?(emotion.to_s)
  end

  def error_result(message)
    {
      success: false,
      error: message
    }
  end
end
