# frozen_string_literal: true

# A utility class for constructing structured prompts for AI models
# This follows the builder pattern to create complex prompts with a fluent interface
class Prompt<PERSON>uilder
  def initialize
    @sections = []
    @system_context = []
    @examples = []
    @output_format = nil
  end
  
  # Add system context (instructions for the AI model)
  def add_system_context(context)
    @system_context << context
    self
  end
  
  # Add campaign-specific context
  def add_campaign_context(campaign)
    return self unless campaign
    
    context = <<~CONTEXT
      Campaign Information:
      - Name: #{campaign.name}
      - Type: #{campaign.type || 'marketing'}
      - Objective: #{campaign.objective || 'engage audience'}
      - Target Completion: #{campaign.target_completion_date&.strftime('%Y-%m-%d') || 'not specified'}
    CONTEXT
    
    # Add custom campaign attributes if present
    if campaign.respond_to?(:custom_attributes) && campaign.custom_attributes.present?
      context += "- Custom attributes:\n"
      campaign.custom_attributes.each do |key, value|
        context += "  - #{key}: #{value}\n"
      end
    end
    
    @sections << context
    self
  end
  
  # Add audience-specific context
  def add_audience_context(audience)
    return self unless audience
    
    context = <<~CONTEXT
      Audience Information:
      - Name: #{audience.name}
      - Size: #{audience.subscriber_count || 'unknown'} subscribers
      - Primary demographics: #{audience.primary_demographics || 'diverse'}
    CONTEXT
    
    # Add audience interests if present
    if audience.respond_to?(:interests) && audience.interests.present?
      context += "- Interests:\n"
      audience.interests.each do |interest|
        context += "  - #{interest}\n"
      end
    end
    
    # Add emotional resonance profile if present
    if audience.respond_to?(:emotional_resonance_profile) && audience.emotional_resonance_profile.present?
      context += "- Emotional resonance profile:\n"
      audience.emotional_resonance_profile.attributes.each do |key, value|
        next if key.in?(%w[id created_at updated_at audience_id])
        context += "  - #{key.humanize}: #{value}\n"
      end
    end
    
    @sections << context
    self
  end
  
  # Add brand voice instructions
  def add_brand_voice(voice)
    return self unless voice
    
    context = <<~CONTEXT
      Brand Voice:
      Write using a #{voice} tone. 
    CONTEXT
    
    # Add specific guidance for common brand voices
    case voice.downcase
    when 'professional'
      context += "Use clear, straightforward language. Be informative and helpful without being overly casual."
    when 'casual', 'friendly'
      context += "Use conversational language. Be approachable and warm, as if speaking to a friend."
    when 'authoritative'
      context += "Use confident language that establishes expertise. Be precise and substantive."
    when 'playful', 'humorous'
      context += "Incorporate appropriate humor and light-hearted language. Be entertaining while remaining respectful."
    when 'technical'
      context += "Use industry terminology appropriately. Be precise and detailed in explanations."
    when 'inspirational', 'motivational'
      context += "Use uplifting and encouraging language. Focus on possibilities and positive outcomes."
    end
    
    @sections << context
    self
  end
  
  # Add email type specific instructions
  def add_email_type(type)
    return self unless type
    
    context = "Email Type: #{type}\n"
    
    # Add specific guidance for common email types
    case type.to_s.downcase
    when 'promotional'
      context += "Create a promotional email highlighting key benefits and including a strong call-to-action."
    when 'newsletter'
      context += "Create an informative newsletter with relevant updates and valuable content for subscribers."
    when 'welcome'
      context += "Create a welcoming email for new subscribers that introduces key information and next steps."
    when 'announcement'
      context += "Create an announcement email that clearly communicates important news or updates."
    when 'follow-up'
      context += "Create a follow-up email that references previous interaction and provides additional value."
    when 'abandoned_cart', 'abandoned cart'
      context += "Create a reminder email about items left in cart, emphasizing benefits and simplifying return."
    when 're-engagement'
      context += "Create a re-engagement email to reactivate dormant subscribers with compelling reasons to return."
    end
    
    @sections << context
    self
  end
  
  # Add key message to be communicated
  def add_key_message(message)
    return self unless message && !message.empty?
    
    @sections << <<~MESSAGE
      Key Message:
      #{message}
    MESSAGE
    
    self
  end
  
  # Add structure requirements for the output
  def add_structure_requirements(structure = nil)
    structure ||= default_email_structure
    
    @sections << <<~STRUCTURE
      Output Structure Requirements:
      #{structure}
    STRUCTURE
    
    self
  end
  
  # Add example content (few-shot learning)
  def add_example(input: nil, output: nil)
    return self if input.nil? && output.nil?
    
    example = "Example:\n"
    example += "Input: #{input}\n" if input
    example += "Output: #{output}\n" if output
    
    @examples << example
    self
  end
  
  # Set the expected output format, typically as a JSON schema
  def add_output_format(format)
    if format.is_a?(Hash)
      @output_format = <<~FORMAT
        Expected Output Format (JSON):
        #{format.to_json}
      FORMAT
    else
      @output_format = <<~FORMAT
        Expected Output Format:
        #{format}
      FORMAT
    end
    
    self
  end
  
  # Add a specific task description
  def add_task(task_description)
    @sections << <<~TASK
      Task:
      #{task_description}
    TASK
    
    self
  end
  
  # Add input data to be processed
  def add_input(input_data)
    @sections << <<~INPUT
      Input:
      #{input_data}
    INPUT
    
    self
  end
  
  # Add constraints or requirements
  def add_constraints(constraints)
    return self unless constraints && !constraints.empty?
    
    if constraints.is_a?(Array)
      constraints_text = constraints.map { |c| "- #{c}" }.join("\n")
    else
      constraints_text = constraints
    end
    
    @sections << <<~CONSTRAINTS
      Constraints:
      #{constraints_text}
    CONSTRAINTS
    
    self
  end
  
  # Add personalization parameters
  def add_personalization(personalization_data)
    return self unless personalization_data && !personalization_data.empty?
    
    if personalization_data.is_a?(Hash)
      personalization_text = personalization_data.map { |k, v| "- #{k}: #{v}" }.join("\n")
    else
      personalization_text = personalization_data
    end
    
    @sections << <<~PERSONALIZATION
      Personalization Variables:
      #{personalization_text}
    PERSONALIZATION
    
    self
  end
  
  # Build the final prompt
  def build
    final_prompt = ""
    
    # Start with system context
    unless @system_context.empty?
      final_prompt += "System Instructions:\n#{@system_context.join("\n")}\n\n"
    end
    
    # Add all content sections
    @sections.each do |section|
      final_prompt += "#{section}\n"
    end
    
    # Add examples if present
    unless @examples.empty?
      final_prompt += "\nExamples:\n"
      @examples.each do |example|
        final_prompt += "#{example}\n"
      end
    end
    
    # Add output format if specified
    if @output_format
      final_prompt += "\n#{@output_format}"
    end
    
    final_prompt
  end
  
  private
  
  def default_email_structure
    <<~STRUCTURE
      Please generate an email with the following components:
      1. Subject line: Attention-grabbing and relevant to the content
      2. Email body: Well-structured with clear paragraphs
      3. Call-to-action: Clear and compelling
      4. Tone: Matching the specified brand voice
      
      The response should be in JSON format with the following structure:
      {
        "subject_line": "The email subject line",
        "content": "The full HTML content of the email",
        "cta": "The primary call-to-action text",
        "tone": "The tone used in the email"
      }
    STRUCTURE
  end
end
