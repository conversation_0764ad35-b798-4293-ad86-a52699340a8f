# frozen_string_literal: true

# Service for implementing API rate limiting using the token bucket algorithm
class AiRateLimiterService
  include ActiveModel::Model

  # Default configuration for different tiers
  TIER_CONFIGURATIONS = {
    free: {
      tokens_per_second: 1,    # Refill rate: 1 token per second
      bucket_size: 60,         # Maximum bucket capacity: 60 tokens
      initial_tokens: 60       # Initial tokens when bucket is created
    },
    standard: {
      tokens_per_second: 5,    # 5 tokens per second
      bucket_size: 300,        # 300 tokens max
      initial_tokens: 300
    },
    premium: {
      tokens_per_second: 20,   # 20 tokens per second
      bucket_size: 1200,       # 1200 tokens max
      initial_tokens: 1200
    },
    enterprise: {
      tokens_per_second: 50,   # 50 tokens per second
      bucket_size: 3000,       # 3000 tokens max
      initial_tokens: 3000
    }
  }

  # Time window for tracking rate limits (in seconds)
  DEFAULT_WINDOW = 60

  class << self
    # Check if an AI request is allowed for the given tenant and operation
    # Returns true if the request is allowed, false if rate limited
    def allowed?(tenant:, operation:, tokens: 1)
      bucket = get_or_create_bucket(tenant, operation)

      # Use mutex to ensure thread safety when consuming tokens
      result = false
      bucket[:mutex].synchronize do
        if bucket[:tokens] >= tokens
          bucket[:tokens] -= tokens
          result = true
        end
      end

      # Record statistics if the request was allowed
      if result
        record_usage(tenant, operation, tokens)
      else
        record_rate_limit(tenant, operation, tokens)
      end

      result
    end

    # Wait until the request is allowed (blocking call)
    # Returns the number of seconds waited
    def wait_until_allowed(tenant:, operation:, tokens: 1, max_wait: 30)
      start_time = Time.now

      # Try to consume tokens
      loop do
        break if allowed?(tenant: tenant, operation: operation, tokens: tokens)

        # Check if we've exceeded max wait time
        if Time.now - start_time > max_wait
          record_timeout(tenant, operation, tokens)
          raise RateLimitTimeoutError, "Rate limit timeout exceeded for #{operation} (#{max_wait}s)"
        end

        # Sleep for a short time before retrying
        sleep(0.1)
      end

      Time.now - start_time
    end

    # Get the current token count for a tenant and operation
    def current_tokens(tenant:, operation:)
      bucket = get_or_create_bucket(tenant, operation)
      bucket[:tokens]
    end

    # Get the estimated wait time (in seconds) until the tenant can consume the specified tokens
    def estimated_wait_time(tenant:, operation:, tokens: 1)
      bucket = get_or_create_bucket(tenant, operation)

      return 0 if bucket[:tokens] >= tokens

      tokens_needed = tokens - bucket[:tokens]
      tokens_needed.to_f / bucket[:refill_rate]
    end

    # Force reset a tenant's bucket (e.g., after plan upgrade)
    def reset_bucket(tenant:, operation: nil)
      tenant_id = tenant.is_a?(Tenant) ? tenant.id : tenant

      if operation
        key = bucket_key(tenant_id, operation)
        @buckets.delete(key)
      else
        # Reset all operations for the tenant
        keys_to_delete = @buckets.keys.select { |k| k.start_with?("#{tenant_id}:") }
        keys_to_delete.each { |k| @buckets.delete(k) }
      end
    end

    # Update a tenant's rate limiting configuration (e.g., after plan change)
    def update_configuration(tenant:, tier:)
      return unless TIER_CONFIGURATIONS.key?(tier.to_sym)

      tenant_id = tenant.is_a?(Tenant) ? tenant.id : tenant

      # Store the tenant's tier configuration
      @tenant_tiers[tenant_id] = tier.to_sym

      # Reset all buckets to apply new configuration
      reset_bucket(tenant: tenant_id)
    end

    private

    # Initialize the class variables
    def initialize_storage
      @buckets ||= {}
      @tenant_tiers ||= {}
      @last_refill ||= {}
      @usage_stats ||= {}
    end

    # Generate a unique key for a tenant's operation bucket
    def bucket_key(tenant_id, operation)
      "#{tenant_id}:#{operation}"
    end

    # Get or create a token bucket for the tenant and operation
    def get_or_create_bucket(tenant, operation)
      initialize_storage

      tenant_id = tenant.is_a?(Tenant) ? tenant.id : tenant
      key = bucket_key(tenant_id, operation)

      # Create the bucket if it doesn't exist
      unless @buckets[key]
        tier = get_tenant_tier(tenant_id)
        config = TIER_CONFIGURATIONS[tier]

        @buckets[key] = {
          tokens: config[:initial_tokens],
          max_tokens: config[:bucket_size],
          refill_rate: config[:tokens_per_second],
          last_updated: Time.now.to_f,
          mutex: Mutex.new
        }

        @last_refill[key] = Time.now.to_f
      end

      # Refill tokens based on elapsed time
      refill_tokens(key)

      @buckets[key]
    end

    # Refill tokens based on elapsed time
    def refill_tokens(key)
      bucket = @buckets[key]

      now = Time.now.to_f
      elapsed = now - bucket[:last_updated]

      if elapsed > 0
        # Calculate new tokens to add
        new_tokens = (elapsed * bucket[:refill_rate]).floor

        if new_tokens > 0
          bucket[:mutex].synchronize do
            bucket[:tokens] = [ bucket[:tokens] + new_tokens, bucket[:max_tokens] ].min
            bucket[:last_updated] = now
          end
        end
      end
    end

    # Get the tenant's tier, defaulting to 'standard' if not set
    def get_tenant_tier(tenant_id)
      if tenant_id.is_a?(Tenant)
        tier = tenant_id.subscription_tier&.to_sym || :standard
        tenant_id = tenant_id.id
      else
        tier = @tenant_tiers[tenant_id] || :standard
      end

      tier
    end

    # Record successful usage for analytics
    def record_usage(tenant, operation, tokens)
      initialize_storage

      tenant_id = tenant.is_a?(Tenant) ? tenant.id : tenant
      key = bucket_key(tenant_id, operation)

      @usage_stats[key] ||= {
        allowed: 0,
        limited: 0,
        timeouts: 0,
        total_tokens: 0,
        window_start: Time.now.to_f,
        window: DEFAULT_WINDOW
      }

      # Reset stats if the window has passed
      if Time.now.to_f - @usage_stats[key][:window_start] > @usage_stats[key][:window]
        @usage_stats[key] = {
          allowed: 0,
          limited: 0,
          timeouts: 0,
          total_tokens: 0,
          window_start: Time.now.to_f,
          window: DEFAULT_WINDOW
        }
      end

      @usage_stats[key][:allowed] += 1
      @usage_stats[key][:total_tokens] += tokens

      # Could send metrics to a monitoring system here
      # Example: Datadog::Statsd.increment('ai_marketing.rate_limiter.allowed', tags: ["operation:#{operation}", "tenant:#{tenant_id}"])
    end

    # Record rate limiting event for analytics
    def record_rate_limit(tenant, operation, tokens)
      initialize_storage

      tenant_id = tenant.is_a?(Tenant) ? tenant.id : tenant
      key = bucket_key(tenant_id, operation)

      @usage_stats[key] ||= {
        allowed: 0,
        limited: 0,
        timeouts: 0,
        total_tokens: 0,
        window_start: Time.now.to_f,
        window: DEFAULT_WINDOW
      }

      @usage_stats[key][:limited] += 1

      # Could send metrics to a monitoring system here
      # Example: Datadog::Statsd.increment('ai_marketing.rate_limiter.limited', tags: ["operation:#{operation}", "tenant:#{tenant_id}"])

      # Create an alert log for repeated rate limiting
      if @usage_stats[key][:limited] % 5 == 0 # Every 5 rate limits
        AlertLog.create!(
          severity: "warning",
          message: "Rate limiting frequent for #{operation}",
          source: "RateLimiter",
          metadata: {
            tenant_id: tenant_id,
            operation: operation,
            limited_count: @usage_stats[key][:limited],
            window_seconds: DEFAULT_WINDOW
          }
        )
      end
    end

    # Record timeout event for analytics
    def record_timeout(tenant, operation, tokens)
      initialize_storage

      tenant_id = tenant.is_a?(Tenant) ? tenant.id : tenant
      key = bucket_key(tenant_id, operation)

      @usage_stats[key] ||= {
        allowed: 0,
        limited: 0,
        timeouts: 0,
        total_tokens: 0,
        window_start: Time.now.to_f,
        window: DEFAULT_WINDOW
      }

      @usage_stats[key][:timeouts] += 1

      # Create an alert log for timeouts
      AlertLog.create!(
        severity: "error",
        message: "Rate limit timeout for #{operation}",
        source: "RateLimiter",
        metadata: {
          tenant_id: tenant_id,
          operation: operation,
          tokens_requested: tokens,
          current_tokens: current_tokens(tenant: tenant_id, operation: operation),
          timeout_count: @usage_stats[key][:timeouts]
        }
      )
    end
  end

  # Custom error for rate limit timeouts
  class RateLimitTimeoutError < StandardError; end
end
