# frozen_string_literal: true

module AiProvidersHelper
  # Configuration for all supported AI providers
  def ai_providers
    {
      openai: {
        name: "OpenAI",
        description: "GPT-4o, GPT-4o Mini - Industry leading language models",
        credentials_key: :openai_api_key,
        env_key: "OPENAI_API_KEY",
        url: "https://platform.openai.com/api-keys",
        strengths: [ "Creative content", "General purpose", "Function calling" ],
        cost_level: "Medium"
      },
      anthropic: {
        name: "Anthropic Claude",
        description: "Claude 3.5 Sonnet, Claude 3 Opus - Advanced reasoning",
        credentials_key: :anthropic_api_key,
        env_key: "ANTHROPIC_API_KEY",
        url: "https://console.anthropic.com/",
        strengths: [ "Complex reasoning", "Analysis", "Safety" ],
        cost_level: "Medium-High"
      },
      gemini: {
        name: "Google Gemini",
        description: "Gemini 1.5 Pro, Flash - High-volume processing",
        credentials_key: :gemini_api_key,
        env_key: "GEMINI_API_KEY",
        url: "https://makersuite.google.com/app/apikey",
        strengths: [ "Cost-effective", "High volume", "Multimodal" ],
        cost_level: "Low-Medium"
      },
      deepseek: {
        name: "DeepSeek",
        description: "Most cost-effective option for high-volume tasks",
        credentials_key: :deepseek_api_key,
        env_key: "DEEPSEEK_API_KEY",
        url: "https://platform.deepseek.com/api_keys",
        strengths: [ "Ultra low cost", "High volume", "Code generation" ],
        cost_level: "Very Low"
      },
      openrouter: {
        name: "OpenRouter",
        description: "Access to multiple models through one API",
        credentials_key: :openrouter_api_key,
        env_key: "OPENROUTER_API_KEY",
        url: "https://openrouter.ai/keys",
        strengths: [ "Model variety", "Unified API", "Flexibility" ],
        cost_level: "Variable"
      }
    }
  end

  # Check if a specific provider is available
  def provider_available?(provider_key)
    RubyLLMExtensions.check_provider_health(provider_key.to_s)
  rescue
    false
  end

  # Count of available providers
  def available_providers_count
    @available_providers_count ||= ai_providers.keys.count { |key| provider_available?(key) }
  end

  # Get list of available provider names
  def available_provider_names
    ai_providers.select { |key, _| provider_available?(key) }.values.map { |config| config[:name] }
  end

  # Check if provider selection UI should be shown
  def show_provider_selection?
    available_providers_count > 1
  end

  # Get current provider strategy (this would come from user/tenant settings)
  def current_provider_strategy
    # This could be customized based on current user/tenant preferences
    :balanced
  end

  # Get description for a provider strategy
  def strategy_description(strategy)
    case strategy.to_sym
    when :cost_sensitive
      "Lowest cost options"
    when :creative_content
      "Best for creative tasks"
    when :data_analysis
      "Optimized for analysis"
    when :complex_reasoning
      "Advanced reasoning capabilities"
    when :balanced
      "Balance of cost and performance"
    when :performance_optimized
      "Highest quality results"
    else
      "Automatic selection"
    end
  end

  # Get provider status with detailed information
  def provider_status_details
    ai_providers.map do |key, config|
      available = provider_available?(key)
      api_key = get_provider_api_key(key, config)

      {
        key: key,
        name: config[:name],
        available: available,
        configured: api_key.present?,
        placeholder: api_key&.include?("placeholder") || api_key&.include?("your_"),
        status: determine_provider_status(available, api_key)
      }
    end
  end

  # Get provider recommendations based on task type
  def recommended_providers_for_task(task_type)
    strategies = RubyLLMExtensions::PROVIDER_STRATEGIES[task_type.to_sym] || []
    available_strategies = strategies.select do |provider_model|
      provider = provider_model.split("-").first
      provider_available?(provider)
    end

    available_strategies.map do |provider_model|
      provider = provider_model.split("-").first
      ai_providers[provider.to_sym]&.merge(model: provider_model)
    end.compact
  end

  # Generate provider setup instructions
  def provider_setup_instructions(provider_key)
    config = ai_providers[provider_key]
    return nil unless config

    {
      name: config[:name],
      url: config[:url],
      env_key: config[:env_key],
      credentials_key: config[:credentials_key],
      description: config[:description]
    }
  end

  # Get cost comparison between providers
  def provider_cost_comparison
    RubyLLMExtensions::PROVIDER_COSTS.map do |model, details|
      provider = model.split("-").first
      provider_config = ai_providers[provider.to_sym]

      next unless provider_config

      {
        provider: provider_config[:name],
        model: model,
        cost_per_token: details[:cost_per_token],
        cost_per_1k_tokens: (details[:cost_per_token] * 1000).round(6),
        capabilities: details[:capabilities],
        available: provider_available?(provider)
      }
    end.compact.sort_by { |p| p[:cost_per_token] }
  end

  private

  # Get API key for a provider
  def get_provider_api_key(provider_key, config)
    # Check credentials first, then environment
    Rails.application.credentials.send(config[:credentials_key]) || ENV[config[:env_key]]
  rescue
    nil
  end

  # Determine the status of a provider
  def determine_provider_status(available, api_key)
    if !api_key.present?
      :not_configured
    elsif api_key.include?("placeholder") || api_key.include?("your_")
      :placeholder
    elsif available
      :active
    else
      :error
    end
  end
end
