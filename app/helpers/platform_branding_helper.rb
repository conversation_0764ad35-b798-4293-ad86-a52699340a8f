# Platform Branding Helper
# Provides brand colors and styling for social media platforms
module PlatformBrandingHelper
  PLATFORM_BRAND_COLORS = {
    "facebook" => {
      primary: "#1877F2",
      secondary: "#42a5f5",
      bg_class: "bg-facebook",
      text_class: "text-white",
      border_class: "border-facebook",
      hover_class: "hover:bg-facebook-dark"
    },
    "twitter" => {
      primary: "#000000",
      secondary: "#1DA1F2",
      bg_class: "bg-twitter",
      text_class: "text-white",
      border_class: "border-twitter",
      hover_class: "hover:bg-gray-800"
    },
    "instagram" => {
      primary: "#E4405F",
      secondary: "#F56040",
      bg_class: "bg-instagram",
      text_class: "text-white",
      border_class: "border-instagram",
      hover_class: "hover:bg-instagram-dark"
    },
    "linkedin" => {
      primary: "#0A66C2",
      secondary: "#004182",
      bg_class: "bg-linkedin",
      text_class: "text-white",
      border_class: "border-linkedin",
      hover_class: "hover:bg-linkedin-dark"
    },
    "youtube" => {
      primary: "#FF0000",
      secondary: "#CC0000",
      bg_class: "bg-youtube",
      text_class: "text-white",
      border_class: "border-youtube",
      hover_class: "hover:bg-youtube-dark"
    },
    "tiktok" => {
      primary: "#000000",
      secondary: "#FF0050",
      bg_class: "bg-tiktok",
      text_class: "text-white",
      border_class: "border-tiktok",
      hover_class: "hover:bg-gray-800"
    }
  }.freeze

  def platform_brand_color(platform_name, type = :primary)
    colors = PLATFORM_BRAND_COLORS[platform_name.to_s.downcase]
    return "#6B7280" unless colors # Default gray for unknown platforms
    colors[type]
  end

  def platform_icon_classes(platform_name, base_classes = "")
    colors = PLATFORM_BRAND_COLORS[platform_name.to_s.downcase]
    return "#{base_classes} text-gray-500" unless colors

    "#{base_classes} text-white"
  end

  def platform_background_classes(platform_name, additional_classes = "")
    colors = PLATFORM_BRAND_COLORS[platform_name.to_s.downcase]
    return "#{additional_classes} bg-gray-500" unless colors

    "#{additional_classes} #{colors[:bg_class]}"
  end

  def platform_button_classes(platform_name, base_classes = "")
    colors = PLATFORM_BRAND_COLORS[platform_name.to_s.downcase]
    return "#{base_classes} bg-gray-500 hover:bg-gray-600 text-white" unless colors

    "#{base_classes} #{colors[:bg_class]} #{colors[:hover_class]} #{colors[:text_class]}"
  end

  def platform_border_classes(platform_name, additional_classes = "")
    colors = PLATFORM_BRAND_COLORS[platform_name.to_s.downcase]
    return "#{additional_classes} border-gray-300" unless colors

    "#{additional_classes} #{colors[:border_class]}"
  end
end
