# frozen_string_literal: true

module DashboardHelper
  # AI Agent Orchestration Helper Methods
  
  def active_workflows_count
    current_tenant.agent_workflows.active.count
  end

  def orchestration_workflows_count
    current_tenant.agent_workflows.active.by_type(:marketing_orchestration).count
  end

  def email_specialist_workflows_count
    current_tenant.agent_workflows.active.by_type(:email_specialist).count
  end

  def social_specialist_workflows_count
    current_tenant.agent_workflows.active.by_type(:social_specialist).count
  end

  def seo_specialist_workflows_count
    current_tenant.agent_workflows.active.by_type(:seo_specialist).count
  end

  def active_workflows
    current_tenant.agent_workflows
                  .active
                  .includes(:campaign)
                  .recent
                  .limit(10)
  end

  def total_workflows_count
    current_tenant.agent_workflows.count
  end

  def workflow_success_rate
    finished_workflows = current_tenant.agent_workflows.where(status: [:completed, :failed])
    return 0 if finished_workflows.empty?

    successful_workflows = finished_workflows.completed
    ((successful_workflows.count.to_f / finished_workflows.count) * 100).round
  end

  def average_workflow_duration
    completed_workflows = current_tenant.agent_workflows
                                       .completed
                                       .where.not(started_at: nil)
                                       .where.not(completed_at: nil)

    return "-" if completed_workflows.empty?

    total_duration = completed_workflows.sum do |workflow|
      workflow.completed_at - workflow.started_at
    end

    average_seconds = total_duration / completed_workflows.count
    format_workflow_duration(average_seconds)
  end

  def parallel_workflows_count
    current_tenant.agent_workflows.running.count
  end

  def format_workflow_duration(duration_in_seconds)
    return "-" if duration_in_seconds.nil?

    duration = duration_in_seconds.to_i
    
    hours = duration / 3600
    minutes = (duration % 3600) / 60
    seconds = duration % 60

    if hours > 0
      "#{hours}h #{minutes}m"
    elsif minutes > 0
      "#{minutes}m"
    else
      "#{seconds}s"
    end
  end

  # Agent Status Helper Methods
  
  def agent_status_color(agent_type)
    case agent_type
    when :marketing_orchestration
      active_count = orchestration_workflows_count
    when :email_specialist
      active_count = email_specialist_workflows_count
    when :social_specialist
      active_count = social_specialist_workflows_count
    when :seo_specialist
      active_count = seo_specialist_workflows_count
    else
      active_count = 0
    end

    if active_count > 0
      "green" # Operational
    else
      "yellow" # Developing/Queued
    end
  end

  def agent_status_text(agent_type)
    case agent_status_color(agent_type)
    when "green"
      "Operational"
    when "yellow"
      "Developing"
    else
      "Offline"
    end
  end

  # Performance Metrics Helper Methods
  
  def agent_performance_metrics
    {
      total_workflows: total_workflows_count,
      success_rate: workflow_success_rate,
      average_duration: average_workflow_duration,
      parallel_jobs: parallel_workflows_count
    }
  end

  def workflow_trend_data
    # Get workflow completion data for the last 7 days
    end_date = Date.current
    start_date = end_date - 6.days

    trend_data = (start_date..end_date).map do |date|
      workflows_count = current_tenant.agent_workflows
                                     .where(completed_at: date.beginning_of_day..date.end_of_day)
                                     .count
      {
        date: date.strftime("%m/%d"),
        completed: workflows_count
      }
    end

    trend_data
  end

  def workflow_type_distribution
    total = current_tenant.agent_workflows.count
    return {} if total.zero?

    distribution = current_tenant.agent_workflows
                                .group(:workflow_type)
                                .count
                                .transform_values { |count| ((count.to_f / total) * 100).round(1) }

    distribution
  end

  def recent_workflow_activities
    current_tenant.agent_workflows
                  .includes(:campaign)
                  .recent
                  .limit(5)
                  .map do |workflow|
      {
        id: workflow.id,
        type: workflow.workflow_type.humanize,
        campaign: workflow.campaign.name,
        status: workflow.status,
        updated_at: time_ago_in_words(workflow.updated_at)
      }
    end
  end

  # Real-time Status Helper Methods
  
  def live_workflow_status(workflow_id)
    workflow = current_tenant.agent_workflows.find_by(id: workflow_id)
    return { status: 'not_found' } unless workflow

    {
      status: workflow.status,
      progress: workflow.progress_percent,
      current_step: workflow.current_step,
      updated_at: workflow.updated_at.iso8601
    }
  end

  def system_health_status
    active_count = active_workflows_count
    error_count = current_tenant.agent_workflows
                               .where(status: :failed)
                               .where(updated_at: 1.hour.ago..Time.current)
                               .count

    if error_count > 3
      { status: 'degraded', message: 'Multiple workflow failures detected' }
    elsif active_count > 10
      { status: 'busy', message: 'High workflow volume' }
    elsif active_count > 0
      { status: 'operational', message: 'All systems operational' }
    else
      { status: 'idle', message: 'No active workflows' }
    end
  end

  # UI Helper Methods
  
  def workflow_progress_bar_class(workflow)
    case workflow.status
    when 'running'
      'bg-gradient-to-r from-blue-500 to-cyan-500'
    when 'completed'
      'bg-gradient-to-r from-green-500 to-emerald-500'
    when 'failed'
      'bg-gradient-to-r from-red-500 to-rose-500'
    when 'cancelled'
      'bg-gray-400'
    else
      'bg-gray-300'
    end
  end

  def workflow_icon_class(workflow_type)
    case workflow_type.to_s
    when 'marketing_orchestration'
      'text-indigo-600'
    when 'email_specialist'
      'text-emerald-600'
    when 'social_specialist'
      'text-purple-600'
    when 'seo_specialist'
      'text-orange-600'
    else
      'text-gray-600'
    end
  end

  def workflow_background_class(workflow_type)
    case workflow_type.to_s
    when 'marketing_orchestration'
      'bg-indigo-100'
    when 'email_specialist'
      'bg-emerald-100'
    when 'social_specialist'
      'bg-purple-100'
    when 'seo_specialist'
      'bg-orange-100'
    else
      'bg-gray-100'
    end
  end

  # Turbo Stream Helper Methods
  
  def broadcast_workflow_update(workflow)
    Turbo::StreamsChannel.broadcast_replace_to(
      "agent_workflows_#{workflow.tenant_id}",
      target: "workflow-item-#{workflow.id}",
      partial: "dashboard/workflow_item",
      locals: { workflow: workflow }
    )
  end

  def broadcast_metrics_update
    Turbo::StreamsChannel.broadcast_replace_to(
      "agent_workflows_#{current_tenant.id}",
      target: "agent-performance-metrics",
      partial: "dashboard/agent_performance_metrics",
      locals: { metrics: agent_performance_metrics }
    )
  end

  private

  def current_tenant
    @current_tenant ||= ActsAsTenant.current_tenant
  end
end
