# frozen_string_literal: true

module ApplicationHelper
  # Safe asset URL helper that returns nil if asset doesn't exist
  def safe_asset_url(asset_name)
    return nil unless asset_exists?(asset_name)
    asset_url(asset_name)
  end

  # Check if an asset exists in the asset pipeline
  def asset_exists?(asset_name)
    Rails.application.assets_manifest.assets.key?(asset_name) ||
      Rails.root.join("app", "assets", "images", asset_name).exist?
  rescue
    false
  end

  # Generate a default social media image URL
  def default_social_image_url
    # You can replace this with a service like social-image generators
    # or use a default image hosted on your CDN
    "https://via.placeholder.com/1200x630/3B82F6/FFFFFF?text=AI+Marketing+Hub"
  end

  # Get Open Graph image URL with fallback
  def og_image_url(custom_image = nil)
    return safe_asset_url(custom_image) if custom_image && asset_exists?(custom_image)
    safe_asset_url("marketing-hub-og-image.svg") || default_social_image_url
  end

  # Get Twitter card image URL with fallback
  def twitter_image_url(custom_image = nil)
    return safe_asset_url(custom_image) if custom_image && asset_exists?(custom_image)
    safe_asset_url("marketing-hub-twitter-card.svg") || default_social_image_url
  end
end
