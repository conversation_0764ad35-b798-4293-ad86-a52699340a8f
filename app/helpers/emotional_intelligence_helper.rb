# frozen_string_literal: true

# EmotionalIntelligenceHelper - Helper methods for Marketing Therapist AI views
module EmotionalIntelligenceHelper
  # Emotion emoji mappings based on <PERSON>lutchik's emotion wheel
  def emotion_emoji(emotion)
    case emotion.to_s.downcase
    when "joy" then "😊"
    when "trust" then "🤝"
    when "fear" then "😰"
    when "surprise" then "😲"
    when "sadness" then "😢"
    when "disgust" then "🤢"
    when "anger" then "😠"
    when "anticipation" then "🤔"
    else "😐" # neutral
    end
  end

  # Intensity level emoji representations
  def intensity_emoji(intensity)
    case intensity.to_s.downcase
    when "subtle" then "🔅"
    when "moderate" then "🔆"
    when "strong" then "🔥"
    when "intense" then "💥"
    else "📊"
    end
  end

  # Confidence score emoji representations
  def confidence_emoji(confidence_score)
    case confidence_score
    when 0..40 then "❓"
    when 41..60 then "🤷"
    when 61..80 then "👍"
    when 81..100 then "🎯"
    else "📊"
    end
  end

  # Border color classes for different emotions
  def emotion_border_color(emotion)
    case emotion.to_s.downcase
    when "joy" then "border-yellow-500 bg-yellow-50"
    when "trust" then "border-blue-500 bg-blue-50"
    when "fear" then "border-purple-500 bg-purple-50"
    when "surprise" then "border-pink-500 bg-pink-50"
    when "sadness" then "border-gray-500 bg-gray-50"
    when "disgust" then "border-green-500 bg-green-50"
    when "anger" then "border-red-500 bg-red-50"
    when "anticipation" then "border-orange-500 bg-orange-50"
    else "border-gray-300 bg-gray-50"
    end
  end

  # Text color classes for compatibility scores
  def compatibility_score_color(score)
    case score
    when 0..30 then "text-red-600"
    when 31..60 then "text-yellow-600"
    when 61..80 then "text-blue-600"
    when 81..100 then "text-green-600"
    else "text-gray-600"
    end
  end

  # Background color classes for different insight types
  def insight_border_color(type)
    case type.to_s
    when "trend" then "border-l-4 border-blue-500"
    when "alert" then "border-l-4 border-yellow-500"
    when "opportunity" then "border-l-4 border-green-500"
    when "warning" then "border-l-4 border-red-500"
    else "border-l-4 border-gray-500"
    end
  end

  # Icons for different insight types
  def insight_icon(type)
    case type.to_s
    when "trend" then "📈"
    when "alert" then "⚠️"
    when "opportunity" then "🚀"
    when "warning" then "🚨"
    when "data_collection" then "📊"
    when "adaptation" then "🔄"
    when "timing_opportunity" then "⏰"
    when "timing_caution" then "⏸️"
    else "💡"
    end
  end

  # Background colors for recommendation priorities
  def recommendation_bg_color(priority)
    case priority.to_s
    when "high" then "bg-red-50 border-red-200"
    when "medium" then "bg-yellow-50 border-yellow-200"
    when "low" then "bg-green-50 border-green-200"
    else "bg-gray-50 border-gray-200"
    end
  end

  # Icons for different recommendation types
  def recommendation_icon(type)
    case type.to_s
    when "opportunity" then "🚀"
    when "caution" then "⚠️"
    when "data_collection" then "📊"
    when "adaptation" then "🔄"
    when "timing" then "⏰"
    when "channel_warning" then "📢"
    when "timing_opportunity" then "🎯"
    when "timing_caution" then "⏸️"
    else "💡"
    end
  end

  # Badge colors for priority levels
  def priority_badge_color(priority)
    case priority.to_s
    when "critical" then "bg-red-100 text-red-800"
    when "high" then "bg-orange-100 text-orange-800"
    when "medium" then "bg-yellow-100 text-yellow-800"
    when "low" then "bg-green-100 text-green-800"
    else "bg-gray-100 text-gray-800"
    end
  end

  # Badge colors for emotional trends
  def trend_badge_color(trend)
    case trend.to_s
    when "improving" then "bg-green-100 text-green-800"
    when "declining" then "bg-red-100 text-red-800"
    when "stable" then "bg-blue-100 text-blue-800"
    when "volatile" then "bg-orange-100 text-orange-800"
    else "bg-gray-100 text-gray-800"
    end
  end

  # Format emotional state for display
  def format_emotional_state(state)
    return "Unknown" unless state.present?

    parts = []
    parts << state.current_emotion.humanize if state.current_emotion.present?
    parts << "(#{state.emotional_intensity})" if state.emotional_intensity.present?
    parts << "#{state.confidence_score.round(1)}%" if state.confidence_score.present?

    parts.join(" ")
  end

  # Format confidence score with appropriate styling
  def format_confidence_score(score, include_emoji: true)
    return "Unknown" unless score.present?

    formatted = "#{score.round(1)}%"
    formatted = "#{confidence_emoji(score)} #{formatted}" if include_emoji

    content_tag :span, formatted, class: confidence_score_class(score)
  end

  # CSS classes for confidence scores
  def confidence_score_class(score)
    case score
    when 0..40 then "text-red-600 font-medium"
    when 41..60 then "text-yellow-600 font-medium"
    when 61..80 then "text-blue-600 font-medium"
    when 81..100 then "text-green-600 font-bold"
    else "text-gray-600"
    end
  end

  # Format emotional intensity with styling
  def format_emotional_intensity(intensity, include_emoji: true)
    return "Unknown" unless intensity.present?

    formatted = intensity.humanize
    formatted = "#{intensity_emoji(intensity)} #{formatted}" if include_emoji

    content_tag :span, formatted, class: intensity_class(intensity)
  end

  # CSS classes for emotional intensity
  def intensity_class(intensity)
    case intensity.to_s.downcase
    when "subtle" then "text-gray-600"
    when "moderate" then "text-blue-600"
    when "strong" then "text-orange-600 font-medium"
    when "intense" then "text-red-600 font-bold"
    else "text-gray-600"
    end
  end

  # Format compatibility score with appropriate styling and icon
  def format_compatibility_score(score, include_recommendation: false)
    return "Unknown" unless score.present?

    emoji = case score
    when 0..30 then "❌"
    when 31..60 then "⚠️"
    when 61..80 then "✅"
    when 81..100 then "🎯"
    else "❓"
    end

    recommendation = if include_recommendation
      case score
      when 0..30 then " (Avoid)"
      when 31..60 then " (Caution)"
      when 61..80 then " (Good)"
      when 81..100 then " (Excellent)"
      else ""
      end
    else
      ""
    end

    content_tag :span, class: compatibility_score_class(score) do
      "#{emoji} #{score.round(1)}%#{recommendation}"
    end
  end

  # CSS classes for compatibility scores
  def compatibility_score_class(score)
    case score
    when 0..30 then "text-red-600 font-medium"
    when 31..60 then "text-yellow-600 font-medium"
    when 61..80 then "text-blue-600 font-medium"
    when 81..100 then "text-green-600 font-bold"
    else "text-gray-600"
    end
  end

  # Format receptivity status with appropriate styling
  def format_receptivity_status(receptive, include_emoji: true)
    if receptive
      emoji = include_emoji ? "✅ " : ""
      content_tag :span, "#{emoji}Receptive", class: "text-green-600 font-medium"
    else
      emoji = include_emoji ? "❌ " : ""
      content_tag :span, "#{emoji}Not Receptive", class: "text-red-600 font-medium"
    end
  end

  # Generate recommendation summary for display
  def format_recommendation_summary(recommendations)
    return "No recommendations" if recommendations.blank?

    high_priority = recommendations.count { |r| r[:priority] == "high" }
    medium_priority = recommendations.count { |r| r[:priority] == "medium" }

    parts = []
    parts << "#{high_priority} urgent" if high_priority > 0
    parts << "#{medium_priority} important" if medium_priority > 0

    return "#{recommendations.size} recommendations" if parts.empty?

    "#{parts.join(', ')} recommendations"
  end

  # Format emotional journey summary
  def format_journey_summary(journey)
    return "No journey data" unless journey.present? && journey[:journey_points].present?

    points = journey[:journey_points]
    return "Insufficient data" if points.size < 2

    stability = (journey[:emotional_stability] * 100).round(1)
    dominant = journey[:dominant_emotions]&.first&.first&.humanize || "Unknown"

    "#{points.size} interactions • #{stability}% stable • #{dominant} dominant"
  end

  # Generate alert badge based on emotional state
  def emotional_state_alert_badge(state)
    if state.current_emotion.in?(%w[anger disgust]) && state.confidence_score > 70
      content_tag :span, "🚨 High Risk",
                  class: "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800"
    elsif !state.receptive_to_marketing? && state.confidence_score > 60
      content_tag :span, "⚠️ Not Receptive",
                  class: "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800"
    elsif state.receptive_to_marketing? && state.confidence_score > 70
      content_tag :span, "✅ Opportunity",
                  class: "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800"
    elsif state.confidence_score < 50
      content_tag :span, "❓ Low Confidence",
                  class: "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
    else
      content_tag :span, "📊 Monitored",
                  class: "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
    end
  end

  # Format time since last interaction with appropriate urgency
  def format_interaction_recency(last_interaction_at)
    return content_tag(:span, "Never", class: "text-gray-500") unless last_interaction_at

    time_ago = time_ago_in_words(last_interaction_at)
    hours_ago = (Time.current - last_interaction_at) / 1.hour

    css_class = case hours_ago
    when 0..2 then "text-green-600 font-medium" # Very recent
    when 2..12 then "text-blue-600"              # Recent
    when 12..48 then "text-yellow-600"           # Moderately old
    else "text-red-600"                          # Stale
    end

    emoji = case hours_ago
    when 0..2 then "🔥 "  # Hot
    when 2..12 then "⏰ " # Recent
    when 12..48 then "📅 " # Aging
    else "❄️ "            # Cold
    end

    content_tag :span, "#{emoji}#{time_ago} ago", class: css_class
  end

  # Generate quick action buttons based on emotional state
  def emotional_state_quick_actions(state)
    actions = []

    if state.receptive_to_marketing? && state.confidence_score > 70
      actions << {
        label: "🚀 Engage Now",
        class: "bg-green-600 hover:bg-green-700 text-white",
        action: "engage"
      }
    end

    if state.current_emotion.in?(%w[anger disgust]) && state.confidence_score > 60
      actions << {
        label: "⏸️ Pause Campaigns",
        class: "bg-red-600 hover:bg-red-700 text-white",
        action: "pause"
      }
    end

    if state.confidence_score < 50
      actions << {
        label: "📊 Collect Data",
        class: "bg-blue-600 hover:bg-blue-700 text-white",
        action: "collect_data"
      }
    end

    if state.significant_emotional_change?
      actions << {
        label: "🔄 Review Campaigns",
        class: "bg-orange-600 hover:bg-orange-700 text-white",
        action: "review"
      }
    end

    # Always include view details action
    actions << {
      label: "👁️ View Details",
      class: "bg-gray-600 hover:bg-gray-700 text-white",
      action: "view_details"
    }

    actions
  end

  # Format behavioral signals summary for display
  def format_behavioral_signals_summary(signals)
    return "No signals" if signals.blank?

    categories = signals.keys
    signal_count = signals.values.map { |category_signals| category_signals.keys.size }.sum

    "#{categories.size} categories, #{signal_count} signals"
  end

  # Generate emotional state trend arrow
  def emotional_trend_arrow(trajectory_analysis)
    return "➡️" unless trajectory_analysis.present? && trajectory_analysis[:trend].present?

    case trajectory_analysis[:trend].to_s
    when "improving" then "📈"
    when "declining" then "📉"
    when "stable" then "➡️"
    when "volatile" then "📊"
    else "❓"
    end
  end

  # Format prediction summary for campaign compatibility
  def format_prediction_summary(prediction)
    return "No prediction available" unless prediction.present?

    engagement = prediction[:predicted_engagement] || 0
    risk_count = prediction[:risk_factors]&.size || 0

    emoji = case engagement
    when 0..30 then "❌"
    when 31..60 then "⚠️"
    when 61..80 then "✅"
    when 81..100 then "🎯"
    else "❓"
    end

    risk_text = risk_count > 0 ? " (#{risk_count} risks)" : ""

    "#{emoji} #{engagement.round(1)}% engagement#{risk_text}"
  end

  # Generate campaign risk warning badge
  def campaign_risk_warning(prediction)
    return unless prediction.present? && prediction[:risk_factors].present?

    high_risks = prediction[:risk_factors].count { |risk| risk[:severity] == "high" }
    medium_risks = prediction[:risk_factors].count { |risk| risk[:severity] == "medium" }

    if high_risks > 0
      content_tag :span, "🚨 #{high_risks} High Risk#{high_risks > 1 ? 's' : ''}",
                  class: "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800"
    elsif medium_risks > 0
      content_tag :span, "⚠️ #{medium_risks} Risk#{medium_risks > 1 ? 's' : ''}",
                  class: "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800"
    else
      content_tag :span, "✅ Low Risk",
                  class: "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800"
    end
  end

  # Format adaptation summary for display
  def format_adaptation_summary(adaptation)
    return "No adaptations" unless adaptation.present?

    message = adaptation[:message] || "Adaptation applied"
    severity = adaptation[:severity] || "info"
    timestamp = adaptation[:timestamp] || Time.current

    time_text = time_ago_in_words(timestamp)

    emoji = case severity
    when "high", "critical" then "🚨"
    when "medium" then "⚠️"
    when "low", "info" then "ℹ️"
    else "📝"
    end

    "#{emoji} #{message} (#{time_text} ago)"
  end

  # Generate dashboard summary statistics
  def format_dashboard_stats(stats)
    return {} unless stats.present?

    {
      total_customers: stats[:total_customers] || 0,
      receptive_percentage: stats[:total_customers] > 0 ?
        (stats[:receptive_customers].to_f / stats[:total_customers] * 100).round(1) : 0,
      avg_confidence: stats[:avg_confidence] || 0,
      emotion_distribution: stats[:emotion_distribution] || {}
    }
  end

  # Helper to determine if emotional data is fresh (within acceptable time bounds)
  def emotional_data_fresh?(last_interaction_at, threshold_hours = 24)
    return false unless last_interaction_at.present?

    Time.current - last_interaction_at < threshold_hours.hours
  end

  # Generate data freshness indicator
  def data_freshness_indicator(last_interaction_at)
    return content_tag(:span, "❄️ Stale", class: "text-gray-500") unless last_interaction_at

    hours_ago = (Time.current - last_interaction_at) / 1.hour

    if hours_ago < 2
      content_tag :span, "🔥 Fresh", class: "text-green-600 font-medium"
    elsif hours_ago < 12
      content_tag :span, "⏰ Recent", class: "text-blue-600"
    elsif hours_ago < 48
      content_tag :span, "📅 Aging", class: "text-yellow-600"
    else
      content_tag :span, "❄️ Stale", class: "text-red-600"
    end
  end
end
