module PlatformConfigurationsHelper
  include PlatformBrandingHelper
  
  # Helper methods for platform configurations
  # Platform branding methods are available through PlatformBrandingHelper

  def platform_connection_status_badge(platform_configuration)
    if platform_configuration.is_active?
      content_tag :span, 'Connected',
        class: 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800'
    else
      content_tag :span, 'Disconnected',
        class: 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800'
    end
  end

  def platform_oauth_status_text(oauth_token)
    return 'No OAuth token' unless oauth_token

    if oauth_token.expired?
      'Token expired'
    elsif oauth_token.expires_soon?
      'Token expires soon'
    else
      'Token valid'
    end
  end
end
