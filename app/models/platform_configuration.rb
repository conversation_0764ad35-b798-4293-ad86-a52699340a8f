# frozen_string_literal: true

class PlatformConfiguration < ApplicationRecord
  # Multi-tenancy
  acts_as_tenant(:tenant)

  # Associations
  belongs_to :user
  belongs_to :tenant
  has_many :oauth_tokens, dependent: :destroy

  # Validations
  validates :platform_name, presence: true,
                           inclusion: { in: %w[twitter facebook instagram linkedin tiktok youtube] }
  validates :platform_name, uniqueness: { scope: :user_id }
  validates :is_active, inclusion: { in: [ true, false ] }
  validates :auto_sync_enabled, inclusion: { in: [ true, false ] }
  validates :posting_enabled, inclusion: { in: [ true, false ] }
  validates :analytics_enabled, inclusion: { in: [ true, false ] }

  # Scopes
  scope :active, -> { where(is_active: true) }
  scope :inactive, -> { where(is_active: false) }
  scope :by_platform, ->(platform) { where(platform_name: platform) }

  # Instance methods
  def connected?
    is_active? && oauth_tokens.active.exists?
  end

  def current_token
    oauth_tokens.active.order(created_at: :desc).first
  end

  def expired_token?
    current_token&.expires_at && current_token.expires_at < Time.current
  end

  def needs_refresh?
    expired_token? && current_token&.refresh_token.present?
  end

  def platform_display_name
    platform_name.titleize
  end
end
