# frozen_string_literal: true

# VibeCampaignOrchestration - Orchestrates multi-campaign vibe marketing strategies
#
# This model manages complex vibe marketing workflows including:
# - Multi-campaign emotional journeys
# - Cultural moment capitalization
# - Cross-platform vibe synchronization
# - Adaptive vibe optimization
class VibeCampaignOrchestration < ApplicationRecord
  belongs_to :campaign_collection
  belongs_to :tenant
  has_many :campaigns, through: :campaign_collection
  has_many :vibe_analysis_records, through: :campaigns
  has_many :authenticity_checks, through: :campaigns
  has_many :emotional_resonance_profiles, through: :campaigns

  # Attribute declarations for enum fields
  attribute :vibe_strategy_type, :string
  attribute :orchestration_status, :string
  attribute :priority_level, :string

  # Validations
  validates :orchestration_name, presence: true
  validates :vibe_strategy_type, presence: true
  validates :orchestration_status, presence: true
  validates :target_vibe_score, presence: true,
            inclusion: { in: 0.0..10.0, message: "must be between 0.0 and 10.0" }
  validates :current_vibe_score, presence: true,
            inclusion: { in: 0.0..10.0, message: "must be between 0.0 and 10.0" }

  # Enums
  enum :vibe_strategy_type, {
    emotional_journey_mapping: "emotional_journey_mapping",
    cultural_moment_surfing: "cultural_moment_surfing",
    authenticity_amplification: "authenticity_amplification",
    psychographic_targeting: "psychographic_targeting",
    cross_platform_sync: "cross_platform_sync",
    adaptive_vibe_optimization: "adaptive_vibe_optimization",
    seasonal_vibe_alignment: "seasonal_vibe_alignment"
  }

  enum :orchestration_status, {
    planning: "planning",
    analyzing: "analyzing",
    active: "active",
    optimizing: "optimizing",
    paused: "paused",
    completed: "completed",
    failed: "failed"
  }

  enum :priority_level, {
    low: "low",
    medium: "medium",
    high: "high",
    critical: "critical"
  }

  # Callbacks
  after_create :initialize_orchestration_data
  after_update :track_orchestration_changes

  # Scopes
  scope :active_orchestrations, -> { where(orchestration_status: [ "active", "optimizing" ]) }
  scope :high_performing, -> { where("current_vibe_score >= ?", 7.0) }
  scope :needs_optimization, -> { where("current_vibe_score < target_vibe_score") }
  scope :by_strategy_type, ->(type) { where(vibe_strategy_type: type) }
  scope :recent_orchestrations, -> { where(created_at: 30.days.ago..Time.current) }

  # Instance Methods

  # Get orchestration configuration data
  def orchestration_config
    orchestration_data&.dig("config") || {}
  end

  # Get current orchestration metrics
  def orchestration_metrics
    orchestration_data&.dig("metrics") || {}
  end

  # Get adaptive optimization rules
  def optimization_rules
    orchestration_data&.dig("optimization_rules") || {}
  end

  # Calculate overall orchestration health score
  def orchestration_health_score
    return 0.0 unless orchestration_data.present?

    vibe_score_weight = 0.4
    campaign_alignment_weight = 0.3
    execution_efficiency_weight = 0.2
    audience_engagement_weight = 0.1

    vibe_health = (current_vibe_score / 10.0) * vibe_score_weight
    alignment_health = campaign_alignment_score * campaign_alignment_weight
    efficiency_health = execution_efficiency_score * execution_efficiency_weight
    engagement_health = audience_engagement_score * audience_engagement_weight

    total_health = vibe_health + alignment_health + efficiency_health + engagement_health
    (total_health * 10).round(2)
  end

  # Get campaign alignment score (how well campaigns work together)
  def campaign_alignment_score
    return 0.0 unless campaigns.count > 1

    emotional_consistency = calculate_emotional_consistency
    timing_coordination = calculate_timing_coordination
    message_coherence = calculate_message_coherence

    ((emotional_consistency + timing_coordination + message_coherence) / 3).round(2)
  end

  # Calculate execution efficiency score
  def execution_efficiency_score
    metrics = orchestration_metrics

    timeline_adherence = metrics.dig("timeline_adherence") || 0.0
    resource_utilization = metrics.dig("resource_utilization") || 0.0
    automation_effectiveness = metrics.dig("automation_effectiveness") || 0.0

    ((timeline_adherence + resource_utilization + automation_effectiveness) / 3).round(2)
  end

  # Calculate audience engagement score across all campaigns
  def audience_engagement_score
    return 0.0 unless campaigns.any?

    engagement_scores = campaigns.map do |campaign|
      campaign.vibe_analysis_records.last&.confidence_score || 0.0
    end

    return 0.0 if engagement_scores.empty?

    (engagement_scores.sum / engagement_scores.size).round(2)
  end

  # Get emotional journey progression
  def emotional_journey_progression
    return {} unless vibe_strategy_type == "emotional_journey_mapping"

    journey_data = orchestration_config.dig("emotional_journey") || {}
    campaigns_ordered = campaigns.joins(:emotional_resonance_profiles)
                               .order("emotional_resonance_profiles.created_at")

    progression = campaigns_ordered.map.with_index do |campaign, index|
      profile = campaign.emotional_resonance_profiles.last
      {
        step: index + 1,
        campaign_id: campaign.id,
        emotion: profile&.primary_emotion,
        resonance_score: profile&.resonance_strength || 0.0,
        transition_effectiveness: calculate_transition_effectiveness(index, campaigns_ordered)
      }
    end

    {
      total_steps: progression.size,
      current_step: determine_current_journey_step,
      progression: progression,
      journey_completion: calculate_journey_completion_percentage
    }
  end

  # Get cultural moment capitalization status
  def cultural_moment_status
    return {} unless vibe_strategy_type == "cultural_moment_surfing"

    cultural_data = orchestration_config.dig("cultural_moments") || {}
    active_moments = CulturalMoment.currently_trending

    {
      targeted_moments: cultural_data.dig("targeted_moments") || [],
      active_capitalizations: calculate_active_capitalizations(active_moments),
      opportunity_score: calculate_cultural_opportunity_score(active_moments),
      timing_effectiveness: cultural_data.dig("timing_effectiveness") || 0.0
    }
  end

  # Get cross-platform synchronization status
  def cross_platform_sync_status
    return {} unless vibe_strategy_type == "cross_platform_sync"

    sync_data = orchestration_config.dig("platform_sync") || {}
    platforms = campaigns.joins(:campaign_metrics)
                        .pluck("campaign_metrics.platform")
                        .uniq

    {
      synchronized_platforms: platforms,
      sync_effectiveness: calculate_sync_effectiveness,
      message_consistency: calculate_cross_platform_consistency,
      timing_coordination: sync_data.dig("timing_coordination_score") || 0.0
    }
  end

  # Get adaptive optimization recommendations
  def adaptive_optimization_recommendations
    recommendations = []

    # Vibe score optimization
    if current_vibe_score < target_vibe_score
      gap = target_vibe_score - current_vibe_score
      recommendations << {
        type: "vibe_optimization",
        priority: gap > 2.0 ? "high" : "medium",
        action: "Improve overall vibe scoring",
        specifics: generate_vibe_improvement_actions(gap)
      }
    end

    # Campaign alignment optimization
    if campaign_alignment_score < 7.0
      recommendations << {
        type: "alignment_optimization",
        priority: "medium",
        action: "Improve campaign coordination",
        specifics: generate_alignment_improvement_actions
      }
    end

    # Emotional journey optimization
    if vibe_strategy_type == "emotional_journey_mapping"
      journey_issues = identify_journey_issues
      if journey_issues.any?
        recommendations << {
          type: "journey_optimization",
          priority: "high",
          action: "Optimize emotional journey flow",
          specifics: journey_issues
        }
      end
    end

    recommendations
  end

  # Trigger orchestration optimization
  def optimize_orchestration!
    return false unless can_optimize?

    optimization_results = perform_optimization_analysis

    if optimization_results[:success]
      update_orchestration_with_optimizations(optimization_results[:optimizations])
      update(
        orchestration_status: "optimizing",
        optimization_count: optimization_count + 1,
        last_optimized_at: Time.current
      )
      true
    else
      false
    end
  end

  # Check if orchestration can be optimized
  def can_optimize?
    orchestration_status.in?([ "active", "paused" ]) &&
    current_vibe_score < target_vibe_score &&
    (last_optimized_at.nil? || last_optimized_at < 6.hours.ago)
  end

  # Generate comprehensive orchestration report
  def generate_orchestration_report
    {
      orchestration_id: id,
      name: orchestration_name,
      strategy_type: vibe_strategy_type,
      status: orchestration_status,
      priority: priority_level,

      performance_metrics: {
        current_vibe_score: current_vibe_score,
        target_vibe_score: target_vibe_score,
        health_score: orchestration_health_score,
        campaign_alignment: campaign_alignment_score,
        execution_efficiency: execution_efficiency_score,
        audience_engagement: audience_engagement_score
      },

      strategy_specific_data: strategy_specific_report_data,

      optimization_data: {
        optimization_count: optimization_count,
        last_optimized_at: last_optimized_at,
        recommendations: adaptive_optimization_recommendations
      },

      campaign_data: {
        total_campaigns: campaigns.count,
        active_campaigns: campaigns.where(status: "active").count,
        completed_campaigns: campaigns.where(status: "completed").count
      },

      timeline: {
        created_at: created_at,
        started_at: started_at,
        expected_completion: expected_completion_date,
        duration_so_far: calculate_duration_so_far
      }
    }
  end

  private

  # Initialize orchestration data on creation
  def initialize_orchestration_data
    initial_data = {
      config: generate_initial_config,
      metrics: initialize_metrics,
      optimization_rules: set_default_optimization_rules
    }

    update_column(:orchestration_data, initial_data)
  end

  # Track orchestration changes
  def track_orchestration_changes
    if saved_changes.key?("current_vibe_score") || saved_changes.key?("orchestration_status")
      # Log changes for analytics
      update_orchestration_metrics
    end
  end

  # Calculate emotional consistency across campaigns
  def calculate_emotional_consistency
    return 10.0 unless campaigns.count > 1

    primary_emotions = campaigns.joins(:emotional_resonance_profiles)
                               .pluck("emotional_resonance_profiles.emotion_wheel_data")
                               .map { |data| data&.dig("primary_emotion") }
                               .compact

    return 0.0 if primary_emotions.empty?

    # Calculate consistency based on emotional journey strategy
    if vibe_strategy_type == "emotional_journey_mapping"
      calculate_journey_emotional_consistency(primary_emotions)
    else
      calculate_general_emotional_consistency(primary_emotions)
    end
  end

  # Calculate timing coordination score
  def calculate_timing_coordination
    return 10.0 unless campaigns.count > 1

    campaign_schedules = campaigns.pluck(:created_at, :updated_at)
    # Simplified timing coordination calculation
    8.0 # This would be more complex in a real implementation
  end

  # Calculate message coherence score
  def calculate_message_coherence
    return 10.0 unless campaigns.count > 1

    # This would analyze message consistency across campaigns
    7.5 # Simplified for now
  end

  # Calculate transition effectiveness for emotional journey
  def calculate_transition_effectiveness(step_index, campaigns_ordered)
    return 10.0 if step_index == 0 || step_index >= campaigns_ordered.size - 1

    current_emotion = campaigns_ordered[step_index]&.emotional_resonance_profiles&.last&.primary_emotion
    next_emotion = campaigns_ordered[step_index + 1]&.emotional_resonance_profiles&.last&.primary_emotion

    return 5.0 unless current_emotion && next_emotion

    # Use emotion transition logic from EmotionalResonanceProfile
    transition_score = 8.0 # Simplified calculation
    transition_score
  end

  # Determine current step in emotional journey
  def determine_current_journey_step
    active_campaigns = campaigns.where(status: "active")
    return 0 if active_campaigns.empty?

    # Return the index of the first active campaign + 1
    campaigns.order(:created_at).find_index(active_campaigns.first) + 1
  end

  # Calculate journey completion percentage
  def calculate_journey_completion_percentage
    return 100.0 unless vibe_strategy_type == "emotional_journey_mapping"

    total_campaigns = campaigns.count
    completed_campaigns = campaigns.where(status: "completed").count

    return 0.0 if total_campaigns.zero?

    ((completed_campaigns.to_f / total_campaigns) * 100).round(2)
  end

  # Calculate active cultural moment capitalizations
  def calculate_active_capitalizations(active_moments)
    campaign_cultural_refs = campaigns.joins(:vibe_analysis_records)
                                   .where("vibe_analysis_records.analysis_type = ?", "cultural")
                                   .count

    {
      active_count: campaign_cultural_refs,
      potential_opportunities: active_moments.count,
      capitalization_rate: campaign_cultural_refs.to_f / [ active_moments.count, 1 ].max
    }
  end

  # Calculate cultural opportunity score
  def calculate_cultural_opportunity_score(active_moments)
    return 0.0 if active_moments.empty?

    avg_relevance = active_moments.average(:relevance_score) || 0.0
    avg_engagement = active_moments.average(:engagement_potential) || 0.0

    ((avg_relevance + avg_engagement) / 2).round(2)
  end

  # Calculate cross-platform synchronization effectiveness
  def calculate_sync_effectiveness
    # This would analyze timing, messaging, and coordination across platforms
    7.8 # Simplified for now
  end

  # Calculate cross-platform message consistency
  def calculate_cross_platform_consistency
    # This would analyze message consistency across different platforms
    8.2 # Simplified for now
  end

  # Generate vibe improvement actions based on gap
  def generate_vibe_improvement_actions(gap)
    actions = []

    if gap > 3.0
      actions << "Conduct comprehensive vibe analysis of all campaigns"
      actions << "Review and optimize emotional targeting strategy"
    elsif gap > 1.5
      actions << "Fine-tune campaign messaging for better emotional resonance"
      actions << "Optimize timing and sequencing of campaign elements"
    else
      actions << "Make minor adjustments to campaign tone and style"
    end

    actions
  end

  # Generate alignment improvement actions
  def generate_alignment_improvement_actions
    [
      "Improve coordination between campaign teams",
      "Establish clearer messaging guidelines across campaigns",
      "Synchronize campaign timing and release schedules",
      "Review and align emotional targeting strategies"
    ]
  end

  # Identify issues in emotional journey
  def identify_journey_issues
    issues = []

    journey_data = emotional_journey_progression
    progression = journey_data[:progression] || []

    progression.each_with_index do |step, index|
      if step[:transition_effectiveness] < 6.0
        issues << "Weak emotional transition at step #{index + 1}"
      end

      if step[:resonance_score] < 6.0
        issues << "Low emotional resonance at step #{index + 1}"
      end
    end

    issues
  end

  # Perform optimization analysis
  def perform_optimization_analysis
    # This would use AI/ML to analyze current performance and suggest optimizations
    {
      success: true,
      optimizations: {
        vibe_adjustments: [ "Increase emotional intensity", "Improve cultural relevance" ],
        timing_adjustments: [ "Delay campaign 2 by 24 hours", "Extend campaign 1 duration" ],
        message_adjustments: [ "Strengthen call-to-action", "Add more emotional triggers" ]
      }
    }
  end

  # Update orchestration with optimization results
  def update_orchestration_with_optimizations(optimizations)
    current_data = orchestration_data || {}
    current_data["optimizations"] ||= []
    current_data["optimizations"] << {
      timestamp: Time.current,
      applied_optimizations: optimizations,
      pre_optimization_score: current_vibe_score
    }

    update_column(:orchestration_data, current_data)
  end

  # Generate strategy-specific report data
  def strategy_specific_report_data
    case vibe_strategy_type
    when "emotional_journey_mapping"
      emotional_journey_progression
    when "cultural_moment_surfing"
      cultural_moment_status
    when "cross_platform_sync"
      cross_platform_sync_status
    else
      {}
    end
  end

  # Generate initial configuration
  def generate_initial_config
    {
      strategy_parameters: {},
      timing_rules: {},
      optimization_thresholds: {
        min_vibe_score: 6.0,
        min_alignment_score: 7.0,
        optimization_frequency: 6.hours
      }
    }
  end

  # Initialize metrics
  def initialize_metrics
    {
      timeline_adherence: 10.0,
      resource_utilization: 8.0,
      automation_effectiveness: 7.0
    }
  end

  # Set default optimization rules
  def set_default_optimization_rules
    {
      auto_optimize: true,
      optimization_triggers: [ "low_vibe_score", "poor_alignment", "low_engagement" ],
      max_optimizations_per_day: 3
    }
  end

  # Update orchestration metrics
  def update_orchestration_metrics
    # Update metrics based on current state
    # This would be more complex in a real implementation
  end

  # Calculate journey emotional consistency
  def calculate_journey_emotional_consistency(emotions)
    # For emotional journeys, consistency means logical emotional progression
    # This is a simplified calculation
    8.5
  end

  # Calculate general emotional consistency
  def calculate_general_emotional_consistency(emotions)
    return 10.0 if emotions.uniq.size == 1

    # For non-journey strategies, some variety is good but not too much
    variety_penalty = emotions.uniq.size * 0.5
    [ 10.0 - variety_penalty, 0.0 ].max
  end

  # Calculate duration so far
  def calculate_duration_so_far
    return 0 unless started_at

    end_time = orchestration_status == "completed" ? updated_at : Time.current
    ((end_time - started_at) / 1.day).round(2)
  end
end
