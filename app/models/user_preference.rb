# frozen_string_literal: true

# == Schema Information
# Table name: user_preferences
#
#  id                   :bigint           not null, primary key
#  user_id              :bigint           not null
#  tenant_id            :bigint           not null
#  timezone             :string           default("UTC")
#  language             :string           default("en")
#  date_format          :string           default("MM/DD/YYYY")
#  theme                :string           default("light")
#  email_notifications  :boolean          default(true)
#  push_notifications   :boolean          default(true)
#  sms_notifications    :boolean          default(false)
#  marketing_emails     :boolean          default(true)
#  product_updates      :boolean          default(true)
#  security_alerts      :boolean          default(true)
#  created_at           :datetime         not null
#  updated_at           :datetime         not null
#
# Indexes
#
#  index_user_preferences_on_tenant_id  (tenant_id)
#  index_user_preferences_on_user_id    (user_id) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (tenant_id => tenants.id)
#  fk_rails_...  (user_id => users.id)
#

class UserPreference < ApplicationRecord
  # Multi-tenancy
  acts_as_tenant(:tenant)
  belongs_to :tenant
  belongs_to :user

  # Validations
  validates :user_id, uniqueness: { scope: :tenant_id }
  validates :timezone, presence: true, inclusion: { in: ActiveSupport::TimeZone.all.map(&:name) }
  validates :language, presence: true, inclusion: { in: %w[en es fr de it pt ja ko zh] }
  validates :date_format, presence: true, inclusion: { in: %w[MM/DD/YYYY DD/MM/YYYY YYYY-MM-DD] }
  validates :theme, presence: true, inclusion: { in: %w[light dark auto] }

  # Attribute declarations for boolean fields
  attribute :email_notifications, :boolean, default: true
  attribute :push_notifications, :boolean, default: true
  attribute :sms_notifications, :boolean, default: false
  attribute :marketing_emails, :boolean, default: true
  attribute :product_updates, :boolean, default: true
  attribute :security_alerts, :boolean, default: true

  # Dashboard preferences (stored as JSON or separate fields)
  attribute :dashboard_layout, :string
  attribute :dashboard_refresh_interval, :integer
  serialize :visible_widgets, Array

  # Callbacks
  after_initialize :set_defaults

  # Scopes
  scope :with_email_notifications, -> { where(email_notifications: true) }
  scope :with_push_notifications, -> { where(push_notifications: true) }
  scope :with_marketing_emails, -> { where(marketing_emails: true) }

  # Instance methods
  def timezone_object
    ActiveSupport::TimeZone[timezone]
  end

  def formatted_date(date)
    case date_format
    when 'MM/DD/YYYY'
      date.strftime('%m/%d/%Y')
    when 'DD/MM/YYYY'
      date.strftime('%d/%m/%Y')
    when 'YYYY-MM-DD'
      date.strftime('%Y-%m-%d')
    else
      date.strftime('%m/%d/%Y')
    end
  end

  def language_name
    case language
    when 'en' then 'English'
    when 'es' then 'Español'
    when 'fr' then 'Français'
    when 'de' then 'Deutsch'
    when 'it' then 'Italiano'
    when 'pt' then 'Português'
    when 'ja' then '日本語'
    when 'ko' then '한국어'
    when 'zh' then '中文'
    else 'English'
    end
  end

  def notification_summary
    enabled_notifications = []
    enabled_notifications << 'Email' if email_notifications?
    enabled_notifications << 'Push' if push_notifications?
    enabled_notifications << 'SMS' if sms_notifications?
    
    if enabled_notifications.any?
      enabled_notifications.join(', ')
    else
      'None'
    end
  end

  # Class methods
  def self.default_preferences
    {
      timezone: 'UTC',
      language: 'en',
      date_format: 'MM/DD/YYYY',
      theme: 'light',
      email_notifications: true,
      push_notifications: true,
      sms_notifications: false,
      marketing_emails: true,
      product_updates: true,
      security_alerts: true
    }
  end

  def self.available_timezones
    ActiveSupport::TimeZone.all.map { |tz| [tz.to_s, tz.name] }
  end

  def self.available_languages
    [
      ['English', 'en'],
      ['Español', 'es'],
      ['Français', 'fr'],
      ['Deutsch', 'de'],
      ['Italiano', 'it'],
      ['Português', 'pt'],
      ['日本語', 'ja'],
      ['한국어', 'ko'],
      ['中文', 'zh']
    ]
  end

  def self.available_date_formats
    [
      ['MM/DD/YYYY (US)', 'MM/DD/YYYY'],
      ['DD/MM/YYYY (EU)', 'DD/MM/YYYY'],
      ['YYYY-MM-DD (ISO)', 'YYYY-MM-DD']
    ]
  end

  def self.available_themes
    [
      ['Light', 'light'],
      ['Dark', 'dark'],
      ['Auto (System)', 'auto']
    ]
  end

  private

  def set_defaults
    return unless new_record?

    self.timezone ||= 'UTC'
    self.language ||= 'en'
    self.date_format ||= 'MM/DD/YYYY'
    self.theme ||= 'light'
    self.email_notifications = true if email_notifications.nil?
    self.push_notifications = true if push_notifications.nil?
    self.sms_notifications = false if sms_notifications.nil?
    self.marketing_emails = true if marketing_emails.nil?
    self.product_updates = true if product_updates.nil?
    self.security_alerts = true if security_alerts.nil?

    # Dashboard preferences
    self.dashboard_layout ||= 'standard'
    self.dashboard_refresh_interval ||= 30000
    self.visible_widgets ||= ['campaigns', 'vibe_metrics', 'performance', 'budget']
  end
end
