# frozen_string_literal: true

# == Schema Information
#
# Table name: audience_insights
#
#  id           :bigint           not null, primary key
#  audience_id  :bigint           not null
#  insight_type :string           not null
#  data         :jsonb            default({}), not null
#  confidence   :decimal(5,2)
#  source       :string
#  created_at   :datetime         not null
#  updated_at   :datetime         not null
#

class AudienceInsight < ApplicationRecord
  # Associations
  belongs_to :audience, required: true

  # Validations
  validates :insight_type, presence: true, inclusion: {
    in: %w[demographic behavioral cultural engagement performance preference]
  }
  validates :confidence, numericality: {
    greater_than_or_equal_to: 0.0,
    less_than_or_equal_to: 100.0
  }, allow_nil: true

  # Enums
  enum :insight_type, {
    demographic: "demographic",
    behavioral: "behavioral",
    cultural: "cultural",
    engagement: "engagement",
    performance: "performance",
    preference: "preference"
  }

  # Scopes
  scope :high_confidence, -> { where("confidence >= ?", 80.0) }
  scope :recent, -> { order(created_at: :desc) }
  scope :by_type, ->(type) { where(insight_type: type) }

  # Instance methods
  def high_confidence?
    confidence && confidence >= 80.0
  end

  def summary
    data.dig("summary") || "#{insight_type.humanize} insight"
  end

  def key_findings
    data.dig("key_findings") || []
  end

  def recommendations
    data.dig("recommendations") || []
  end

  def metrics
    data.dig("metrics") || {}
  end

  def trends
    data.dig("trends") || {}
  end

  def age_in_days
    (Time.current - created_at) / 1.day
  end

  def stale?
    age_in_days > 30 # Consider insights stale after 30 days
  end
end
