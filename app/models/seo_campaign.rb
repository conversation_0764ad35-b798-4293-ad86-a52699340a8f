# frozen_string_literal: true

# == Schema Information
# Table name: seo_campaigns
#
#  id                   :bigint           not null, primary key
#  campaign_id          :bigint           not null
#  target_keywords      :text
#  meta_title           :string(60)
#  meta_description     :text
#  content_strategy     :jsonb            default({}), not null
#  technical_settings   :jsonb            default({}), not null
#  seo_settings         :jsonb            default({}), not null
#  created_at           :datetime         not null
#  updated_at           :datetime         not null
#
# Indexes
#
#  index_seo_campaigns_on_campaign_id     (campaign_id) UNIQUE
#  index_seo_campaigns_on_content_strategy (content_strategy) USING gin
#  index_seo_campaigns_on_seo_settings    (seo_settings) USING gin
#
# Foreign Keys
#
#  fk_rails_...  (campaign_id => campaigns.id)
#

class SeoCampaign < ApplicationRecord
  # Associations
  belongs_to :campaign, required: true

  # Validations
  validates :target_keywords, presence: true
  validates :meta_title, presence: true, length: { maximum: 60 }
  validates :meta_description, presence: true, length: { maximum: 160 }
  validates :content_strategy, presence: true
  validate :keywords_format
  validate :content_strategy_structure

  # Scopes
  scope :ready_to_optimize, -> {
    joins(:campaign)
      .where(campaigns: { status: "active" })
      .where.not(target_keywords: [ nil, "" ])
      .where.not(meta_title: [ nil, "" ])
      .where.not(meta_description: [ nil, "" ])
  }

  # Delegate tenant access through campaign
  def tenant
    campaign.tenant
  end

  # Instance methods
  def ready_to_optimize?
    return false unless campaign&.active?
    return false if target_keywords.blank?
    return false if meta_title.blank?
    return false if meta_description.blank?
    return false unless valid?

    true
  end

  def keyword_list
    return [] if target_keywords.blank?

    target_keywords.split(/[,\n]/).map(&:strip).reject(&:blank?)
  end

  def primary_keyword
    keyword_list.first
  end

  def secondary_keywords
    keyword_list[1..-1] || []
  end

  def content_pillars
    content_strategy.dig("pillars") || []
  end

  def target_pages
    content_strategy.dig("target_pages") || []
  end

  def optimization_score
    score = 0
    total_checks = 8

    # Basic SEO elements
    score += 1 if meta_title.present? && meta_title.length.between?(30, 60)
    score += 1 if meta_description.present? && meta_description.length.between?(120, 160)
    score += 1 if keyword_list.any?
    score += 1 if primary_keyword.present? && meta_title.downcase.include?(primary_keyword.downcase)

    # Content strategy
    score += 1 if content_pillars.any?
    score += 1 if target_pages.any?

    # Technical settings
    score += 1 if technical_settings.dig("schema_markup").present?
    score += 1 if technical_settings.dig("internal_linking_strategy").present?

    ((score.to_f / total_checks) * 100).round
  end

  def estimated_traffic_increase
    keyword_count = keyword_list.count
    base_increase = keyword_count * 50 # Base estimate per keyword

    # Adjust based on optimization score
    multiplier = optimization_score / 100.0
    (base_increase * multiplier).round
  end

  def competition_level
    seo_settings.dig("competition_analysis", "level") || "medium"
  end

  def difficulty_score
    seo_settings.dig("keyword_difficulty") || 50
  end

  def content_gaps
    seo_settings.dig("content_gaps") || []
  end

  def backlink_opportunities
    seo_settings.dig("backlink_opportunities") || []
  end

  private

  def keywords_format
    return if target_keywords.blank?

    if keyword_list.empty?
      errors.add(:target_keywords, "must contain at least one valid keyword")
    end

    if keyword_list.any? { |keyword| keyword.length > 100 }
      errors.add(:target_keywords, "individual keywords must be less than 100 characters")
    end
  end

  def content_strategy_structure
    return if content_strategy.blank?

    required_keys = %w[pillars target_pages content_calendar]
    missing_keys = required_keys - content_strategy.keys

    if missing_keys.any?
      errors.add(:content_strategy, "must include: #{missing_keys.join(', ')}")
    end
  end
end
