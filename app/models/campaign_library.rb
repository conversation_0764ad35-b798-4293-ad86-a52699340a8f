# frozen_string_literal: true

# == Schema Information
#
# Table name: campaign_libraries
#
#  id          :bigint           not null, primary key
#  name        :string           not null
#  description :text
#  library_type :string          not null
#  tags        :string           default([]), is an array
#  is_public   :boolean          default(false), not null
#  tenant_id   :bigint           not null
#  created_by_id :bigint         not null
#  created_at  :datetime         not null
#  updated_at  :datetime         not null
#
# Indexes
#
#  idx_campaign_libraries_created_by  (created_by_id)
#  idx_campaign_libraries_tenant      (tenant_id)
#  index_campaign_libraries_on_name_and_tenant_id  (name,tenant_id) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (created_by_id => users.id)
#  fk_rails_...  (tenant_id => tenants.id)
#

class CampaignLibrary < ApplicationRecord
  # Multi-tenancy
  acts_as_tenant(:tenant)
  belongs_to :tenant, required: true
  belongs_to :created_by, class_name: "User", required: true

  # Associations
  has_many :campaign_library_items, dependent: :destroy
  has_many :campaigns, through: :campaign_library_items

  # Validations
  validates :name, presence: true, uniqueness: { scope: :tenant_id }
  validates :library_type, presence: true, inclusion: { in: %w[template best_practice archive shared] }
  validates :tags, length: { maximum: 10 }, allow_blank: true

  # Scopes
  scope :by_type, ->(type) { where(library_type: type) }
  scope :public_libraries, -> { where(is_public: true) }
  scope :private_libraries, -> { where(is_public: false) }
  scope :with_tags, ->(tags) { where("tags && ARRAY[?]::varchar[]", Array(tags)) }
  scope :recent, ->(limit = 10) { order(created_at: :desc).limit(limit) }

  # Instance methods
  def campaign_count
    campaigns.count
  end

  def duplicate_for_user(user)
    new_library = self.dup
    new_library.name = "#{name} (Copy)"
    new_library.created_by = user
    new_library.is_public = false
    new_library.save!

    # Duplicate campaign library items
    campaign_library_items.each do |item|
      new_library.campaign_library_items.create!(
        campaign: item.campaign,
        notes: item.notes,
        added_by: user
      )
    end

    new_library
  end

  def add_campaign(campaign, user, notes = nil)
    return false if campaigns.include?(campaign)

    campaign_library_items.create!(
      campaign: campaign,
      added_by: user,
      notes: notes
    )
  end

  def remove_campaign(campaign)
    item = campaign_library_items.find_by(campaign: campaign)
    item&.destroy
  end

  def type_color
    case library_type
    when "template" then "blue"
    when "best_practice" then "green"
    when "archive" then "gray"
    when "shared" then "purple"
    else "gray"
    end
  end
end
