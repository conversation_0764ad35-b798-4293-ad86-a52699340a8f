# frozen_string_literal: true

# == Schema Information
# Table name: customer_emotional_states
#
#  id                    :bigint           not null, primary key
#  tenant_id             :bigint           not null
#  customer_identifier   :string           not null
#  current_emotion       :string           not null
#  emotional_intensity   :string           default("moderate")
#  confidence_score      :decimal(5, 2)
#  context_data          :jsonb            default({})
#  behavioral_signals    :jsonb            default({})
#  interaction_history   :jsonb            default([])
#  last_interaction_at   :datetime
#  created_at            :datetime         not null
#  updated_at            :datetime         not null
#
# CustomerEmotionalState - Real-time tracking of customer emotional states
#
# This model provides the core emotional intelligence for "The Marketing Therapist AI":
# - Tracks current emotional state based on behavioral signals
# - Maintains interaction history for emotional journey mapping
# - Provides confidence scoring for AI decision making
# - Enables real-time campaign adaptation based on emotional context
class CustomerEmotionalState < ApplicationRecord
  # Multi-tenancy
  acts_as_tenant(:tenant)
  belongs_to :tenant, required: true

  # Plutchik's 8 primary emotions
  PLUTCHIK_PRIMARY_EMOTIONS = %w[
    joy anger fear sadness disgust surprise anticipation trust
  ].freeze

  # Emotional intensity levels
  EMOTIONAL_INTENSITIES = %w[subtle moderate strong intense].freeze

  # Behavioral signal types for emotional detection
  BEHAVIORAL_SIGNAL_TYPES = %w[
    email_engagement website_behavior purchase_patterns
    social_interaction support_interaction timing_patterns
    content_consumption communication_style response_speed
  ].freeze

  # Validations
  validates :customer_identifier, presence: true, uniqueness: { scope: :tenant_id }
  validates :current_emotion, presence: true, inclusion: { in: PLUTCHIK_PRIMARY_EMOTIONS }
  validates :emotional_intensity, inclusion: { in: EMOTIONAL_INTENSITIES }
  validates :confidence_score, numericality: { 
    greater_than_or_equal_to: 0.0, 
    less_than_or_equal_to: 100.0 
  }, allow_nil: true

  # Scopes
  scope :high_confidence, -> { where("confidence_score >= ?", 80.0) }
  scope :recent_interactions, -> { where("last_interaction_at >= ?", 24.hours.ago) }
  scope :by_emotion, ->(emotion) { where(current_emotion: emotion) }
  scope :by_intensity, ->(intensity) { where(emotional_intensity: intensity) }

  # Instance Methods

  # Update emotional state based on new behavioral signals
  def update_emotional_state!(new_signals)
    return false unless new_signals.is_a?(Hash)

    # Merge new signals with existing ones
    updated_signals = behavioral_signals.merge(new_signals)
    
    # Analyze emotional state from behavioral signals
    emotional_analysis = EmotionalStateAnalyzer.new(
      customer_identifier: customer_identifier,
      behavioral_signals: updated_signals,
      interaction_history: interaction_history,
      tenant: tenant
    ).analyze

    if emotional_analysis[:success]
      update!(
        current_emotion: emotional_analysis[:primary_emotion],
        emotional_intensity: emotional_analysis[:intensity],
        confidence_score: emotional_analysis[:confidence],
        behavioral_signals: updated_signals,
        interaction_history: add_to_interaction_history(emotional_analysis),
        last_interaction_at: Time.current,
        context_data: emotional_analysis[:context]
      )
      
      # Trigger adaptive campaign adjustments
      trigger_adaptive_response(emotional_analysis)
      true
    else
      Rails.logger.error "Failed to update emotional state: #{emotional_analysis[:error]}"
      false
    end
  end

  # Get emotional journey over time
  def emotional_journey(timeframe = 7.days)
    journey_data = interaction_history.select do |interaction|
      Time.parse(interaction["timestamp"]) >= timeframe.ago
    rescue ArgumentError
      false
    end

    {
      current_state: {
        emotion: current_emotion,
        intensity: emotional_intensity,
        confidence: confidence_score
      },
      journey_points: journey_data.map do |point|
        {
          timestamp: point["timestamp"],
          emotion: point["emotion"],
          intensity: point["intensity"],
          trigger: point["trigger_event"],
          confidence: point["confidence"]
        }
      end,
      emotional_stability: calculate_emotional_stability(journey_data),
      dominant_emotions: calculate_dominant_emotions(journey_data)
    }
  end

  # Check if customer is in a receptive emotional state for marketing
  def receptive_to_marketing?
    case current_emotion
    when "joy", "anticipation", "trust"
      confidence_score >= 70.0
    when "surprise"
      confidence_score >= 80.0 && emotional_intensity.in?(%w[moderate strong])
    when "fear", "anger", "sadness", "disgust"
      false
    else
      false
    end
  end

  # Get personalized campaign adjustments based on emotional state
  def campaign_adjustments
    EmotionalCampaignAdapter.new(
      emotional_state: self,
      tenant: tenant
    ).generate_adjustments
  end

  # Check if emotional state has changed significantly
  def significant_emotional_change?
    return false if interaction_history.empty?

    last_emotion = interaction_history.last&.dig("emotion")
    return false if last_emotion.blank?

    # Significant change if emotion changed or intensity increased dramatically
    current_emotion != last_emotion || 
    (emotional_intensity == "intense" && previous_intensity != "intense")
  end

  # Get recommended next interaction approach
  def recommended_interaction_approach
    case current_emotion
    when "joy"
      {
        tone: "enthusiastic",
        timing: "immediate",
        content_type: "promotional",
        messaging_style: "celebratory"
      }
    when "trust"
      {
        tone: "confident",
        timing: "immediate",
        content_type: "educational_or_promotional",
        messaging_style: "authoritative_friendly"
      }
    when "anticipation"
      {
        tone: "exciting",
        timing: "immediate",
        content_type: "preview_or_teaser",
        messaging_style: "build_excitement"
      }
    when "surprise"
      {
        tone: "explanatory",
        timing: "give_processing_time",
        content_type: "clarifying",
        messaging_style: "helpful_context"
      }
    when "fear"
      {
        tone: "reassuring",
        timing: "delayed",
        content_type: "supportive",
        messaging_style: "empathetic_helpful"
      }
    when "sadness"
      {
        tone: "compassionate",
        timing: "significantly_delayed",
        content_type: "supportive_only",
        messaging_style: "understanding"
      }
    when "anger"
      {
        tone: "apologetic",
        timing: "delayed_after_cooldown",
        content_type: "solution_focused",
        messaging_style: "problem_solving"
      }
    when "disgust"
      {
        tone: "respectful_distance",
        timing: "significantly_delayed",
        content_type: "value_rebuilding",
        messaging_style: "authentic_transparent"
      }
    else
      {
        tone: "neutral",
        timing: "standard",
        content_type: "informational",
        messaging_style: "professional"
      }
    end
  end

  # Get emotional compatibility score with a campaign
  def campaign_compatibility_score(campaign)
    return 0.0 unless campaign.present?

    campaign_emotion = campaign.emotional_resonance_data.dig("primary_emotion")
    return 50.0 unless campaign_emotion.present?

    # Use Plutchik's emotion wheel for compatibility
    emotion_compatibility_score(current_emotion, campaign_emotion)
  end

  # Predict emotional response to campaign content
  def predict_campaign_response(campaign)
    compatibility = campaign_compatibility_score(campaign)
    approach = recommended_interaction_approach
    receptive = receptive_to_marketing?

    {
      predicted_engagement: calculate_predicted_engagement(compatibility, receptive),
      recommended_adjustments: approach,
      optimal_timing: calculate_optimal_timing,
      personalization_suggestions: generate_personalization_suggestions(campaign),
      risk_factors: identify_risk_factors(campaign)
    }
  end

  private

  def add_to_interaction_history(analysis)
    new_entry = {
      "timestamp" => Time.current.iso8601,
      "emotion" => analysis[:primary_emotion],
      "intensity" => analysis[:intensity],
      "confidence" => analysis[:confidence],
      "trigger_event" => analysis[:trigger_event],
      "behavioral_signals" => analysis[:signals_summary]
    }

    # Keep last 30 interactions
    updated_history = (interaction_history + [new_entry]).last(30)
    updated_history
  end

  def trigger_adaptive_response(emotional_analysis)
    # Queue job to adjust active campaigns based on emotional state change
    if significant_emotional_change?
      AdaptiveCampaignAdjustmentJob.perform_later(
        customer_emotional_state_id: id,
        emotional_analysis: emotional_analysis
      )
    end
  end

  def calculate_emotional_stability(journey_data)
    return 1.0 if journey_data.size < 3

    emotions = journey_data.map { |point| point["emotion"] }.compact
    unique_emotions = emotions.uniq.size
    
    # More unique emotions = less stability
    case unique_emotions
    when 1 then 1.0      # Very stable
    when 2 then 0.8      # Mostly stable  
    when 3 then 0.6      # Moderately stable
    when 4 then 0.4      # Somewhat unstable
    else 0.2             # Very unstable
    end
  end

  def calculate_dominant_emotions(journey_data)
    emotions = journey_data.map { |point| point["emotion"] }.compact
    return {} if emotions.empty?

    emotion_counts = emotions.tally
    total_count = emotions.size

    emotion_counts.transform_values do |count|
      ((count.to_f / total_count) * 100).round(1)
    end.sort_by { |_, percentage| -percentage }.to_h
  end

  def previous_intensity
    interaction_history.last&.dig("intensity") || "moderate"
  end

  def emotion_compatibility_score(emotion1, emotion2)
    # Plutchik's emotion wheel compatibility (simplified)
    compatibility_matrix = {
      "joy" => { "joy" => 100, "trust" => 90, "anticipation" => 85, "surprise" => 70, "fear" => 20, "anger" => 15, "sadness" => 10, "disgust" => 25 },
      "trust" => { "trust" => 100, "joy" => 90, "fear" => 75, "anticipation" => 80, "surprise" => 60, "anger" => 25, "sadness" => 20, "disgust" => 30 },
      "fear" => { "fear" => 100, "trust" => 75, "surprise" => 80, "sadness" => 70, "joy" => 20, "anger" => 40, "disgust" => 45, "anticipation" => 30 },
      "surprise" => { "surprise" => 100, "fear" => 80, "sadness" => 70, "anticipation" => 60, "joy" => 70, "trust" => 60, "anger" => 35, "disgust" => 40 },
      "sadness" => { "sadness" => 100, "surprise" => 70, "disgust" => 75, "fear" => 70, "joy" => 10, "trust" => 20, "anger" => 45, "anticipation" => 15 },
      "disgust" => { "disgust" => 100, "sadness" => 75, "anger" => 80, "fear" => 45, "surprise" => 40, "joy" => 25, "trust" => 30, "anticipation" => 20 },
      "anger" => { "anger" => 100, "disgust" => 80, "anticipation" => 75, "sadness" => 45, "fear" => 40, "joy" => 15, "trust" => 25, "surprise" => 35 },
      "anticipation" => { "anticipation" => 100, "anger" => 75, "joy" => 85, "trust" => 80, "surprise" => 60, "fear" => 30, "sadness" => 15, "disgust" => 20 }
    }

    compatibility_matrix.dig(emotion1, emotion2) || 50.0
  end

  def calculate_predicted_engagement(compatibility, receptive)
    base_score = compatibility

    # Adjust based on receptivity
    if receptive
      base_score *= 1.2
    else
      base_score *= 0.6
    end

    # Adjust based on confidence
    confidence_factor = (confidence_score || 70.0) / 100.0
    adjusted_score = base_score * (0.7 + (confidence_factor * 0.3))

    # Adjust based on emotional intensity
    intensity_multiplier = case emotional_intensity
    when "subtle" then 0.8
    when "moderate" then 1.0
    when "strong" then 1.1
    when "intense" then 1.2
    else 1.0
    end

    final_score = adjusted_score * intensity_multiplier
    [final_score, 100.0].min.round(2)
  end

  def calculate_optimal_timing
    approach = recommended_interaction_approach
    
    case approach[:timing]
    when "immediate"
      Time.current
    when "give_processing_time"
      2.hours.from_now
    when "delayed"
      6.hours.from_now
    when "delayed_after_cooldown"
      12.hours.from_now
    when "significantly_delayed"
      24.hours.from_now
    else
      4.hours.from_now # standard timing
    end
  end

  def generate_personalization_suggestions(campaign)
    suggestions = []

    # Emotional tone adjustments
    if current_emotion == "fear" && campaign.emotional_tone != "reassuring"
      suggestions << "Adjust tone to be more reassuring and supportive"
    end

    if current_emotion == "joy" && campaign.emotional_tone != "celebratory"
      suggestions << "Increase enthusiasm and celebratory language"
    end

    # Content type adjustments
    approach = recommended_interaction_approach
    suggestions << "Use #{approach[:content_type]} content type"
    suggestions << "Apply #{approach[:messaging_style]} messaging style"

    # Timing adjustments
    if approach[:timing] != "immediate"
      suggestions << "Delay send time - customer not in optimal emotional state"
    end

    suggestions
  end

  def identify_risk_factors(campaign)
    risks = []

    # Emotional mismatch risks
    compatibility = campaign_compatibility_score(campaign)
    if compatibility < 30.0
      risks << {
        type: "emotional_mismatch",
        severity: "high",
        description: "Campaign emotion conflicts with customer's current emotional state"
      }
    end

    # Receptivity risks  
    unless receptive_to_marketing?
      risks << {
        type: "low_receptivity",
        severity: "medium", 
        description: "Customer not in receptive emotional state for marketing"
      }
    end

    # Confidence risks
    if confidence_score < 60.0
      risks << {
        type: "low_confidence",
        severity: "medium",
        description: "Low confidence in emotional state detection"
      }
    end

    risks
  end
end
