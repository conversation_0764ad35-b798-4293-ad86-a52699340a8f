# frozen_string_literal: true

# EmotionalResonanceProfile - Tracks and analyzes emotional resonance patterns for campaigns
#
# This model stores detailed emotional analysis data including:
# - Plutchik's emotion wheel mappings
# - Audience emotional response predictions
# - Resonance strength scoring
# - Demographic-specific emotional patterns
class EmotionalResonanceProfile < ApplicationRecord
  # Plutchik's 8 primary emotions
  PLUTCHIK_PRIMARY_EMOTIONS = %w[
    joy anger fear sadness disgust surprise anticipation trust
  ].freeze

  # Multi-tenancy
  acts_as_tenant(:tenant)
  belongs_to :tenant
  belongs_to :created_by, class_name: "User"

  # Attribute declarations for enum fields
  attribute :profile_type, :string
  attribute :emotional_intensity, :string

  # Validations
  validates :name, presence: true, uniqueness: { scope: :tenant_id }
  validates :primary_emotion, presence: true, inclusion: { in: PLUTCHIK_PRIMARY_EMOTIONS }

  # Scopes
  scope :by_emotion, ->(emotion) { where(primary_emotion: emotion) }
  scope :by_age_range, ->(range) { where(target_age_range: range) }
  scope :recent_profiles, -> { where(created_at: 30.days.ago..Time.current) }

  # Emotion intensity levels in <PERSON>lutchi<PERSON>'s model
  EMOTION_INTENSITIES = {
    "joy" => %w[serenity joy ecstasy],
    "anger" => %w[annoyance anger rage],
    "fear" => %w[apprehension fear terror],
    "sadness" => %w[pensiveness sadness grief],
    "disgust" => %w[boredom disgust loathing],
    "surprise" => %w[distraction surprise amazement],
    "anticipation" => %w[interest anticipation vigilance],
    "trust" => %w[acceptance trust admiration]
  }.freeze

  # Instance Methods

  # Get secondary emotions that complement the primary
  def secondary_emotions
    emotion_weights.keys.map(&:to_s) - [ primary_emotion ]
  end

  # Check if emotion profile is suitable for specific campaign type
  def suitable_for_campaign_type?(campaign_type)
    suitable_emotions = campaign_type_emotion_mapping[campaign_type.to_s] || []
    suitable_emotions.include?(primary_emotion)
  end

  # Get recommended complementary emotions
  def recommended_complementary_emotions
    return [] unless primary_emotion.present?

    emotion_combinations[primary_emotion] || []
  end

  # Export emotional profile for external analysis
  def export_emotional_profile
    {
      profile_id: id,
      name: name,
      primary_emotion: primary_emotion,
      secondary_emotions: secondary_emotions,
      target_age_range: target_age_range,
      created_at: created_at,
      analysis_metadata: {
        emotion_weights: emotion_weights,
        tone_preferences: tone_preferences,
        content_guidelines: content_guidelines,
        psychographic_traits: psychographic_traits
      }
    }
  end

  private

  # Mapping of campaign types to suitable emotions
  def campaign_type_emotion_mapping
    {
      "product_launch" => %w[anticipation joy surprise],
      "brand_awareness" => %w[trust joy anticipation],
      "seasonal_promotion" => %w[joy anticipation surprise],
      "crisis_response" => %w[trust sadness fear],
      "community_building" => %w[trust joy anticipation],
      "educational" => %w[trust anticipation joy],
      "entertainment" => %w[joy surprise anticipation],
      "social_cause" => %w[anger sadness trust anticipation]
    }
  end

  # Plutchik's emotion combinations
  def emotion_combinations
    {
      "joy" => %w[trust anticipation],
      "trust" => %w[joy fear],
      "fear" => %w[trust surprise],
      "surprise" => %w[fear sadness],
      "sadness" => %w[surprise disgust],
      "disgust" => %w[sadness anger],
      "anger" => %w[disgust anticipation],
      "anticipation" => %w[anger joy]
    }
  end

  # Seasonal emotion mapping
  def seasonal_emotion_mapping
    {
      "spring" => %w[joy anticipation trust],
      "summer" => %w[joy anticipation surprise],
      "autumn" => %w[sadness anticipation trust],
      "winter" => %w[trust sadness fear]
    }
  end
end
