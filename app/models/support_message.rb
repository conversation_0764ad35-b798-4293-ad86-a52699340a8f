# frozen_string_literal: true

# == Schema Information
# Table name: support_messages
#
#  id                :bigint           not null, primary key
#  support_ticket_id :bigint           not null
#  user_id           :bigint           not null
#  tenant_id         :bigint           not null
#  message           :text             not null
#  message_type      :string           default("user")
#  is_internal       :boolean          default(false)
#  created_at        :datetime         not null
#  updated_at        :datetime         not null
#
# Indexes
#
#  index_support_messages_on_support_ticket_id  (support_ticket_id)
#  index_support_messages_on_tenant_id          (tenant_id)
#  index_support_messages_on_user_id            (user_id)
#
# Foreign Keys
#
#  fk_rails_...  (support_ticket_id => support_tickets.id)
#  fk_rails_...  (tenant_id => tenants.id)
#  fk_rails_...  (user_id => users.id)
#

class SupportMessage < ApplicationRecord
  # Multi-tenancy
  acts_as_tenant(:tenant)
  belongs_to :tenant
  belongs_to :support_ticket
  belongs_to :user

  # Validations
  validates :message, presence: true, length: { minimum: 1, maximum: 2000 }
  validates :message_type, presence: true, inclusion: { in: %w[user support system] }

  # Attribute declarations
  attribute :message_type, :string, default: "user"
  attribute :is_internal, :boolean, default: false

  # Enums
  enum :message_type, {
    user: "user",
    support: "support",
    system: "system"
  }, prefix: true

  # Scopes
  scope :recent, -> { order(created_at: :desc) }
  scope :chronological, -> { order(created_at: :asc) }
  scope :public_messages, -> { where(is_internal: false) }
  scope :internal_messages, -> { where(is_internal: true) }
  scope :from_support, -> { where(message_type: "support") }
  scope :from_users, -> { where(message_type: "user") }

  # Instance methods
  def from_support?
    message_type == "support"
  end

  def from_user?
    message_type == "user"
  end

  def from_system?
    message_type == "system"
  end

  def author_name
    case message_type
    when "user"
      user.full_name
    when "support"
      "Support Team"
    when "system"
      "System"
    else
      "Unknown"
    end
  end

  def author_avatar_initials
    case message_type
    when "user"
      "#{user.first_name&.first}#{user.last_name&.first}".upcase
    when "support"
      "ST"
    when "system"
      "SY"
    else
      "??"
    end
  end

  def author_avatar_color
    case message_type
    when "user"
      "bg-blue-500"
    when "support"
      "bg-green-500"
    when "system"
      "bg-gray-500"
    else
      "bg-gray-400"
    end
  end

  def formatted_message
    # Simple formatting for now - could be enhanced with markdown support
    message.gsub(/\n/, "<br>").html_safe
  end

  def time_ago
    time_diff = Time.current - created_at

    case time_diff
    when 0..59
      "Just now"
    when 60..3599
      minutes = (time_diff / 60).to_i
      "#{minutes} minute#{'s' if minutes != 1} ago"
    when 3600..86399
      hours = (time_diff / 3600).to_i
      "#{hours} hour#{'s' if hours != 1} ago"
    when 86400..604799
      days = (time_diff / 86400).to_i
      "#{days} day#{'s' if days != 1} ago"
    else
      created_at.strftime("%B %d, %Y at %I:%M %p")
    end
  end

  def can_be_edited?
    # Users can edit their messages within 5 minutes
    message_type == "user" && created_at > 5.minutes.ago
  end

  def can_be_deleted?
    # Only support team can delete messages
    false # For now, don't allow deletion
  end

  # Class methods
  def self.create_system_message(support_ticket, message_text)
    create!(
      support_ticket: support_ticket,
      user: support_ticket.user, # System messages are associated with the ticket owner
      message: message_text,
      message_type: "system",
      is_internal: false
    )
  end

  def self.create_internal_note(support_ticket, user, message_text)
    create!(
      support_ticket: support_ticket,
      user: user,
      message: message_text,
      message_type: "support",
      is_internal: true
    )
  end

  # Callbacks
  after_create :update_ticket_status
  after_create :send_notifications

  private

  def update_ticket_status
    return unless support_ticket

    case message_type
    when "user"
      # User replied, set status to waiting for support
      if support_ticket.waiting_for_user? || support_ticket.resolved?
        support_ticket.update(status: "waiting_for_support")
      end
    when "support"
      # Support replied, set status to waiting for user
      if support_ticket.open? || support_ticket.waiting_for_support?
        support_ticket.update(status: "waiting_for_user")
      end
    end
  end

  def send_notifications
    # In a real application, this would trigger email/push notifications
    # For now, we'll just log the action
    Rails.logger.info "New message in ticket #{support_ticket.ticket_number} from #{author_name}"
  end
end
