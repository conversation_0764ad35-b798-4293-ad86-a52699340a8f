# frozen_string_literal: true

# == Schema Information
#
# Table name: audience_segments
#
#  id           :bigint           not null, primary key
#  audience_id  :bigint           not null
#  name         :string           not null
#  segment_type :string           not null
#  criteria     :jsonb            default({}), not null
#  description  :text
#  size         :integer
#  created_at   :datetime         not null
#  updated_at   :datetime         not null
#

class AudienceSegment < ApplicationRecord
  # Associations
  belongs_to :audience, required: true

  # Validations
  validates :name, presence: true, uniqueness: { scope: :audience_id }
  validates :segment_type, presence: true, inclusion: {
    in: %w[demographic behavioral cultural geographic psychographic temporal]
  }

  # Enums
  enum :segment_type, {
    demographic: "demographic",
    behavioral: "behavioral",
    cultural: "cultural",
    geographic: "geographic",
    psychographic: "psychographic",
    temporal: "temporal"
  }

  # Scopes
  scope :by_type, ->(type) { where(segment_type: type) }
  scope :large_segments, -> { where("size >= ?", 1000) }
  scope :recent, -> { order(created_at: :desc) }

  # Instance methods
  def segment_size
    size || calculate_estimated_size
  end

  def criteria_summary
    return "No criteria defined" if criteria.blank?

    summary_parts = []

    if criteria["age_range"]
      summary_parts << "Age: #{criteria['age_range']}"
    end

    if criteria["cultural_context"]
      summary_parts << "Culture: #{criteria['cultural_context'].humanize}"
    end

    if criteria["interests"]
      interests = criteria["interests"].is_a?(Array) ? criteria["interests"] : [ criteria["interests"] ]
      summary_parts << "Interests: #{interests.first(3).join(', ')}"
    end

    if criteria["location"]
      summary_parts << "Location: #{criteria['location']}"
    end

    summary_parts.any? ? summary_parts.join(" | ") : "Custom criteria"
  end

  def matches_criteria?(user_attributes)
    return false if criteria.blank?

    criteria.all? do |key, expected_value|
      user_value = user_attributes[key] || user_attributes[key.to_sym]

      case key
      when "age_range"
        matches_age_range?(user_value, expected_value)
      when "interests"
        matches_interests?(user_value, expected_value)
      when "cultural_context"
        user_value == expected_value
      else
        user_value == expected_value
      end
    end
  end

  def performance_metrics
    # Calculate performance metrics for this segment
    campaigns = audience.campaigns.includes(:campaign_metrics)

    return {} if campaigns.empty?

    total_campaigns = campaigns.count
    avg_engagement = campaigns.joins(:campaign_metrics)
                             .average("campaign_metrics.engagement_rate") || 0
    avg_conversion = campaigns.joins(:campaign_metrics)
                             .average("campaign_metrics.conversion_rate") || 0

    {
      total_campaigns: total_campaigns,
      avg_engagement_rate: avg_engagement.round(2),
      avg_conversion_rate: avg_conversion.round(2),
      segment_size: segment_size
    }
  end

  def similar_segments
    # Find similar segments based on criteria overlap
    audience.audience_segments
            .where.not(id: id)
            .select { |segment| criteria_overlap_score(segment) > 0.5 }
            .sort_by { |segment| -criteria_overlap_score(segment) }
            .first(5)
  end

  private

  def calculate_estimated_size
    # Estimate segment size based on criteria and audience size
    base_size = audience.campaigns.joins(:campaign_metrics)
                        .sum("campaign_metrics.impressions") / 1000 # Rough estimate

    # Apply criteria filters to estimate size reduction
    reduction_factor = 1.0

    if criteria["age_range"]
      reduction_factor *= 0.3 # Age targeting reduces by ~70%
    end

    if criteria["cultural_context"]
      reduction_factor *= 0.4 # Cultural targeting reduces by ~60%
    end

    if criteria["interests"]
      interests_count = criteria["interests"].is_a?(Array) ? criteria["interests"].length : 1
      reduction_factor *= (0.2 * interests_count).clamp(0.1, 0.8)
    end

    (base_size * reduction_factor).to_i
  end

  def matches_age_range?(user_age, expected_range)
    return false unless user_age && expected_range

    if expected_range.is_a?(String) && expected_range.include?("-")
      min_age, max_age = expected_range.split("-").map(&:to_i)
      user_age >= min_age && user_age <= max_age
    else
      user_age == expected_range.to_i
    end
  end

  def matches_interests?(user_interests, expected_interests)
    return false unless user_interests && expected_interests

    user_interests = [ user_interests ] unless user_interests.is_a?(Array)
    expected_interests = [ expected_interests ] unless expected_interests.is_a?(Array)

    (user_interests & expected_interests).any?
  end

  def criteria_overlap_score(other_segment)
    return 0.0 if criteria.blank? || other_segment.criteria.blank?

    common_keys = criteria.keys & other_segment.criteria.keys
    return 0.0 if common_keys.empty?

    matching_criteria = common_keys.count do |key|
      criteria[key] == other_segment.criteria[key]
    end

    matching_criteria.to_f / [ criteria.keys.length, other_segment.criteria.keys.length ].max
  end
end
