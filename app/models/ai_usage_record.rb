# frozen_string_literal: true

##
# AI Usage Record Model
#
# Stores detailed AI usage information for analytics, billing, and monitoring.
# Each record represents a single AI API call with associated metadata.
#
# @example Creating a usage record
#   AiUsageRecord.create!(
#     tenant: tenant,
#     model: 'gpt-4o',
#     task_type: 'content_generation',
#     input_tokens: 100,
#     output_tokens: 50,
#     total_tokens: 150,
#     duration_ms: 1500.0,
#     cost: 0.0045,
#     request_timestamp: Time.current,
#     metadata: { campaign_id: 123, user_id: 456 }
#   )
#
class AiUsageRecord < ApplicationRecord
  acts_as_tenant(:tenant)

  belongs_to :tenant

  # Validations
  validates :model, presence: true, length: { maximum: 100 }
  validates :task_type, presence: true, length: { maximum: 50 }
  validates :input_tokens, presence: true, numericality: { greater_than_or_equal_to: 0 }
  validates :output_tokens, presence: true, numericality: { greater_than_or_equal_to: 0 }
  validates :total_tokens, presence: true, numericality: { greater_than_or_equal_to: 0 }
  validates :duration_ms, presence: true, numericality: { greater_than_or_equal_to: 0 }
  validates :cost, presence: true, numericality: { greater_than_or_equal_to: 0 }
  validates :request_timestamp, presence: true
  validates :metadata, presence: true

  # Custom validation to ensure total_tokens matches input + output
  validate :total_tokens_consistency

  # Scopes for common queries
  scope :for_model, ->(model) { where(model: model) }
  scope :for_task_type, ->(task_type) { where(task_type: task_type) }
  scope :recent, -> { order(request_timestamp: :desc) }
  scope :by_date_range, ->(start_date, end_date) {
    where(request_timestamp: start_date.beginning_of_day..end_date.end_of_day)
  }
  scope :by_month, ->(year, month) {
    start_date = Date.new(year, month, 1)
    end_date = start_date.end_of_month
    by_date_range(start_date, end_date)
  }
  scope :expensive, -> { where("cost > ?", 0.01) }
  scope :slow, -> { where("duration_ms > ?", 5000) }

  # Enumerated task types for validation
  TASK_TYPES = %w[
    creative_content
    data_analysis
    real_time_chat
    cost_sensitive
    vision_tasks
    complex_reasoning
    function_calling
    embeddings
    general
  ].freeze

  validates :task_type, inclusion: { in: TASK_TYPES }

  # Enumerated model types for validation
  SUPPORTED_MODELS = %w[
    gpt-4o
    gpt-4o-mini
    claude-3.5-sonnet
    claude-3-opus
    gemini-1.5-pro
    gemini-1.5-flash
    deepseek-chat
    text-embedding-3-small
    text-embedding-3-large
  ].freeze

  validates :model, inclusion: { in: SUPPORTED_MODELS }

  # Class methods for analytics

  # Get usage summary for a tenant and date range
  #
  # @param tenant [Tenant] The tenant to analyze
  # @param start_date [Date] Start date for analysis
  # @param end_date [Date] End date for analysis
  # @return [Hash] Usage summary with aggregated metrics
  def self.usage_summary_for_tenant(tenant, start_date: 30.days.ago.to_date, end_date: Date.current)
    records = ActsAsTenant.with_tenant(tenant) do
      by_date_range(start_date, end_date)
    end

    {
      total_requests: records.count,
      total_tokens: records.sum(:total_tokens),
      total_cost: records.sum(:cost),
      avg_duration_ms: records.average(:duration_ms)&.round(2) || 0,
      models_used: records.distinct.pluck(:model),
      task_types_used: records.distinct.pluck(:task_type),
      most_expensive_request: records.maximum(:cost) || 0,
      slowest_request_ms: records.maximum(:duration_ms) || 0,
      daily_breakdown: daily_breakdown(records)
    }
  end

  # Get cost breakdown by model
  #
  # @param tenant [Tenant] The tenant to analyze
  # @param start_date [Date] Start date for analysis
  # @param end_date [Date] End date for analysis
  # @return [Hash] Cost breakdown by model
  def self.cost_breakdown_by_model(tenant, start_date: 30.days.ago.to_date, end_date: Date.current)
    ActsAsTenant.with_tenant(tenant) do
      by_date_range(start_date, end_date)
        .group(:model)
        .group_by(&:model)
        .transform_values do |records|
          {
            total_cost: records.sum(&:cost),
            total_requests: records.count,
            avg_cost_per_request: records.sum(&:cost) / records.count.nonzero? || 0,
            total_tokens: records.sum(&:total_tokens)
          }
        end
    end
  end

  # Get performance metrics by task type
  #
  # @param tenant [Tenant] The tenant to analyze
  # @param start_date [Date] Start date for analysis
  # @param end_date [Date] End date for analysis
  # @return [Hash] Performance metrics by task type
  def self.performance_by_task_type(tenant, start_date: 30.days.ago.to_date, end_date: Date.current)
    ActsAsTenant.with_tenant(tenant) do
      by_date_range(start_date, end_date)
        .group(:task_type)
        .group_by(&:task_type)
        .transform_values do |records|
          {
            avg_duration_ms: records.sum(&:duration_ms) / records.count.nonzero? || 0,
            avg_tokens_per_request: records.sum(&:total_tokens) / records.count.nonzero? || 0,
            total_requests: records.count,
            success_rate: 100.0 # We only store successful requests
          }
        end
    end
  end

  # Find anomalous usage patterns
  #
  # @param tenant [Tenant] The tenant to analyze
  # @return [Hash] Anomalous usage patterns
  def self.detect_anomalies(tenant)
    ActsAsTenant.with_tenant(tenant) do
      recent_records = where("request_timestamp > ?", 24.hours.ago)

      avg_cost = recent_records.average(:cost) || 0
      avg_duration = recent_records.average(:duration_ms) || 0

      {
        high_cost_requests: recent_records.where("cost > ?", avg_cost * 3).count,
        slow_requests: recent_records.where("duration_ms > ?", avg_duration * 2).count,
        unusual_models: recent_records.group(:model).having("COUNT(*) = 1").pluck(:model),
        burst_activity: detect_burst_activity(recent_records)
      }
    end
  end

  # Instance methods

  # Check if this is an expensive request
  #
  # @return [Boolean] True if cost is above average
  def expensive?
    return false unless cost

    avg_cost = self.class.where(tenant: tenant, model: model).average(:cost) || 0
    cost > avg_cost * 2
  end

  # Check if this is a slow request
  #
  # @return [Boolean] True if duration is above average
  def slow?
    return false unless duration_ms

    avg_duration = self.class.where(tenant: tenant, model: model).average(:duration_ms) || 0
    duration_ms > avg_duration * 2
  end

  # Calculate tokens per second
  #
  # @return [Float] Tokens processed per second
  def tokens_per_second
    return 0 if duration_ms.zero?

    (total_tokens / (duration_ms / 1000.0)).round(2)
  end

  # Calculate cost per token
  #
  # @return [Float] Cost per token
  def cost_per_token
    return 0 if total_tokens.zero?

    (cost / total_tokens).round(8)
  end

  private

  # Validate that total_tokens equals input_tokens + output_tokens
  def total_tokens_consistency
    return unless input_tokens && output_tokens && total_tokens

    expected_total = input_tokens + output_tokens
    unless total_tokens == expected_total
      errors.add(:total_tokens, "must equal input_tokens (#{input_tokens}) + output_tokens (#{output_tokens})")
    end
  end

  # Generate daily breakdown for usage summary
  def self.daily_breakdown(records)
    records.group_by { |r| r.request_timestamp.to_date }
           .transform_values do |daily_records|
             {
               requests: daily_records.count,
               total_cost: daily_records.sum(&:cost),
               total_tokens: daily_records.sum(&:total_tokens),
               avg_duration_ms: (daily_records.sum(&:duration_ms) / daily_records.count.nonzero? || 0).round(2)
             }
           end
  end

  # Detect burst activity in recent records
  def self.detect_burst_activity(records)
    hourly_counts = records.group_by { |r| r.request_timestamp.beginning_of_hour }
                           .transform_values(&:count)

    return false if hourly_counts.empty?

    avg_hourly = hourly_counts.values.sum / hourly_counts.size.nonzero? || 0
    max_hourly = hourly_counts.values.max || 0

    # Consider it burst activity if any hour has 3x the average
    max_hourly > avg_hourly * 3
  end
end
