# frozen_string_literal: true

# == Schema Information
# Table name: campaign_metrics
#
#  id                    :bigint           not null, primary key
#  campaign_id           :bigint           not null
#  metric_date           :date             not null
#  impressions           :integer          default(0)
#  clicks                :integer          default(0)
#  conversions           :integer          default(0)
#  revenue_cents         :integer          default(0)
#  cost_cents            :integer          default(0)
#  email_opens           :integer          default(0)
#  email_clicks          :integer          default(0)
#  email_bounces         :integer          default(0)
#  social_engagements    :integer          default(0)
#  social_shares         :integer          default(0)
#  social_comments       :integer          default(0)
#  seo_organic_traffic   :integer          default(0)
#  seo_keyword_rankings  :jsonb            default({}), not null
#  custom_metrics        :jsonb            default({}), not null
#  created_at            :datetime         not null
#  updated_at            :datetime         not null
#
# Indexes
#
#  index_campaign_metrics_on_campaign_id_and_date  (campaign_id,metric_date) UNIQUE
#  index_campaign_metrics_on_metric_date           (metric_date)
#  index_campaign_metrics_on_custom_metrics        (custom_metrics) USING gin
#  index_campaign_metrics_on_seo_keyword_rankings  (seo_keyword_rankings) USING gin
#
# Foreign Keys
#
#  fk_rails_...  (campaign_id => campaigns.id)
#

class CampaignMetric < ApplicationRecord
  # Associations
  belongs_to :campaign, required: true

  # Validations
  validates :metric_date, presence: true, uniqueness: { scope: :campaign_id }
  validates :impressions, :clicks, :conversions, :revenue_cents, :cost_cents,
            :email_opens, :email_clicks, :email_bounces,
            :social_engagements, :social_shares, :social_comments,
            :seo_organic_traffic,
            numericality: { greater_than_or_equal_to: 0 }

  # Scopes
  scope :for_date_range, ->(start_date, end_date) { where(metric_date: start_date..end_date) }
  scope :recent, ->(days = 30) { where(metric_date: days.days.ago..Date.current) }
  scope :this_month, -> { where(metric_date: Date.current.beginning_of_month..Date.current.end_of_month) }
  scope :last_month, -> { where(metric_date: 1.month.ago.beginning_of_month..1.month.ago.end_of_month) }

  # Delegate tenant access through campaign
  def tenant
    campaign.tenant
  end

  # Calculated metrics
  def click_through_rate
    return 0.0 if impressions.zero?
    (clicks.to_f / impressions * 100).round(2)
  end

  def conversion_rate
    return 0.0 if clicks.zero?
    (conversions.to_f / clicks * 100).round(2)
  end

  def cost_per_click
    return 0.0 if clicks.zero?
    cost_in_dollars / clicks
  end

  def cost_per_conversion
    return 0.0 if conversions.zero?
    cost_in_dollars / conversions
  end

  def return_on_ad_spend
    return 0.0 if cost_cents.zero?
    (revenue_cents.to_f / cost_cents * 100).round(2)
  end

  def revenue_in_dollars
    revenue_cents / 100.0
  end

  def cost_in_dollars
    cost_cents / 100.0
  end

  def profit_in_dollars
    revenue_in_dollars - cost_in_dollars
  end

  # Email specific metrics
  def email_open_rate
    return 0.0 if email_total_sent.zero?
    (email_opens.to_f / email_total_sent * 100).round(2)
  end

  def email_click_rate
    return 0.0 if email_opens.zero?
    (email_clicks.to_f / email_opens * 100).round(2)
  end

  def email_bounce_rate
    return 0.0 if email_total_sent.zero?
    (email_bounces.to_f / email_total_sent * 100).round(2)
  end

  def email_total_sent
    custom_metrics.dig("email", "total_sent") || 0
  end

  # Social media metrics
  def social_engagement_rate
    return 0.0 if social_total_reach.zero?
    (social_engagements.to_f / social_total_reach * 100).round(2)
  end

  def social_total_reach
    custom_metrics.dig("social", "total_reach") || 0
  end

  def social_total_interactions
    social_engagements + social_shares + social_comments
  end

  # SEO metrics
  def average_keyword_position
    rankings = seo_keyword_rankings.values
    return 0.0 if rankings.empty?

    (rankings.sum.to_f / rankings.count).round(1)
  end

  def keywords_in_top_10
    seo_keyword_rankings.values.count { |position| position <= 10 }
  end

  def keywords_improved
    custom_metrics.dig("seo", "keywords_improved") || 0
  end

  # Aggregation methods
  def self.aggregate_for_campaign(campaign, start_date = nil, end_date = nil)
    metrics = campaign.campaign_metrics
    metrics = metrics.for_date_range(start_date, end_date) if start_date && end_date

    {
      total_impressions: metrics.sum(:impressions),
      total_clicks: metrics.sum(:clicks),
      total_conversions: metrics.sum(:conversions),
      total_revenue: metrics.sum(:revenue_cents) / 100.0,
      total_cost: metrics.sum(:cost_cents) / 100.0,
      average_ctr: calculate_average_ctr(metrics),
      average_conversion_rate: calculate_average_conversion_rate(metrics),
      total_email_opens: metrics.sum(:email_opens),
      total_email_clicks: metrics.sum(:email_clicks),
      total_social_engagements: metrics.sum(:social_engagements),
      total_seo_traffic: metrics.sum(:seo_organic_traffic)
    }
  end

  def self.daily_summary(campaign, date = Date.current)
    metrics = campaign.campaign_metrics.find_by(metric_date: date)
    return default_summary if metrics.nil?

    {
      date: date,
      impressions: metrics.impressions,
      clicks: metrics.clicks,
      conversions: metrics.conversions,
      revenue: metrics.revenue_in_dollars,
      cost: metrics.cost_in_dollars,
      ctr: metrics.click_through_rate,
      conversion_rate: metrics.conversion_rate,
      roas: metrics.return_on_ad_spend
    }
  end

  private

  def self.calculate_average_ctr(metrics)
    total_impressions = metrics.sum(:impressions)
    total_clicks = metrics.sum(:clicks)
    return 0.0 if total_impressions.zero?

    (total_clicks.to_f / total_impressions * 100).round(2)
  end

  def self.calculate_average_conversion_rate(metrics)
    total_clicks = metrics.sum(:clicks)
    total_conversions = metrics.sum(:conversions)
    return 0.0 if total_clicks.zero?

    (total_conversions.to_f / total_clicks * 100).round(2)
  end

  def self.default_summary
    {
      date: Date.current,
      impressions: 0,
      clicks: 0,
      conversions: 0,
      revenue: 0.0,
      cost: 0.0,
      ctr: 0.0,
      conversion_rate: 0.0,
      roas: 0.0
    }
  end
end
