# frozen_string_literal: true

# == Schema Information
# Table name: notification_settings
#
#  id                  :bigint           not null, primary key
#  user_id             :bigint           not null
#  tenant_id           :bigint           not null
#  email_campaigns     :boolean          default(true)
#  email_system        :boolean          default(true)
#  email_marketing     :boolean          default(true)
#  push_campaigns      :boolean          default(true)
#  push_system         :boolean          default(true)
#  push_marketing      :boolean          default(false)
#  sms_campaigns       :boolean          default(false)
#  sms_system          :boolean          default(false)
#  sms_security        :boolean          default(true)
#  digest_frequency    :string           default("daily")
#  quiet_hours_start   :time
#  quiet_hours_end     :time
#  created_at          :datetime         not null
#  updated_at          :datetime         not null
#
# Indexes
#
#  index_notification_settings_on_tenant_id  (tenant_id)
#  index_notification_settings_on_user_id    (user_id) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (tenant_id => tenants.id)
#  fk_rails_...  (user_id => users.id)
#

class NotificationSetting < ApplicationRecord
  # Multi-tenancy
  acts_as_tenant(:tenant)
  belongs_to :tenant
  belongs_to :user

  # Validations
  validates :user_id, uniqueness: { scope: :tenant_id }
  validates :digest_frequency, presence: true, inclusion: { in: %w[immediate hourly daily weekly never] }

  # Attribute declarations for boolean fields
  attribute :email_campaigns, :boolean, default: true
  attribute :email_system, :boolean, default: true
  attribute :email_marketing, :boolean, default: true
  attribute :push_campaigns, :boolean, default: true
  attribute :push_system, :boolean, default: true
  attribute :push_marketing, :boolean, default: false
  attribute :sms_campaigns, :boolean, default: false
  attribute :sms_system, :boolean, default: false
  attribute :sms_security, :boolean, default: true

  # Scopes
  scope :with_email_enabled, -> { where("email_campaigns = ? OR email_system = ? OR email_marketing = ?", true, true, true) }
  scope :with_push_enabled, -> { where("push_campaigns = ? OR push_system = ? OR push_marketing = ?", true, true, true) }
  scope :with_sms_enabled, -> { where("sms_campaigns = ? OR sms_system = ? OR sms_security = ?", true, true, true) }

  # Instance methods
  def email_notifications_enabled?
    email_campaigns? || email_system? || email_marketing?
  end

  def push_notifications_enabled?
    push_campaigns? || push_system? || push_marketing?
  end

  def sms_notifications_enabled?
    sms_campaigns? || sms_system? || sms_security?
  end

  def any_notifications_enabled?
    email_notifications_enabled? || push_notifications_enabled? || sms_notifications_enabled?
  end

  def in_quiet_hours?(time = Time.current)
    return false unless quiet_hours_start && quiet_hours_end

    current_time = time.strftime("%H:%M")
    start_time = quiet_hours_start.strftime("%H:%M")
    end_time = quiet_hours_end.strftime("%H:%M")

    if start_time < end_time
      # Same day quiet hours (e.g., 22:00 to 06:00 next day)
      current_time >= start_time && current_time <= end_time
    else
      # Overnight quiet hours (e.g., 22:00 to 06:00 next day)
      current_time >= start_time || current_time <= end_time
    end
  end

  def should_send_digest?
    case digest_frequency
    when "immediate"
      true
    when "hourly"
      Time.current.min == 0
    when "daily"
      Time.current.hour == 9 && Time.current.min == 0
    when "weekly"
      Time.current.monday? && Time.current.hour == 9 && Time.current.min == 0
    when "never"
      false
    else
      false
    end
  end

  def notification_channels_summary
    channels = []
    channels << "Email" if email_notifications_enabled?
    channels << "Push" if push_notifications_enabled?
    channels << "SMS" if sms_notifications_enabled?

    if channels.any?
      channels.join(", ")
    else
      "None"
    end
  end

  def quiet_hours_summary
    return "Not set" unless quiet_hours_start && quiet_hours_end

    "#{quiet_hours_start.strftime('%I:%M %p')} - #{quiet_hours_end.strftime('%I:%M %p')}"
  end

  # Class methods
  def self.default_settings
    {
      email_campaigns: true,
      email_system: true,
      email_marketing: true,
      push_campaigns: true,
      push_system: true,
      push_marketing: false,
      sms_campaigns: false,
      sms_system: false,
      sms_security: true,
      digest_frequency: "daily",
      quiet_hours_start: nil,
      quiet_hours_end: nil
    }
  end

  def self.digest_frequency_options
    [
      [ "Immediate", "immediate" ],
      [ "Hourly", "hourly" ],
      [ "Daily", "daily" ],
      [ "Weekly", "weekly" ],
      [ "Never", "never" ]
    ]
  end

  def self.notification_types
    {
      email: {
        campaigns: "Campaign updates and results",
        system: "System notifications and alerts",
        marketing: "Product updates and tips"
      },
      push: {
        campaigns: "Campaign status changes",
        system: "Important system alerts",
        marketing: "New features and updates"
      },
      sms: {
        campaigns: "Critical campaign alerts",
        system: "System maintenance notices",
        security: "Security and login alerts"
      }
    }
  end
end
