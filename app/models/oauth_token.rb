# frozen_string_literal: true

class OauthToken < ApplicationRecord
  # Multi-tenancy
  acts_as_tenant(:tenant)
  
  # Associations
  belongs_to :platform_configuration
  belongs_to :tenant

  # Validations
  validates :access_token, presence: true
  validates :token_type, presence: true
  validates :is_active, inclusion: { in: [true, false] }

  # Scopes
  scope :active, -> { where(is_active: true) }
  scope :inactive, -> { where(is_active: false) }
  scope :expired, -> { where('expires_at < ?', Time.current) }
  scope :token_valid, -> { where('expires_at IS NULL OR expires_at > ?', Time.current) }

  # Callbacks
  before_save :deactivate_other_tokens, if: :is_active?

  # Instance methods
  def expired?
    expires_at && expires_at < Time.current
  end

  def token_valid?
    !expired?
  end

  def expires_soon?(threshold = 10.minutes)
    expires_at && expires_at < Time.current + threshold
  end

  def can_refresh?
    refresh_token.present?
  end

  private

  def deactivate_other_tokens
    platform_configuration.oauth_tokens.where.not(id: id).update_all(is_active: false)
  end
end
