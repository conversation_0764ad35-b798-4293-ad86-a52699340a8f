# frozen_string_literal: true

# == Schema Information
# Table name: support_tickets
#
#  id             :bigint           not null, primary key
#  user_id        :bigint           not null
#  tenant_id      :bigint           not null
#  ticket_number  :string           not null
#  subject        :string           not null
#  description    :text             not null
#  category       :string           not null
#  priority       :string           default("normal")
#  status         :string           default("open")
#  assigned_to_id :bigint
#  closed_at      :datetime
#  created_at     :datetime         not null
#  updated_at     :datetime         not null
#
# Indexes
#
#  index_support_tickets_on_assigned_to_id  (assigned_to_id)
#  index_support_tickets_on_status          (status)
#  index_support_tickets_on_tenant_id       (tenant_id)
#  index_support_tickets_on_ticket_number   (ticket_number) UNIQUE
#  index_support_tickets_on_user_id         (user_id)
#
# Foreign Keys
#
#  fk_rails_...  (assigned_to_id => users.id)
#  fk_rails_...  (tenant_id => tenants.id)
#  fk_rails_...  (user_id => users.id)
#

class SupportTicket < ApplicationRecord
  # Multi-tenancy
  acts_as_tenant(:tenant)
  belongs_to :tenant
  belongs_to :user
  belongs_to :assigned_to, class_name: "User", optional: true

  # Associations
  has_many :support_messages, dependent: :destroy

  # Validations
  validates :ticket_number, presence: true, uniqueness: true
  validates :subject, presence: true, length: { minimum: 5, maximum: 200 }
  validates :description, presence: true, length: { minimum: 10, maximum: 2000 }
  validates :category, presence: true, inclusion: { in: %w[general_question technical_issue billing_account feature_request integration_help bug_report] }
  validates :priority, presence: true, inclusion: { in: %w[low normal high urgent] }
  validates :status, presence: true, inclusion: { in: %w[open waiting_for_support waiting_for_user in_progress resolved closed] }

  # Attribute declarations for enum fields
  attribute :category, :string
  attribute :priority, :string, default: "normal"
  attribute :status, :string, default: "open"

  # Enums (using string values for better readability)
  enum :category, {
    general_question: "general_question",
    technical_issue: "technical_issue",
    billing_account: "billing_account",
    feature_request: "feature_request",
    integration_help: "integration_help",
    bug_report: "bug_report"
  }, prefix: true

  enum :priority, {
    low: "low",
    normal: "normal",
    high: "high",
    urgent: "urgent"
  }, prefix: true

  enum :status, {
    open: "open",
    waiting_for_support: "waiting_for_support",
    waiting_for_user: "waiting_for_user",
    in_progress: "in_progress",
    resolved: "resolved",
    closed: "closed"
  }, prefix: true

  # Scopes
  scope :recent, -> { order(created_at: :desc) }
  scope :active, -> { where.not(status: [ "resolved", "closed" ]) }
  scope :by_priority, ->(priority) { where(priority: priority) }
  scope :by_category, ->(category) { where(category: category) }
  scope :assigned, -> { where.not(assigned_to_id: nil) }
  scope :unassigned, -> { where(assigned_to_id: nil) }

  # Instance methods
  def active?
    !resolved? && !closed?
  end

  def can_be_closed?
    !closed?
  end

  def can_be_reopened?
    closed? || resolved?
  end

  def response_time
    return nil unless support_messages.where(message_type: "support").any?

    first_response = support_messages.where(message_type: "support").order(:created_at).first
    return nil unless first_response

    time_diff = first_response.created_at - created_at
    format_duration(time_diff)
  end

  def resolution_time
    return nil unless closed_at

    time_diff = closed_at - created_at
    format_duration(time_diff)
  end

  def last_activity
    last_message = support_messages.order(:created_at).last
    last_message&.created_at || updated_at
  end

  def category_display_name
    category.humanize.titleize
  end

  def priority_display_name
    priority.humanize.titleize
  end

  def status_display_name
    status.humanize.titleize
  end

  def priority_color
    case priority
    when "low" then "text-green-600 bg-green-100"
    when "normal" then "text-blue-600 bg-blue-100"
    when "high" then "text-orange-600 bg-orange-100"
    when "urgent" then "text-red-600 bg-red-100"
    else "text-gray-600 bg-gray-100"
    end
  end

  def status_color
    case status
    when "open" then "text-green-600 bg-green-100"
    when "waiting_for_support" then "text-yellow-600 bg-yellow-100"
    when "waiting_for_user" then "text-blue-600 bg-blue-100"
    when "in_progress" then "text-purple-600 bg-purple-100"
    when "resolved" then "text-indigo-600 bg-indigo-100"
    when "closed" then "text-gray-600 bg-gray-100"
    else "text-gray-600 bg-gray-100"
    end
  end

  # Class methods
  def self.categories_for_select
    [
      [ "General Question", "general_question" ],
      [ "Technical Issue", "technical_issue" ],
      [ "Billing & Account", "billing_account" ],
      [ "Feature Request", "feature_request" ],
      [ "Integration Help", "integration_help" ],
      [ "Bug Report", "bug_report" ]
    ]
  end

  def self.priorities_for_select
    [
      [ "Low", "low" ],
      [ "Normal", "normal" ],
      [ "High", "high" ],
      [ "Urgent", "urgent" ]
    ]
  end

  def self.statuses_for_select
    [
      [ "Open", "open" ],
      [ "Waiting for Support", "waiting_for_support" ],
      [ "Waiting for User", "waiting_for_user" ],
      [ "In Progress", "in_progress" ],
      [ "Resolved", "resolved" ],
      [ "Closed", "closed" ]
    ]
  end

  private

  def format_duration(seconds)
    return "0 minutes" if seconds < 60

    hours = seconds / 3600
    minutes = (seconds % 3600) / 60

    if hours > 0
      "#{hours.to_i} hour#{'s' if hours != 1} #{minutes.to_i} minute#{'s' if minutes != 1}"
    else
      "#{minutes.to_i} minute#{'s' if minutes != 1}"
    end
  end
end
