# frozen_string_literal: true

# == Schema Information
#
# Table name: campaign_library_items
#
#  id                  :bigint           not null, primary key
#  campaign_library_id :bigint           not null
#  campaign_id         :bigint           not null
#  notes               :text
#  added_by_id         :bigint           not null
#  created_at          :datetime         not null
#  updated_at          :datetime         not null
#
# Indexes
#
#  idx_campaign_library_items_added_by  (added_by_id)
#  idx_campaign_library_items_campaign  (campaign_id)
#  idx_campaign_library_items_library   (campaign_library_id)
#  index_campaign_library_items_on_library_and_campaign  (campaign_library_id,campaign_id) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (added_by_id => users.id)
#  fk_rails_...  (campaign_id => campaigns.id)
#  fk_rails_...  (campaign_library_id => campaign_libraries.id)
#

class CampaignLibraryItem < ApplicationRecord
  belongs_to :campaign_library, required: true
  belongs_to :campaign, required: true
  belongs_to :added_by, class_name: "User", required: true

  # Validations
  validates :campaign_library_id, uniqueness: { scope: :campaign_id }

  # Scopes
  scope :recent, ->(limit = 10) { order(created_at: :desc).limit(limit) }
  scope :by_user, ->(user) { where(added_by: user) }

  # Instance methods
  def tenant
    campaign_library.tenant
  end
end
