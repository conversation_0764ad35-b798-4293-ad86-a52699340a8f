# frozen_string_literal: true

# == Schema Information
# Table name: email_campaigns
#
#  id           :bigint           not null, primary key
#  campaign_id  :bigint           not null
#  subject_line :string(150)      not null
#  preview_text :string(200)
#  content      :text             not null
#  from_name    :string           not null
#  from_email   :string           not null
#  settings     :jsonb            default({}), not null
#  created_at   :datetime         not null
#  updated_at   :datetime         not null
#
# Indexes
#
#  index_email_campaigns_on_campaign_id  (campaign_id) UNIQUE
#  index_email_campaigns_on_from_email   (from_email)
#  index_email_campaigns_on_settings     (settings) USING gin
#
# Foreign Keys
#
#  fk_rails_...  (campaign_id => campaigns.id)
#

class EmailCampaign < ApplicationRecord
  # Associations
  belongs_to :campaign, required: true

  # Validations
  validates :subject_line, presence: true, length: { maximum: 150 }
  validates :content, presence: true
  validates :from_name, presence: true
  validates :from_email, presence: true, format: {
    with: URI::MailTo::EMAIL_REGEXP,
    message: "must be a valid email address"
  }
  validates :preview_text, length: { maximum: 200 }, allow_blank: true

  # Scopes
  scope :ready_to_send, -> {
    joins(:campaign)
      .where(campaigns: { status: "active" })
      .where.not(subject_line: [ nil, "" ])
      .where.not(content: [ nil, "" ])
      .where.not(from_email: [ nil, "" ])
  }

  # Delegate tenant access through campaign
  def tenant
    campaign.tenant
  end

  # Instance methods
  def ready_to_send?
    valid? &&
    campaign&.active? &&
    subject_line.present? &&
    content.present? &&
    from_email.present? &&
    valid_email_format?
  end

  def preview_snippet(length = 100)
    return "" if content.blank?

    if content.length <= length
      content
    else
      content[0..length-1] + "..."
    end
  end

  def estimated_send_time
    recipient_count = settings.dig("recipient_count") || 0
    send_rate_per_minute = 1000 # emails per minute

    minutes = (recipient_count.to_f / send_rate_per_minute).ceil
    [ minutes, 1 ].max # minimum 1 minute
  end

  def personalize_content_for(user_data = {})
    personalized_content = content.dup

    user_data.each do |key, value|
      personalized_content.gsub!("{{#{key}}}", value.to_s)
    end

    # Remove any remaining merge tags
    personalized_content.gsub!(/\{\{[^}]+\}\}/, "")

    personalized_content
  end

  def recipient_count
    settings.dig("recipient_count") || 0
  end

  def a_b_test_enabled?
    settings.dig("a_b_test", "enabled") == true
  end

  def scheduled?
    settings.dig("delivery_options", "scheduled_at").present?
  end

  def scheduled_at
    scheduled_time = settings.dig("delivery_options", "scheduled_at")
    return nil if scheduled_time.blank?

    Time.zone.parse(scheduled_time)
  rescue ArgumentError
    nil
  end

  private

  def valid_email_format?
    return false if from_email.blank?
    from_email.match?(URI::MailTo::EMAIL_REGEXP)
  end
end
