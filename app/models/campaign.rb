# frozen_string_literal: true

# == Schema Information
# Table name: campaigns
#
#  id              :bigint           not null, primary key
#  name            :string           not null
#  description     :text
#  campaign_type   :integer          default(0), not null
#  status          :integer          default(0), not null
#  target_audience :string           not null
#  start_date      :date
#  end_date        :date
#  budget_cents    :integer          default(0)
#  settings        :jsonb            default({}), not null
#  tenant_id       :bigint           not null
#  created_by_id   :bigint           not null
#  created_at      :datetime         not null
#  updated_at      :datetime         not null
#
# Indexes
#
#  index_campaigns_on_campaign_type        (campaign_type)
#  index_campaigns_on_created_by_id        (created_by_id)
#  index_campaigns_on_name_and_tenant_id   (name,tenant_id) UNIQUE
#  index_campaigns_on_settings             (settings) USING gin
#  index_campaigns_on_start_date           (start_date)
#  index_campaigns_on_status               (status)
#  index_campaigns_on_tenant_id            (tenant_id)
#
# Foreign Keys
#
#  fk_rails_...  (created_by_id => users.id)
#  fk_rails_...  (tenant_id => tenants.id)
#

class Campaign < ApplicationRecord
  # Multi-tenancy
  acts_as_tenant(:tenant)
  belongs_to :tenant, required: true
  belongs_to :created_by, class_name: "User", required: true

  # Attribute declarations for enum fields
  attribute :status, :integer
  attribute :campaign_type, :integer
  attribute :vibe_status, :string

  # Specialized campaign associations
  has_one :email_campaign, dependent: :destroy
  has_one :social_campaign, dependent: :destroy
  has_one :seo_campaign, dependent: :destroy

  # Metrics association
  has_many :campaign_metrics, dependent: :destroy

  # Audience associations
  has_many :campaign_audiences, dependent: :destroy
  has_many :audiences, through: :campaign_audiences

  # AI Agent Workflows
  has_many :agent_workflows, dependent: :destroy

  # Library and Collection associations (must be defined before through associations)
  has_many :campaign_library_items, dependent: :destroy
  has_many :campaign_libraries, through: :campaign_library_items
  has_many :campaign_collection_items, dependent: :destroy
  has_many :campaign_collections, through: :campaign_collection_items

  # Vibe Marketing associations
  has_many :vibe_analysis_records, dependent: :destroy
  has_many :authenticity_checks, dependent: :destroy
  has_many :vibe_campaign_orchestrations, through: :campaign_collections

  # Validations
  validates :name, presence: true, uniqueness: { scope: :tenant_id }
  validates :campaign_type, presence: true
  validates :status, presence: true
  validates :target_audience, presence: true
  validates :budget_cents, numericality: { greater_than_or_equal_to: 0 }

  # Date validations
  validate :end_date_after_start_date, if: -> { start_date.present? && end_date.present? }

  # Enums
  enum :status, {
    draft: 0,
    active: 1,
    paused: 2,
    completed: 3,
    cancelled: 4
  }

  enum :campaign_type, {
    email: 0,
    social: 1,
    seo: 2,
    multi_channel: 3
  }

  # Vibe Marketing enums
  enum :vibe_status, {
    pending: "pending",
    analyzing: "analyzing",
    vibe_approved: "vibe_approved",
    vibe_flagged: "vibe_flagged",
    authenticity_verified: "authenticity_verified",
    cultural_validated: "cultural_validated"
  }

  # Scopes
  scope :by_status, ->(status) { where(status: status) }
  scope :by_type, ->(type) { where(campaign_type: type) }
  scope :recent, -> { order(created_at: :desc) }
  scope :ongoing, -> { where(status: [ :active, :paused ]) }

  # Vibe Marketing scopes
  scope :by_vibe_status, ->(status) { where(vibe_status: status) }
  scope :by_emotional_tone, ->(tone) { where(emotional_tone: tone) }
  scope :culturally_relevant, -> { where("cultural_relevance_score >= ?", 75.0) }
  scope :authenticity_verified, -> { where("authenticity_score >= ?", 85.0) }
  scope :vibe_ready, -> { where(vibe_status: [ :vibe_approved, :authenticity_verified, :cultural_validated ]) }

  # Instance methods
  def can_be_activated?
    draft? || paused?
  end

  def duration_in_days
    return nil unless start_date.present? && end_date.present?
    (end_date - start_date).to_i
  end

  def progress_percentage
    return 100 if completed?
    return 0 if draft? || start_date.blank? || end_date.blank?

    now = Date.current
    return 0 if now < start_date
    return 100 if now > end_date

    total_days = duration_in_days
    return 0 if total_days <= 0

    elapsed_days = (now - start_date).to_i
    ((elapsed_days.to_f / total_days) * 100).round
  end

  def budget_in_dollars
    budget_cents / 100.0
  end

  def budget_in_dollars=(amount)
    self.budget_cents = (amount.to_f * 100).round
  end

  # Performance metrics calculated from campaign_metrics
  def click_through_rate
    total_impressions = campaign_metrics.sum(:impressions)
    total_clicks = campaign_metrics.sum(:clicks)
    return 0.0 if total_impressions.zero?

    (total_clicks.to_f / total_impressions * 100).round(2)
  end

  def conversion_rate
    total_clicks = campaign_metrics.sum(:clicks)
    total_conversions = campaign_metrics.sum(:conversions)
    return 0.0 if total_clicks.zero?

    (total_conversions.to_f / total_clicks * 100).round(2)
  end

  def total_impressions
    campaign_metrics.sum(:impressions)
  end

  def total_clicks
    campaign_metrics.sum(:clicks)
  end

  def total_conversions
    campaign_metrics.sum(:conversions)
  end

  def total_revenue
    campaign_metrics.sum(:revenue_cents) / 100.0
  end

  def total_cost
    campaign_metrics.sum(:cost_cents) / 100.0
  end

  def return_on_ad_spend
    return 0.0 if total_cost.zero?
    ((total_revenue / total_cost) * 100).round(2)
  end

  def roi
    return 0.0 if total_cost.zero?
    (((total_revenue - total_cost) / total_cost) * 100).round(2)
  end

  # AI Agent Workflow methods
  def has_active_ai_workflows?
    agent_workflows.active.exists?
  end

  def latest_ai_workflow
    agent_workflows.recent.first
  end

  def ai_content_generated?
    case campaign_type
    when "email"
      email_campaign&.content.present? && email_campaign&.settings&.dig("ai_generated")
    when "social"
      social_campaign&.content_variants&.present? && social_campaign&.social_settings&.dig("ai_generated")
    when "seo"
      seo_campaign&.content_strategy&.present? && seo_campaign&.seo_settings&.dig("ai_generated")
    when "multi_channel"
      ai_content_generated_for_any_channel?
    else
      false
    end
  end

  def ai_workflow_status
    latest_workflow = latest_ai_workflow
    return "none" unless latest_workflow

    case latest_workflow.status
    when "running", "pending"
      "generating"
    when "completed"
      "completed"
    when "failed"
      "error"
    when "cancelled"
      "cancelled"
    else
      "unknown"
    end
  end

  def can_generate_ai_content?
    draft? && !has_active_ai_workflows?
  end

  def ai_optimization_available?
    (active? || completed?) && !has_active_ai_workflows? && campaign_metrics.any?
  end

  # Vibe Marketing methods
  def vibe_score
    return 0.0 unless cultural_relevance_score.present? && authenticity_score.present?

    # Weighted average: 60% authenticity, 40% cultural relevance
    (authenticity_score * 0.6 + cultural_relevance_score * 0.4).round(2)
  end

  def emotional_resonance_data
    vibe_analysis_records.find_by(analysis_type: "emotional")&.analysis_data || {}
  end

  def cultural_analysis_data
    vibe_analysis_records.find_by(analysis_type: "cultural")&.analysis_data || {}
  end

  def psychographic_insights
    vibe_analysis_records.find_by(analysis_type: "psychographic")&.analysis_data || {}
  end

  def authenticity_findings
    authenticity_checks.order(created_at: :desc).first&.findings || {}
  end

  def vibe_recommendations
    authenticity_checks.order(created_at: :desc).first&.recommendations || []
  end

  def needs_vibe_analysis?
    pending? || (vibe_data.blank? && vibe_analysis_records.empty?)
  end

  def cultural_moment_alignment
    return nil unless vibe_data.dig("cultural_moment").present?

    cultural_moment = CulturalMoment.active.find_by(title: vibe_data["cultural_moment"])
    return nil unless cultural_moment

    {
      moment: cultural_moment.title,
      relevance_score: cultural_moment.relevance_score,
      category: cultural_moment.category,
      alignment_percentage: calculate_cultural_alignment(cultural_moment)
    }
  end

  def emotional_journey_stage
    vibe_data.dig("emotional_journey", "current_stage") || "awareness"
  end

  def target_psychographics
    vibe_data.dig("psychographics") || {}
  end

  def vibe_validation_status
    return "pending" if authenticity_checks.empty?

    latest_check = authenticity_checks.order(created_at: :desc).first
    case latest_check.status
    when "approved"
      "validated"
    when "flagged"
      "needs_review"
    when "rejected"
      "failed_validation"
    else
      "under_review"
    end
  end

  def can_launch_with_vibe?
    vibe_score >= 70.0 && vibe_validation_status == "validated"
  end

  def emotional_tone_match_score
    return 0.0 unless emotional_tone.present? && emotional_resonance_data.present?

    target_emotion = emotional_resonance_data.dig("primary_emotion")
    return 100.0 if emotional_tone.downcase == target_emotion&.downcase

    # Calculate compatibility between emotions using Plutchik's wheel
    emotion_compatibility(emotional_tone, target_emotion)
  end

  private

  def ai_content_generated_for_any_channel?
    [
      email_campaign&.settings&.dig("ai_generated"),
      social_campaign&.social_settings&.dig("ai_generated"),
      seo_campaign&.seo_settings&.dig("ai_generated")
    ].any?
  end

  # Vibe Marketing private methods
  def calculate_cultural_alignment(cultural_moment)
    return 0 unless cultural_moment.target_demographics.present? && target_audience.present?

    # Simple alignment calculation based on overlapping demographics
    # In a real implementation, this would use more sophisticated matching
    overlap_score = 75.0 # Default good alignment

    # Adjust based on moment relevance and timing
    if cultural_moment.start_date.present? && cultural_moment.end_date.present?
      campaign_overlap = date_overlap_percentage(cultural_moment)
      overlap_score *= (campaign_overlap / 100.0)
    end

    overlap_score.round(1)
  end

  def date_overlap_percentage(cultural_moment)
    return 0 unless start_date.present? && end_date.present?

    moment_start = cultural_moment.start_date
    moment_end = cultural_moment.end_date || Date.current + 30.days

    overlap_start = [ start_date, moment_start ].max
    overlap_end = [ end_date, moment_end ].min

    return 0 if overlap_start > overlap_end

    overlap_days = (overlap_end - overlap_start).to_i + 1
    campaign_days = (end_date - start_date).to_i + 1

    ((overlap_days.to_f / campaign_days) * 100).round(1)
  end

  def emotion_compatibility(emotion1, emotion2)
    return 100.0 if emotion1.nil? || emotion2.nil?

    # Plutchik's wheel compatibility mapping (simplified)
    compatibility_map = {
      "joy" => { "trust" => 90, "anticipation" => 80, "surprise" => 70, "anger" => 20, "fear" => 10, "sadness" => 15, "disgust" => 25 },
      "trust" => { "joy" => 90, "fear" => 75, "surprise" => 60, "anger" => 25, "sadness" => 20, "disgust" => 30, "anticipation" => 85 },
      "fear" => { "trust" => 75, "surprise" => 80, "sadness" => 70, "joy" => 10, "anger" => 30, "disgust" => 40, "anticipation" => 20 },
      "surprise" => { "fear" => 80, "sadness" => 75, "trust" => 60, "joy" => 70, "anger" => 35, "disgust" => 45, "anticipation" => 50 },
      "sadness" => { "surprise" => 75, "disgust" => 80, "fear" => 70, "joy" => 15, "trust" => 20, "anger" => 40, "anticipation" => 10 },
      "disgust" => { "sadness" => 80, "anger" => 75, "surprise" => 45, "joy" => 25, "trust" => 30, "fear" => 40, "anticipation" => 15 },
      "anger" => { "disgust" => 75, "anticipation" => 80, "sadness" => 40, "joy" => 20, "trust" => 25, "fear" => 30, "surprise" => 35 },
      "anticipation" => { "anger" => 80, "joy" => 80, "trust" => 85, "fear" => 20, "surprise" => 50, "sadness" => 10, "disgust" => 15 }
    }

    emotion1_key = emotion1.downcase
    emotion2_key = emotion2.downcase

    compatibility_map.dig(emotion1_key, emotion2_key) || 50.0
  end

  private

  def end_date_after_start_date
    return unless start_date.present? && end_date.present?

    if end_date <= start_date
      errors.add(:end_date, "must be after start date")
    end
  end
end
