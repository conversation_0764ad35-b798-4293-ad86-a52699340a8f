# frozen_string_literal: true

# == Schema Information
#
# Table name: audiences
#
#  id                         :bigint           not null, primary key
#  name                       :string           not null
#  description                :text
#  target_demographics        :text
#  cultural_context           :string
#  primary_language           :string
#  secondary_languages        :string           is an Array
#  geographic_regions         :string           is an Array
#  age_range_min              :integer
#  age_range_max              :integer
#  interests                  :text             is an Array
#  behavioral_traits          :text             is an Array
#  communication_preferences  :text
#  cultural_values            :text
#  engagement_patterns        :text
#  cultural_alignment_score   :decimal(5,2)
#  demographics               :jsonb
#  cultural_attributes        :jsonb
#  preferences                :jsonb
#  tenant_id                  :bigint           not null
#  created_by_id              :bigint           not null
#  created_at                 :datetime         not null
#  updated_at                 :datetime         not null
#

class Audience < ApplicationRecord
  # Multi-tenancy
  acts_as_tenant(:tenant)
  belongs_to :tenant, required: true
  belongs_to :created_by, class_name: "User", required: true

  # Associations
  has_many :campaign_audiences, dependent: :destroy
  has_many :campaigns, through: :campaign_audiences
  has_many :audience_insights, dependent: :destroy
  has_many :audience_segments, dependent: :destroy

  # Validations
  validates :name, presence: true, uniqueness: { scope: :tenant_id }
  validates :cultural_context, inclusion: {
    in: %w[western eastern latin_american african middle_eastern asian_pacific],
    allow_blank: true
  }
  validates :primary_language, presence: true
  validates :age_range_min, :age_range_max, numericality: {
    greater_than: 0,
    less_than_or_equal_to: 120,
    allow_blank: true
  }
  validate :age_range_consistency

  # Scopes
  scope :by_cultural_context, ->(context) { where(cultural_context: context) }
  scope :by_language, ->(language) { where(primary_language: language) }
  scope :by_age_range, ->(min_age, max_age) {
    where("age_range_min >= ? AND age_range_max <= ?", min_age, max_age)
  }
  scope :with_high_cultural_alignment, -> { where("cultural_alignment_score >= ?", 80.0) }
  scope :recent, -> { order(created_at: :desc) }

  # Serialized attributes (Rails 8 style)
  serialize :secondary_languages, type: Array, coder: JSON
  serialize :geographic_regions, type: Array, coder: JSON
  serialize :interests, type: Array, coder: JSON
  serialize :behavioral_traits, type: Array, coder: JSON

  # Callbacks
  before_save :calculate_cultural_alignment_score
  after_create :create_default_segments

  # Instance methods
  def age_range
    return nil unless age_range_min && age_range_max
    "#{age_range_min}-#{age_range_max}"
  end

  def primary_demographics
    {
      age_range: age_range,
      cultural_context: cultural_context,
      primary_language: primary_language,
      geographic_regions: geographic_regions
    }
  end

  def engagement_score
    return 0 unless campaigns.any?

    # Calculate based on campaign performance
    campaign_metrics = campaigns.joins(:campaign_metrics)
                               .average("campaign_metrics.engagement_rate")

    (campaign_metrics || 0).round(2)
  end

  def cultural_compatibility_score
    return cultural_alignment_score if cultural_alignment_score.present?

    # Calculate based on cultural attributes and campaign performance
    calculate_cultural_compatibility
  end

  def vibe_approval_rate
    total_campaigns = campaigns.where.not(vibe_status: [ "pending", nil ]).count
    return 0 if total_campaigns.zero?

    approved_campaigns = campaigns.where(vibe_status: "vibe_approved").count
    ((approved_campaigns.to_f / total_campaigns) * 100).round(1)
  end

  def performance_summary
    {
      total_campaigns: campaigns.count,
      active_campaigns: campaigns.active.count,
      avg_engagement_rate: engagement_score,
      cultural_alignment: cultural_alignment_score || 0,
      vibe_approval_rate: vibe_approval_rate
    }
  end

  def demographic_profile
    {
      basic: {
        age_range: age_range,
        cultural_context: cultural_context&.humanize,
        primary_language: primary_language,
        geographic_regions: geographic_regions
      },
      detailed: {
        interests: interests,
        behavioral_traits: behavioral_traits,
        communication_preferences: communication_preferences,
        cultural_values: cultural_values
      },
      custom: demographics || {}
    }
  end

  def cultural_profile
    {
      context: cultural_context,
      values: cultural_values,
      alignment_score: cultural_alignment_score,
      attributes: cultural_attributes || {},
      communication_style: communication_preferences
    }
  end

  def targeting_recommendations
    recommendations = []

    # Based on cultural alignment
    if cultural_alignment_score && cultural_alignment_score < 70
      recommendations << {
        type: "cultural_improvement",
        priority: "high",
        message: "Consider refining cultural targeting to improve alignment"
      }
    end

    # Based on engagement patterns
    if engagement_score < 2.0
      recommendations << {
        type: "engagement_optimization",
        priority: "medium",
        message: "Engagement rates are below average - review content strategy"
      }
    end

    # Based on vibe approval rate
    if vibe_approval_rate < 80
      recommendations << {
        type: "vibe_optimization",
        priority: "medium",
        message: "Improve vibe compatibility for better campaign approval rates"
      }
    end

    recommendations
  end

  def similar_audiences(limit: 5)
    # Find audiences with similar characteristics
    similar = self.class.where(tenant: tenant)
                       .where.not(id: id)
                       .where(cultural_context: cultural_context)
                       .where(primary_language: primary_language)

    # Add age range similarity
    if age_range_min && age_range_max
      similar = similar.where(
        "ABS(age_range_min - ?) + ABS(age_range_max - ?) < 20",
        age_range_min, age_range_max
      )
    end

    similar.limit(limit)
  end

  def campaign_performance_by_type
    campaigns.joins(:campaign_metrics)
             .group(:campaign_type)
             .average("campaign_metrics.engagement_rate")
  end

  def seasonal_performance_trends
    campaigns.joins(:campaign_metrics)
             .group_by_month(:created_at, last: 12)
             .average("campaign_metrics.engagement_rate")
  end

  def content_preferences
    # Analyze successful campaigns to determine content preferences
    successful_campaigns = campaigns.joins(:campaign_metrics)
                                   .where("campaign_metrics.engagement_rate > ?", 3.0)

    emotional_tones = successful_campaigns.where.not(emotional_tone: nil)
                                         .group(:emotional_tone)
                                         .count

    {
      preferred_emotional_tones: emotional_tones.sort_by { |_, count| -count }.first(3).to_h,
      successful_campaign_types: successful_campaigns.group(:campaign_type).count,
      avg_performance: successful_campaigns.joins(:campaign_metrics)
                                          .average("campaign_metrics.engagement_rate")
    }
  end

  private

  def age_range_consistency
    return unless age_range_min && age_range_max

    if age_range_min >= age_range_max
      errors.add(:age_range_max, "must be greater than minimum age")
    end
  end

  def calculate_cultural_alignment_score
    return unless cultural_context.present? && primary_language.present?

    # Base score calculation
    base_score = 50.0

    # Adjust based on cultural context specificity
    base_score += 20.0 if cultural_context.present?

    # Adjust based on language specificity
    base_score += 15.0 if primary_language.present?

    # Adjust based on demographic completeness
    demographic_completeness = calculate_demographic_completeness
    base_score += (demographic_completeness * 15.0)

    # Adjust based on campaign performance if available
    if campaigns.any?
      avg_vibe_approval = vibe_approval_rate
      base_score += (avg_vibe_approval * 0.1) if avg_vibe_approval > 0
    end

    self.cultural_alignment_score = [ base_score, 100.0 ].min.round(2)
  end

  def calculate_demographic_completeness
    total_fields = 8.0
    completed_fields = 0.0

    completed_fields += 1 if age_range_min.present?
    completed_fields += 1 if age_range_max.present?
    completed_fields += 1 if geographic_regions.present? && geographic_regions.any?
    completed_fields += 1 if interests.present? && interests.any?
    completed_fields += 1 if behavioral_traits.present? && behavioral_traits.any?
    completed_fields += 1 if communication_preferences.present?
    completed_fields += 1 if cultural_values.present?
    completed_fields += 1 if secondary_languages.present? && secondary_languages.any?

    completed_fields / total_fields
  end

  def calculate_cultural_compatibility
    # Placeholder for more sophisticated cultural compatibility calculation
    base_score = 60.0

    # Adjust based on available data
    base_score += 20.0 if cultural_context.present?
    base_score += 10.0 if cultural_values.present?
    base_score += 10.0 if communication_preferences.present?

    base_score.round(2)
  end

  def create_default_segments
    # Create default audience segments based on demographics
    if age_range_min && age_range_max
      age_segment = audience_segments.create!(
        name: "#{name} - Age #{age_range}",
        segment_type: "demographic",
        criteria: { age_range: age_range },
        description: "Age-based segment for #{name}"
      )
    end

    if cultural_context.present?
      cultural_segment = audience_segments.create!(
        name: "#{name} - #{cultural_context.humanize}",
        segment_type: "cultural",
        criteria: { cultural_context: cultural_context },
        description: "Cultural segment for #{name}"
      )
    end
  end
end
