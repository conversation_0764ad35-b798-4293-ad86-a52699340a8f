# frozen_string_literal: true

# == Schema Information
#
# Table name: vibe_analysis_records
#
#  id               :bigint           not null, primary key
#  campaign_id      :bigint           not null
#  tenant_id        :bigint           not null
#  analysis_type    :string           not null
#  analysis_data    :jsonb            default({}), not null
#  confidence_score :decimal(5, 2)
#  ai_reasoning     :text
#  model_used       :string
#  metadata         :jsonb            default({}), not null
#  created_at       :datetime         not null
#  updated_at       :datetime         not null
#
# Indexes
#
#  index_vibe_analysis_records_on_analysis_data              (analysis_data) USING gin
#  index_vibe_analysis_records_on_analysis_type              (analysis_type)
#  index_vibe_analysis_records_on_campaign_id_and_analysis_type  (campaign_id,analysis_type) UNIQUE
#  index_vibe_analysis_records_on_confidence_score           (confidence_score)
#  index_vibe_analysis_records_on_tenant_id                  (tenant_id)
#
# Foreign Keys
#
#  fk_rails_...  (campaign_id => campaigns.id)
#  fk_rails_...  (tenant_id => tenants.id)
#

class VibeAnalysisRecord < ApplicationRecord
  # Multi-tenancy
  acts_as_tenant(:tenant)
  belongs_to :tenant, required: true
  belongs_to :campaign, required: true

  # Validations
  validates :analysis_type, presence: true, inclusion: {
    in: %w[emotional cultural authenticity psychographic]
  }
  validates :analysis_type, uniqueness: { scope: :campaign_id }
  validates :confidence_score, numericality: {
    greater_than_or_equal_to: 0.0,
    less_than_or_equal_to: 100.0
  }, allow_nil: true

  # Attribute declarations for enum fields
  attribute :analysis_type, :string

  # Enums
  enum :analysis_type, {
    emotional: "emotional",
    cultural: "cultural",
    authenticity: "authenticity",
    psychographic: "psychographic"
  }

  # Scopes
  scope :by_analysis_type, ->(type) { where(analysis_type: type) }
  scope :high_confidence, -> { where("confidence_score >= ?", 80.0) }
  scope :recent, -> { order(created_at: :desc) }
  scope :for_campaign, ->(campaign) { where(campaign: campaign) }

  # Instance methods
  def high_confidence?
    confidence_score.present? && confidence_score >= 80.0
  end

  def moderate_confidence?
    confidence_score.present? && confidence_score.between?(60.0, 79.9)
  end

  def low_confidence?
    confidence_score.present? && confidence_score < 60.0
  end

  def confidence_level
    return "unknown" unless confidence_score.present?

    case confidence_score
    when 80.0..100.0 then "high"
    when 60.0..79.9 then "moderate"
    when 40.0..59.9 then "fair"
    else "low"
    end
  end

  def primary_emotion
    return nil unless emotional?

    analysis_data.dig("primary_emotion")
  end

  def emotion_weights
    return {} unless emotional?

    analysis_data.dig("emotion_weights") || {}
  end

  def cultural_relevance_factors
    return {} unless cultural?

    analysis_data.dig("relevance_factors") || {}
  end

  def authenticity_concerns
    return [] unless authenticity?

    analysis_data.dig("concerns") || []
  end

  def psychographic_segments
    return [] unless psychographic?

    analysis_data.dig("segments") || []
  end

  def key_insights
    case analysis_type
    when "emotional"
      extract_emotional_insights
    when "cultural"
      extract_cultural_insights
    when "authenticity"
      extract_authenticity_insights
    when "psychographic"
      extract_psychographic_insights
    else
      []
    end
  end

  def recommendations
    analysis_data.dig("recommendations") || []
  end

  def summary
    analysis_data.dig("summary") || ai_reasoning&.truncate(200) || "#{analysis_type.humanize} analysis completed"
  end

  def needs_review?
    low_confidence? || (authenticity? && authenticity_concerns.any?)
  end

  def model_provider
    return "unknown" unless model_used.present?

    case model_used.downcase
    when /gpt|openai/ then "OpenAI"
    when /claude|anthropic/ then "Anthropic"
    when /gemini|google/ then "Google"
    when /deepseek/ then "DeepSeek"
    else "Other"
    end
  end

  def analysis_age_in_days
    (Time.current - created_at) / 1.day
  end

  def stale?
    analysis_age_in_days > 7 # Consider analysis stale after 7 days
  end

  private

  def extract_emotional_insights
    insights = []

    if primary_emotion.present?
      insights << "Primary emotion detected: #{primary_emotion.humanize}"
    end

    if emotion_weights.any?
      top_emotions = emotion_weights.sort_by { |_emotion, weight| -weight }.first(3)
      insights << "Top emotional drivers: #{top_emotions.map { |emotion, weight| "#{emotion.humanize} (#{(weight * 100).round}%)" }.join(', ')}"
    end

    insights
  end

  def extract_cultural_insights
    insights = []

    relevance_score = analysis_data.dig("cultural_relevance_score")
    if relevance_score.present?
      insights << "Cultural relevance score: #{relevance_score}%"
    end

    cultural_factors = cultural_relevance_factors
    if cultural_factors.any?
      top_factor = cultural_factors.max_by { |_factor, score| score }
      insights << "Strongest cultural connection: #{top_factor.first.humanize}"
    end

    insights
  end

  def extract_authenticity_insights
    insights = []

    authenticity_score = analysis_data.dig("authenticity_score")
    if authenticity_score.present?
      insights << "Authenticity score: #{authenticity_score}%"
    end

    if authenticity_concerns.any?
      insights << "Concerns identified: #{authenticity_concerns.count} areas need attention"
    else
      insights << "No major authenticity concerns detected"
    end

    insights
  end

  def extract_psychographic_insights
    insights = []

    if psychographic_segments.any?
      insights << "Target segments identified: #{psychographic_segments.count}"
      primary_segment = psychographic_segments.first
      if primary_segment.present?
        insights << "Primary segment: #{primary_segment.dig('name') || 'Unnamed segment'}"
      end
    end

    insights
  end
end
