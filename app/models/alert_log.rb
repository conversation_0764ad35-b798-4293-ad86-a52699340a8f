# frozen_string_literal: true

##
# Alert Log Model
#
# Stores system alerts for monitoring, auditing, and alerting purposes.
# Used to track budget alerts, error rate alerts, circuit breaker alerts, etc.
#
# @example Creating an alert log
#   AlertLog.create!(
#     tenant: tenant,
#     alert_type: 'budget_threshold',
#     severity: 'warning',
#     message: 'Budget threshold 80% reached',
#     metadata: { threshold: 80, current_percentage: 85.5 }
#   )
#
class AlertLog < ApplicationRecord
  acts_as_tenant(:tenant)

  belongs_to :tenant

  # Validations
  validates :alert_type, presence: true, length: { maximum: 50 }
  validates :severity, presence: true, length: { maximum: 20 }
  validates :message, presence: true, length: { maximum: 1000 }
  validates :metadata, presence: true

  # Enumerated alert types
  ALERT_TYPES = %w[
    budget_threshold
    error_rate
    circuit_breaker_opened
    usage_anomaly
    provider_failure
    cost_spike
    performance_degradation
    security_violation
    quota_exceeded
    system_health
  ].freeze

  validates :alert_type, inclusion: { in: ALERT_TYPES }

  # Enumerated severity levels
  SEVERITY_LEVELS = %w[
    info
    warning
    error
    critical
  ].freeze

  validates :severity, inclusion: { in: SEVERITY_LEVELS }

  # Scopes for common queries
  scope :by_alert_type, ->(type) { where(alert_type: type) }
  scope :by_severity, ->(severity) { where(severity: severity) }
  scope :recent, -> { order(created_at: :desc) }
  scope :critical, -> { where(severity: "critical") }
  scope :warnings_and_above, -> { where(severity: %w[warning error critical]) }
  scope :errors_and_above, -> { where(severity: %w[error critical]) }
  scope :today, -> { where(created_at: Date.current.beginning_of_day..Date.current.end_of_day) }
  scope :this_week, -> { where(created_at: 1.week.ago.beginning_of_day..Time.current) }
  scope :this_month, -> { where(created_at: 1.month.ago.beginning_of_day..Time.current) }

  # Class methods for analytics

  # Get alert summary for a tenant
  #
  # @param tenant [Tenant] The tenant to analyze
  # @param start_date [DateTime] Start date for analysis
  # @param end_date [DateTime] End date for analysis
  # @return [Hash] Alert summary with counts and breakdowns
  def self.summary_for_tenant(tenant, start_date: 30.days.ago, end_date: Time.current)
    alerts = ActsAsTenant.with_tenant(tenant) do
      where(created_at: start_date..end_date)
    end

    {
      total_alerts: alerts.count,
      by_severity: alerts.group(:severity).count,
      by_type: alerts.group(:alert_type).count,
      recent_critical: alerts.critical.limit(5).pluck(:message, :created_at),
      alert_frequency: calculate_alert_frequency(alerts),
      trending_up: detect_trending_alerts(tenant, start_date, end_date)
    }
  end

  # Get critical alerts that need immediate attention
  #
  # @param tenant [Tenant] The tenant to check
  # @return [ActiveRecord::Relation] Critical alerts from last 24 hours
  def self.critical_alerts_for_tenant(tenant)
    ActsAsTenant.with_tenant(tenant) do
      critical.where(created_at: 24.hours.ago..Time.current).recent
    end
  end

  # Check if alert rate is increasing
  #
  # @param tenant [Tenant] The tenant to check
  # @param alert_type [String] Specific alert type to check (optional)
  # @return [Boolean] True if alert rate is increasing
  def self.alert_rate_increasing?(tenant, alert_type: nil)
    current_week = ActsAsTenant.with_tenant(tenant) do
      query = where(created_at: 1.week.ago..Time.current)
      query = query.where(alert_type: alert_type) if alert_type
      query.count
    end

    previous_week = ActsAsTenant.with_tenant(tenant) do
      query = where(created_at: 2.weeks.ago..1.week.ago)
      query = query.where(alert_type: alert_type) if alert_type
      query.count
    end

    return false if previous_week.zero?

    current_week > previous_week * 1.5 # 50% increase threshold
  end

  # Find duplicate alerts (same type and similar message within time window)
  #
  # @param tenant [Tenant] The tenant to check
  # @param time_window [Integer] Time window in minutes (default: 60)
  # @return [Array] Duplicate alert groups
  def self.find_duplicate_alerts(tenant, time_window: 60)
    ActsAsTenant.with_tenant(tenant) do
      where(created_at: time_window.minutes.ago..Time.current)
        .group(:alert_type, :message)
        .having("COUNT(*) > 1")
        .count
        .map { |key, count| { alert_type: key[0], message: key[1], count: count } }
    end
  end

  # Instance methods

  # Check if this is a critical alert
  #
  # @return [Boolean] True if severity is critical
  def critical?
    severity == "critical"
  end

  # Check if this is a budget-related alert
  #
  # @return [Boolean] True if alert_type is budget-related
  def budget_related?
    alert_type.start_with?("budget_")
  end

  # Check if this is a performance-related alert
  #
  # @return [Boolean] True if alert_type is performance-related
  def performance_related?
    %w[error_rate performance_degradation circuit_breaker_opened].include?(alert_type)
  end

  # Get human-readable severity with emoji
  #
  # @return [String] Severity with appropriate emoji
  def severity_with_emoji
    case severity
    when "info"
      "ℹ️ Info"
    when "warning"
      "⚠️ Warning"
    when "error"
      "❌ Error"
    when "critical"
      "🚨 Critical"
    else
      severity.titleize
    end
  end

  # Get formatted timestamp
  #
  # @return [String] Human-readable timestamp
  def formatted_timestamp
    created_at.strftime("%Y-%m-%d %H:%M:%S %Z")
  end

  # Extract specific metadata values safely
  #
  # @param key [String] Metadata key to extract
  # @return [Object] Metadata value or nil
  def metadata_value(key)
    metadata&.dig(key.to_s)
  end

  # Check if alert should trigger notification
  #
  # @return [Boolean] True if notification should be sent
  def should_notify?
    case severity
    when "critical"
      true
    when "error"
      # Only notify for errors if it's the first occurrence in last hour
      !self.class.where(
        tenant: tenant,
        alert_type: alert_type,
        severity: "error",
        created_at: 1.hour.ago..created_at
      ).where.not(id: id).exists?
    when "warning"
      # Only notify for warnings if it's budget-related or first occurrence today
      budget_related? || !self.class.where(
        tenant: tenant,
        alert_type: alert_type,
        severity: "warning",
        created_at: Date.current.beginning_of_day..created_at
      ).where.not(id: id).exists?
    else
      false
    end
  end

  private

  # Calculate alert frequency for summary
  def self.calculate_alert_frequency(alerts)
    return 0 if alerts.empty?

    time_span_hours = (alerts.maximum(:created_at) - alerts.minimum(:created_at)) / 1.hour
    return alerts.count if time_span_hours < 1

    (alerts.count / time_span_hours).round(2)
  end

  # Detect trending alert types
  def self.detect_trending_alerts(tenant, start_date, end_date)
    midpoint = start_date + (end_date - start_date) / 2

    first_half = ActsAsTenant.with_tenant(tenant) do
      where(created_at: start_date..midpoint).group(:alert_type).count
    end

    second_half = ActsAsTenant.with_tenant(tenant) do
      where(created_at: midpoint..end_date).group(:alert_type).count
    end

    trending = []
    second_half.each do |alert_type, second_count|
      first_count = first_half[alert_type] || 0
      next if first_count.zero?

      growth_rate = ((second_count - first_count).to_f / first_count * 100).round(1)
      trending << { alert_type: alert_type, growth_rate: growth_rate } if growth_rate > 50
    end

    trending.sort_by { |item| -item[:growth_rate] }
  end
end
