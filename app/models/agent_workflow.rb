# frozen_string_literal: true

# == Schema Information
# Table name: agent_workflows
#
#  id                :bigint           not null, primary key
#  campaign_id       :bigint           not null
#  workflow_type     :integer          default(0), not null
#  status            :integer          default(0), not null
#  progress_percent  :integer          default(0), not null
#  current_step      :string
#  total_steps       :integer          default(0)
#  context_data      :jsonb            default({}), not null
#  results           :jsonb            default({}), not null
#  error_details     :jsonb            default({}), not null
#  started_at        :datetime
#  completed_at      :datetime
#  tenant_id         :bigint           not null
#  created_at        :datetime         not null
#  updated_at        :datetime         not null
#
# Indexes
#
#  index_agent_workflows_on_campaign_id     (campaign_id)
#  index_agent_workflows_on_context_data    (context_data) USING gin
#  index_agent_workflows_on_results         (results) USING gin
#  index_agent_workflows_on_status          (status)
#  index_agent_workflows_on_tenant_id       (tenant_id)
#  index_agent_workflows_on_workflow_type   (workflow_type)
#
# Foreign Keys
#
#  fk_rails_...  (campaign_id => campaigns.id)
#  fk_rails_...  (tenant_id => tenants.id)

class AgentWorkflow < ApplicationRecord
  # Multi-tenancy
  acts_as_tenant(:tenant)
  belongs_to :tenant, required: true
  belongs_to :campaign, required: true

  # Validations
  validates :workflow_type, presence: true
  validates :status, presence: true
  validates :progress_percent, numericality: { in: 0..100 }
  validates :total_steps, numericality: { greater_than: 0 }, if: -> { total_steps.present? }

  # Attribute declarations for enum fields
  attribute :status, :integer
  attribute :workflow_type, :integer

  # Enums
  enum :status, {
    pending: 0,
    running: 1,
    completed: 2,
    failed: 3,
    cancelled: 4
  }

  enum :workflow_type, {
    campaign_generation: 0,
    content_optimization: 1,
    performance_analysis: 2,
    multi_channel_coordination: 3,
    audience_segmentation: 4
  }

  # Scopes
  scope :active, -> { where(status: [ :pending, :running ]) }
  scope :recent, -> { order(created_at: :desc) }
  scope :by_type, ->(type) { where(workflow_type: type) }

  # Callbacks
  after_update :broadcast_progress_update, if: :saved_change_to_progress_percent?

  # Instance methods
  def duration
    return nil unless started_at.present?
    end_time = completed_at || Time.current
    end_time - started_at
  end

  def mark_as_started!
    update!(
      status: :running,
      started_at: Time.current,
      progress_percent: 0
    )
  end

  def mark_as_completed!(results = {})
    update!(
      status: :completed,
      completed_at: Time.current,
      progress_percent: 100,
      results: self.results.merge(results)
    )
  end

  def mark_as_failed!(error_message, error_details = {})
    update!(
      status: :failed,
      completed_at: Time.current,
      error_details: {
        message: error_message,
        occurred_at: Time.current,
        details: error_details
      }.merge(self.error_details)
    )
  end

  def update_progress!(step_name, percent, step_results = {})
    update!(
      current_step: step_name,
      progress_percent: [ percent, 100 ].min,
      results: self.results.merge(step_name => step_results)
    )
  end

  def add_context(key, value)
    update!(context_data: context_data.merge(key.to_s => value))
  end

  def get_context(key)
    context_data[key.to_s]
  end

  private

  def broadcast_progress_update
    # Broadcast real-time updates via Hotwire
    Turbo::StreamsChannel.broadcast_replace_to(
      "agent_workflow_#{id}",
      target: "workflow-progress-#{id}",
      partial: "agent_workflows/progress",
      locals: { workflow: self }
    )
  end
end
