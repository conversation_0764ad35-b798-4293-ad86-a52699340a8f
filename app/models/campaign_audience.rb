# frozen_string_literal: true

# == Schema Information
#
# Table name: campaign_audiences
#
#  id           :bigint           not null, primary key
#  campaign_id  :bigint           not null
#  audience_id  :bigint           not null
#  tenant_id    :bigint           not null
#  created_at   :datetime         not null
#  updated_at   :datetime         not null
#

class CampaignAudience < ApplicationRecord
  # Multi-tenancy
  acts_as_tenant(:tenant)
  belongs_to :tenant, required: true
  
  # Associations
  belongs_to :campaign, required: true
  belongs_to :audience, required: true

  # Validations
  validates :campaign_id, uniqueness: { scope: [:audience_id, :tenant_id] }

  # Scopes
  scope :for_campaign, ->(campaign) { where(campaign: campaign) }
  scope :for_audience, ->(audience) { where(audience: audience) }
  scope :recent, -> { order(created_at: :desc) }

  # Instance methods
  def campaign_name
    campaign&.name
  end

  def audience_name
    audience&.name
  end

  def cultural_alignment_score
    audience&.cultural_alignment_score || 0
  end

  def engagement_score
    audience&.engagement_score || 0
  end
end
