// Campaigns View Toggle Functionality
document.addEventListener('DOMContentLoaded', function() {
  // Initialize view toggle functionality
  initCampaignsViewToggle();
  
  function initCampaignsViewToggle() {
    const viewButtons = document.querySelectorAll('.campaigns-view-btn');
    const viewContainers = document.querySelectorAll('.campaign-view');
    
    // Set initial view state
    const defaultView = 'grid';
    showView(defaultView);
    setActiveButton(defaultView);
    
    // Add click event listeners to view toggle buttons
    viewButtons.forEach(button => {
      button.addEventListener('click', function() {
        const viewType = this.getAttribute('data-view');
        
        // Update active button state
        setActiveButton(viewType);
        
        // Show corresponding view
        showView(viewType);
        
        // Store user preference
        localStorage.setItem('campaigns_preferred_view', viewType);
        
        // Trigger custom event for analytics
        document.dispatchEvent(new CustomEvent('campaignViewChanged', {
          detail: { viewType: viewType }
        }));
      });
    });
    
    // Restore user's preferred view from localStorage
    const preferredView = localStorage.getItem('campaigns_preferred_view');
    if (preferredView && document.querySelector(`[data-view="${preferredView}"]`)) {
      showView(preferredView);
      setActiveButton(preferredView);
    }
  }
  
  function setActiveButton(activeView) {
    const viewButtons = document.querySelectorAll('.campaigns-view-btn');
    
    viewButtons.forEach(button => {
      const viewType = button.getAttribute('data-view');
      
      if (viewType === activeView) {
        button.classList.add('active');
        button.classList.remove('text-gray-600', 'hover:text-gray-800');
        button.classList.add('text-blue-600', 'bg-blue-50');
      } else {
        button.classList.remove('active');
        button.classList.remove('text-blue-600', 'bg-blue-50');
        button.classList.add('text-gray-600', 'hover:text-gray-800');
      }
    });
  }
  
  function showView(viewType) {
    const viewContainers = document.querySelectorAll('.campaign-view');
    
    viewContainers.forEach(container => {
      const containerViewType = container.getAttribute('data-view');
      
      if (containerViewType === viewType) {
        container.style.display = 'block';
        container.classList.add('active');
        
        // Add fade-in animation
        container.style.opacity = '0';
        setTimeout(() => {
          container.style.opacity = '1';
          container.style.transition = 'opacity 0.3s ease-in-out';
        }, 10);
      } else {
        container.style.display = 'none';
        container.classList.remove('active');
      }
    });
    
    // Handle responsive behavior
    handleResponsiveView(viewType);
  }
  
  function handleResponsiveView(viewType) {
    const isMobile = window.innerWidth < 768;
    const isTablet = window.innerWidth >= 768 && window.innerWidth < 1024;
    
    // On mobile, force mobile view if available
    if (isMobile && viewType !== 'mobile') {
      const mobileView = document.querySelector('[data-view="mobile"]');
      if (mobileView) {
        // Auto-switch to mobile view on small screens
        setTimeout(() => {
          showView('mobile');
          setActiveButton('mobile');
        }, 100);
      }
    }
    
    // On tablet, prefer grid view over table for better UX
    if (isTablet && viewType === 'table') {
      const gridView = document.querySelector('[data-view="grid"]');
      if (gridView) {
        // Suggest grid view on tablets
        console.log('Grid view recommended for tablet screens');
      }
    }
  }
  
  // Handle window resize events
  window.addEventListener('resize', function() {
    const currentActiveButton = document.querySelector('.campaigns-view-btn.active');
    if (currentActiveButton) {
      const currentView = currentActiveButton.getAttribute('data-view');
      handleResponsiveView(currentView);
    }
  });
  
  // Handle keyboard navigation
  document.addEventListener('keydown', function(e) {
    // Allow switching views with keyboard shortcuts
    if (e.ctrlKey || e.metaKey) {
      switch(e.key) {
        case '1':
          e.preventDefault();
          showView('grid');
          setActiveButton('grid');
          break;
        case '2':
          e.preventDefault();
          showView('table');
          setActiveButton('table');
          break;
        case '3':
          e.preventDefault();
          if (document.querySelector('[data-view="mobile"]')) {
            showView('mobile');
            setActiveButton('mobile');
          }
          break;
      }
    }
  });
  
  // Initialize bulk selection functionality
  initBulkSelection();
  
  function initBulkSelection() {
    const selectAllCheckboxes = document.querySelectorAll('.campaigns-select-all, .campaigns-select-all-table');
    const individualCheckboxes = document.querySelectorAll('.campaigns-select-checkbox, .campaigns-table-select');
    
    // Handle select all functionality
    selectAllCheckboxes.forEach(selectAll => {
      selectAll.addEventListener('change', function() {
        const isChecked = this.checked;
        const viewType = this.classList.contains('campaigns-select-all-table') ? 'table' : 'grid';
        
        // Update all individual checkboxes in the current view
        const targetCheckboxes = viewType === 'table' 
          ? document.querySelectorAll('.campaigns-table-select')
          : document.querySelectorAll('.campaigns-select-checkbox');
          
        targetCheckboxes.forEach(checkbox => {
          checkbox.checked = isChecked;
        });
        
        updateBulkActionsVisibility();
      });
    });
    
    // Handle individual checkbox changes
    individualCheckboxes.forEach(checkbox => {
      checkbox.addEventListener('change', function() {
        updateSelectAllState();
        updateBulkActionsVisibility();
      });
    });
  }
  
  function updateSelectAllState() {
    const currentView = document.querySelector('.campaign-view.active');
    if (!currentView) return;
    
    const viewType = currentView.getAttribute('data-view');
    const selectAllCheckbox = viewType === 'table' 
      ? document.querySelector('.campaigns-select-all-table')
      : document.querySelector('.campaigns-select-all');
      
    const individualCheckboxes = viewType === 'table'
      ? document.querySelectorAll('.campaigns-table-select')
      : document.querySelectorAll('.campaigns-select-checkbox');
    
    if (selectAllCheckbox && individualCheckboxes.length > 0) {
      const checkedCount = Array.from(individualCheckboxes).filter(cb => cb.checked).length;
      
      selectAllCheckbox.checked = checkedCount === individualCheckboxes.length;
      selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < individualCheckboxes.length;
    }
  }
  
  function updateBulkActionsVisibility() {
    const checkedCheckboxes = document.querySelectorAll('.campaigns-select-checkbox:checked, .campaigns-table-select:checked');
    const bulkActionContainers = document.querySelectorAll('.campaigns-table-bulk-actions, .campaigns-bulk-actions');
    const selectedCountElements = document.querySelectorAll('.campaigns-selected-count');
    
    // Update selected count
    selectedCountElements.forEach(element => {
      element.textContent = checkedCheckboxes.length;
    });
    
    // Show/hide bulk actions
    bulkActionContainers.forEach(container => {
      if (checkedCheckboxes.length > 0) {
        container.style.opacity = '1';
        container.style.pointerEvents = 'auto';
      } else {
        container.style.opacity = '0';
        container.style.pointerEvents = 'none';
      }
    });
  }
});
