// Emotional Customer Detail Stimulus Controller
// Handles customer-specific emotional intelligence interactions

import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["updateModal", "predictionModal", "predictionResults"]
  static values = { 
    customerIdentifier: String,
    refreshInterval: { type: Number, default: 60000 } // 1 minute
  }

  connect() {
    console.log(`👤 Customer detail connected: ${this.customerIdentifierValue}`)
    
    // Start periodic refresh for this customer's data
    this.startPeriodicRefresh()
    
    // Set up form submission handlers
    this.setupFormHandlers()
  }

  disconnect() {
    this.stopPeriodicRefresh()
  }

  // Start periodic refresh of customer emotional state
  startPeriodicRefresh() {
    if (this.refreshTimer) return
    
    this.refreshTimer = setInterval(() => {
      if (!document.hidden) {
        this.refreshState()
      }
    }, this.refreshIntervalValue)
  }

  // Stop periodic refresh
  stopPeriodicRefresh() {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer)
      this.refreshTimer = null
    }
  }

  // Refresh customer emotional state
  async refreshState(event) {
    if (event) event.preventDefault()
    
    try {
      console.log(`🔄 Refreshing emotional state for ${this.customerIdentifierValue}`)
      
      const response = await fetch(window.location.href + '.json', {
        headers: { 'Accept': 'application/json' }
      })
      
      if (!response.ok) throw new Error(`HTTP ${response.status}`)
      
      const data = await response.json()
      this.updateCustomerData(data)
      
      this.showNotification('success', 'Emotional state refreshed')
      
    } catch (error) {
      console.error("❌ Customer state refresh failed:", error)
      this.showNotification('error', 'Failed to refresh emotional state')
    }
  }

  // Update customer data on the page
  updateCustomerData(data) {
    const customer = data.customer
    
    // Update current emotional state display
    this.updateEmotionalStateDisplay(customer)
    
    // Update compatibility scores
    if (data.campaign_compatibility) {
      this.updateCompatibilityDisplay(data.campaign_compatibility)
    }
    
    // Update recommendations
    if (data.recommendations) {
      this.updateRecommendationsDisplay(data.recommendations)
    }
    
    console.log("✅ Customer data updated")
  }

  // Update the emotional state display elements
  updateEmotionalStateDisplay(customer) {
    // Update primary emotion
    const emotionElement = this.element.querySelector('[data-current-emotion]')
    if (emotionElement) {
      emotionElement.textContent = customer.current_emotion
      emotionElement.className = `capitalize ${this.getEmotionClass(customer.current_emotion)}`
    }
    
    // Update intensity
    const intensityElement = this.element.querySelector('[data-emotional-intensity]')
    if (intensityElement) {
      intensityElement.textContent = customer.intensity
    }
    
    // Update confidence
    const confidenceElement = this.element.querySelector('[data-confidence-score]')
    if (confidenceElement) {
      confidenceElement.textContent = `${customer.confidence}%`
      confidenceElement.className = this.getConfidenceClass(customer.confidence)
    }
    
    // Update receptivity
    const receptivityElement = this.element.querySelector('[data-receptivity-status]')
    if (receptivityElement) {
      receptivityElement.textContent = customer.receptive ? 'Receptive' : 'Not Receptive'
      receptivityElement.className = customer.receptive ? 'text-green-600 font-medium' : 'text-red-600 font-medium'
    }
  }

  // Update campaign compatibility display
  updateCompatibilityDisplay(compatibilityData) {
    compatibilityData.forEach((compatibility, index) => {
      const compatibilityElement = this.element.querySelector(`[data-compatibility-index="${index}"]`)
      if (compatibilityElement) {
        const scoreElement = compatibilityElement.querySelector('[data-compatibility-score]')
        if (scoreElement) {
          scoreElement.textContent = `${compatibility.compatibility_score.toFixed(1)}%`
          scoreElement.className = this.getCompatibilityClass(compatibility.compatibility_score)
        }
      }
    })
  }

  // Update recommendations display
  updateRecommendationsDisplay(recommendations) {
    const recommendationsContainer = this.element.querySelector('[data-recommendations-container]')
    if (!recommendationsContainer) return
    
    // Update recommendation count
    const countElement = this.element.querySelector('[data-recommendations-count]')
    if (countElement) {
      countElement.textContent = recommendations.length
    }
    
    // Update high priority count
    const highPriorityCount = recommendations.filter(r => r.priority === 'high').length
    const highPriorityElement = this.element.querySelector('[data-high-priority-count]')
    if (highPriorityElement) {
      highPriorityElement.textContent = highPriorityCount
    }
  }

  // Show the update signals modal
  showUpdateModal(event) {
    if (event) event.preventDefault()
    
    const modal = document.getElementById('updateSignalsModal')
    if (modal) {
      modal.classList.remove('hidden')
      
      // Focus on first input
      const firstInput = modal.querySelector('input[type="number"]')
      if (firstInput) {
        setTimeout(() => firstInput.focus(), 100)
      }
    }
  }

  // Hide the update signals modal
  hideUpdateModal(event) {
    if (event) event.preventDefault()
    
    const modal = document.getElementById('updateSignalsModal')
    if (modal) {
      modal.classList.add('hidden')
      
      // Reset form
      const form = modal.querySelector('form')
      if (form) form.reset()
    }
  }

  // Show prediction modal with campaign response prediction
  async predictResponse(event) {
    if (event) event.preventDefault()
    
    const campaignId = event.currentTarget.dataset.campaignId
    if (!campaignId) return
    
    try {
      console.log(`🔮 Predicting response for campaign ${campaignId}`)
      
      const response = await fetch(`/emotional_intelligence/customers/${this.customerIdentifierValue}/predict_response`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content,
          'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({ campaign_id: campaignId })
      })
      
      if (!response.ok) throw new Error(`HTTP ${response.status}`)
      
      const data = await response.json()
      
      if (data.success) {
        this.showPredictionResults(data)
      } else {
        this.showNotification('error', data.error || 'Prediction failed')
      }
      
    } catch (error) {
      console.error("❌ Response prediction failed:", error)
      this.showNotification('error', 'Failed to predict campaign response')
    }
  }

  // Show prediction results in modal
  showPredictionResults(predictionData) {
    const modal = document.getElementById('responsePredictionModal')
    const resultsContainer = document.getElementById('predictionResults')
    
    if (!modal || !resultsContainer) return
    
    // Build prediction results HTML
    const prediction = predictionData.prediction
    const compatibilityScore = predictionData.compatibility_score
    
    resultsContainer.innerHTML = `
      <div class="space-y-4">
        <div class="bg-gray-50 rounded-lg p-4">
          <h4 class="font-medium text-gray-900 mb-2">Compatibility Analysis</h4>
          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-600">Emotional Compatibility:</span>
            <span class="${this.getCompatibilityClass(compatibilityScore)}">
              ${compatibilityScore.toFixed(1)}%
            </span>
          </div>
          <div class="flex items-center justify-between mt-2">
            <span class="text-sm text-gray-600">Customer Emotion:</span>
            <span class="text-sm font-medium capitalize">${predictionData.customer_emotion}</span>
          </div>
        </div>
        
        <div class="bg-blue-50 rounded-lg p-4">
          <h4 class="font-medium text-gray-900 mb-2">Engagement Prediction</h4>
          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-600">Predicted Engagement:</span>
            <span class="text-lg font-bold ${this.getEngagementClass(prediction.predicted_engagement)}">
              ${prediction.predicted_engagement.toFixed(1)}%
            </span>
          </div>
          <div class="mt-2">
            <span class="text-sm text-gray-600">Optimal Timing:</span>
            <span class="text-sm font-medium ml-2">
              ${this.formatOptimalTiming(prediction.optimal_timing)}
            </span>
          </div>
        </div>
        
        ${this.renderRiskFactors(prediction.risk_factors)}
        ${this.renderPersonalizationSuggestions(prediction.personalization_suggestions)}
      </div>
    `
    
    modal.classList.remove('hidden')
  }

  // Hide prediction modal
  hidePredictionModal(event) {
    if (event) event.preventDefault()
    
    const modal = document.getElementById('responsePredictionModal')
    if (modal) {
      modal.classList.add('hidden')
    }
  }

  // Render risk factors section
  renderRiskFactors(riskFactors) {
    if (!riskFactors || riskFactors.length === 0) {
      return `
        <div class="bg-green-50 rounded-lg p-4">
          <h4 class="font-medium text-gray-900 mb-2">Risk Assessment</h4>
          <div class="flex items-center space-x-2">
            <span class="text-green-600">✅</span>
            <span class="text-sm text-green-700">No significant risks identified</span>
          </div>
        </div>
      `
    }
    
    const highRisks = riskFactors.filter(r => r.severity === 'high')
    const mediumRisks = riskFactors.filter(r => r.severity === 'medium')
    
    return `
      <div class="bg-yellow-50 rounded-lg p-4">
        <h4 class="font-medium text-gray-900 mb-2">Risk Assessment</h4>
        <div class="space-y-2">
          ${highRisks.map(risk => `
            <div class="flex items-start space-x-2">
              <span class="text-red-600 text-sm">🚨</span>
              <div class="flex-1">
                <span class="text-sm font-medium text-red-700">${risk.type.replace(/_/g, ' ')}</span>
                <p class="text-xs text-red-600">${risk.description}</p>
              </div>
            </div>
          `).join('')}
          ${mediumRisks.map(risk => `
            <div class="flex items-start space-x-2">
              <span class="text-yellow-600 text-sm">⚠️</span>
              <div class="flex-1">
                <span class="text-sm font-medium text-yellow-700">${risk.type.replace(/_/g, ' ')}</span>
                <p class="text-xs text-yellow-600">${risk.description}</p>
              </div>
            </div>
          `).join('')}
        </div>
      </div>
    `
  }

  // Render personalization suggestions
  renderPersonalizationSuggestions(suggestions) {
    if (!suggestions || suggestions.length === 0) return ''
    
    return `
      <div class="bg-purple-50 rounded-lg p-4">
        <h4 class="font-medium text-gray-900 mb-2">Personalization Suggestions</h4>
        <div class="space-y-1">
          ${suggestions.map(suggestion => `
            <div class="flex items-start space-x-2">
              <span class="text-purple-600 text-sm">💡</span>
              <span class="text-sm text-purple-700">${suggestion}</span>
            </div>
          `).join('')}
        </div>
      </div>
    `
  }

  // Generate customer report
  async generateReport(event) {
    if (event) event.preventDefault()
    
    try {
      console.log(`📄 Generating report for ${this.customerIdentifierValue}`)
      
      // Fetch latest customer data
      const response = await fetch(window.location.href + '.json')
      const data = await response.json()
      
      // Create comprehensive report
      const report = this.buildCustomerReport(data)
      
      // Download as JSON
      const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      
      const link = document.createElement('a')
      link.href = url
      link.download = `customer_emotional_profile_${this.customerIdentifierValue}_${new Date().toISOString().split('T')[0]}.json`
      link.click()
      
      URL.revokeObjectURL(url)
      
      this.showNotification('success', 'Customer report generated')
      
    } catch (error) {
      console.error("❌ Report generation failed:", error)
      this.showNotification('error', 'Failed to generate report')
    }
  }

  // Build comprehensive customer report
  buildCustomerReport(data) {
    return {
      report_type: 'customer_emotional_profile',
      generated_at: new Date().toISOString(),
      customer: data.customer,
      emotional_journey: data.emotional_journey,
      campaign_compatibility: data.campaign_compatibility,
      recommendations: data.recommendations,
      trajectory_analysis: data.trajectory_analysis,
      summary: {
        current_state: `${data.customer.current_emotion} (${data.customer.intensity})`,
        confidence_level: `${data.customer.confidence}%`,
        marketing_readiness: data.customer.receptive ? 'Receptive' : 'Not Receptive',
        total_recommendations: data.recommendations.length,
        high_priority_recommendations: data.recommendations.filter(r => r.priority === 'high').length
      }
    }
  }

  // Export customer data
  async exportData(event) {
    if (event) event.preventDefault()
    
    try {
      const response = await fetch(window.location.href + '.json')
      const data = await response.json()
      
      const exportData = {
        export_type: 'customer_emotional_data',
        exported_at: new Date().toISOString(),
        customer_identifier: this.customerIdentifierValue,
        data: data
      }
      
      const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      
      const link = document.createElement('a')
      link.href = url
      link.download = `customer_${this.customerIdentifierValue}_emotional_data_${new Date().toISOString().split('T')[0]}.json`
      link.click()
      
      URL.revokeObjectURL(url)
      
      this.showNotification('success', 'Customer data exported')
      
    } catch (error) {
      console.error("❌ Export failed:", error)
      this.showNotification('error', 'Failed to export customer data')
    }
  }

  // Setup form submission handlers
  setupFormHandlers() {
    const updateForm = document.getElementById('updateSignalsForm')
    if (updateForm) {
      updateForm.addEventListener('submit', this.handleUpdateSignals.bind(this))
    }
  }

  // Handle update signals form submission
  async handleUpdateSignals(event) {
    event.preventDefault()
    
    try {
      const formData = new FormData(event.target)
      
      const response = await fetch(event.target.action, {
        method: 'POST',
        body: formData,
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
          'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
        }
      })
      
      const result = await response.json()
      
      if (result.success) {
        this.hideUpdateModal()
        this.showNotification('success', result.message)
        
        // Refresh the page data after a short delay
        setTimeout(() => {
          this.refreshState()
        }, 2000)
      } else {
        this.showNotification('error', result.error || 'Update failed')
      }
      
    } catch (error) {
      console.error("❌ Signal update failed:", error)
      this.showNotification('error', 'Failed to update behavioral signals')
    }
  }

  // Utility methods for CSS classes and formatting

  getEmotionClass(emotion) {
    const emotionClasses = {
      'joy': 'text-yellow-600',
      'trust': 'text-blue-600',
      'fear': 'text-purple-600',
      'surprise': 'text-pink-600',
      'sadness': 'text-gray-600',
      'disgust': 'text-green-600',
      'anger': 'text-red-600',
      'anticipation': 'text-orange-600'
    }
    return emotionClasses[emotion] || 'text-gray-600'
  }

  getConfidenceClass(confidence) {
    if (confidence >= 80) return 'text-green-600 font-bold'
    if (confidence >= 60) return 'text-blue-600 font-medium'
    if (confidence >= 40) return 'text-yellow-600'
    return 'text-red-600'
  }

  getCompatibilityClass(score) {
    if (score >= 80) return 'text-green-600 font-bold'
    if (score >= 60) return 'text-blue-600 font-medium'
    if (score >= 40) return 'text-yellow-600'
    return 'text-red-600'
  }

  getEngagementClass(engagement) {
    if (engagement >= 80) return 'text-green-600'
    if (engagement >= 60) return 'text-blue-600'
    if (engagement >= 40) return 'text-yellow-600'
    return 'text-red-600'
  }

  formatOptimalTiming(timing) {
    if (!timing) return 'Unknown'
    
    const now = new Date()
    const timingDate = new Date(timing)
    const diffMs = timingDate - now
    const diffHours = Math.round(diffMs / (1000 * 60 * 60))
    
    if (diffHours <= 0) return 'Send now'
    if (diffHours === 1) return 'Send in 1 hour'
    if (diffHours < 24) return `Send in ${diffHours} hours`
    if (diffHours < 48) return 'Send tomorrow'
    return `Send in ${Math.round(diffHours / 24)} days`
  }

  // Show notification
  showNotification(type, message) {
    const notification = document.createElement('div')
    notification.className = `fixed top-4 right-4 px-4 py-3 rounded-lg text-sm font-medium z-50 transition-all duration-300 ${
      type === 'success' 
        ? 'bg-green-100 text-green-800 border border-green-200' 
        : 'bg-red-100 text-red-800 border border-red-200'
    }`
    
    notification.innerHTML = `
      <div class="flex items-center space-x-2">
        <span>${type === 'success' ? '✅' : '❌'}</span>
        <span>${message}</span>
      </div>
    `
    
    document.body.appendChild(notification)
    
    setTimeout(() => {
      notification.style.opacity = '0'
      notification.style.transform = 'translateY(-10px)'
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification)
        }
      }, 300)
    }, 5000)
  }
}
