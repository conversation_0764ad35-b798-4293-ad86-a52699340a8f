import { Controller } from "@hotwired/stimulus"

// Connects to data-controller="modal"
export default class extends Controller {
  static targets = ["container"]
  static values = { target: String }

  connect() {
    // Bind methods to preserve context
    this.closeOnEscape = this.closeOnEscape.bind(this)
    this.closeOnBackdrop = this.closeOnBackdrop.bind(this)
  }

  open(event) {
    event.preventDefault()
    
    // Get target modal ID from data attribute or value
    const targetId = event.currentTarget.dataset.modalTargetValue || this.targetValue
    const modal = targetId ? document.querySelector(targetId) : this.element
    
    if (modal) {
      modal.classList.remove('hidden')
      document.body.style.overflow = 'hidden'
      
      // Focus management for accessibility
      const firstFocusable = modal.querySelector('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])')
      if (firstFocusable) {
        firstFocusable.focus()
      }
    }
  }

  close(event) {
    if (event) {
      event.preventDefault()
    }
    
    this.element.classList.add('hidden')
    document.body.style.overflow = 'auto'
    
    // Return focus to the trigger element if available
    const trigger = document.querySelector('[data-modal-target-value="#' + this.element.id + '"]')
    if (trigger) {
      trigger.focus()
    }
  }

  closeOnEscape(event) {
    if (event.key === 'Escape' && !this.element.classList.contains('hidden')) {
      this.close()
    }
  }

  closeOnBackdrop(event) {
    // Only close if clicking directly on the backdrop (not on child elements)
    if (event.target === this.element) {
      this.close()
    }
  }

  // Prevent modal from closing when clicking inside the modal content
  preventClose(event) {
    event.stopPropagation()
  }
}
