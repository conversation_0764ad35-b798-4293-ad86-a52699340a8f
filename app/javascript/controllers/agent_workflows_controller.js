import { Controller } from "@hotwired/stimulus"

// Connects to data-controller="agent-workflows"
export default class extends Controller {
  static values = { 
    campaignId: Number,
    updateInterval: { type: Number, default: 5000 },
    maxRetries: { type: Number, default: 3 }
  }
  
  static targets = ["progressBar", "statusIndicator", "workflowCard"]

  connect() {
    console.log("AgentWorkflows controller connected for campaign:", this.campaignIdValue)
    this.retryCount = 0
    this.isPolling = false
    this.startPolling()
    
    // Listen for Turbo Stream updates
    this.element.addEventListener("turbo:before-stream-render", this.handleStreamUpdate.bind(this))
  }

  disconnect() {
    this.stopPolling()
    this.element.removeEventListener("turbo:before-stream-render", this.handleStreamUpdate.bind(this))
  }

  // Start polling for active workflows
  startPolling() {
    if (this.isPolling) return
    
    this.isPolling = true
    this.pollTimer = setInterval(() => {
      this.checkActiveWorkflows()
    }, this.updateIntervalValue)
  }

  // Stop polling
  stopPolling() {
    if (this.pollTimer) {
      clearInterval(this.pollTimer)
      this.pollTimer = null
    }
    this.isPolling = false
  }

  // Check for active workflows via API
  async checkActiveWorkflows() {
    try {
      const response = await fetch(`/api/v1/campaigns/${this.campaignIdValue}/progress`, {
        headers: {
          'Accept': 'application/json',
          'X-Requested-With': 'XMLHttpRequest'
        }
      })

      if (response.ok) {
        const data = await response.json()
        this.updateWorkflowProgress(data)
        this.retryCount = 0 // Reset retry count on success
      } else {
        this.handlePollingError(response)
      }
    } catch (error) {
      console.error("Error checking workflow progress:", error)
      this.handlePollingError(error)
    }
  }

  // Update workflow progress displays
  updateWorkflowProgress(data) {
    if (data.active_workflows && data.active_workflows.length > 0) {
      data.active_workflows.forEach(workflow => {
        this.updateWorkflowCard(workflow)
        this.updateProgressBar(workflow)
      })
    } else {
      // No active workflows, can reduce polling frequency
      this.adjustPollingFrequency(false)
    }
  }

  // Update individual workflow card
  updateWorkflowCard(workflow) {
    const cardElement = document.getElementById(`workflow-${workflow.id}`)
    if (!cardElement) return

    // Update progress percentage
    const progressBar = cardElement.querySelector('.progress-bar')
    if (progressBar) {
      progressBar.style.width = `${workflow.progress_percent}%`
      progressBar.setAttribute('aria-valuenow', workflow.progress_percent)
    }

    // Update current step
    const stepElement = cardElement.querySelector('.current-step')
    if (stepElement && workflow.current_step) {
      stepElement.textContent = workflow.current_step.replace(/_/g, ' ').toLowerCase()
        .replace(/\b\w/g, l => l.toUpperCase())
    }

    // Update status if changed
    this.updateWorkflowStatus(cardElement, workflow)
  }

  // Update progress bar elements
  updateProgressBar(workflow) {
    this.progressBarTargets.forEach(bar => {
      if (bar.dataset.workflowId == workflow.id) {
        const progressFill = bar.querySelector('.progress-fill')
        if (progressFill) {
          progressFill.style.width = `${workflow.progress_percent}%`
          progressFill.className = `progress-fill h-2 rounded-full transition-all duration-300 ${this.getProgressColorClass(workflow.progress_percent)}`
        }
      }
    })
  }

  // Update workflow status display
  updateWorkflowStatus(cardElement, workflow) {
    const statusBadge = cardElement.querySelector('.status-badge')
    if (statusBadge) {
      statusBadge.textContent = workflow.status.charAt(0).toUpperCase() + workflow.status.slice(1)
      statusBadge.className = `status-badge inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${this.getStatusBadgeClass(workflow.status)}`
    }

    // Show/hide action buttons based on status
    this.updateActionButtons(cardElement, workflow)
  }

  // Update action buttons visibility
  updateActionButtons(cardElement, workflow) {
    const cancelButton = cardElement.querySelector('.cancel-button')
    const retryButton = cardElement.querySelector('.retry-button')

    if (cancelButton) {
      cancelButton.style.display = (workflow.status === 'running' || workflow.status === 'pending') ? 'inline' : 'none'
    }

    if (retryButton) {
      retryButton.style.display = workflow.status === 'failed' ? 'inline' : 'none'
    }
  }

  // Handle Turbo Stream updates
  handleStreamUpdate(event) {
    console.log("Received Turbo Stream update:", event.detail)
    
    // Flash success message for completed workflows
    if (event.detail.action === 'replace' && event.detail.target.includes('workflow-progress')) {
      this.showUpdateNotification("Workflow updated", "success")
    }
  }

  // Handle content generation button clicks
  generateContent(event) {
    const button = event.currentTarget
    const agentType = button.dataset.agentType
    
    if (!agentType) {
      console.error("Agent type not specified")
      return
    }

    // Disable button and show loading state
    button.disabled = true
    button.innerHTML = `
      <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
      Generating...
    `

    // Re-enable button after timeout (in case of no response)
    setTimeout(() => {
      if (button.disabled) {
        button.disabled = false
        button.innerHTML = `✨ Generate`
      }
    }, 30000) // 30 seconds
  }

  // Adjust polling frequency based on activity
  adjustPollingFrequency(hasActiveWorkflows) {
    const newInterval = hasActiveWorkflows ? 3000 : 10000 // 3s vs 10s
    
    if (newInterval !== this.updateIntervalValue) {
      this.updateIntervalValue = newInterval
      this.stopPolling()
      this.startPolling()
    }
  }

  // Handle polling errors
  handlePollingError(error) {
    this.retryCount++
    
    if (this.retryCount >= this.maxRetriesValue) {
      console.warn("Max retries reached, stopping polling")
      this.stopPolling()
      this.showUpdateNotification("Connection lost. Please refresh the page.", "error")
    } else {
      console.warn(`Polling error, retry ${this.retryCount}/${this.maxRetriesValue}:`, error)
      // Exponential backoff
      setTimeout(() => {
        this.checkActiveWorkflows()
      }, Math.pow(2, this.retryCount) * 1000)
    }
  }

  // Show notification to user
  showUpdateNotification(message, type = "info") {
    // Create temporary notification element
    const notification = document.createElement('div')
    notification.className = `fixed top-4 right-4 z-50 max-w-sm p-4 rounded-md shadow-lg transition-all duration-300 ${this.getNotificationClass(type)}`
    notification.innerHTML = `
      <div class="flex items-center">
        <span class="flex-1">${message}</span>
        <button class="ml-2 text-current opacity-60 hover:opacity-100" onclick="this.parentElement.parentElement.remove()">×</button>
      </div>
    `
    
    document.body.appendChild(notification)
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
      if (notification.parentElement) {
        notification.remove()
      }
    }, 5000)
  }

  // Utility methods for CSS classes
  getProgressColorClass(progress) {
    if (progress < 25) return 'bg-red-500'
    if (progress < 50) return 'bg-yellow-500'
    if (progress < 75) return 'bg-blue-500'
    return 'bg-green-500'
  }

  getStatusBadgeClass(status) {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800'
      case 'failed': return 'bg-red-100 text-red-800'
      case 'running': return 'bg-blue-100 text-blue-800'
      case 'pending': return 'bg-yellow-100 text-yellow-800'
      case 'cancelled': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  getNotificationClass(type) {
    switch (type) {
      case 'success': return 'bg-green-500 text-white'
      case 'error': return 'bg-red-500 text-white'
      case 'warning': return 'bg-yellow-500 text-white'
      default: return 'bg-blue-500 text-white'
    }
  }

  // Event handlers for form submissions
  workflowFormSubmitted(event) {
    this.showUpdateNotification("Workflow started successfully!", "success")
    // Immediately start more frequent polling
    this.adjustPollingFrequency(true)
  }

  contentGenerationStarted(event) {
    const agentType = event.detail?.agentType || 'AI'
    this.showUpdateNotification(`${agentType} content generation started!`, "success")
  }
}
