import { Controller } from "@hotwired/stimulus"

// Connects to data-controller="audience-insights"
export default class extends Controller {
  static targets = [
    "modal",
    "loading", 
    "content",
    "error",
    "totalSubscribers",
    "activeSubscribers", 
    "avgEngagement",
    "ageChart",
    "locationList",
    "sendTimes",
    "contentPreferences",
    "recommendations"
  ]

  static values = {
    audience: String,
    apiEndpoint: String
  }

  connect() {
    console.log("Audience Insights controller connected")
    this.isOpen = false
    this.currentAudience = null
    
    // Bind keyboard events when modal is shown
    this.handleKeydown = this.handleKeydown.bind(this)
  }

  disconnect() {
    // Clean up any event listeners
    document.removeEventListener('keydown', this.handleKeydown)
  }

  // Public method to open the modal
  open(event) {
    const audience = event?.detail?.audience || this.audienceValue || "default"
    this.currentAudience = audience
    
    this.showModal()
    this.loadAudienceData(audience)
  }

  // Show the modal with proper accessibility
  showModal() {
    this.modalTarget.classList.remove("hidden")
    this.modalTarget.classList.add("flex")
    this.isOpen = true
    
    // Add keyboard event listener
    document.addEventListener('keydown', this.handleKeydown)
    
    // Focus management for accessibility
    this.modalTarget.focus()
    
    // Prevent body scroll
    document.body.style.overflow = 'hidden'
    
    // Announce to screen readers
    this.announceToScreenReader("Audience insights modal opened")
  }

  // Close the modal
  close() {
    this.modalTarget.classList.add("hidden")
    this.modalTarget.classList.remove("flex")
    this.isOpen = false
    
    // Remove keyboard event listener
    document.removeEventListener('keydown', this.handleKeydown)
    
    // Restore body scroll
    document.body.style.overflow = 'auto'
    
    // Reset modal state
    this.resetModalState()
    
    // Announce to screen readers
    this.announceToScreenReader("Audience insights modal closed")
  }

  // Handle backdrop clicks
  closeOnBackdrop(event) {
    if (event.target === this.modalTarget) {
      this.close()
    }
  }

  // Prevent event propagation for modal content clicks
  stopPropagation(event) {
    event.stopPropagation()
  }

  // Handle keyboard navigation
  handleKeydown(event) {
    if (!this.isOpen) return
    
    switch (event.key) {
      case 'Escape':
        this.close()
        break
      case 'Tab':
        this.handleTabNavigation(event)
        break
    }
  }

  // Handle tab navigation within modal
  handleTabNavigation(event) {
    const focusableElements = this.modalTarget.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    )
    
    const firstElement = focusableElements[0]
    const lastElement = focusableElements[focusableElements.length - 1]
    
    if (event.shiftKey && document.activeElement === firstElement) {
      event.preventDefault()
      lastElement.focus()
    } else if (!event.shiftKey && document.activeElement === lastElement) {
      event.preventDefault()
      firstElement.focus()
    }
  }

  // Load audience data from API
  async loadAudienceData(audience = "default") {
    this.showLoadingState()
    
    try {
      const endpoint = this.apiEndpointValue || `/api/audience/${audience}/insights`
      const response = await fetch(endpoint, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'X-Requested-With': 'XMLHttpRequest',
          'X-CSRF-Token': this.getCSRFToken()
        }
      })
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
      
      const data = await response.json()
      this.populateAudienceData(data)
      this.showContentState()
      
    } catch (error) {
      console.error('Failed to load audience insights:', error)
      this.showErrorState()
    }
  }

  // Populate the modal with audience data
  populateAudienceData(data) {
    // Overview metrics
    if (this.hasTotalSubscribersTarget) {
      this.totalSubscribersTarget.textContent = this.formatNumber(data.totalSubscribers || 0)
    }
    if (this.hasActiveSubscribersTarget) {
      this.activeSubscribersTarget.textContent = this.formatNumber(data.activeSubscribers || 0)
    }
    if (this.hasAvgEngagementTarget) {
      this.avgEngagementTarget.textContent = this.formatPercentage(data.avgEngagement || 0)
    }

    // Age distribution
    this.populateAgeChart(data.demographics?.age || {})
    
    // Location data
    this.populateLocationList(data.demographics?.locations || [])
    
    // Send times
    this.populateSendTimes(data.engagement?.bestSendTimes || [])
    
    // Content preferences
    this.populateContentPreferences(data.engagement?.contentPreferences || {})
    
    // AI recommendations
    this.populateRecommendations(data.recommendations || [])
  }

  // Populate age distribution chart
  populateAgeChart(ageData) {
    if (!this.hasAgeChartTarget) return
    
    const ageRanges = ['18-24', '25-34', '35-44', '45+']
    const chartContainer = this.ageChartTarget
    
    ageRanges.forEach((range, index) => {
      const percentage = ageData[range] || 0
      const barElement = chartContainer.children[index]
      
      if (barElement) {
        const progressBar = barElement.querySelector('.bg-indigo-500')
        const percentageText = barElement.querySelector('.text-right')
        
        if (progressBar) {
          progressBar.style.width = `${percentage}%`
        }
        if (percentageText) {
          percentageText.textContent = `${percentage}%`
        }
      }
    })
  }

  // Populate location list
  populateLocationList(locations) {
    if (!this.hasLocationListTarget) return
    
    const html = locations.map(location => `
      <div class="flex justify-between items-center p-2 bg-white rounded-lg">
        <span class="text-sm text-slate-600">${location.name}</span>
        <span class="text-sm font-medium text-slate-700">${location.percentage}%</span>
      </div>
    `).join('')
    
    this.locationListTarget.innerHTML = html || '<div class="text-sm text-slate-500 p-2">No location data available</div>'
  }

  // Populate send times
  populateSendTimes(sendTimes) {
    if (!this.hasSendTimesTarget || !sendTimes.length) return
    
    const html = sendTimes.map(time => `
      <div class="flex justify-between items-center p-3 bg-white rounded-lg shadow-sm">
        <div>
          <span class="text-sm font-medium text-slate-700">${time.day}, ${time.time}</span>
          <p class="text-xs text-slate-500">${time.metric}</p>
        </div>
        <span class="text-sm font-semibold text-${time.color}-600">${time.value}</span>
      </div>
    `).join('')
    
    this.sendTimesTarget.innerHTML = html
  }

  // Populate content preferences
  populateContentPreferences(preferences) {
    if (!this.hasContentPreferencesTarget) return
    
    const container = this.contentPreferencesTarget
    const types = ['Educational', 'Promotional', 'Newsletter']
    
    types.forEach((type, index) => {
      const percentage = preferences[type.toLowerCase()] || 0
      const barElement = container.children[index]
      
      if (barElement) {
        const progressBar = barElement.querySelector('.h-2:not(.bg-slate-200)')
        const percentageText = barElement.querySelector('.font-medium')
        
        if (progressBar) {
          progressBar.style.width = `${percentage}%`
        }
        if (percentageText) {
          percentageText.textContent = `${percentage}%`
        }
      }
    })
  }

  // Populate AI recommendations
  populateRecommendations(recommendations) {
    if (!this.hasRecommendationsTarget || !recommendations.length) return
    
    const html = recommendations.map(rec => `
      <div class="flex items-start space-x-3 p-4 bg-white rounded-lg shadow-sm">
        <svg class="h-5 w-5 text-${rec.type === 'info' ? 'blue' : 'green'}-500 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          ${rec.type === 'info' 
            ? '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>'
            : '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>'
          }
        </svg>
        <div>
          <p class="text-sm font-medium text-slate-800">${rec.title}</p>
          <p class="text-sm text-slate-600 mt-1">${rec.description}</p>
        </div>
      </div>
    `).join('')
    
    this.recommendationsTarget.innerHTML = html
  }

  // State management methods
  showLoadingState() {
    this.hideAllStates()
    if (this.hasLoadingTarget) {
      this.loadingTarget.classList.remove("hidden")
    }
  }

  showContentState() {
    this.hideAllStates()
    if (this.hasContentTarget) {
      this.contentTarget.classList.remove("hidden")
    }
  }

  showErrorState() {
    this.hideAllStates()
    if (this.hasErrorTarget) {
      this.errorTarget.classList.remove("hidden")
    }
  }

  hideAllStates() {
    [this.loadingTarget, this.contentTarget, this.errorTarget].forEach(target => {
      if (target) {
        target.classList.add("hidden")
      }
    })
  }

  resetModalState() {
    this.currentAudience = null
    this.showContentState()
  }

  // Retry loading data
  retry() {
    if (this.currentAudience) {
      this.loadAudienceData(this.currentAudience)
    }
  }

  // Export audience data
  async exportData() {
    if (!this.currentAudience) return
    
    try {
      const endpoint = this.apiEndpointValue?.replace('/insights', '/export') || `/api/audience/${this.currentAudience}/export`
      const response = await fetch(endpoint, {
        method: 'GET',
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
          'X-CSRF-Token': this.getCSRFToken()
        }
      })
      
      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `audience-insights-${this.currentAudience}-${new Date().toISOString().split('T')[0]}.pdf`
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        window.URL.revokeObjectURL(url)
        
        this.announceToScreenReader("Audience insights report downloaded")
      } else {
        throw new Error('Export failed')
      }
    } catch (error) {
      console.error('Failed to export audience data:', error)
      this.announceToScreenReader("Failed to export audience insights")
    }
  }

  // Utility methods
  formatNumber(num) {
    return new Intl.NumberFormat().format(num)
  }

  formatPercentage(num) {
    return `${Math.round(num * 100) / 100}%`
  }

  getCSRFToken() {
    const token = document.querySelector('meta[name="csrf-token"]')
    return token ? token.getAttribute('content') : ''
  }

  announceToScreenReader(message) {
    const announcement = document.createElement('div')
    announcement.setAttribute('aria-live', 'polite')
    announcement.setAttribute('aria-atomic', 'true')
    announcement.className = 'sr-only'
    announcement.textContent = message
    
    document.body.appendChild(announcement)
    
    setTimeout(() => {
      document.body.removeChild(announcement)
    }, 1000)
  }
}
