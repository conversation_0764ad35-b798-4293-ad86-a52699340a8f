import { Controller } from "@hotwired/stimulus"

// Content Generation Controller
// 
// This controller enhances the AI content generation experience by providing:
// 1. Real-time feedback during generation
// 2. Version history with undo/redo functionality
// 3. Enhanced error handling with recovery options
// 4. Progress indicators with status messages
// 5. Keyboard shortcuts for common operations
//
// Usage example:
// <div data-controller="content-generation" 
//      data-content-generation-campaign-id-value="123"
//      data-content-generation-endpoint-value="/email_content/generate">
//   <button data-action="content-generation#generate">Generate Content</button>
//   <div data-content-generation-target="progress" class="hidden">...</div>
//   <div data-content-generation-target="output"></div>
//   <div data-content-generation-target="error" class="hidden"></div>
// </div>

export default class extends Controller {
  static targets = [
    "output",      // Where the generated content will be displayed
    "progress",    // Progress indicator container
    "status",      // Text status updates
    "error",       // Error message container
    "versionList", // List of content versions
    "form"         // Form with generation parameters
  ]
  
  static values = {
    campaignId: Number,           // ID of the current campaign
    endpoint: String,             // API endpoint for content generation
    checkInterval: { type: Number, default: 1000 },  // Polling interval for status updates
    maxRetries: { type: Number, default: 3 },        // Maximum number of automatic retries
    generationInProgress: Boolean, // Whether generation is currently in progress
    versions: Array               // History of content versions
  }
  
  static classes = [
    "hidden",     // Used to hide elements
    "loading",    // Applied to elements during loading
    "error",      // Applied to elements when error occurs
    "success",    // Applied to elements on successful completion
    "active"      // Applied to active version
  ]
  
  connect() {
    this.versionsValue = []
    this.generationInProgressValue = false
    this.retryCount = 0
    this.pollingTimeout = null
    this.setupKeyboardShortcuts()
    this.updateVersionsList()
    
    // Check if we need to restore state from localStorage
    this.restoreState()
    
    // Initialize TipTap editor if it's available
    this.initializeEditor()
  }
  
  disconnect() {
    this.clearPollingTimeout()
    this.saveState()
    
    // Clean up the editor if it exists
    if (this.editor) {
      this.editor.destroy()
    }
  }
  
  // Initialize the rich text editor if TipTap is available
  initializeEditor() {
    if (window.Editor) {
      const outputTarget = this.hasOutputTarget ? this.outputTarget : null
      if (outputTarget) {
        import('./editor_extension').then(module => {
          const EditorExtension = module.default
          this.editor = new EditorExtension(outputTarget, {
            onUpdate: html => this.handleContentUpdate(html),
            placeholder: 'Generated content will appear here...'
          })
        }).catch(error => {
          console.error('Could not load editor extension:', error)
        })
      }
    }
  }
  
  // Main method to generate content
  generate(event) {
    if (event) {
      event.preventDefault()
    }
    
    if (this.generationInProgressValue) {
      return // Prevent multiple simultaneous generations
    }
    
    this.startGeneration()
    this.fetchContent()
  }
  
  // Handle retry after error
  retry() {
    if (this.retryCount >= this.maxRetriesValue) {
      this.showError('Maximum retry attempts reached. Please try again later.')
      return
    }
    
    this.retryCount++
    this.startGeneration()
    this.fetchContent()
  }
  
  // Setup the UI for starting a generation
  startGeneration() {
    this.generationInProgressValue = true
    this.clearError()
    
    if (this.hasProgressTarget) {
      this.progressTarget.classList.remove(this.hiddenClass)
      this.progressTarget.classList.add(this.loadingClass)
    }
    
    if (this.hasStatusTarget) {
      this.statusTarget.textContent = 'Initializing content generation...'
    }
    
    this.updateGenerateButtonState()
  }
  
  // Fetch content from the API
  fetchContent() {
    const formData = this.getFormData()
    
    fetch(this.endpointValue, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': this.getCSRFToken()
      },
      body: JSON.stringify(formData)
    })
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`)
      }
      return response.json()
    })
    .then(data => {
      this.handleInitialResponse(data)
    })
    .catch(error => {
      this.handleFetchError(error)
    })
  }
  
  // Handle the initial response, which might be a job ID for polling
  handleInitialResponse(data) {
    if (data.status === 'processing' || data.status === 'queued') {
      // Start polling for results
      this.jobId = data.job_id
      this.updateStatus('Content generation in progress...')
      this.startPolling()
    } else if (data.status === 'success') {
      // Immediate result
      this.handleSuccessfulGeneration(data)
    } else if (data.status === 'error') {
      // Error occurred
      this.handleGenerationError(data)
    } else if (data.status === 'warning' && data.requires_confirmation) {
      // Handle cases that require user confirmation
      this.handleConfirmationRequired(data)
    } else {
      // Unknown status
      this.showError('Received unknown status from the server.')
    }
  }
  
  // Start polling for results
  startPolling() {
    this.clearPollingTimeout()
    
    this.pollingTimeout = setTimeout(() => {
      this.pollForResults()
    }, this.checkIntervalValue)
  }
  
  // Poll for results using the job ID
  pollForResults() {
    if (!this.jobId || !this.generationInProgressValue) {
      return
    }
    
    fetch(`${this.endpointValue}/status?job_id=${this.jobId}`, {
      headers: {
        'X-CSRF-Token': this.getCSRFToken()
      }
    })
    .then(response => response.json())
    .then(data => {
      this.handlePollingResponse(data)
    })
    .catch(error => {
      console.error('Polling error:', error)
      this.startPolling() // Continue polling despite errors
    })
  }
  
  // Handle the polling response
  handlePollingResponse(data) {
    if (data.status === 'processing' || data.status === 'queued') {
      // Still processing
      this.updateProgress(data.progress || 0)
      this.updateStatus(data.message || 'Processing...')
      this.startPolling()
    } else if (data.status === 'success') {
      // Generation complete
      this.handleSuccessfulGeneration(data)
    } else if (data.status === 'error') {
      // Error occurred
      this.handleGenerationError(data)
    } else {
      // Unknown status
      this.showError('Received unknown status from the server.')
    }
  }
  
  // Handle successful generation
  handleSuccessfulGeneration(data) {
    this.generationInProgressValue = false
    this.retryCount = 0
    
    if (this.hasProgressTarget) {
      this.progressTarget.classList.add(this.hiddenClass)
      this.progressTarget.classList.remove(this.loadingClass)
    }
    
    if (this.hasOutputTarget) {
      // Update the output with the generated content
      if (this.editor) {
        this.editor.setContent(data.campaign_data.content)
      } else {
        this.outputTarget.innerHTML = data.campaign_data.content
      }
      
      // Add other data as data attributes for later reference
      this.outputTarget.dataset.subjectLine = data.campaign_data.subject_line
      this.outputTarget.dataset.cta = data.campaign_data.cta
      this.outputTarget.dataset.tone = data.campaign_data.tone
      this.outputTarget.dataset.modelUsed = data.model_used
      this.outputTarget.dataset.source = data.source
    }
    
    // Add to version history
    this.addVersion({
      content: data.campaign_data.content,
      subject_line: data.campaign_data.subject_line,
      cta: data.campaign_data.cta,
      tone: data.campaign_data.tone,
      timestamp: new Date().toISOString()
    })
    
    this.updateGenerateButtonState()
    this.saveState()
    
    // Dispatch event for other components
    this.element.dispatchEvent(new CustomEvent('content-generation:complete', {
      detail: data,
      bubbles: true
    }))
  }
  
  // Handle generation error
  handleGenerationError(data) {
    this.generationInProgressValue = false
    
    if (this.hasProgressTarget) {
      this.progressTarget.classList.add(this.hiddenClass)
    }
    
    this.showError(data.message || 'An error occurred during content generation.')
    
    if (data.recovery_options && data.recovery_options.includes('retry')) {
      this.showRetryOption()
    }
    
    this.updateGenerateButtonState()
    
    // Dispatch event for other components
    this.element.dispatchEvent(new CustomEvent('content-generation:error', {
      detail: data,
      bubbles: true
    }))
  }
  
  // Handle cases that require user confirmation
  handleConfirmationRequired(data) {
    this.generationInProgressValue = false
    
    if (this.hasProgressTarget) {
      this.progressTarget.classList.add(this.hiddenClass)
    }
    
    // Create a confirmation dialog
    const confirmMessage = data.message || 'This operation requires confirmation.'
    
    if (confirm(`${confirmMessage} Do you want to proceed anyway?`)) {
      // User confirmed, proceed with force parameter
      this.startGeneration()
      
      const formData = this.getFormData()
      formData.force = true
      
      fetch(this.endpointValue, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': this.getCSRFToken()
        },
        body: JSON.stringify(formData)
      })
      .then(response => response.json())
      .then(responseData => {
        this.handleInitialResponse(responseData)
      })
      .catch(error => {
        this.handleFetchError(error)
      })
    } else {
      // User cancelled
      this.updateGenerateButtonState()
    }
  }
  
  // Handle fetch error
  handleFetchError(error) {
    console.error('Fetch error:', error)
    this.generationInProgressValue = false
    
    if (this.hasProgressTarget) {
      this.progressTarget.classList.add(this.hiddenClass)
    }
    
    this.showError('Network error. Please check your connection and try again.')
    this.updateGenerateButtonState()
  }
  
  // Show error message
  showError(message) {
    if (this.hasErrorTarget) {
      this.errorTarget.textContent = message
      this.errorTarget.classList.remove(this.hiddenClass)
      this.errorTarget.classList.add(this.errorClass)
    }
  }
  
  // Clear error message
  clearError() {
    if (this.hasErrorTarget) {
      this.errorTarget.textContent = ''
      this.errorTarget.classList.add(this.hiddenClass)
      this.errorTarget.classList.remove(this.errorClass)
    }
  }
  
  // Show retry option
  showRetryOption() {
    if (this.hasErrorTarget) {
      const retryButton = document.createElement('button')
      retryButton.textContent = 'Retry'
      retryButton.classList.add('retry-button')
      retryButton.addEventListener('click', () => this.retry())
      this.errorTarget.appendChild(retryButton)
    }
  }
  
  // Update status message
  updateStatus(message) {
    if (this.hasStatusTarget) {
      this.statusTarget.textContent = message
    }
  }
  
  // Update progress indicator
  updateProgress(percent) {
    if (this.hasProgressTarget) {
      const progressBar = this.progressTarget.querySelector('.progress-bar')
      if (progressBar) {
        progressBar.style.width = `${percent}%`
      }
    }
  }
  
  // Update the state of the generate button
  updateGenerateButtonState() {
    const generateButtons = this.element.querySelectorAll('[data-action*="content-generation#generate"]')
    generateButtons.forEach(button => {
      if (this.generationInProgressValue) {
        button.disabled = true
        button.textContent = 'Generating...'
      } else {
        button.disabled = false
        button.textContent = 'Generate Content'
      }
    })
  }
  
  // Get form data for the API request
  getFormData() {
    const formData = {
      campaign_id: this.campaignIdValue,
      use_real_response: true  // Always request real responses, not mock ones
      // No hardcoded model - will use best available model based on task
    }
    
    // If we have a form target, add its data
    if (this.hasFormTarget) {
      const formElements = this.formTarget.elements
      for (let i = 0; i < formElements.length; i++) {
        const element = formElements[i]
        if (element.name && element.name !== '') {
          if (element.type === 'checkbox' || element.type === 'radio') {
            if (element.checked) {
              formData[element.name] = element.value
            }
          } else {
            formData[element.name] = element.value
          }
        }
      }
    }
    
    return formData
  }
  
  // Get CSRF token from meta tag
  getCSRFToken() {
    const metaTag = document.querySelector('meta[name="csrf-token"]')
    return metaTag ? metaTag.content : ''
  }
  
  // Clear the polling timeout
  clearPollingTimeout() {
    if (this.pollingTimeout) {
      clearTimeout(this.pollingTimeout)
      this.pollingTimeout = null
    }
  }
  
  // Cancel the current generation
  cancel() {
    this.clearPollingTimeout()
    this.generationInProgressValue = false
    
    if (this.hasProgressTarget) {
      this.progressTarget.classList.add(this.hiddenClass)
    }
    
    this.updateStatus('Generation cancelled')
    this.updateGenerateButtonState()
    
    // If we have a job ID, send a cancellation request to the server
    if (this.jobId) {
      fetch(`${this.endpointValue}/cancel?job_id=${this.jobId}`, {
        method: 'POST',
        headers: {
          'X-CSRF-Token': this.getCSRFToken()
        }
      }).catch(error => {
        console.error('Error cancelling job:', error)
      })
    }
  }
  
  // Add a version to the version history
  addVersion(versionData) {
    // Add timestamp if not present
    if (!versionData.timestamp) {
      versionData.timestamp = new Date().toISOString()
    }
    
    // Add to versions array
    this.versionsValue = [...this.versionsValue, versionData]
    
    // Update the versions list
    this.updateVersionsList()
  }
  
  // Update the versions list in the UI
  updateVersionsList() {
    if (!this.hasVersionListTarget) return
    
    // Clear the current list
    this.versionListTarget.innerHTML = ''
    
    // Add each version to the list
    this.versionsValue.forEach((version, index) => {
      const versionItem = document.createElement('div')
      versionItem.classList.add('version-item')
      versionItem.dataset.index = index
      
      const date = new Date(version.timestamp)
      const timeString = date.toLocaleTimeString()
      
      versionItem.innerHTML = `
        <span class="version-time">${timeString}</span>
        <span class="version-subject">${version.subject_line}</span>
      `
      
      versionItem.addEventListener('click', () => this.loadVersion(index))
      
      if (index === this.versionsValue.length - 1) {
        versionItem.classList.add(this.activeClass)
      }
      
      this.versionListTarget.appendChild(versionItem)
    })
  }
  
  // Load a specific version
  loadVersion(index) {
    const version = this.versionsValue[index]
    if (!version) return
    
    if (this.hasOutputTarget) {
      if (this.editor) {
        this.editor.setContent(version.content)
      } else {
        this.outputTarget.innerHTML = version.content
      }
      
      this.outputTarget.dataset.subjectLine = version.subject_line
      this.outputTarget.dataset.cta = version.cta
      this.outputTarget.dataset.tone = version.tone
    }
    
    // Update active state in the versions list
    if (this.hasVersionListTarget) {
      const versionItems = this.versionListTarget.querySelectorAll('.version-item')
      versionItems.forEach(item => {
        if (parseInt(item.dataset.index) === index) {
          item.classList.add(this.activeClass)
        } else {
          item.classList.remove(this.activeClass)
        }
      })
    }
    
    // Dispatch event
    this.element.dispatchEvent(new CustomEvent('content-generation:version-loaded', {
      detail: { version, index },
      bubbles: true
    }))
  }
  
  // Handle content updates from the editor
  handleContentUpdate(html) {
    // This is called when the editor content changes
    // We could use this to save drafts or update the UI
    this.currentContent = html
  }
  
  // Save the current state to localStorage
  saveState() {
    if (!this.campaignIdValue) return
    
    const stateKey = `content-generation-state-${this.campaignIdValue}`
    const state = {
      versions: this.versionsValue,
      currentContent: this.currentContent || (this.hasOutputTarget ? this.outputTarget.innerHTML : '')
    }
    
    try {
      localStorage.setItem(stateKey, JSON.stringify(state))
    } catch (e) {
      console.error('Could not save state to localStorage:', e)
    }
  }
  
  // Restore state from localStorage
  restoreState() {
    if (!this.campaignIdValue) return
    
    const stateKey = `content-generation-state-${this.campaignIdValue}`
    
    try {
      const stateJson = localStorage.getItem(stateKey)
      if (stateJson) {
        const state = JSON.parse(stateJson)
        
        if (state.versions && Array.isArray(state.versions)) {
          this.versionsValue = state.versions
          this.updateVersionsList()
        }
        
        if (state.currentContent && this.hasOutputTarget) {
          if (this.editor) {
            // Wait for editor to initialize
            setTimeout(() => {
              this.editor.setContent(state.currentContent)
            }, 100)
          } else {
            this.outputTarget.innerHTML = state.currentContent
          }
        }
      }
    } catch (e) {
      console.error('Could not restore state from localStorage:', e)
    }
  }
  
  // Setup keyboard shortcuts
  setupKeyboardShortcuts() {
    document.addEventListener('keydown', event => {
      // Only process if this controller is the most specific one
      if (!this.element.contains(event.target) && !event.target.matches('body')) {
        return
      }
      
      // Check for keyboard shortcuts
      if (event.ctrlKey || event.metaKey) {
        if (event.key === 'g') {
          // Ctrl+G / Cmd+G: Generate content
          event.preventDefault()
          this.generate()
        } else if (event.key === 'z' && event.shiftKey) {
          // Ctrl+Shift+Z / Cmd+Shift+Z: Redo (load newer version)
          event.preventDefault()
          this.loadNewerVersion()
        } else if (event.key === 'z') {
          // Ctrl+Z / Cmd+Z: Undo (load older version)
          event.preventDefault()
          this.loadOlderVersion()
        }
      } else if (event.key === 'Escape') {
        // Escape: Cancel generation
        if (this.generationInProgressValue) {
          event.preventDefault()
          this.cancel()
        }
      }
    })
  }
  
  // Load the next older version (for undo functionality)
  loadOlderVersion() {
    if (!this.hasVersionListTarget) return
    
    const activeItem = this.versionListTarget.querySelector(`.${this.activeClass}`)
    if (!activeItem) return
    
    const currentIndex = parseInt(activeItem.dataset.index)
    if (currentIndex > 0) {
      this.loadVersion(currentIndex - 1)
    }
  }
  
  // Load the next newer version (for redo functionality)
  loadNewerVersion() {
    if (!this.hasVersionListTarget) return
    
    const activeItem = this.versionListTarget.querySelector(`.${this.activeClass}`)
    if (!activeItem) return
    
    const currentIndex = parseInt(activeItem.dataset.index)
    if (currentIndex < this.versionsValue.length - 1) {
      this.loadVersion(currentIndex + 1)
    }
  }
}
