import { Controller } from "@hotwired/stimulus"

// Workflow monitoring controller for AI agent orchestration
export default class extends Controller {
  static targets = ["workflowItem", "progressBar", "statusIndicator", "metricsPanel"]
  static values = { 
    updateInterval: { type: Number, default: 5000 },
    tenantId: Number,
    autoRefresh: { type: Boolean, default: true }
  }

  connect() {
    this.initializeMonitoring()
    this.setupEventListeners()
    this.startPeriodicUpdates()
    
    console.log("Workflow monitor connected for tenant:", this.tenantIdValue)
  }

  disconnect() {
    this.stopPeriodicUpdates()
    console.log("Workflow monitor disconnected")
  }

  // Initialize monitoring features
  initializeMonitoring() {
    this.animateInitialLoad()
    this.setupProgressBarAnimations()
    this.initializeStatusIndicators()
  }

  // Setup event listeners for real-time updates
  setupEventListeners() {
    // Listen for Turbo Stream updates
    document.addEventListener("turbo:before-stream-render", this.handleStreamUpdate.bind(this))
    
    // Listen for workflow status changes
    document.addEventListener("workflow:status-changed", this.handleStatusChange.bind(this))
    
    // Listen for workflow progress updates
    document.addEventListener("workflow:progress-updated", this.handleProgressUpdate.bind(this))
  }

  // Start periodic updates for workflow monitoring
  startPeriodicUpdates() {
    if (!this.autoRefreshValue) return

    this.updateTimer = setInterval(() => {
      this.refreshWorkflowStatuses()
    }, this.updateIntervalValue)
  }

  // Stop periodic updates
  stopPeriodicUpdates() {
    if (this.updateTimer) {
      clearInterval(this.updateTimer)
      this.updateTimer = null
    }
  }

  // Animate initial load of workflow items
  animateInitialLoad() {
    this.workflowItemTargets.forEach((item, index) => {
      item.style.opacity = '0'
      item.style.transform = 'translateX(-20px)'
      
      setTimeout(() => {
        item.style.transition = 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)'
        item.style.opacity = '1'
        item.style.transform = 'translateX(0)'
      }, index * 100)
    })
  }

  // Setup progress bar animations
  setupProgressBarAnimations() {
    this.progressBarTargets.forEach(bar => {
      const progress = bar.dataset.progress || '0'
      const status = bar.dataset.status || 'pending'
      
      bar.style.width = '0%'
      
      setTimeout(() => {
        bar.style.transition = 'width 1s cubic-bezier(0.4, 0, 0.2, 1)'
        bar.style.width = progress + '%'
        
        // Add pulsing animation for running workflows
        if (status === 'running') {
          bar.classList.add('animate-pulse')
        }
      }, 300)
    })
  }

  // Initialize status indicators with animations
  initializeStatusIndicators() {
    this.statusIndicatorTargets.forEach(indicator => {
      const status = indicator.dataset.status || 'pending'
      
      switch (status) {
        case 'running':
          indicator.classList.add('animate-pulse', 'bg-blue-500')
          break
        case 'completed':
          indicator.classList.add('bg-green-500')
          this.addCheckmarkAnimation(indicator)
          break
        case 'failed':
          indicator.classList.add('bg-red-500', 'animate-bounce')
          break
        case 'pending':
          indicator.classList.add('bg-yellow-500', 'animate-pulse')
          break
        default:
          indicator.classList.add('bg-gray-400')
      }
    })
  }

  // Add checkmark animation for completed workflows
  addCheckmarkAnimation(indicator) {
    const checkmark = document.createElement('svg')
    checkmark.className = 'w-2 h-2 text-white'
    checkmark.innerHTML = `
      <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
            d="M5 13l4 4L19 7" fill="none" />
    `
    
    indicator.appendChild(checkmark)
    
    // Animate the checkmark
    setTimeout(() => {
      checkmark.style.opacity = '1'
      checkmark.style.transform = 'scale(1)'
    }, 200)
  }

  // Handle Turbo Stream updates
  handleStreamUpdate(event) {
    const target = event.target
    
    if (target.id && target.id.startsWith('workflow-item-')) {
      this.handleWorkflowItemUpdate(target)
    }
  }

  // Handle workflow item updates
  handleWorkflowItemUpdate(element) {
    // Add update animation
    element.style.transition = 'background-color 0.3s ease'
    element.style.backgroundColor = '#fef3c7' // yellow-100
    
    setTimeout(() => {
      element.style.backgroundColor = ''
    }, 1000)
    
    // Re-initialize animations for updated element
    this.reinitializeElement(element)
  }

  // Reinitialize animations for updated elements
  reinitializeElement(element) {
    const progressBar = element.querySelector('[data-workflow-monitor-target="progressBar"]')
    const statusIndicator = element.querySelector('[data-workflow-monitor-target="statusIndicator"]')
    
    if (progressBar) {
      this.animateProgressBar(progressBar)
    }
    
    if (statusIndicator) {
      this.updateStatusIndicator(statusIndicator)
    }
  }

  // Animate individual progress bar
  animateProgressBar(bar) {
    const progress = bar.dataset.progress || '0'
    const currentWidth = parseInt(bar.style.width) || 0
    
    if (parseInt(progress) > currentWidth) {
      bar.style.transition = 'width 0.5s cubic-bezier(0.4, 0, 0.2, 1)'
      bar.style.width = progress + '%'
    }
  }

  // Update status indicator
  updateStatusIndicator(indicator) {
    const status = indicator.dataset.status || 'pending'
    
    // Remove existing status classes
    indicator.className = indicator.className.replace(/bg-\w+-\d+|animate-\w+/g, '')
    
    // Add new status classes
    this.initializeStatusIndicators()
  }

  // Handle status change events
  handleStatusChange(event) {
    const { workflowId, status, progress } = event.detail
    const workflowElement = document.getElementById(`workflow-item-${workflowId}`)
    
    if (workflowElement) {
      this.updateWorkflowElement(workflowElement, status, progress)
    }
  }

  // Handle progress update events
  handleProgressUpdate(event) {
    const { workflowId, progress, currentStep } = event.detail
    const workflowElement = document.getElementById(`workflow-item-${workflowId}`)
    
    if (workflowElement) {
      this.updateWorkflowProgress(workflowElement, progress, currentStep)
    }
  }

  // Update workflow element with new status
  updateWorkflowElement(element, status, progress) {
    const statusIndicator = element.querySelector('[data-workflow-monitor-target="statusIndicator"]')
    const progressBar = element.querySelector('[data-workflow-monitor-target="progressBar"]')
    
    if (statusIndicator) {
      statusIndicator.dataset.status = status
      this.updateStatusIndicator(statusIndicator)
    }
    
    if (progressBar && progress !== undefined) {
      progressBar.dataset.progress = progress
      this.animateProgressBar(progressBar)
    }
    
    // Add visual feedback
    this.addUpdateFeedback(element)
  }

  // Update workflow progress
  updateWorkflowProgress(element, progress, currentStep) {
    const progressBar = element.querySelector('[data-workflow-monitor-target="progressBar"]')
    const stepText = element.querySelector('.current-step-text')
    
    if (progressBar) {
      progressBar.dataset.progress = progress
      this.animateProgressBar(progressBar)
    }
    
    if (stepText && currentStep) {
      stepText.textContent = currentStep.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
    }
    
    this.addUpdateFeedback(element)
  }

  // Add visual feedback for updates
  addUpdateFeedback(element) {
    element.style.boxShadow = '0 4px 12px rgba(59, 130, 246, 0.3)'
    
    setTimeout(() => {
      element.style.transition = 'box-shadow 0.5s ease'
      element.style.boxShadow = ''
    }, 1000)
  }

  // Refresh workflow statuses via AJAX
  async refreshWorkflowStatuses() {
    if (!this.tenantIdValue) return

    try {
      const response = await fetch(`/api/workflows/status?tenant_id=${this.tenantIdValue}`, {
        headers: {
          'Accept': 'application/json',
          'X-Requested-With': 'XMLHttpRequest'
        }
      })
      
      if (response.ok) {
        const data = await response.json()
        this.processStatusUpdates(data.workflows)
      }
    } catch (error) {
      console.error('Failed to refresh workflow statuses:', error)
    }
  }

  // Process status updates from API
  processStatusUpdates(workflows) {
    workflows.forEach(workflow => {
      const element = document.getElementById(`workflow-item-${workflow.id}`)
      if (element) {
        this.updateWorkflowElement(element, workflow.status, workflow.progress_percent)
      }
    })
  }

  // Manual refresh action
  refresh() {
    this.addRefreshIndicator()
    this.refreshWorkflowStatuses()
  }

  // Add refresh indicator
  addRefreshIndicator() {
    const refreshIcon = this.element.querySelector('.refresh-icon')
    if (refreshIcon) {
      refreshIcon.classList.add('animate-spin')
      setTimeout(() => {
        refreshIcon.classList.remove('animate-spin')
      }, 1000)
    }
  }

  // Toggle auto-refresh
  toggleAutoRefresh() {
    this.autoRefreshValue = !this.autoRefreshValue
    
    if (this.autoRefreshValue) {
      this.startPeriodicUpdates()
    } else {
      this.stopPeriodicUpdates()
    }
    
    // Update UI indicator
    const toggleButton = this.element.querySelector('.auto-refresh-toggle')
    if (toggleButton) {
      toggleButton.textContent = this.autoRefreshValue ? 'Auto-refresh: ON' : 'Auto-refresh: OFF'
      toggleButton.className = this.autoRefreshValue 
        ? 'text-green-600 font-medium' 
        : 'text-gray-500 font-medium'
    }
  }

  // Pause/resume workflow monitoring
  pauseMonitoring() {
    this.stopPeriodicUpdates()
    console.log('Workflow monitoring paused')
  }

  resumeMonitoring() {
    this.startPeriodicUpdates()
    console.log('Workflow monitoring resumed')
  }

  // Clear completed workflows from view
  clearCompleted() {
    const completedItems = this.workflowItemTargets.filter(item => {
      const status = item.querySelector('[data-workflow-monitor-target="statusIndicator"]')?.dataset.status
      return status === 'completed'
    })
    
    completedItems.forEach(item => {
      item.style.transition = 'opacity 0.3s ease, transform 0.3s ease'
      item.style.opacity = '0'
      item.style.transform = 'translateX(20px)'
      
      setTimeout(() => {
        item.remove()
      }, 300)
    })
  }

  // Export workflow data for debugging
  exportWorkflowData() {
    const workflowData = this.workflowItemTargets.map(item => ({
      id: item.dataset.workflowId,
      status: item.querySelector('[data-workflow-monitor-target="statusIndicator"]')?.dataset.status,
      progress: item.querySelector('[data-workflow-monitor-target="progressBar"]')?.dataset.progress
    }))
    
    console.log('Current workflow data:', workflowData)
    return workflowData
  }
}
