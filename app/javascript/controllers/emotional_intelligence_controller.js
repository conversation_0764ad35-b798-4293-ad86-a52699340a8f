// Emotional Intelligence Dashboard Stimulus Controller
// Handles real-time updates, data refresh, and dashboard interactions

import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["stats", "emotionDistribution", "insights", "adaptations"]
  static values = { 
    refreshInterval: { type: Number, default: 30000 }, // 30 seconds
    autoRefresh: { type: Boolean, default: true }
  }

  connect() {
    console.log("🧠 Emotional Intelligence Dashboard connected")
    
    if (this.autoRefreshValue) {
      this.startAutoRefresh()
    }
    
    // Set up visibility change handler to pause when tab is hidden
    document.addEventListener('visibilitychange', this.handleVisibilityChange.bind(this))
  }

  disconnect() {
    this.stopAutoRefresh()
    document.removeEventListener('visibilitychange', this.handleVisibilityChange.bind(this))
  }

  // Start automatic refresh of dashboard data
  startAutoRefresh() {
    if (this.refreshTimer) return
    
    this.refreshTimer = setInterval(() => {
      if (!document.hidden) {
        this.refreshDashboard()
      }
    }, this.refreshIntervalValue)
  }

  // Stop automatic refresh
  stopAutoRefresh() {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer)
      this.refreshTimer = null
    }
  }

  // Handle browser tab visibility changes
  handleVisibilityChange() {
    if (document.hidden) {
      console.log("👁️ Dashboard hidden - pausing auto refresh")
    } else {
      console.log("👁️ Dashboard visible - resuming auto refresh")
      // Refresh immediately when tab becomes visible
      this.refreshDashboard()
    }
  }

  // Manually refresh dashboard data
  async refreshDashboard() {
    try {
      console.log("🔄 Refreshing emotional intelligence dashboard...")
      
      const response = await fetch(window.location.href + '.json', {
        headers: { 'Accept': 'application/json' }
      })
      
      if (!response.ok) throw new Error(`HTTP ${response.status}`)
      
      const data = await response.json()
      this.updateDashboardData(data)
      
      // Show subtle success indicator
      this.showRefreshIndicator('success')
      
    } catch (error) {
      console.error("❌ Dashboard refresh failed:", error)
      this.showRefreshIndicator('error')
    }
  }

  // Update dashboard elements with new data
  updateDashboardData(data) {
    // Update stats if targets exist
    if (this.hasStatsTarget) {
      this.updateStatsSection(data.emotional_stats)
    }
    
    // Update emotion distribution
    if (this.hasEmotionDistributionTarget) {
      this.updateEmotionDistribution(data.emotional_stats.emotion_distribution)
    }
    
    // Update insights
    if (this.hasInsightsTarget) {
      this.updateInsights(data.insights)
    }
    
    // Update recent adaptations
    if (this.hasAdaptationsTarget) {
      this.updateAdaptations(data.recent_adaptations)
    }
    
    console.log("✅ Dashboard updated with fresh data")
  }

  // Update statistics section
  updateStatsSection(stats) {
    const totalElement = this.element.querySelector('[data-stat="total-customers"]')
    const receptiveElement = this.element.querySelector('[data-stat="receptive-customers"]')
    const confidenceElement = this.element.querySelector('[data-stat="avg-confidence"]')
    const highConfidenceElement = this.element.querySelector('[data-stat="high-confidence"]')
    
    if (totalElement) totalElement.textContent = stats.total_customers || 0
    if (receptiveElement) receptiveElement.textContent = stats.receptive_customers || 0
    if (confidenceElement) confidenceElement.textContent = `${stats.avg_confidence || 0}%`
    if (highConfidenceElement) highConfidenceElement.textContent = stats.high_confidence_states || 0
  }

  // Update emotion distribution display
  updateEmotionDistribution(distribution) {
    if (!distribution) return
    
    Object.entries(distribution).forEach(([emotion, count]) => {
      const emotionElement = this.element.querySelector(`[data-emotion="${emotion}"]`)
      if (emotionElement) {
        const countElement = emotionElement.querySelector('[data-emotion-count]')
        const percentElement = emotionElement.querySelector('[data-emotion-percent]')
        
        if (countElement) countElement.textContent = count
        if (percentElement) {
          const total = Object.values(distribution).reduce((sum, c) => sum + c, 0)
          const percentage = total > 0 ? ((count / total) * 100).toFixed(1) : 0
          percentElement.textContent = `${percentage}%`
        }
      }
    })
  }

  // Update insights section
  updateInsights(insights) {
    if (!insights || !this.hasInsightsTarget) return
    
    // This would typically re-render the insights section
    // For now, just update the count
    const insightCountElement = this.element.querySelector('[data-insights-count]')
    if (insightCountElement) {
      insightCountElement.textContent = insights.length
    }
  }

  // Update adaptations section
  updateAdaptations(adaptations) {
    if (!adaptations || !this.hasAdaptationsTarget) return
    
    // Update adaptations count
    const adaptationCountElement = this.element.querySelector('[data-adaptations-count]')
    if (adaptationCountElement) {
      adaptationCountElement.textContent = adaptations.length
    }
  }

  // Show visual indicator for refresh status
  showRefreshIndicator(status) {
    const indicator = document.createElement('div')
    indicator.className = `fixed top-4 right-4 px-3 py-2 rounded-lg text-sm font-medium z-50 transition-all duration-300 ${
      status === 'success' 
        ? 'bg-green-100 text-green-800 border border-green-200' 
        : 'bg-red-100 text-red-800 border border-red-200'
    }`
    
    indicator.textContent = status === 'success' 
      ? '✅ Dashboard updated' 
      : '❌ Update failed'
    
    document.body.appendChild(indicator)
    
    // Fade out and remove after 3 seconds
    setTimeout(() => {
      indicator.style.opacity = '0'
      indicator.style.transform = 'translateY(-10px)'
      setTimeout(() => {
        if (indicator.parentNode) {
          indicator.parentNode.removeChild(indicator)
        }
      }, 300)
    }, 3000)
  }

  // Toggle auto refresh on/off
  toggleAutoRefresh() {
    this.autoRefreshValue = !this.autoRefreshValue
    
    if (this.autoRefreshValue) {
      this.startAutoRefresh()
      console.log("🔄 Auto refresh enabled")
    } else {
      this.stopAutoRefresh()
      console.log("⏸️ Auto refresh disabled")
    }
    
    // Update UI to reflect state
    const toggleButton = this.element.querySelector('[data-action*="toggleAutoRefresh"]')
    if (toggleButton) {
      toggleButton.textContent = this.autoRefreshValue ? '⏸️ Pause Updates' : '▶️ Resume Updates'
      toggleButton.className = toggleButton.className.replace(
        this.autoRefreshValue ? 'bg-gray-600' : 'bg-green-600',
        this.autoRefreshValue ? 'bg-green-600' : 'bg-gray-600'
      )
    }
  }

  // Navigate to specific customer detail
  viewCustomerDetail(event) {
    const customerIdentifier = event.currentTarget.dataset.customerIdentifier
    if (customerIdentifier) {
      window.location.href = `/emotional_intelligence/customers/${customerIdentifier}`
    }
  }

  // Navigate to campaign emotional analysis
  viewCampaignAnalysis(event) {
    const campaignId = event.currentTarget.dataset.campaignId
    if (campaignId) {
      window.location.href = `/emotional_intelligence/campaigns/${campaignId}/emotional_analysis`
    }
  }

  // Trigger bulk analysis
  async triggerBulkAnalysis(event) {
    event.preventDefault()
    
    if (!confirm("This will analyze all customers against active campaigns. Continue?")) {
      return
    }
    
    try {
      const formData = new FormData(event.target.closest('form'))
      
      const response = await fetch('/emotional_intelligence/bulk_analyze', {
        method: 'POST',
        body: formData,
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
          'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
        }
      })
      
      const result = await response.json()
      
      if (result.success) {
        this.showNotification('success', result.message)
        
        // Refresh dashboard after a delay to show updated results
        setTimeout(() => {
          this.refreshDashboard()
        }, 5000)
      } else {
        this.showNotification('error', result.error || 'Bulk analysis failed')
      }
      
    } catch (error) {
      console.error("❌ Bulk analysis failed:", error)
      this.showNotification('error', 'Failed to start bulk analysis')
    }
  }

  // Show notification message
  showNotification(type, message) {
    const notification = document.createElement('div')
    notification.className = `fixed top-4 left-1/2 transform -translate-x-1/2 px-4 py-3 rounded-lg text-sm font-medium z-50 transition-all duration-300 ${
      type === 'success' 
        ? 'bg-green-100 text-green-800 border border-green-200' 
        : 'bg-red-100 text-red-800 border border-red-200'
    }`
    
    notification.innerHTML = `
      <div class="flex items-center space-x-2">
        <span>${type === 'success' ? '✅' : '❌'}</span>
        <span>${message}</span>
      </div>
    `
    
    document.body.appendChild(notification)
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
      notification.style.opacity = '0'
      notification.style.transform = 'translate(-50%, -10px)'
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification)
        }
      }, 300)
    }, 5000)
  }

  // Filter customers by emotional state
  filterByEmotion(event) {
    const emotion = event.currentTarget.dataset.emotion
    const customerElements = this.element.querySelectorAll('[data-customer-emotion]')
    
    customerElements.forEach(element => {
      const customerEmotion = element.dataset.customerEmotion
      
      if (!emotion || emotion === 'all' || customerEmotion === emotion) {
        element.style.display = 'block'
        element.classList.remove('hidden')
      } else {
        element.style.display = 'none'
        element.classList.add('hidden')
      }
    })
    
    // Update filter button states
    const filterButtons = this.element.querySelectorAll('[data-action*="filterByEmotion"]')
    filterButtons.forEach(button => {
      if (button.dataset.emotion === emotion) {
        button.classList.add('bg-indigo-600', 'text-white')
        button.classList.remove('bg-gray-200', 'text-gray-700')
      } else {
        button.classList.remove('bg-indigo-600', 'text-white')
        button.classList.add('bg-gray-200', 'text-gray-700')
      }
    })
    
    console.log(`🔍 Filtered customers by emotion: ${emotion || 'all'}`)
  }

  // Sort customers by various criteria
  sortCustomers(event) {
    const sortBy = event.currentTarget.dataset.sortBy
    const container = this.element.querySelector('[data-customers-container]')
    
    if (!container) return
    
    const customers = Array.from(container.querySelectorAll('[data-customer-item]'))
    
    customers.sort((a, b) => {
      switch (sortBy) {
        case 'confidence':
          return parseFloat(b.dataset.confidence || 0) - parseFloat(a.dataset.confidence || 0)
        case 'interaction':
          return new Date(b.dataset.lastInteraction || 0) - new Date(a.dataset.lastInteraction || 0)
        case 'emotion':
          return (a.dataset.customerEmotion || '').localeCompare(b.dataset.customerEmotion || '')
        case 'identifier':
          return (a.dataset.customerIdentifier || '').localeCompare(b.dataset.customerIdentifier || '')
        default:
          return 0
      }
    })
    
    // Re-append sorted elements
    customers.forEach(customer => container.appendChild(customer))
    
    console.log(`📊 Sorted customers by: ${sortBy}`)
  }

  // Export dashboard data
  async exportData(event) {
    event.preventDefault()
    
    try {
      const response = await fetch(window.location.href + '.json')
      const data = await response.json()
      
      const exportData = {
        exported_at: new Date().toISOString(),
        dashboard_data: data,
        export_type: 'emotional_intelligence_dashboard'
      }
      
      const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      
      const link = document.createElement('a')
      link.href = url
      link.download = `emotional_intelligence_dashboard_${new Date().toISOString().split('T')[0]}.json`
      link.click()
      
      URL.revokeObjectURL(url)
      
      this.showNotification('success', 'Dashboard data exported successfully')
      
    } catch (error) {
      console.error("❌ Export failed:", error)
      this.showNotification('error', 'Failed to export dashboard data')
    }
  }
}
