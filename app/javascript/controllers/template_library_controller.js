import { Controller } from "@hotwired/stimulus"

// Connects to data-controller="template-library"
export default class extends Controller {
  static targets = [
    "modal",
    "loading",
    "templatesGrid", 
    "noResults",
    "error",
    "searchInput",
    "categoryFilter",
    "industryFilter",
    "templateCard",
    "templateCount",
    "previewSidebar",
    "previewTitle",
    "previewContent",
    "useSelectedButton"
  ]

  static values = {
    apiEndpoint: String,
    formTarget: String
  }

  connect() {
    console.log("Template Library controller connected")
    this.isOpen = false
    this.selectedTemplate = null
    this.allTemplates = []
    this.filteredTemplates = []
    
    // Bind keyboard events
    this.handleKeydown = this.handleKeydown.bind(this)
  }

  disconnect() {
    document.removeEventListener('keydown', this.handleKeydown)
  }

  // Public method to open the modal
  open(event) {
    this.showModal()
    this.loadTemplates()
  }

  // Show the modal with proper accessibility
  showModal() {
    this.modalTarget.classList.remove("hidden")
    this.modalTarget.classList.add("flex")
    this.isOpen = true
    
    // Add keyboard event listener
    document.addEventListener('keydown', this.handleKeydown)
    
    // Focus management
    this.modalTarget.focus()
    
    // Prevent body scroll
    document.body.style.overflow = 'hidden'
    
    // Reset state
    this.selectedTemplate = null
    this.updateSelectedButton()
    this.closePreview()
    
    // Announce to screen readers
    this.announceToScreenReader("Template library modal opened")
  }

  // Close the modal
  close() {
    this.modalTarget.classList.add("hidden")
    this.modalTarget.classList.remove("flex")
    this.isOpen = false
    
    // Remove keyboard event listener
    document.removeEventListener('keydown', this.handleKeydown)
    
    // Restore body scroll
    document.body.style.overflow = 'auto'
    
    // Reset state
    this.selectedTemplate = null
    this.closePreview()
    
    // Announce to screen readers
    this.announceToScreenReader("Template library modal closed")
  }

  // Handle backdrop clicks
  closeOnBackdrop(event) {
    if (event.target === this.modalTarget) {
      this.close()
    }
  }

  // Prevent event propagation for modal content clicks
  stopPropagation(event) {
    event.stopPropagation()
  }

  // Handle keyboard navigation
  handleKeydown(event) {
    if (!this.isOpen) return
    
    switch (event.key) {
      case 'Escape':
        if (this.hasPreviewSidebarTarget && !this.previewSidebarTarget.classList.contains('hidden')) {
          this.closePreview()
        } else {
          this.close()
        }
        break
      case 'Tab':
        this.handleTabNavigation(event)
        break
    }
  }

  // Handle tab navigation within modal
  handleTabNavigation(event) {
    const focusableElements = this.modalTarget.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    )
    
    const firstElement = focusableElements[0]
    const lastElement = focusableElements[focusableElements.length - 1]
    
    if (event.shiftKey && document.activeElement === firstElement) {
      event.preventDefault()
      lastElement.focus()
    } else if (!event.shiftKey && document.activeElement === lastElement) {
      event.preventDefault()
      firstElement.focus()
    }
  }

  // Load templates from API
  async loadTemplates() {
    this.showLoadingState()
    
    try {
      const endpoint = this.apiEndpointValue || '/api/email_templates'
      const response = await fetch(endpoint, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'X-Requested-With': 'XMLHttpRequest',
          'X-CSRF-Token': this.getCSRFToken()
        }
      })
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
      
      const data = await response.json()
      this.allTemplates = data.templates || this.getMockTemplates()
      this.filteredTemplates = [...this.allTemplates]
      
      this.renderTemplates()
      this.updateTemplateCount()
      this.showContentState()
      
    } catch (error) {
      console.error('Failed to load templates:', error)
      // Fallback to mock data for development
      this.allTemplates = this.getMockTemplates()
      this.filteredTemplates = [...this.allTemplates]
      this.renderTemplates()
      this.updateTemplateCount()
      this.showContentState()
    }
  }

  // Get mock templates for development/fallback
  getMockTemplates() {
    return [
      {
        id: 'newsletter-modern',
        title: 'Modern Newsletter',
        description: 'Clean, professional newsletter template with header, content sections, and footer.',
        category: 'newsletter',
        industry: 'technology',
        rating: 4.8,
        content: {
          subject: 'Your Weekly Tech Update',
          html: '<div style="font-family: Arial, sans-serif;"><h1>Weekly Newsletter</h1><p>Your content here...</p></div>',
          text: 'Weekly Newsletter\n\nYour content here...'
        },
        preview_url: '#',
        thumbnail: 'newsletter-modern.png'
      },
      {
        id: 'promo-sale',
        title: 'Sale Announcement',
        description: 'Eye-catching promotional template perfect for sales, discounts, and special offers.',
        category: 'promotional',
        industry: 'ecommerce',
        rating: 4.9,
        content: {
          subject: 'Limited Time Sale - Don\'t Miss Out!',
          html: '<div style="font-family: Arial, sans-serif; text-align: center;"><h1 style="color: #e53e3e;">SALE!</h1><p>Limited time offer...</p></div>',
          text: 'SALE!\n\nLimited time offer...'
        },
        preview_url: '#',
        thumbnail: 'promo-sale.png'
      },
      {
        id: 'welcome-series',
        title: 'Welcome Series',
        description: 'Warm welcome template for new subscribers with onboarding information.',
        category: 'welcome',
        industry: 'finance',
        rating: 4.7,
        content: {
          subject: 'Welcome to our community!',
          html: '<div style="font-family: Arial, sans-serif;"><h1 style="color: #10b981;">Welcome!</h1><p>We\'re excited to have you...</p></div>',
          text: 'Welcome!\n\nWe\'re excited to have you...'
        },
        preview_url: '#',
        thumbnail: 'welcome-series.png'
      }
    ]
  }

  // Render templates in the grid
  renderTemplates() {
    if (!this.hasTemplatesGridTarget) return
    
    if (this.filteredTemplates.length === 0) {
      this.showNoResults()
      return
    }
    
    const html = this.filteredTemplates.map(template => `
      <div class="template-card bg-white rounded-xl shadow-sm border border-slate-200 hover:shadow-md transition-all duration-300 cursor-pointer ${this.selectedTemplate?.id === template.id ? 'ring-2 ring-purple-500' : ''}"
           data-template-library-target="templateCard"
           data-template-id="${template.id}"
           data-category="${template.category}"
           data-industry="${template.industry}"
           data-action="click->template-library#selectTemplate">
        
        <div class="aspect-[4/3] bg-gradient-to-br ${this.getCategoryGradient(template.category)} rounded-t-xl relative overflow-hidden">
          <div class="absolute inset-0 p-4">
            ${this.getTemplatePreview(template)}
          </div>
          <span class="absolute top-3 right-3 ${this.getCategoryBadge(template.category)} text-white text-xs px-2 py-1 rounded-full capitalize">
            ${template.category}
          </span>
        </div>
        
        <div class="p-4">
          <h3 class="font-semibold text-slate-800 mb-1">${template.title}</h3>
          <p class="text-sm text-slate-600 mb-3">${template.description}</p>
          
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-2">
              <span class="text-xs bg-slate-100 text-slate-600 px-2 py-1 rounded capitalize">${template.industry}</span>
              <div class="flex items-center">
                <svg class="h-3 w-3 text-yellow-400 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                </svg>
                <span class="text-xs text-slate-500">${template.rating}</span>
              </div>
            </div>
            <button class="text-purple-600 hover:text-purple-700 text-sm font-medium"
                    data-action="click->template-library#previewTemplate"
                    data-template-id="${template.id}">
              Preview
            </button>
          </div>
        </div>
      </div>
    `).join('')
    
    this.templatesGridTarget.innerHTML = html
  }

  // Get category-specific gradient
  getCategoryGradient(category) {
    const gradients = {
      newsletter: 'from-blue-50 to-indigo-100',
      promotional: 'from-red-50 to-pink-100',
      welcome: 'from-green-50 to-emerald-100',
      product: 'from-yellow-50 to-orange-100',
      event: 'from-purple-50 to-violet-100',
      survey: 'from-teal-50 to-cyan-100'
    }
    return gradients[category] || 'from-slate-50 to-gray-100'
  }

  // Get category-specific badge color
  getCategoryBadge(category) {
    const badges = {
      newsletter: 'bg-blue-600',
      promotional: 'bg-red-600',
      welcome: 'bg-green-600',
      product: 'bg-yellow-600',
      event: 'bg-purple-600',
      survey: 'bg-teal-600'
    }
    return badges[category] || 'bg-slate-600'
  }

  // Get template preview mockup
  getTemplatePreview(template) {
    const previews = {
      newsletter: `
        <div class="bg-white rounded-lg shadow-sm p-3 mb-2">
          <div class="h-2 bg-slate-200 rounded mb-2"></div>
          <div class="h-1.5 bg-slate-100 rounded mb-1"></div>
          <div class="h-1.5 bg-slate-100 rounded w-3/4"></div>
        </div>
        <div class="space-y-2">
          <div class="h-1 bg-white/60 rounded"></div>
          <div class="h-1 bg-white/60 rounded w-4/5"></div>
          <div class="h-1 bg-white/60 rounded w-3/5"></div>
        </div>
      `,
      promotional: `
        <div class="bg-white rounded-lg shadow-sm p-3 mb-2 text-center">
          <div class="h-3 bg-red-500 rounded mb-2 w-1/2 mx-auto"></div>
          <div class="h-1.5 bg-slate-100 rounded mb-1"></div>
          <div class="h-1.5 bg-slate-100 rounded w-3/4 mx-auto"></div>
        </div>
        <div class="text-center space-y-1">
          <div class="h-1 bg-white/60 rounded w-2/3 mx-auto"></div>
          <div class="h-1 bg-white/60 rounded w-1/2 mx-auto"></div>
        </div>
      `,
      welcome: `
        <div class="bg-white rounded-lg shadow-sm p-3 mb-2">
          <div class="h-2 bg-green-500 rounded mb-2 w-1/3"></div>
          <div class="h-1.5 bg-slate-100 rounded mb-1"></div>
          <div class="h-1.5 bg-slate-100 rounded w-4/5"></div>
        </div>
        <div class="space-y-1">
          <div class="h-1 bg-white/60 rounded"></div>
          <div class="h-1 bg-white/60 rounded w-3/4"></div>
          <div class="h-1 bg-white/60 rounded w-1/2"></div>
        </div>
      `
    }
    return previews[template.category] || previews.newsletter
  }

  // Select a template
  selectTemplate(event) {
    const templateId = event.currentTarget.dataset.templateId
    const template = this.allTemplates.find(t => t.id === templateId)
    
    if (template) {
      // Remove previous selection
      this.templateCardTargets.forEach(card => {
        card.classList.remove('ring-2', 'ring-purple-500')
      })
      
      // Add selection to current card
      event.currentTarget.classList.add('ring-2', 'ring-purple-500')
      
      this.selectedTemplate = template
      this.updateSelectedButton()
      
      // Announce selection
      this.announceToScreenReader(`Template selected: ${template.title}`)
    }
  }

  // Preview a template
  previewTemplate(event) {
    event.stopPropagation()
    
    const templateId = event.currentTarget.dataset.templateId
    const template = this.allTemplates.find(t => t.id === templateId)
    
    if (template) {
      this.showTemplatePreview(template)
    }
  }

  // Show template preview in sidebar
  showTemplatePreview(template) {
    if (!this.hasPreviewSidebarTarget) return
    
    this.previewTitleTarget.textContent = template.title
    
    const previewHtml = `
      <div class="space-y-4">
        <div class="aspect-video bg-slate-100 rounded-lg overflow-hidden">
          <iframe src="${template.preview_url || 'about:blank'}" 
                  class="w-full h-full border-0"
                  title="Template preview">
          </iframe>
        </div>
        
        <div>
          <h4 class="font-medium text-slate-800 mb-2">Template Details</h4>
          <div class="space-y-2 text-sm">
            <div class="flex justify-between">
              <span class="text-slate-600">Category:</span>
              <span class="font-medium capitalize">${template.category}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-slate-600">Industry:</span>
              <span class="font-medium capitalize">${template.industry}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-slate-600">Rating:</span>
              <span class="font-medium">${template.rating}/5</span>
            </div>
          </div>
        </div>
        
        <div>
          <h4 class="font-medium text-slate-800 mb-2">Description</h4>
          <p class="text-sm text-slate-600">${template.description}</p>
        </div>
        
        <div>
          <h4 class="font-medium text-slate-800 mb-2">Sample Subject</h4>
          <p class="text-sm text-slate-600 italic">"${template.content?.subject || 'Sample subject line'}"</p>
        </div>
      </div>
    `
    
    this.previewContentTarget.innerHTML = previewHtml
    this.previewSidebarTarget.classList.remove('hidden')
    
    // Select the template being previewed
    this.selectedTemplate = template
    this.updateSelectedButton()
    
    // Update visual selection
    this.templateCardTargets.forEach(card => {
      card.classList.remove('ring-2', 'ring-purple-500')
      if (card.dataset.templateId === template.id) {
        card.classList.add('ring-2', 'ring-purple-500')
      }
    })
  }

  // Close preview sidebar
  closePreview() {
    if (this.hasPreviewSidebarTarget) {
      this.previewSidebarTarget.classList.add('hidden')
    }
  }

  // Filter templates based on search and filters
  filterTemplates() {
    const searchTerm = this.hasSearchInputTarget ? this.searchInputTarget.value.toLowerCase().trim() : ''
    const categoryFilter = this.hasCategoryFilterTarget ? this.categoryFilterTarget.value : ''
    const industryFilter = this.hasIndustryFilterTarget ? this.industryFilterTarget.value : ''
    
    this.filteredTemplates = this.allTemplates.filter(template => {
      const matchesSearch = !searchTerm || 
        template.title.toLowerCase().includes(searchTerm) ||
        template.description.toLowerCase().includes(searchTerm)
      
      const matchesCategory = !categoryFilter || template.category === categoryFilter
      const matchesIndustry = !industryFilter || template.industry === industryFilter
      
      return matchesSearch && matchesCategory && matchesIndustry
    })
    
    this.renderTemplates()
    this.updateTemplateCount()
    
    if (this.filteredTemplates.length === 0) {
      this.showNoResults()
    }
  }

  // Clear all filters
  clearFilters() {
    if (this.hasSearchInputTarget) this.searchInputTarget.value = ''
    if (this.hasCategoryFilterTarget) this.categoryFilterTarget.value = ''
    if (this.hasIndustryFilterTarget) this.industryFilterTarget.value = ''
    
    this.filterTemplates()
  }

  // Use selected template
  useSelectedTemplate() {
    if (this.selectedTemplate) {
      this.useTemplate()
    }
  }

  // Use template (apply to form)
  useTemplate() {
    if (!this.selectedTemplate) return
    
    try {
      // Apply template content to form
      this.applyTemplateToForm(this.selectedTemplate)
      
      // Close modal
      this.close()
      
      // Announce success
      this.announceToScreenReader(`Template "${this.selectedTemplate.title}" applied successfully`)
      
    } catch (error) {
      console.error('Failed to apply template:', error)
      this.announceToScreenReader('Failed to apply template')
    }
  }

  // Customize template before use
  customizeTemplate() {
    if (!this.selectedTemplate) return
    
    // For now, just use the template
    // TODO: Open customization interface
    this.useTemplate()
  }

  // Apply template content to the email form
  applyTemplateToForm(template) {
    const formSelector = this.formTargetValue || '#email_content_form'
    const form = document.querySelector(formSelector)
    
    if (!form) {
      throw new Error('Email form not found')
    }
    
    // Apply subject line
    const subjectField = form.querySelector('#email_content_subject, [name="email_content[subject]"]')
    if (subjectField && template.content?.subject) {
      subjectField.value = template.content.subject
      subjectField.dispatchEvent(new Event('input', { bubbles: true }))
    }
    
    // Apply HTML content
    const htmlField = form.querySelector('#email_content_html_content, [name="email_content[html_content]"]')
    if (htmlField && template.content?.html) {
      htmlField.value = template.content.html
      htmlField.dispatchEvent(new Event('input', { bubbles: true }))
    }
    
    // Apply text content
    const textField = form.querySelector('#email_content_text_content, [name="email_content[text_content]"]')
    if (textField && template.content?.text) {
      textField.value = template.content.text
      textField.dispatchEvent(new Event('input', { bubbles: true }))
    }
    
    // Trigger any form validation or preview updates
    form.dispatchEvent(new Event('change', { bubbles: true }))
  }

  // Update template count display
  updateTemplateCount() {
    if (this.hasTemplateCountTarget) {
      this.templateCountTarget.textContent = this.filteredTemplates.length
    }
  }

  // Update selected button state
  updateSelectedButton() {
    if (this.hasUseSelectedButtonTarget) {
      this.useSelectedButtonTarget.disabled = !this.selectedTemplate
      
      if (this.selectedTemplate) {
        this.useSelectedButtonTarget.textContent = `Use "${this.selectedTemplate.title}"`
      } else {
        this.useSelectedButtonTarget.textContent = 'Use Template'
      }
    }
  }

  // State management methods
  showLoadingState() {
    this.hideAllStates()
    if (this.hasLoadingTarget) {
      this.loadingTarget.classList.remove("hidden")
    }
  }

  showContentState() {
    this.hideAllStates()
    if (this.hasTemplatesGridTarget) {
      this.templatesGridTarget.parentElement.classList.remove("hidden")
    }
  }

  showNoResults() {
    this.hideAllStates()
    if (this.hasNoResultsTarget) {
      this.noResultsTarget.classList.remove("hidden")
    }
  }

  showErrorState() {
    this.hideAllStates()
    if (this.hasErrorTarget) {
      this.errorTarget.classList.remove("hidden")
    }
  }

  hideAllStates() {
    [this.loadingTarget, this.noResultsTarget, this.errorTarget].forEach(target => {
      if (target) {
        target.classList.add("hidden")
      }
    })
    
    if (this.hasTemplatesGridTarget) {
      this.templatesGridTarget.parentElement.classList.add("hidden")
    }
  }

  // Retry loading templates
  retry() {
    this.loadTemplates()
  }

  // Utility methods
  getCSRFToken() {
    const token = document.querySelector('meta[name="csrf-token"]')
    return token ? token.getAttribute('content') : ''
  }

  announceToScreenReader(message) {
    const announcement = document.createElement('div')
    announcement.setAttribute('aria-live', 'polite')
    announcement.setAttribute('aria-atomic', 'true')
    announcement.className = 'sr-only'
    announcement.textContent = message
    
    document.body.appendChild(announcement)
    
    setTimeout(() => {
      document.body.removeChild(announcement)
    }, 1000)
  }
}
