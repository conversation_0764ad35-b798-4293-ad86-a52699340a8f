// Email Content Preview Controller using Stimulus
import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["subjectField", "fromNameField", "fromEmailField", "contentField", 
                   "previewSubject", "previewFrom", "previewContent", "subjectCounter",
                   "previewField", "previewCounter", "submitButton"]
  
  static values = { 
    subjectMaxLength: { type: Number, default: 150 },
    previewMaxLength: { type: Number, default: 200 }
  }

  connect() {
    this.updatePreview()
    this.setupCharacterCounters()
    this.setupAutoSave()
    this.initializeCounters()
  }

  disconnect() {
    if (this.autoSaveTimeout) {
      clearTimeout(this.autoSaveTimeout)
    }
  }

  // Initialize character counters for existing content
  initializeCounters() {
    if (this.hasSubjectFieldTarget && this.subjectFieldTarget.value) {
      this.updateSubjectCounter()
    }
    if (this.hasPreviewFieldTarget && this.previewFieldTarget.value) {
      this.updatePreviewCounter()
    }
  }

  // Event handlers for form field updates
  updatePreview() {
    this.updateSubjectPreview()
    this.updateFromPreview()
    this.updateContentPreview()
  }

  updateSubjectPreview() {
    if (this.hasSubjectFieldTarget && this.hasPreviewSubjectTarget) {
      this.previewSubjectTarget.textContent = this.subjectFieldTarget.value || 'Your email subject line...'
    }
  }

  updateFromPreview() {
    if (this.hasFromNameFieldTarget && this.hasFromEmailFieldTarget && this.hasPreviewFromTarget) {
      const name = this.fromNameFieldTarget.value || 'Your Name'
      const email = this.fromEmailFieldTarget.value || '<EMAIL>'
      this.previewFromTarget.innerHTML = `${name} &lt;${email}&gt;`
    }
  }

  updateContentPreview() {
    if (this.hasContentFieldTarget && this.hasPreviewContentTarget) {
      const content = this.contentFieldTarget.value || 'Start typing your email content to see a preview here...'
      const formattedContent = content.replace(/\n/g, '<br>')
      this.previewContentTarget.innerHTML = formattedContent
    }
  }

  setupCharacterCounters() {
    this.setupSubjectCounter()
    this.setupPreviewCounter()
  }

  setupSubjectCounter() {
    if (this.hasSubjectFieldTarget && !this.hasSubjectCounterTarget) {
      const counter = document.createElement('div')
      counter.className = 'text-xs text-slate-500 mt-1 character-counter'
      counter.innerHTML = `<span data-email-content-preview-target="subjectCounter">0</span>/${this.subjectMaxLengthValue} characters`
      
      const parentDiv = this.subjectFieldTarget.parentNode
      const existingHint = parentDiv.querySelector('p')
      if (existingHint) {
        existingHint.remove()
      }
      parentDiv.appendChild(counter)
    }
  }

  setupPreviewCounter() {
    if (this.hasPreviewFieldTarget && !this.hasPreviewCounterTarget) {
      const counter = document.createElement('div')
      counter.className = 'text-xs text-slate-500 mt-1 character-counter'
      counter.innerHTML = `<span data-email-content-preview-target="previewCounter">0</span>/${this.previewMaxLengthValue} characters`
      
      const parentDiv = this.previewFieldTarget.parentNode
      const existingHint = parentDiv.querySelector('p')
      if (existingHint) {
        existingHint.remove()
      }
      parentDiv.appendChild(counter)
    }
  }

  updateSubjectCounter() {
    if (this.hasSubjectFieldTarget && this.hasSubjectCounterTarget) {
      const length = this.subjectFieldTarget.value.length
      this.subjectCounterTarget.textContent = length
      
      const maxLength = this.subjectMaxLengthValue
      const counterElement = this.subjectCounterTarget.parentElement
      
      // Reset classes first
      counterElement.className = counterElement.className.replace(/text-(slate-500|amber-600|blue-600)/, '')
      
      if (length > maxLength * 0.9) {
        counterElement.className += ' text-amber-600'
      } else if (length > maxLength * 0.8) {
        counterElement.className += ' text-blue-600'
      } else {
        counterElement.className += ' text-slate-500'
      }
    }
  }

  updatePreviewCounter() {
    if (this.hasPreviewFieldTarget && this.hasPreviewCounterTarget) {
      const length = this.previewFieldTarget.value.length
      this.previewCounterTarget.textContent = length
      
      const maxLength = this.previewMaxLengthValue
      const counterElement = this.previewCounterTarget.parentElement
      
      // Reset classes first
      counterElement.className = counterElement.className.replace(/text-(slate-500|amber-600|blue-600)/, '')
      
      if (length > maxLength * 0.9) {
        counterElement.className += ' text-amber-600'
      } else if (length > maxLength * 0.8) {
        counterElement.className += ' text-blue-600'
      } else {
        counterElement.className += ' text-slate-500'
      }
    }
  }

  validateEmail() {
    if (this.hasFromEmailFieldTarget) {
      const email = this.fromEmailFieldTarget.value
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      
      // Remove existing validation message
      const existingMessage = this.fromEmailFieldTarget.parentNode.querySelector('.validation-message')
      if (existingMessage) {
        existingMessage.remove()
      }
      
      if (email && !emailRegex.test(email)) {
        const message = document.createElement('p')
        message.className = 'text-xs text-red-500 mt-1 validation-message'
        message.textContent = 'Please enter a valid email address'
        this.fromEmailFieldTarget.parentNode.appendChild(message)
        
        // Add visual error state with ring instead of border
        this.fromEmailFieldTarget.className = this.fromEmailFieldTarget.className.replace('focus:ring-emerald-500', 'focus:ring-red-500')
      } else {
        // Reset to normal state
        this.fromEmailFieldTarget.className = this.fromEmailFieldTarget.className.replace('focus:ring-red-500', 'focus:ring-emerald-500')
      }
    }
  }

  // AI Form submission handling
  submitAiForm(event) {
    if (this.hasSubmitButtonTarget) {
      this.submitButtonTarget.disabled = true
      const originalValue = this.submitButtonTarget.value
      this.submitButtonTarget.value = 'Generating...'
      this.submitButtonTarget.className = this.submitButtonTarget.className.replace('from-violet-600 to-purple-600', 'from-slate-400 to-slate-500')
      
      // Add spinner
      const spinner = document.createElement('svg')
      spinner.className = 'animate-spin w-4 h-4 mr-2'
      spinner.innerHTML = '<circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" fill="none"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>'
      this.submitButtonTarget.style.position = 'relative'
      this.submitButtonTarget.insertBefore(spinner, this.submitButtonTarget.firstChild)
      
      // Reset after timeout if no response
      setTimeout(() => {
        if (this.submitButtonTarget.disabled) {
          this.resetSubmitButton(originalValue, spinner)
        }
      }, 30000) // 30 second timeout
    }
  }

  resetSubmitButton(originalValue, spinner) {
    if (this.hasSubmitButtonTarget) {
      this.submitButtonTarget.disabled = false
      this.submitButtonTarget.value = originalValue
      this.submitButtonTarget.className = this.submitButtonTarget.className.replace('from-slate-400 to-slate-500', 'from-violet-600 to-purple-600')
      if (spinner && spinner.parentNode) {
        spinner.remove()
      }
    }
  }

  // Auto-save functionality
  setupAutoSave() {
    this.autoSaveTimeout = null
  }

  triggerAutoSave() {
    if (this.autoSaveTimeout) {
      clearTimeout(this.autoSaveTimeout)
    }
    
    this.autoSaveTimeout = setTimeout(() => {
      this.showAutoSaveIndicator()
    }, 2000)
  }

  showAutoSaveIndicator() {
    const indicator = document.createElement('div')
    indicator.className = 'fixed bottom-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg text-sm shadow-lg z-50'
    indicator.innerHTML = '<span class="flex items-center"><svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>Draft auto-saved</span>'
    document.body.appendChild(indicator)
    
    setTimeout(() => {
      indicator.remove()
    }, 3000)
  }

  // Stimulus action handlers
  fieldChanged() {
    this.updatePreview()
    this.updateSubjectCounter()
    this.updatePreviewCounter()
    this.triggerAutoSave()
  }

  emailFieldBlurred() {
    this.validateEmail()
  }

  aiFormSubmitted(event) {
    this.submitAiForm(event)
  }
}
