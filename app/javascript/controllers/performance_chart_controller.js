import { Controller } from "@hotwired/stimulus"

// Import Chart.js from CDN
const Chart = window.Chart

export default class extends Controller {
  static targets = ["canvas"]
  static values = {
    data: Object,
    timeframe: String
  }

  connect() {
    // Wait for Chart.js to load
    if (typeof Chart !== 'undefined') {
      this.initializeChart()
    } else {
      // Retry after a short delay
      setTimeout(() => {
        if (typeof Chart !== 'undefined') {
          this.initializeChart()
        }
      }, 100)
    }
  }

  disconnect() {
    if (this.chart) {
      this.chart.destroy()
    }
  }

  initializeChart() {
    const ctx = this.canvasTarget.getContext('2d')
    
    // Hide fallback when chart initializes
    const fallback = this.element.querySelector('.chart-fallback')
    if (fallback) {
      fallback.style.display = 'none'
    }
    
    // Generate sample data based on timeframe
    const chartData = this.generateChartData()
    
    this.chart = new window.Chart(ctx, {
      type: 'line',
      data: {
        labels: chartData.labels,
        datasets: [
          {
            label: 'Campaign Performance',
            data: chartData.performance,
            borderColor: 'rgb(59, 130, 246)',
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            tension: 0.4,
            fill: true
          },
          {
            label: 'Engagement Rate',
            data: chartData.engagement,
            borderColor: 'rgb(147, 51, 234)',
            backgroundColor: 'rgba(147, 51, 234, 0.1)',
            tension: 0.4,
            fill: true
          },
          {
            label: 'Conversion Rate',
            data: chartData.conversion,
            borderColor: 'rgb(16, 185, 129)',
            backgroundColor: 'rgba(16, 185, 129, 0.1)',
            tension: 0.4,
            fill: true
          }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'top',
            labels: {
              usePointStyle: true,
              font: {
                size: 12
              }
            }
          },
          tooltip: {
            mode: 'index',
            intersect: false,
            backgroundColor: 'rgba(255, 255, 255, 0.95)',
            titleColor: '#374151',
            bodyColor: '#6B7280',
            borderColor: '#E5E7EB',
            borderWidth: 1,
            cornerRadius: 8,
            displayColors: true
          }
        },
        scales: {
          x: {
            display: true,
            grid: {
              display: false
            },
            ticks: {
              font: {
                size: 11
              },
              color: '#9CA3AF'
            }
          },
          y: {
            display: true,
            grid: {
              color: '#F3F4F6',
              drawBorder: false
            },
            ticks: {
              font: {
                size: 11
              },
              color: '#9CA3AF',
              callback: function(value) {
                return value + '%'
              }
            },
            beginAtZero: true
          }
        },
        interaction: {
          mode: 'nearest',
          axis: 'x',
          intersect: false
        },
        elements: {
          point: {
            radius: 4,
            hoverRadius: 6,
            backgroundColor: '#ffffff',
            borderWidth: 2,
            hoverBorderWidth: 3
          }
        }
      }
    })
  }

  generateChartData() {
    const timeframe = this.timeframeValue || '30'
    
    // Generate labels based on timeframe
    let labels = []
    let dataPoints = parseInt(timeframe === '7' ? 7 : timeframe === '90' ? 12 : 30)
    
    if (timeframe === '7') {
      // Last 7 days
      for (let i = 6; i >= 0; i--) {
        const date = new Date()
        date.setDate(date.getDate() - i)
        labels.push(date.toLocaleDateString('en-US', { weekday: 'short' }))
      }
    } else if (timeframe === '90') {
      // Last 90 days (show weekly)
      for (let i = 11; i >= 0; i--) {
        const date = new Date()
        date.setDate(date.getDate() - (i * 7))
        labels.push(date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }))
      }
    } else {
      // Last 30 days
      for (let i = 29; i >= 0; i--) {
        const date = new Date()
        date.setDate(date.getDate() - i)
        labels.push(date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }))
      }
    }

    // Generate realistic performance data
    const performance = this.generatePerformanceData(dataPoints)
    const engagement = this.generateEngagementData(dataPoints)
    const conversion = this.generateConversionData(dataPoints)

    return {
      labels,
      performance,
      engagement,
      conversion
    }
  }

  generatePerformanceData(points) {
    const data = []
    let base = 75 + Math.random() * 15 // Start between 75-90%
    
    for (let i = 0; i < points; i++) {
      // Add some trending and random variation
      const trend = Math.sin(i / points * Math.PI) * 5
      const noise = (Math.random() - 0.5) * 8
      base = Math.max(60, Math.min(95, base + trend + noise))
      data.push(Math.round(base * 10) / 10)
    }
    
    return data
  }

  generateEngagementData(points) {
    const data = []
    let base = 6 + Math.random() * 4 // Start between 6-10%
    
    for (let i = 0; i < points; i++) {
      const trend = Math.cos(i / points * Math.PI * 2) * 2
      const noise = (Math.random() - 0.5) * 2
      base = Math.max(3, Math.min(15, base + trend + noise))
      data.push(Math.round(base * 10) / 10)
    }
    
    return data
  }

  generateConversionData(points) {
    const data = []
    let base = 2 + Math.random() * 3 // Start between 2-5%
    
    for (let i = 0; i < points; i++) {
      const trend = Math.sin(i / points * Math.PI * 1.5) * 1
      const noise = (Math.random() - 0.5) * 1.5
      base = Math.max(1, Math.min(8, base + trend + noise))
      data.push(Math.round(base * 10) / 10)
    }
    
    return data
  }

  timeframeChanged(event) {
    this.timeframeValue = event.target.value
    this.chart.destroy()
    this.initializeChart()
  }
}