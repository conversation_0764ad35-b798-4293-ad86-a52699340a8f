import { Controller } from "@hotwired/stimulus"

// Connects to data-controller="vibe-analytics"
export default class extends Controller {
  static targets = ["emotionalChart", "culturalChart", "authenticityChart", "vibeStatusChart"]
  static values = { 
    data: Object,
    dateRange: String,
    campaignFilter: String
  }

  connect() {
    console.log("Vibe Analytics controller connected")
    this.initializeCharts()
    this.setupFilterHandlers()
  }

  initializeCharts() {
    if (this.hasDataValue && this.dataValue) {
      this.renderEmotionalToneChart()
      this.renderCulturalScoresChart()
      this.renderAuthenticityChart()
      this.renderVibeStatusChart()
    }
  }

  setupFilterHandlers() {
    // Date range filter
    const dateRangeSelect = document.getElementById('date_range')
    if (dateRangeSelect) {
      dateRangeSelect.addEventListener('change', (e) => {
        this.updateAnalytics({ date_range: e.target.value })
      })
    }

    // Campaign filter
    const campaignFilterSelect = document.getElementById('campaign_filter')
    if (campaignFilterSelect) {
      campaignFilterSelect.addEventListener('change', (e) => {
        this.updateAnalytics({ campaign_filter: e.target.value })
      })
    }
  }

  renderEmotionalToneChart() {
    if (!this.hasEmotionalChartTarget) return

    const data = this.dataValue.emotional_trends?.distribution || {}
    const chartData = Object.entries(data).map(([tone, count]) => ({
      name: this.humanizeTone(tone),
      value: count
    }))

    this.createPieChart(this.emotionalChartTarget, chartData, {
      title: 'Emotional Tone Distribution',
      colors: ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4', '#84CC16', '#F97316']
    })
  }

  renderCulturalScoresChart() {
    if (!this.hasCulturalChartTarget) return

    const data = this.dataValue.cultural_metrics?.score_distribution || {}
    const chartData = Object.entries(data).map(([range, count]) => ({
      name: range,
      value: count
    }))

    this.createBarChart(this.culturalChartTarget, chartData, {
      title: 'Cultural Relevance Score Distribution',
      color: '#8B5CF6'
    })
  }

  renderAuthenticityChart() {
    if (!this.hasAuthenticityChartTarget) return

    const data = this.dataValue.authenticity_stats?.score_distribution || {}
    const chartData = Object.entries(data).map(([range, count]) => ({
      name: range,
      value: count
    }))

    this.createBarChart(this.authenticityChartTarget, chartData, {
      title: 'Authenticity Score Distribution',
      color: '#F59E0B'
    })
  }

  renderVibeStatusChart() {
    if (!this.hasVibeStatusChartTarget) return

    const data = this.dataValue.vibe_status_distribution || {}
    const chartData = Object.entries(data).map(([status, count]) => ({
      name: this.humanizeStatus(status),
      value: count
    }))

    this.createDonutChart(this.vibeStatusChartTarget, chartData, {
      title: 'Vibe Status Overview',
      colors: ['#10B981', '#EF4444', '#F59E0B', '#6B7280']
    })
  }

  createPieChart(container, data, options = {}) {
    // Simple SVG pie chart implementation
    container.innerHTML = this.generatePieChartSVG(data, options)
  }

  createBarChart(container, data, options = {}) {
    // Simple SVG bar chart implementation
    container.innerHTML = this.generateBarChartSVG(data, options)
  }

  createDonutChart(container, data, options = {}) {
    // Simple SVG donut chart implementation
    container.innerHTML = this.generateDonutChartSVG(data, options)
  }

  generatePieChartSVG(data, options) {
    if (!data || data.length === 0) {
      return '<div class="flex items-center justify-center h-full text-gray-500">No data available</div>'
    }

    const total = data.reduce((sum, item) => sum + item.value, 0)
    let currentAngle = 0
    const radius = 80
    const centerX = 120
    const centerY = 120

    let paths = ''
    let legends = ''

    data.forEach((item, index) => {
      const percentage = (item.value / total) * 100
      const angle = (item.value / total) * 360
      const color = options.colors?.[index % options.colors.length] || '#3B82F6'

      if (percentage > 0) {
        const startAngle = currentAngle
        const endAngle = currentAngle + angle
        
        const x1 = centerX + radius * Math.cos((startAngle * Math.PI) / 180)
        const y1 = centerY + radius * Math.sin((startAngle * Math.PI) / 180)
        const x2 = centerX + radius * Math.cos((endAngle * Math.PI) / 180)
        const y2 = centerY + radius * Math.sin((endAngle * Math.PI) / 180)
        
        const largeArcFlag = angle > 180 ? 1 : 0
        
        paths += `<path d="M ${centerX} ${centerY} L ${x1} ${y1} A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x2} ${y2} Z" fill="${color}" stroke="white" stroke-width="2"/>`
        
        legends += `
          <div class="flex items-center mb-2">
            <div class="w-3 h-3 rounded-full mr-2" style="background-color: ${color}"></div>
            <span class="text-sm text-gray-700">${item.name}: ${item.value} (${percentage.toFixed(1)}%)</span>
          </div>
        `
        
        currentAngle += angle
      }
    })

    return `
      <div class="flex items-center justify-between">
        <svg width="240" height="240" viewBox="0 0 240 240">
          ${paths}
        </svg>
        <div class="ml-6">
          ${legends}
        </div>
      </div>
    `
  }

  generateBarChartSVG(data, options) {
    if (!data || data.length === 0) {
      return '<div class="flex items-center justify-center h-full text-gray-500">No data available</div>'
    }

    const maxValue = Math.max(...data.map(item => item.value))
    const barWidth = 40
    const barSpacing = 20
    const chartHeight = 200
    const chartWidth = data.length * (barWidth + barSpacing)

    let bars = ''
    let labels = ''

    data.forEach((item, index) => {
      const barHeight = (item.value / maxValue) * chartHeight
      const x = index * (barWidth + barSpacing)
      const y = chartHeight - barHeight

      bars += `
        <rect x="${x}" y="${y}" width="${barWidth}" height="${barHeight}" 
              fill="${options.color || '#3B82F6'}" rx="4"/>
        <text x="${x + barWidth/2}" y="${y - 5}" text-anchor="middle" 
              class="text-xs fill-gray-600">${item.value}</text>
      `

      labels += `
        <text x="${x + barWidth/2}" y="${chartHeight + 20}" text-anchor="middle" 
              class="text-xs fill-gray-700">${item.name}</text>
      `
    })

    return `
      <svg width="${chartWidth + 40}" height="${chartHeight + 40}" viewBox="0 0 ${chartWidth + 40} ${chartHeight + 40}">
        <g transform="translate(20, 10)">
          ${bars}
          ${labels}
        </g>
      </svg>
    `
  }

  generateDonutChartSVG(data, options) {
    if (!data || data.length === 0) {
      return '<div class="flex items-center justify-center h-full text-gray-500">No data available</div>'
    }

    const total = data.reduce((sum, item) => sum + item.value, 0)
    let currentAngle = 0
    const outerRadius = 80
    const innerRadius = 50
    const centerX = 120
    const centerY = 120

    let paths = ''
    let legends = ''

    data.forEach((item, index) => {
      const percentage = (item.value / total) * 100
      const angle = (item.value / total) * 360
      const color = options.colors?.[index % options.colors.length] || '#3B82F6'

      if (percentage > 0) {
        const startAngle = currentAngle
        const endAngle = currentAngle + angle
        
        const x1Outer = centerX + outerRadius * Math.cos((startAngle * Math.PI) / 180)
        const y1Outer = centerY + outerRadius * Math.sin((startAngle * Math.PI) / 180)
        const x2Outer = centerX + outerRadius * Math.cos((endAngle * Math.PI) / 180)
        const y2Outer = centerY + outerRadius * Math.sin((endAngle * Math.PI) / 180)
        
        const x1Inner = centerX + innerRadius * Math.cos((startAngle * Math.PI) / 180)
        const y1Inner = centerY + innerRadius * Math.sin((startAngle * Math.PI) / 180)
        const x2Inner = centerX + innerRadius * Math.cos((endAngle * Math.PI) / 180)
        const y2Inner = centerY + innerRadius * Math.sin((endAngle * Math.PI) / 180)
        
        const largeArcFlag = angle > 180 ? 1 : 0
        
        paths += `
          <path d="M ${x1Outer} ${y1Outer} A ${outerRadius} ${outerRadius} 0 ${largeArcFlag} 1 ${x2Outer} ${y2Outer} 
                   L ${x2Inner} ${y2Inner} A ${innerRadius} ${innerRadius} 0 ${largeArcFlag} 0 ${x1Inner} ${y1Inner} Z" 
                fill="${color}" stroke="white" stroke-width="2"/>
        `
        
        legends += `
          <div class="flex items-center mb-2">
            <div class="w-3 h-3 rounded-full mr-2" style="background-color: ${color}"></div>
            <span class="text-sm text-gray-700">${item.name}: ${item.value}</span>
          </div>
        `
        
        currentAngle += angle
      }
    })

    return `
      <div class="flex items-center justify-between">
        <svg width="240" height="240" viewBox="0 0 240 240">
          ${paths}
          <text x="${centerX}" y="${centerY}" text-anchor="middle" class="text-lg font-semibold fill-gray-700">
            ${total}
          </text>
          <text x="${centerX}" y="${centerY + 20}" text-anchor="middle" class="text-sm fill-gray-500">
            Total
          </text>
        </svg>
        <div class="ml-6">
          ${legends}
        </div>
      </div>
    `
  }

  async updateAnalytics(filters) {
    try {
      const params = new URLSearchParams({
        date_range: this.dateRangeValue,
        campaign_filter: this.campaignFilterValue,
        ...filters
      })

      const response = await fetch(`/vibe_analytics?${params}`, {
        headers: {
          'Accept': 'application/json',
          'X-Requested-With': 'XMLHttpRequest'
        }
      })

      if (response.ok) {
        const data = await response.json()
        this.dataValue = data
        this.initializeCharts()
        this.updateMetrics(data)
      }
    } catch (error) {
      console.error('Failed to update analytics:', error)
    }
  }

  updateMetrics(data) {
    // Update overview metrics
    const metrics = data.overview || {}
    
    this.updateMetric('total_campaigns', metrics.total_campaigns)
    this.updateMetric('approval_rate', `${metrics.approval_rate}%`)
    this.updateMetric('avg_cultural_score', metrics.avg_cultural_score)
    this.updateMetric('avg_authenticity_score', metrics.avg_authenticity_score)
  }

  updateMetric(metricName, value) {
    const element = document.querySelector(`[data-metric="${metricName}"]`)
    if (element) {
      element.textContent = value
    }
  }

  humanizeTone(tone) {
    return tone.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ')
  }

  humanizeStatus(status) {
    return status.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ')
  }
}
