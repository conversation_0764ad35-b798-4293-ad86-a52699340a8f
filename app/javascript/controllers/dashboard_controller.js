import { Controller } from "@hotwired/stimulus"

// Dashboard controller for interactive features
export default class extends Controller {
  static targets = ["card", "progressBar", "metric"]
  static values = { 
    refreshInterval: { type: Number, default: 30000 },
    animationDelay: { type: Number, default: 100 }
  }

  connect() {
    this.animateCards()
    this.startProgressBars()
    this.startMetricCounters()
    this.scheduleRefresh()
    
    // Add intersection observer for scroll animations
    this.setupScrollAnimations()
  }

  disconnect() {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer)
    }
  }

  // Animate dashboard cards on load
  animateCards() {
    this.cardTargets.forEach((card, index) => {
      card.style.opacity = '0'
      card.style.transform = 'translateY(20px)'
      
      setTimeout(() => {
        card.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)'
        card.style.opacity = '1'
        card.style.transform = 'translateY(0)'
      }, index * this.animationDelayValue)
    })
  }

  // Animate progress bars
  startProgressBars() {
    this.progressBarTargets.forEach(bar => {
      const width = bar.dataset.width || '0'
      bar.style.width = '0%'
      
      setTimeout(() => {
        bar.style.transition = 'width 1.5s cubic-bezier(0.4, 0, 0.2, 1)'
        bar.style.width = width + '%'
      }, 500)
    })
  }

  // Animate metric counters
  startMetricCounters() {
    this.metricTargets.forEach(metric => {
      const finalValue = parseInt(metric.textContent.replace(/[^\d]/g, ''))
      const increment = finalValue / 60 // 60 steps for smooth animation
      let currentValue = 0
      
      const counter = setInterval(() => {
        currentValue += increment
        if (currentValue >= finalValue) {
          metric.textContent = metric.textContent.replace(/\d+/, finalValue)
          clearInterval(counter)
        } else {
          metric.textContent = metric.textContent.replace(/\d+/, Math.floor(currentValue))
        }
      }, 25)
    })
  }

  // Setup scroll-triggered animations
  setupScrollAnimations() {
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animate-slide-in')
        }
      })
    }, {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    })

    // Observe all card elements
    this.cardTargets.forEach(card => {
      observer.observe(card)
    })
  }

  // Schedule periodic refresh for live data
  scheduleRefresh() {
    this.refreshTimer = setInterval(() => {
      this.refreshMetrics()
    }, this.refreshIntervalValue)
  }

  // Refresh dashboard metrics (simulate real-time updates)
  refreshMetrics() {
    // Add subtle indicator that data is refreshing
    const indicators = document.querySelectorAll('.pulse-indicator')
    indicators.forEach(indicator => {
      indicator.style.animation = 'pulse-glow 0.5s ease-in-out'
      setTimeout(() => {
        indicator.style.animation = 'pulse-glow 2s infinite'
      }, 500)
    })

    // Here you could fetch updated data via AJAX
    // For now, we'll just add a visual refresh indicator
    console.log('Dashboard metrics refreshed at:', new Date().toLocaleTimeString())
  }

  // Handle card interactions
  cardHover(event) {
    const card = event.currentTarget
    card.style.transform = 'translateY(-4px) scale(1.02)'
    card.style.boxShadow = '0 20px 40px rgba(0, 0, 0, 0.15)'
  }

  cardLeave(event) {
    const card = event.currentTarget
    card.style.transform = 'translateY(0) scale(1)'
    card.style.boxShadow = ''
  }

  // Handle quick actions
  createCampaign(event) {
    // Add loading state to button
    const button = event.currentTarget
    const originalText = button.innerHTML
    
    button.innerHTML = `
      <svg class="w-5 h-5 mr-2 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
      </svg>
      Creating...
    `
    
    // Reset after navigation (this will be interrupted by page change)
    setTimeout(() => {
      button.innerHTML = originalText
    }, 2000)
  }

  // Format numbers with animations
  formatNumber(num) {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M'
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K'
    }
    return num.toString()
  }

  // Add tooltip functionality
  showTooltip(event) {
    const tooltip = event.currentTarget.querySelector('.tooltip')
    if (tooltip) {
      tooltip.style.opacity = '1'
      tooltip.style.transform = 'translateY(-8px)'
    }
  }

  hideTooltip(event) {
    const tooltip = event.currentTarget.querySelector('.tooltip')
    if (tooltip) {
      tooltip.style.opacity = '0'
      tooltip.style.transform = 'translateY(0)'
    }
  }
}
