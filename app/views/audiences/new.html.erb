<% content_for :title, @audience.persisted? ? "Edit #{@audience.name}" : "New Audience" %>

<div class="min-h-screen bg-gray-50">
  <!-- Header -->
  <div class="bg-white shadow-sm border-b border-gray-100">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center py-6">
        <div class="flex items-center">
          <%= link_to audiences_path, class: "text-gray-400 hover:text-gray-600 mr-4" do %>
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
          <% end %>
          <div>
            <h1 class="text-2xl font-bold text-gray-900">
              <%= @audience.persisted? ? "Edit #{@audience.name}" : "Create New Audience" %>
            </h1>
            <p class="mt-1 text-sm text-gray-600">
              <%= @audience.persisted? ? "Update audience segment details" : "Define a new target audience segment for your campaigns" %>
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <%= form_with model: @audience, local: true, class: "space-y-8" do |form| %>
      <% if @audience.errors.any? %>
        <div class="bg-red-50 border border-red-200 rounded-lg p-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-red-800">
                There were <%= pluralize(@audience.errors.count, "error") %> with your submission:
              </h3>
              <div class="mt-2 text-sm text-red-700">
                <ul class="list-disc pl-5 space-y-1">
                  <% @audience.errors.full_messages.each do |message| %>
                    <li><%= message %></li>
                  <% end %>
                </ul>
              </div>
            </div>
          </div>
        </div>
      <% end %>

      <!-- Basic Information -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-6">Basic Information</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <%= form.label :name, class: "block text-sm font-medium text-gray-700 mb-2" %>
            <%= form.text_field :name, class: "w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500", placeholder: "e.g., Young Urban Professionals" %>
          </div>
          
          <div>
            <%= form.label :primary_language, class: "block text-sm font-medium text-gray-700 mb-2" %>
            <%= form.select :primary_language, 
                options_for_select([
                  ['English', 'en'],
                  ['Spanish', 'es'],
                  ['French', 'fr'],
                  ['German', 'de'],
                  ['Chinese', 'zh'],
                  ['Japanese', 'ja'],
                  ['Arabic', 'ar'],
                  ['Portuguese', 'pt'],
                  ['Russian', 'ru'],
                  ['Hindi', 'hi']
                ], @audience.primary_language),
                { prompt: 'Select primary language' },
                { class: "w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" } %>
          </div>
        </div>
        
        <div class="mt-6">
          <%= form.label :description, class: "block text-sm font-medium text-gray-700 mb-2" %>
          <%= form.text_area :description, rows: 3, class: "w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500", placeholder: "Describe this audience segment..." %>
        </div>
      </div>

      <!-- Demographics -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-6">Demographics</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <%= form.label :cultural_context, class: "block text-sm font-medium text-gray-700 mb-2" %>
            <%= form.select :cultural_context,
                options_for_select([
                  ['Western', 'western'],
                  ['Eastern', 'eastern'],
                  ['Latin American', 'latin_american'],
                  ['African', 'african'],
                  ['Middle Eastern', 'middle_eastern'],
                  ['Asian Pacific', 'asian_pacific']
                ], @audience.cultural_context),
                { prompt: 'Select cultural context' },
                { class: "w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" } %>
          </div>
          
          <div>
            <%= form.label :target_demographics, class: "block text-sm font-medium text-gray-700 mb-2" %>
            <%= form.text_field :target_demographics, class: "w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500", placeholder: "e.g., 25-35 year old professionals" %>
          </div>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
          <div>
            <%= form.label :age_range_min, "Minimum Age", class: "block text-sm font-medium text-gray-700 mb-2" %>
            <%= form.number_field :age_range_min, class: "w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500", min: 13, max: 120 %>
          </div>
          
          <div>
            <%= form.label :age_range_max, "Maximum Age", class: "block text-sm font-medium text-gray-700 mb-2" %>
            <%= form.number_field :age_range_max, class: "w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500", min: 13, max: 120 %>
          </div>
        </div>
        
        <div class="mt-6">
          <%= form.label :geographic_regions, "Geographic Regions (comma-separated)", class: "block text-sm font-medium text-gray-700 mb-2" %>
          <%= form.text_field :geographic_regions, class: "w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500", placeholder: "e.g., North America, Europe, Asia" %>
        </div>
      </div>

      <!-- Interests & Behavior -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-6">Interests & Behavior</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <%= form.label :interests, "Interests (comma-separated)", class: "block text-sm font-medium text-gray-700 mb-2" %>
            <%= form.text_area :interests, rows: 3, class: "w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500", placeholder: "e.g., technology, fitness, travel, food" %>
          </div>
          
          <div>
            <%= form.label :behavioral_traits, "Behavioral Traits (comma-separated)", class: "block text-sm font-medium text-gray-700 mb-2" %>
            <%= form.text_area :behavioral_traits, rows: 3, class: "w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500", placeholder: "e.g., early adopters, price-conscious, brand loyal" %>
          </div>
        </div>
        
        <div class="mt-6">
          <%= form.label :engagement_patterns, class: "block text-sm font-medium text-gray-700 mb-2" %>
          <%= form.text_area :engagement_patterns, rows: 2, class: "w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500", placeholder: "Describe typical engagement patterns and preferences..." %>
        </div>
      </div>

      <!-- Cultural Attributes -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-6">Cultural Attributes</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <%= form.label :cultural_values, class: "block text-sm font-medium text-gray-700 mb-2" %>
            <%= form.text_area :cultural_values, rows: 3, class: "w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500", placeholder: "Describe important cultural values and beliefs..." %>
          </div>
          
          <div>
            <%= form.label :communication_preferences, class: "block text-sm font-medium text-gray-700 mb-2" %>
            <%= form.text_area :communication_preferences, rows: 3, class: "w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500", placeholder: "Describe preferred communication styles and channels..." %>
          </div>
        </div>
        
        <div class="mt-6">
          <%= form.label :secondary_languages, "Secondary Languages (comma-separated)", class: "block text-sm font-medium text-gray-700 mb-2" %>
          <%= form.text_field :secondary_languages, class: "w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500", placeholder: "e.g., Spanish, French, Mandarin" %>
        </div>
      </div>

      <!-- Form Actions -->
      <div class="flex items-center justify-end space-x-4 pt-6">
        <%= link_to audiences_path, class: "px-6 py-2 border border-gray-300 text-gray-700 font-medium rounded-lg hover:bg-gray-50 transition-colors" do %>
          Cancel
        <% end %>
        
        <%= form.submit (@audience.persisted? ? "Update Audience" : "Create Audience"), class: "px-6 py-2 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors" %>
      </div>
    <% end %>
  </div>
</div>
