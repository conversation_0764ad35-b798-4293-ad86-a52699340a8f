<% content_for :title, "Audiences" %>

<div class="min-h-screen bg-gray-50">
  <!-- Header -->
  <div class="bg-white shadow-sm border-b border-gray-100">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center py-6">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">Audiences</h1>
          <p class="mt-1 text-sm text-gray-600">Manage and analyze your target audience segments</p>
        </div>
        
        <div class="flex items-center space-x-4">
          <%= link_to new_audience_path, class: "inline-flex items-center px-4 py-2 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors" do %>
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            New Audience
          <% end %>
        </div>
      </div>
    </div>
  </div>

  <!-- Filters -->
  <div class="bg-white border-b border-gray-100">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
      <%= form_with url: audiences_path, method: :get, local: true, class: "flex flex-wrap items-center gap-4" do |form| %>
        <!-- Search -->
        <div class="flex-1 min-w-64">
          <%= form.text_field :search, 
              value: params[:search],
              placeholder: "Search audiences...",
              class: "w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" %>
        </div>

        <!-- Cultural Context Filter -->
        <div class="min-w-48">
          <%= form.select :cultural_context, 
              options_for_select([
                ['All Cultural Contexts', ''],
                ['Western', 'western'],
                ['Eastern', 'eastern'],
                ['Latin American', 'latin_american'],
                ['African', 'african'],
                ['Middle Eastern', 'middle_eastern'],
                ['Asian Pacific', 'asian_pacific']
              ], params[:cultural_context]),
              {},
              { class: "w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" } %>
        </div>

        <!-- Language Filter -->
        <div class="min-w-40">
          <%= form.select :primary_language,
              options_for_select([
                ['All Languages', ''],
                ['English', 'en'],
                ['Spanish', 'es'],
                ['French', 'fr'],
                ['German', 'de'],
                ['Chinese', 'zh'],
                ['Japanese', 'ja'],
                ['Arabic', 'ar']
              ], params[:primary_language]),
              {},
              { class: "w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" } %>
        </div>

        <!-- Sort Options -->
        <div class="min-w-40">
          <%= form.select :sort_by,
              options_for_select([
                ['Recently Created', 'created_at'],
                ['Name', 'name'],
                ['Campaign Count', 'campaigns_count'],
                ['Cultural Alignment', 'cultural_alignment']
              ], params[:sort_by]),
              {},
              { class: "w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" } %>
        </div>

        <!-- Submit Button -->
        <%= form.submit "Filter", class: "px-4 py-2 bg-gray-600 text-white font-medium rounded-md hover:bg-gray-700 transition-colors" %>
      <% end %>
    </div>
  </div>

  <!-- Main Content -->
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <% if @audiences.any? %>
      <!-- Audiences Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <% @audiences.each do |audience| %>
          <div class="bg-white rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-shadow">
            <!-- Header -->
            <div class="p-6 border-b border-gray-100">
              <div class="flex items-start justify-between">
                <div class="flex-1">
                  <h3 class="text-lg font-semibold text-gray-900 mb-2">
                    <%= link_to audience.name, audience_path(audience), class: "hover:text-blue-600 transition-colors" %>
                  </h3>
                  <p class="text-sm text-gray-600 line-clamp-2">
                    <%= audience.description %>
                  </p>
                </div>
                
                <!-- Actions Dropdown -->
                <div class="relative ml-4" data-controller="dropdown">
                  <button data-action="click->dropdown#toggle" class="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
                    </svg>
                  </button>
                  
                  <div data-dropdown-target="menu" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-10">
                    <%= link_to audience_path(audience), class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50" do %>
                      View Details
                    <% end %>
                    <%= link_to edit_audience_path(audience), class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50" do %>
                      Edit
                    <% end %>
                    <%= link_to audience_path(audience),
                        data: {
                          turbo_method: :delete,
                          turbo_confirm: "Are you sure you want to delete this audience?"
                        },
                        class: "block px-4 py-2 text-sm text-red-600 hover:bg-red-50" do %>
                      Delete
                    <% end %>
                  </div>
                </div>
              </div>
            </div>

            <!-- Demographics -->
            <div class="p-6 border-b border-gray-100">
              <h4 class="text-sm font-medium text-gray-900 mb-3">Demographics</h4>
              <div class="space-y-2">
                <div class="flex items-center text-sm">
                  <svg class="w-4 h-4 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                  <span class="text-gray-600">Cultural Context:</span>
                  <span class="ml-1 text-gray-900"><%= audience.cultural_context&.humanize || 'Not specified' %></span>
                </div>
                
                <div class="flex items-center text-sm">
                  <svg class="w-4 h-4 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"></path>
                  </svg>
                  <span class="text-gray-600">Language:</span>
                  <span class="ml-1 text-gray-900"><%= audience.primary_language || 'Not specified' %></span>
                </div>
                
                <div class="flex items-center text-sm">
                  <svg class="w-4 h-4 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                  </svg>
                  <span class="text-gray-600">Age Range:</span>
                  <span class="ml-1 text-gray-900">
                    <% if audience.age_range_min && audience.age_range_max %>
                      <%= audience.age_range_min %>-<%= audience.age_range_max %>
                    <% else %>
                      Not specified
                    <% end %>
                  </span>
                </div>
              </div>
            </div>

            <!-- Metrics -->
            <div class="p-6">
              <div class="grid grid-cols-2 gap-4">
                <div class="text-center">
                  <div class="text-2xl font-bold text-gray-900">
                    <%= audience.campaigns.count %>
                  </div>
                  <div class="text-xs text-gray-500">Campaigns</div>
                </div>
                
                <div class="text-center">
                  <div class="text-2xl font-bold text-gray-900">
                    <%= audience.cultural_alignment_score || 0 %>
                  </div>
                  <div class="text-xs text-gray-500">Cultural Score</div>
                </div>
              </div>
              
              <!-- Engagement Score Bar -->
              <div class="mt-4">
                <div class="flex items-center justify-between text-sm mb-1">
                  <span class="text-gray-600">Engagement Score</span>
                  <span class="text-gray-900 font-medium"><%= audience.engagement_score %>%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                  <div class="bg-blue-600 h-2 rounded-full" style="width: <%= audience.engagement_score %>%"></div>
                </div>
              </div>
            </div>

            <!-- Footer -->
            <div class="px-6 py-3 bg-gray-50 rounded-b-xl">
              <div class="flex items-center justify-between text-xs text-gray-500">
                <span>Created <%= time_ago_in_words(audience.created_at) %> ago</span>
                <span>by <%= audience.created_by&.email || 'Unknown' %></span>
              </div>
            </div>
          </div>
        <% end %>
      </div>
    <% else %>
      <!-- Empty State -->
      <div class="text-center py-16">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
        </svg>
        <h3 class="mt-4 text-lg font-medium text-gray-900">No audiences found</h3>
        <p class="mt-2 text-sm text-gray-500">
          <% if @search_term.present? || @filter_params.values.any?(&:present?) %>
            Try adjusting your search criteria or filters.
          <% else %>
            Get started by creating your first audience segment.
          <% end %>
        </p>
        <div class="mt-6">
          <%= link_to new_audience_path, class: "inline-flex items-center px-4 py-2 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors" do %>
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Create New Audience
          <% end %>
        </div>
      </div>
    <% end %>
  </div>
</div>

<% content_for :javascript do %>
  <script>
    // Helper function for engagement score calculation (placeholder)
    function calculateEngagementScore(audience) {
      // This would be calculated based on actual engagement metrics
      return Math.floor(Math.random() * 100);
    }
  </script>
<% end %>
