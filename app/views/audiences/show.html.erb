<% content_for :title, @audience.name %>

<div class="min-h-screen bg-gray-50">
  <!-- Header -->
  <div class="bg-white shadow-sm border-b border-gray-100">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center py-6">
        <div class="flex items-center">
          <%= link_to audiences_path, class: "text-gray-400 hover:text-gray-600 mr-4" do %>
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
          <% end %>
          <div>
            <h1 class="text-2xl font-bold text-gray-900"><%= @audience.name %></h1>
            <p class="mt-1 text-sm text-gray-600"><%= @audience.description %></p>
          </div>
        </div>
        
        <div class="flex items-center space-x-4">
          <%= link_to edit_audience_path(@audience), class: "inline-flex items-center px-4 py-2 bg-white border border-gray-300 text-gray-700 font-medium rounded-lg hover:bg-gray-50 transition-colors" do %>
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
            </svg>
            Edit Audience
          <% end %>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Overview Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <!-- Total Campaigns -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Total Campaigns</p>
            <p class="text-2xl font-bold text-gray-900"><%= @audience_analytics[:overview][:total_campaigns] %></p>
          </div>
        </div>
      </div>

      <!-- Active Campaigns -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Active Campaigns</p>
            <p class="text-2xl font-bold text-gray-900"><%= @audience_analytics[:overview][:active_campaigns] %></p>
          </div>
        </div>
      </div>

      <!-- Engagement Rate -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Avg Engagement</p>
            <p class="text-2xl font-bold text-gray-900"><%= @audience_analytics[:overview][:avg_engagement_rate] %>%</p>
          </div>
        </div>
      </div>

      <!-- Cultural Alignment -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Cultural Alignment</p>
            <p class="text-2xl font-bold text-gray-900"><%= @audience_analytics[:overview][:cultural_alignment_score] %></p>
          </div>
        </div>
      </div>
    </div>

    <!-- Demographics and Analytics -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
      <!-- Demographics Profile -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-6">Demographics Profile</h3>
        
        <div class="space-y-4">
          <div>
            <label class="text-sm font-medium text-gray-600">Cultural Context</label>
            <p class="text-gray-900"><%= @audience.cultural_context&.humanize || 'Not specified' %></p>
          </div>
          
          <div>
            <label class="text-sm font-medium text-gray-600">Primary Language</label>
            <p class="text-gray-900"><%= @audience.primary_language || 'Not specified' %></p>
          </div>
          
          <div>
            <label class="text-sm font-medium text-gray-600">Age Range</label>
            <p class="text-gray-900">
              <% if @audience.age_range_min && @audience.age_range_max %>
                <%= @audience.age_range_min %>-<%= @audience.age_range_max %> years
              <% else %>
                Not specified
              <% end %>
            </p>
          </div>
          
          <div>
            <label class="text-sm font-medium text-gray-600">Geographic Regions</label>
            <p class="text-gray-900">
              <% if @audience.geographic_regions.present? %>
                <%= @audience.geographic_regions.join(', ') %>
              <% else %>
                Not specified
              <% end %>
            </p>
          </div>
          
          <div>
            <label class="text-sm font-medium text-gray-600">Interests</label>
            <p class="text-gray-900">
              <% if @audience.interests.present? %>
                <%= @audience.interests.join(', ') %>
              <% else %>
                Not specified
              <% end %>
            </p>
          </div>
        </div>
      </div>

      <!-- Cultural Alignment -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-6">Cultural Alignment</h3>
        
        <div class="space-y-4">
          <div>
            <div class="flex items-center justify-between mb-2">
              <span class="text-sm font-medium text-gray-600">Alignment Score</span>
              <span class="text-lg font-bold text-gray-900"><%= @audience_analytics[:cultural_alignment][:alignment_score] %></span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div class="bg-purple-600 h-2 rounded-full" style="width: <%= @audience_analytics[:cultural_alignment][:alignment_score] %>%"></div>
            </div>
          </div>
          
          <div>
            <div class="flex items-center justify-between mb-2">
              <span class="text-sm font-medium text-gray-600">Vibe Approval Rate</span>
              <span class="text-lg font-bold text-gray-900"><%= @audience_analytics[:cultural_alignment][:vibe_approval_rate] %>%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div class="bg-green-600 h-2 rounded-full" style="width: <%= @audience_analytics[:cultural_alignment][:vibe_approval_rate] %>%"></div>
            </div>
          </div>
          
          <div>
            <label class="text-sm font-medium text-gray-600">Cultural Values</label>
            <p class="text-gray-900 text-sm"><%= @audience.cultural_values || 'Not specified' %></p>
          </div>
          
          <div>
            <label class="text-sm font-medium text-gray-600">Communication Preferences</label>
            <p class="text-gray-900 text-sm"><%= @audience.communication_preferences || 'Not specified' %></p>
          </div>
        </div>
      </div>

      <!-- Vibe Compatibility -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-6">Vibe Compatibility</h3>
        
        <div class="space-y-4">
          <div>
            <div class="flex items-center justify-between mb-2">
              <span class="text-sm font-medium text-gray-600">Compatibility Score</span>
              <span class="text-lg font-bold text-gray-900"><%= @audience_analytics[:vibe_compatibility][:compatibility_score] %>%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div class="bg-blue-600 h-2 rounded-full" style="width: <%= @audience_analytics[:vibe_compatibility][:compatibility_score] %>%"></div>
            </div>
          </div>
          
          <div>
            <label class="text-sm font-medium text-gray-600 mb-2 block">Vibe Status Distribution</label>
            <div class="space-y-2">
              <% @audience_analytics[:vibe_compatibility][:vibe_status_distribution].each do |status, count| %>
                <div class="flex items-center justify-between text-sm">
                  <span class="text-gray-600"><%= status.humanize %></span>
                  <span class="font-medium text-gray-900"><%= count %></span>
                </div>
              <% end %>
            </div>
          </div>
          
          <div>
            <label class="text-sm font-medium text-gray-600 mb-2 block">Top Emotional Tones</label>
            <div class="space-y-2">
              <% @audience_analytics[:vibe_compatibility][:emotional_tone_distribution].first(3).each do |tone, count| %>
                <div class="flex items-center justify-between text-sm">
                  <span class="text-gray-600"><%= tone.humanize %></span>
                  <span class="font-medium text-gray-900"><%= count %></span>
                </div>
              <% end %>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Recent Campaigns -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100">
      <div class="px-6 py-4 border-b border-gray-100">
        <h3 class="text-lg font-semibold text-gray-900">Recent Campaigns</h3>
      </div>
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Campaign</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Vibe Status</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cultural Score</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <% @campaigns.each do |campaign| %>
              <tr class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm font-medium text-gray-900">
                    <%= link_to campaign.name, campaign_path(campaign), class: "text-blue-600 hover:text-blue-800" %>
                  </div>
                  <div class="text-sm text-gray-500"><%= campaign.description&.truncate(50) %></div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                    <%= case campaign.status
                        when 'active' then 'bg-green-100 text-green-800'
                        when 'paused' then 'bg-yellow-100 text-yellow-800'
                        when 'completed' then 'bg-blue-100 text-blue-800'
                        else 'bg-gray-100 text-gray-800'
                        end %>">
                    <%= campaign.status.humanize %>
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                    <%= case campaign.vibe_status
                        when 'vibe_approved' then 'bg-green-100 text-green-800'
                        when 'vibe_flagged' then 'bg-red-100 text-red-800'
                        when 'analyzing' then 'bg-yellow-100 text-yellow-800'
                        else 'bg-gray-100 text-gray-800'
                        end %>">
                    <%= campaign.vibe_status&.humanize || 'Pending' %>
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  <%= campaign.cultural_relevance_score || '-' %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <%= time_ago_in_words(campaign.created_at) %> ago
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <%= link_to campaign_path(campaign), class: "text-blue-600 hover:text-blue-900" do %>
                    View
                  <% end %>
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>
