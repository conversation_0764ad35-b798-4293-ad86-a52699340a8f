<!-- Modern AI Marketing Hub Landing Page -->
<% content_for :title, "Transform Your Marketing with AI" %>

<!-- Custom Styles for Landing Page -->
<style>
  @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
  
  .gradient-text {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .glass-effect {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.18);
  }
  
  .floating-element {
    animation: float 6s ease-in-out infinite;
  }
  
  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
  }
  
  .glow-effect {
    box-shadow: 0 0 40px rgba(59, 130, 246, 0.15);
  }
  
  .hover-lift {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  .hover-lift:hover {
    transform: translateY(-8px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.1);
  }
  
  .blob {
    border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
    animation: blob 20s infinite;
  }
  
  @keyframes blob {
    0%, 100% { border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%; }
    25% { border-radius: 58% 42% 75% 25% / 76% 46% 54% 24%; }
    50% { border-radius: 50% 50% 33% 67% / 55% 27% 73% 45%; }
    75% { border-radius: 33% 67% 58% 42% / 63% 68% 32% 37%; }
  }
</style>

<!-- Hero Section -->
<section class="min-h-screen relative overflow-hidden bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
  <!-- Background Elements -->
  <div class="absolute inset-0 overflow-hidden">
    <div class="absolute -top-40 -right-40 w-80 h-80 bg-blue-400 rounded-full opacity-20 blob"></div>
    <div class="absolute -bottom-40 -left-40 w-96 h-96 bg-purple-400 rounded-full opacity-15 blob" style="animation-delay: -10s;"></div>
    <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-indigo-300 rounded-full opacity-10 blob" style="animation-delay: -5s;"></div>
  </div>
  
  <!-- Hero Content -->
  <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-20">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
      <!-- Left Column -->
      <div class="text-center lg:text-left">
        <!-- Badge -->
        <div class="inline-flex items-center space-x-2 glass-effect rounded-full px-4 py-2 mb-8 shadow-sm border border-white/20">
          <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
          <span class="text-sm font-medium text-gray-700">10,000+ businesses trust us</span>
          <div class="flex space-x-1">
            <% 5.times do %>
              <svg class="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
              </svg>
            <% end %>
            <span class="text-sm text-gray-600">4.9/5</span>
          </div>
        </div>
        
        <!-- Main Headline -->
        <h1 class="text-5xl lg:text-7xl font-bold leading-tight mb-6">
          <span class="text-gray-900">Transform Your</span><br>
          <span class="gradient-text">Marketing with AI</span>
        </h1>
        
        <!-- Subheadline -->
        <p class="text-xl lg:text-2xl text-gray-600 mb-8 leading-relaxed max-w-2xl">
          Automate campaigns, optimize performance, and scale your business with our AI-powered marketing platform. 
          <span class="font-semibold text-blue-600">Get 3x more leads</span> in 30 days or get your money back.
        </p>
        
        <!-- Stats -->
        <div class="grid grid-cols-3 gap-6 mb-10">
          <div class="text-center">
            <div class="text-3xl font-bold text-blue-600">312%</div>
            <div class="text-sm text-gray-500">Average ROI</div>
          </div>
          <div class="text-center">
            <div class="text-3xl font-bold text-green-600">30 days</div>
            <div class="text-sm text-gray-500">To see results</div>
          </div>
          <div class="text-center">
            <div class="text-3xl font-bold text-purple-600">10k+</div>
            <div class="text-sm text-gray-500">Happy customers</div>
          </div>
        </div>
        
        <!-- CTA Buttons -->
        <div class="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start mb-8">
          <%= link_to new_user_registration_path, 
              class: "group bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-full text-lg font-semibold hover:from-blue-700 hover:to-purple-700 transition-all transform hover:scale-105 shadow-xl glow-effect" do %>
            <span class="flex items-center justify-center space-x-2">
              <span>Start Free Trial</span>
              <svg class="w-5 h-5 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
              </svg>
            </span>
          <% end %>
          
          <button class="group glass-effect text-gray-700 px-8 py-4 rounded-full text-lg font-semibold hover:bg-white transition-all">
            <span class="flex items-center justify-center space-x-2">
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path>
              </svg>
              <span>Watch Demo</span>
            </span>
          </button>
        </div>
        
        <!-- Trust Indicators -->
        <div class="flex items-center justify-center lg:justify-start space-x-6 text-sm text-gray-500">
          <div class="flex items-center space-x-1">
            <svg class="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>
            <span>No credit card</span>
          </div>
          <div class="flex items-center space-x-1">
            <svg class="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>
            <span>14-day free trial</span>
          </div>
          <div class="flex items-center space-x-1">
            <svg class="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>
            <span>Cancel anytime</span>
          </div>
        </div>
      </div>
      
      <!-- Right Column - Product Demo -->
      <div class="relative">
        <div class="relative floating-element">
          <!-- Main Dashboard -->
          <div class="glass-effect rounded-3xl p-6 shadow-2xl">
            <!-- Header -->
            <div class="flex items-center justify-between mb-6">
              <div class="flex items-center space-x-3">
                <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                <div class="w-3 h-3 bg-green-500 rounded-full"></div>
              </div>
              <div class="text-sm text-gray-500">AI Marketing Dashboard</div>
            </div>
            
            <!-- Stats Cards -->
            <div class="grid grid-cols-2 gap-4 mb-6">
              <div class="bg-gradient-to-br from-blue-50 to-blue-100 p-4 rounded-2xl">
                <div class="text-2xl font-bold text-blue-600">312%</div>
                <div class="text-sm text-blue-600/70">ROI Increase</div>
                <div class="w-full bg-blue-200 rounded-full h-1 mt-2">
                  <div class="bg-blue-500 h-1 rounded-full w-3/4"></div>
                </div>
              </div>
              <div class="bg-gradient-to-br from-green-50 to-green-100 p-4 rounded-2xl">
                <div class="text-2xl font-bold text-green-600">+156K</div>
                <div class="text-sm text-green-600/70">New Leads</div>
                <div class="w-full bg-green-200 rounded-full h-1 mt-2">
                  <div class="bg-green-500 h-1 rounded-full w-4/5"></div>
                </div>
              </div>
            </div>
            
            <!-- Chart Visualization -->
            <div class="space-y-3">
              <div class="flex justify-between text-sm text-gray-600">
                <span>Campaign Performance</span>
                <span>This Month</span>
              </div>
              <div class="space-y-2">
                <div class="flex items-center space-x-3">
                  <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <div class="flex-1 bg-blue-100 rounded-full h-2">
                    <div class="bg-blue-500 h-2 rounded-full w-4/5"></div>
                  </div>
                  <span class="text-sm text-gray-600">80%</span>
                </div>
                <div class="flex items-center space-x-3">
                  <div class="w-2 h-2 bg-purple-500 rounded-full"></div>
                  <div class="flex-1 bg-purple-100 rounded-full h-2">
                    <div class="bg-purple-500 h-2 rounded-full w-3/5"></div>
                  </div>
                  <span class="text-sm text-gray-600">65%</span>
                </div>
                <div class="flex items-center space-x-3">
                  <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                  <div class="flex-1 bg-green-100 rounded-full h-2">
                    <div class="bg-green-500 h-2 rounded-full w-2/3"></div>
                  </div>
                  <span class="text-sm text-gray-600">70%</span>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Floating Elements -->
          <div class="absolute -top-4 -right-4 bg-yellow-400 rounded-2xl p-3 shadow-lg">
            <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clip-rule="evenodd"></path>
            </svg>
          </div>
          
          <div class="absolute -bottom-4 -left-4 bg-pink-500 rounded-2xl p-3 shadow-lg">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
            </svg>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Social Proof -->
<section class="py-16 bg-white">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-12">
      <h3 class="text-lg font-semibold text-gray-400 mb-8">Trusted by industry leaders worldwide</h3>
      <div class="grid grid-cols-2 md:grid-cols-5 gap-8 items-center">
        <div class="flex justify-center">
          <div class="bg-gradient-to-r from-blue-600 to-blue-700 text-white px-6 py-3 rounded-lg font-bold opacity-60 hover:opacity-100 transition-opacity">
            TechCorp
          </div>
        </div>
        <div class="flex justify-center">
          <div class="bg-gradient-to-r from-green-600 to-green-700 text-white px-6 py-3 rounded-lg font-bold opacity-60 hover:opacity-100 transition-opacity">
            GrowthCo
          </div>
        </div>
        <div class="flex justify-center">
          <div class="bg-gradient-to-r from-purple-600 to-purple-700 text-white px-6 py-3 rounded-lg font-bold opacity-60 hover:opacity-100 transition-opacity">
            ScaleUp
          </div>
        </div>
        <div class="flex justify-center">
          <div class="bg-gradient-to-r from-orange-600 to-orange-700 text-white px-6 py-3 rounded-lg font-bold opacity-60 hover:opacity-100 transition-opacity">
            Innovation
          </div>
        </div>
        <div class="flex justify-center">
          <div class="bg-gradient-to-r from-red-600 to-red-700 text-white px-6 py-3 rounded-lg font-bold opacity-60 hover:opacity-100 transition-opacity">
            FutureTech
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Features Section -->
<section id="features" class="py-24 bg-gradient-to-br from-slate-50 to-blue-50">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-20">
      <h2 class="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
        Everything you need to 
        <span class="gradient-text">scale your marketing</span>
      </h2>
      <p class="text-xl text-gray-600 max-w-3xl mx-auto">
        Replace your entire marketing stack with one AI-powered platform that works 24/7 to grow your business.
      </p>
    </div>
    
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      <!-- AI Campaign Creation -->
      <div class="group hover-lift glass-effect p-8 rounded-3xl">
        <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform">
          <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
          </svg>
        </div>
        <h3 class="text-xl font-bold text-gray-900 mb-4">AI Campaign Creation</h3>
        <p class="text-gray-600 mb-4 leading-relaxed">
          Describe your goal in plain English. Our AI creates complete campaigns with copy, images, and targeting in under 60 seconds.
        </p>
        <div class="text-sm font-semibold text-blue-600 flex items-center space-x-1">
          <span>⚡ 95% faster than manual creation</span>
        </div>
      </div>
      
      <!-- Smart Optimization -->
      <div class="group hover-lift glass-effect p-8 rounded-3xl">
        <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform">
          <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
          </svg>
        </div>
        <h3 class="text-xl font-bold text-gray-900 mb-4">Real-Time Optimization</h3>
        <p class="text-gray-600 mb-4 leading-relaxed">
          AI continuously tests and improves your campaigns. Automatically pause low-performers and scale winners.
        </p>
        <div class="text-sm font-semibold text-purple-600 flex items-center space-x-1">
          <span>📈 Average 40% performance boost</span>
        </div>
      </div>
      
      <!-- Multi-Platform Sync -->
      <div class="group hover-lift glass-effect p-8 rounded-3xl">
        <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform">
          <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
          </svg>
        </div>
        <h3 class="text-xl font-bold text-gray-900 mb-4">Multi-Platform Automation</h3>
        <p class="text-gray-600 mb-4 leading-relaxed">
          Deploy campaigns across email, social media, Google Ads, and more from one dashboard with unified tracking.
        </p>
        <div class="text-sm font-semibold text-green-600 flex items-center space-x-1">
          <span>🎯 Reach 5x more customers</span>
        </div>
      </div>
      
      <!-- Advanced Analytics -->
      <div class="group hover-lift glass-effect p-8 rounded-3xl">
        <div class="w-16 h-16 bg-gradient-to-br from-orange-500 to-orange-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform">
          <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
          </svg>
        </div>
        <h3 class="text-xl font-bold text-gray-900 mb-4">Advanced Analytics</h3>
        <p class="text-gray-600 mb-4 leading-relaxed">
          Get deep insights into customer behavior, campaign performance, and ROI with AI-powered recommendations.
        </p>
        <div class="text-sm font-semibold text-orange-600 flex items-center space-x-1">
          <span>📊 Real-time insights</span>
        </div>
      </div>
      
      <!-- Smart Personalization -->
      <div class="group hover-lift glass-effect p-8 rounded-3xl">
        <div class="w-16 h-16 bg-gradient-to-br from-pink-500 to-pink-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform">
          <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
          </svg>
        </div>
        <h3 class="text-xl font-bold text-gray-900 mb-4">Smart Personalization</h3>
        <p class="text-gray-600 mb-4 leading-relaxed">
          AI analyzes customer data to create personalized messages that increase engagement and conversions.
        </p>
        <div class="text-sm font-semibold text-pink-600 flex items-center space-x-1">
          <span>💝 2x higher engagement</span>
        </div>
      </div>
      
      <!-- Automated Workflows -->
      <div class="group hover-lift glass-effect p-8 rounded-3xl">
        <div class="w-16 h-16 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform">
          <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
          </svg>
        </div>
        <h3 class="text-xl font-bold text-gray-900 mb-4">Automated Workflows</h3>
        <p class="text-gray-600 mb-4 leading-relaxed">
          Set up complex marketing funnels that run automatically, nurturing leads and converting customers 24/7.
        </p>
        <div class="text-sm font-semibold text-indigo-600 flex items-center space-x-1">
          <span>🔄 Works while you sleep</span>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Testimonials -->
<section id="testimonials" class="py-24 bg-white">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-20">
      <h2 class="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
        Loved by <span class="gradient-text">10,000+ marketers</span>
      </h2>
      <p class="text-xl text-gray-600 max-w-3xl mx-auto">
        See how businesses like yours are transforming their marketing with AI.
      </p>
    </div>
    
    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
      <!-- Testimonial 1 -->
      <div class="hover-lift glass-effect p-8 rounded-3xl">
        <div class="flex items-center mb-4">
          <div class="flex space-x-1">
            <% 5.times do %>
              <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
              </svg>
            <% end %>
          </div>
        </div>
        <p class="text-gray-700 mb-6 leading-relaxed">
          "This platform increased our lead generation by 400% in just 2 months. The AI does things I never thought possible. It's like having a team of marketing experts working 24/7."
        </p>
        <div class="flex items-center">
          <img class="w-12 h-12 rounded-full mr-4" src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face" alt="Sarah Johnson">
          <div>
            <div class="font-semibold text-gray-900">Sarah Johnson</div>
            <div class="text-gray-500">CEO, TechStartup</div>
          </div>
        </div>
      </div>
      
      <!-- Testimonial 2 -->
      <div class="hover-lift glass-effect p-8 rounded-3xl">
        <div class="flex items-center mb-4">
          <div class="flex space-x-1">
            <% 5.times do %>
              <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
              </svg>
            <% end %>
          </div>
        </div>
        <p class="text-gray-700 mb-6 leading-relaxed">
          "We went from spending 40 hours a week on marketing to just 5 hours, while our results improved dramatically. The ROI is incredible."
        </p>
        <div class="flex items-center">
          <img class="w-12 h-12 rounded-full mr-4" src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face" alt="Mike Chen">
          <div>
            <div class="font-semibold text-gray-900">Mike Chen</div>
            <div class="text-gray-500">Marketing Director, GrowthCorp</div>
          </div>
        </div>
      </div>
      
      <!-- Testimonial 3 -->
      <div class="hover-lift glass-effect p-8 rounded-3xl">
        <div class="flex items-center mb-4">
          <div class="flex space-x-1">
            <% 5.times do %>
              <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
              </svg>
            <% end %>
          </div>
        </div>
        <p class="text-gray-700 mb-6 leading-relaxed">
          "The personalization features are mind-blowing. Our email open rates went from 18% to 47%. This AI understands our customers better than we do."
        </p>
        <div class="flex items-center">
          <img class="w-12 h-12 rounded-full mr-4" src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face" alt="David Rodriguez">
          <div>
            <div class="font-semibold text-gray-900">David Rodriguez</div>
            <div class="text-gray-500">Founder, E-commerce Plus</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- CTA Section -->
<section class="py-24 bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 text-white relative overflow-hidden">
  <!-- Background Elements -->
  <div class="absolute inset-0">
    <div class="absolute top-0 left-0 w-96 h-96 bg-white rounded-full opacity-5 blob"></div>
    <div class="absolute bottom-0 right-0 w-80 h-80 bg-white rounded-full opacity-5 blob" style="animation-delay: -10s;"></div>
  </div>
  
  <div class="relative max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
    <h2 class="text-4xl lg:text-6xl font-bold mb-8">
      Ready to transform your marketing?
    </h2>
    <p class="text-xl lg:text-2xl mb-12 opacity-90 leading-relaxed">
      Join 10,000+ businesses using AI to grow faster than ever before. Start your free trial today.
    </p>
    
    <div class="flex flex-col sm:flex-row gap-6 justify-center mb-12">
      <%= link_to new_user_registration_path, 
          class: "group bg-white text-blue-600 px-10 py-5 rounded-full text-xl font-bold hover:bg-gray-50 transition-all transform hover:scale-105 shadow-2xl" do %>
        <span class="flex items-center justify-center space-x-3">
          <span>Start Free Trial</span>
          <svg class="w-6 h-6 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
          </svg>
        </span>
      <% end %>
      
      <button class="group glass-effect text-white px-10 py-5 rounded-full text-xl font-bold hover:bg-white/20 transition-all border border-white/20">
        <span class="flex items-center justify-center space-x-3">
          <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path>
          </svg>
          <span>Watch Demo</span>
        </span>
      </button>
    </div>
    
    <!-- Final Trust Indicators -->
    <div class="flex items-center justify-center space-x-8 text-sm opacity-80">
      <div class="flex items-center space-x-2">
        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
        </svg>
        <span>14-day free trial</span>
      </div>
      <div class="flex items-center space-x-2">
        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
        </svg>
        <span>No setup fees</span>
      </div>
      <div class="flex items-center space-x-2">
        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
        </svg>
        <span>Cancel anytime</span>
      </div>
    </div>
  </div>
</section>
