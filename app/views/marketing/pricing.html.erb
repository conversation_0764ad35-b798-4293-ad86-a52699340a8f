<!-- Pricing Page -->

<!-- Hero Section -->
<section class="relative overflow-hidden bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 py-20">
  <!-- Background Effects -->
  <div class="absolute inset-0">
    <div class="absolute top-0 left-0 w-96 h-96 bg-blue-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse"></div>
    <div class="absolute bottom-0 right-0 w-96 h-96 bg-purple-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-1000"></div>
  </div>
  
  <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
    <h1 class="text-5xl lg:text-6xl font-black text-gray-900 mb-6 leading-tight">
      Simple, Transparent
      <span class="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
        Pricing
      </span>
    </h1>
    <p class="text-xl lg:text-2xl text-gray-700 mb-8 max-w-4xl mx-auto leading-relaxed">
      Choose the perfect plan for your business. All plans include our core AI features, 
      24/7 support, and a 14-day free trial. No hidden fees, cancel anytime.
    </p>
    
    <!-- Billing Toggle -->
    <div class="flex items-center justify-center mb-12">
      <div class="bg-white rounded-full p-1 shadow-lg border border-gray-200">
        <div class="flex">
          <button id="monthly-btn" class="px-6 py-2 rounded-full text-sm font-medium transition-all duration-200 bg-blue-600 text-white">
            Monthly
          </button>
          <button id="annual-btn" class="px-6 py-2 rounded-full text-sm font-medium transition-all duration-200 text-gray-600 hover:text-gray-900">
            Annual
            <span class="ml-2 text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">Save 25%</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Pricing Cards Section -->
<section class="py-20 bg-white">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      
      <!-- Starter Plan -->
      <div class="relative bg-white rounded-2xl shadow-lg border border-gray-200 p-8 hover:shadow-xl transition-shadow duration-300">
        <!-- Plan Header -->
        <div class="text-center mb-8">
          <h3 class="text-2xl font-bold text-gray-900 mb-2">Starter</h3>
          <p class="text-gray-600 mb-6">Perfect for small businesses getting started with AI marketing</p>
          
          <!-- Price -->
          <div class="mb-6">
            <div class="monthly-price">
              <span class="text-4xl font-bold text-gray-900">$49</span>
              <span class="text-gray-600">/month</span>
            </div>
            <div class="annual-price hidden">
              <span class="text-4xl font-bold text-gray-900">$37</span>
              <span class="text-gray-600">/month</span>
              <div class="text-sm text-green-600 mt-1">Billed annually ($444/year)</div>
            </div>
          </div>
          
          <!-- Free Trial Badge -->
          <div class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 mb-6">
            <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>
            14-day free trial
          </div>
        </div>
        
        <!-- Features List -->
        <div class="space-y-4 mb-8">
          <div class="flex items-start space-x-3">
            <div class="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <svg class="w-3 h-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <span class="text-gray-700">Up to 3 active campaigns</span>
          </div>
          <div class="flex items-start space-x-3">
            <div class="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <svg class="w-3 h-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <span class="text-gray-700">5,000 monthly email sends</span>
          </div>
          <div class="flex items-start space-x-3">
            <div class="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <svg class="w-3 h-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <span class="text-gray-700">AI campaign creation</span>
          </div>
          <div class="flex items-start space-x-3">
            <div class="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <svg class="w-3 h-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <span class="text-gray-700">Basic analytics & reporting</span>
          </div>
          <div class="flex items-start space-x-3">
            <div class="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <svg class="w-3 h-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <span class="text-gray-700">Email support</span>
          </div>
          <div class="flex items-start space-x-3">
            <div class="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <svg class="w-3 h-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <span class="text-gray-700">Standard integrations</span>
          </div>
        </div>
        
        <!-- CTA Button -->
        <%= link_to new_user_registration_path, 
            class: "block w-full text-center py-3 px-6 border border-blue-600 text-blue-600 font-semibold rounded-lg hover:bg-blue-50 transition-colors duration-200" do %>
          Start Free Trial
        <% end %>
      </div>
      
      <!-- Professional Plan (Popular) -->
      <div class="relative bg-white rounded-2xl shadow-2xl border-2 border-blue-500 p-8 transform scale-105 z-10">
        <!-- Popular Badge -->
        <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
          <div class="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-2 rounded-full text-sm font-semibold shadow-lg">
            Most Popular
          </div>
        </div>
        
        <!-- Plan Header -->
        <div class="text-center mb-8 pt-4">
          <h3 class="text-2xl font-bold text-gray-900 mb-2">Professional</h3>
          <p class="text-gray-600 mb-6">Ideal for growing businesses that want to scale their marketing</p>
          
          <!-- Price -->
          <div class="mb-6">
            <div class="monthly-price">
              <span class="text-4xl font-bold text-gray-900">$149</span>
              <span class="text-gray-600">/month</span>
            </div>
            <div class="annual-price hidden">
              <span class="text-4xl font-bold text-gray-900">$112</span>
              <span class="text-gray-600">/month</span>
              <div class="text-sm text-green-600 mt-1">Billed annually ($1,344/year)</div>
            </div>
          </div>
          
          <!-- Free Trial Badge -->
          <div class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 mb-6">
            <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>
            14-day free trial
          </div>
        </div>
        
        <!-- Features List -->
        <div class="space-y-4 mb-8">
          <div class="flex items-start space-x-3">
            <div class="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <svg class="w-3 h-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <span class="text-gray-700"><strong>Unlimited campaigns</strong></span>
          </div>
          <div class="flex items-start space-x-3">
            <div class="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <svg class="w-3 h-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <span class="text-gray-700"><strong>50,000 monthly email sends</strong></span>
          </div>
          <div class="flex items-start space-x-3">
            <div class="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <svg class="w-3 h-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <span class="text-gray-700">Advanced AI optimization</span>
          </div>
          <div class="flex items-start space-x-3">
            <div class="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <svg class="w-3 h-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <span class="text-gray-700">Multi-platform automation</span>
          </div>
          <div class="flex items-start space-x-3">
            <div class="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <svg class="w-3 h-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <span class="text-gray-700">Advanced analytics & attribution</span>
          </div>
          <div class="flex items-start space-x-3">
            <div class="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <svg class="w-3 h-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <span class="text-gray-700">Priority support</span>
          </div>
          <div class="flex items-start space-x-3">
            <div class="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <svg class="w-3 h-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <span class="text-gray-700">Premium integrations</span>
          </div>
          <div class="flex items-start space-x-3">
            <div class="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <svg class="w-3 h-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <span class="text-gray-700">Custom reporting</span>
          </div>
        </div>
        
        <!-- CTA Button -->
        <%= link_to new_user_registration_path, 
            class: "block w-full text-center py-3 px-6 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-lg hover:shadow-lg transform hover:-translate-y-0.5 transition-all duration-200" do %>
          Start Free Trial
        <% end %>
      </div>
      
      <!-- Enterprise Plan -->
      <div class="relative bg-white rounded-2xl shadow-lg border border-gray-200 p-8 hover:shadow-xl transition-shadow duration-300">
        <!-- Plan Header -->
        <div class="text-center mb-8">
          <h3 class="text-2xl font-bold text-gray-900 mb-2">Enterprise</h3>
          <p class="text-gray-600 mb-6">For large organizations with advanced needs and custom requirements</p>
          
          <!-- Price -->
          <div class="mb-6">
            <div class="monthly-price">
              <span class="text-4xl font-bold text-gray-900">$449</span>
              <span class="text-gray-600">/month</span>
            </div>
            <div class="annual-price hidden">
              <span class="text-4xl font-bold text-gray-900">$337</span>
              <span class="text-gray-600">/month</span>
              <div class="text-sm text-green-600 mt-1">Billed annually ($4,044/year)</div>
            </div>
          </div>
          
          <!-- Free Trial Badge -->
          <div class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 mb-6">
            <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>
            30-day free trial
          </div>
        </div>
        
        <!-- Features List -->
        <div class="space-y-4 mb-8">
          <div class="flex items-start space-x-3">
            <div class="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <svg class="w-3 h-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <span class="text-gray-700"><strong>Everything in Professional</strong></span>
          </div>
          <div class="flex items-start space-x-3">
            <div class="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <svg class="w-3 h-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <span class="text-gray-700">500,000+ monthly email sends</span>
          </div>
          <div class="flex items-start space-x-3">
            <div class="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <svg class="w-3 h-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <span class="text-gray-700">Dedicated account manager</span>
          </div>
          <div class="flex items-start space-x-3">
            <div class="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <svg class="w-3 h-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <span class="text-gray-700">Custom AI model training</span>
          </div>
          <div class="flex items-start space-x-3">
            <div class="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <svg class="w-3 h-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <span class="text-gray-700">API access & webhooks</span>
          </div>
          <div class="flex items-start space-x-3">
            <div class="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <svg class="w-3 h-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <span class="text-gray-700">White-label options</span>
          </div>
          <div class="flex items-start space-x-3">
            <div class="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <svg class="w-3 h-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <span class="text-gray-700">99.9% SLA guarantee</span>
          </div>
          <div class="flex items-start space-x-3">
            <div class="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <svg class="w-3 h-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <span class="text-gray-700">Custom onboarding & training</span>
          </div>
        </div>
        
        <!-- CTA Button -->
        <a href="#" class="block w-full text-center py-3 px-6 border border-purple-600 text-purple-600 font-semibold rounded-lg hover:bg-purple-50 transition-colors duration-200">
          Contact Sales
        </a>
      </div>
    </div>
  </div>
</section>

<!-- Features Comparison Table -->
<section class="py-20 bg-gray-50">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-16">
      <h2 class="text-4xl font-bold text-gray-900 mb-6">
        Compare All Features
      </h2>
      <p class="text-xl text-gray-600 max-w-3xl mx-auto">
        See exactly what's included in each plan to choose the perfect fit for your business needs.
      </p>
    </div>
    
    <!-- Feature Comparison Table -->
    <div class="bg-white rounded-2xl shadow-lg border border-gray-200 overflow-hidden">
      <div class="overflow-x-auto">
        <table class="w-full">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-4 text-left text-sm font-semibold text-gray-900">Features</th>
              <th class="px-6 py-4 text-center text-sm font-semibold text-gray-900">Starter</th>
              <th class="px-6 py-4 text-center text-sm font-semibold text-gray-900 bg-blue-50">Professional</th>
              <th class="px-6 py-4 text-center text-sm font-semibold text-gray-900">Enterprise</th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-200">
            <tr>
              <td class="px-6 py-4 text-sm text-gray-900 font-medium">Active Campaigns</td>
              <td class="px-6 py-4 text-center text-sm text-gray-600">3</td>
              <td class="px-6 py-4 text-center text-sm text-gray-600 bg-blue-50">Unlimited</td>
              <td class="px-6 py-4 text-center text-sm text-gray-600">Unlimited</td>
            </tr>
            <tr>
              <td class="px-6 py-4 text-sm text-gray-900 font-medium">Monthly Email Sends</td>
              <td class="px-6 py-4 text-center text-sm text-gray-600">5,000</td>
              <td class="px-6 py-4 text-center text-sm text-gray-600 bg-blue-50">50,000</td>
              <td class="px-6 py-4 text-center text-sm text-gray-600">500,000+</td>
            </tr>
            <tr>
              <td class="px-6 py-4 text-sm text-gray-900 font-medium">AI Campaign Creation</td>
              <td class="px-6 py-4 text-center">
                <svg class="w-5 h-5 text-green-500 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
              </td>
              <td class="px-6 py-4 text-center bg-blue-50">
                <svg class="w-5 h-5 text-green-500 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
              </td>
              <td class="px-6 py-4 text-center">
                <svg class="w-5 h-5 text-green-500 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
              </td>
            </tr>
            <tr>
              <td class="px-6 py-4 text-sm text-gray-900 font-medium">Multi-Platform Automation</td>
              <td class="px-6 py-4 text-center">
                <svg class="w-5 h-5 text-gray-300 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
              </td>
              <td class="px-6 py-4 text-center bg-blue-50">
                <svg class="w-5 h-5 text-green-500 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
              </td>
              <td class="px-6 py-4 text-center">
                <svg class="w-5 h-5 text-green-500 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
              </td>
            </tr>
            <tr>
              <td class="px-6 py-4 text-sm text-gray-900 font-medium">Advanced Analytics</td>
              <td class="px-6 py-4 text-center">
                <svg class="w-5 h-5 text-gray-300 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
              </td>
              <td class="px-6 py-4 text-center bg-blue-50">
                <svg class="w-5 h-5 text-green-500 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
              </td>
              <td class="px-6 py-4 text-center">
                <svg class="w-5 h-5 text-green-500 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
              </td>
            </tr>
            <tr>
              <td class="px-6 py-4 text-sm text-gray-900 font-medium">API Access</td>
              <td class="px-6 py-4 text-center">
                <svg class="w-5 h-5 text-gray-300 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
              </td>
              <td class="px-6 py-4 text-center bg-blue-50">
                <svg class="w-5 h-5 text-gray-300 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
              </td>
              <td class="px-6 py-4 text-center">
                <svg class="w-5 h-5 text-green-500 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
              </td>
            </tr>
            <tr>
              <td class="px-6 py-4 text-sm text-gray-900 font-medium">Dedicated Support</td>
              <td class="px-6 py-4 text-center text-sm text-gray-600">Email</td>
              <td class="px-6 py-4 text-center text-sm text-gray-600 bg-blue-50">Priority</td>
              <td class="px-6 py-4 text-center text-sm text-gray-600">Account Manager</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</section>

<!-- FAQ Section -->
<section class="py-20 bg-white">
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-16">
      <h2 class="text-4xl font-bold text-gray-900 mb-6">
        Frequently Asked Questions
      </h2>
      <p class="text-xl text-gray-600">
        Everything you need to know about our pricing and plans.
      </p>
    </div>
    
    <div class="space-y-8">
      <!-- FAQ Item -->
      <div class="border border-gray-200 rounded-lg p-6 hover:border-blue-300 transition-colors duration-200">
        <h3 class="text-lg font-semibold text-gray-900 mb-3">
          Can I change my plan at any time?
        </h3>
        <p class="text-gray-600">
          Yes! You can upgrade or downgrade your plan at any time. Changes take effect immediately, 
          and you'll be charged/credited the prorated difference for the current billing period.
        </p>
      </div>
      
      <!-- FAQ Item -->
      <div class="border border-gray-200 rounded-lg p-6 hover:border-blue-300 transition-colors duration-200">
        <h3 class="text-lg font-semibold text-gray-900 mb-3">
          What happens when I exceed my email limit?
        </h3>
        <p class="text-gray-600">
          If you approach your monthly email limit, we'll notify you with options to upgrade your plan. 
          We'll never stop your campaigns unexpectedly - you'll always have the option to purchase additional sends or upgrade.
        </p>
      </div>
      
      <!-- FAQ Item -->
      <div class="border border-gray-200 rounded-lg p-6 hover:border-blue-300 transition-colors duration-200">
        <h3 class="text-lg font-semibold text-gray-900 mb-3">
          Is there a setup fee or contract?
        </h3>
        <p class="text-gray-600">
          No setup fees, no contracts, no hidden costs. You only pay for your monthly or annual subscription. 
          Cancel anytime with just a few clicks - no questions asked.
        </p>
      </div>
      
      <!-- FAQ Item -->
      <div class="border border-gray-200 rounded-lg p-6 hover:border-blue-300 transition-colors duration-200">
        <h3 class="text-lg font-semibold text-gray-900 mb-3">
          Do you offer discounts for nonprofits or education?
        </h3>
        <p class="text-gray-600">
          Yes! We offer special pricing for qualified nonprofits and educational institutions. 
          Contact our sales team for more information about available discounts.
        </p>
      </div>
      
      <!-- FAQ Item -->
      <div class="border border-gray-200 rounded-lg p-6 hover:border-blue-300 transition-colors duration-200">
        <h3 class="text-lg font-semibold text-gray-900 mb-3">
          What payment methods do you accept?
        </h3>
        <p class="text-gray-600">
          We accept all major credit cards (Visa, MasterCard, American Express) and PayPal. 
          Enterprise customers can also pay by invoice with NET 30 terms.
        </p>
      </div>
    </div>
  </div>
</section>

<!-- Final CTA Section -->
<section class="py-20 bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-700 text-white relative overflow-hidden">
  <!-- Background Effects -->
  <div class="absolute inset-0">
    <div class="absolute top-0 left-0 w-96 h-96 bg-white rounded-full mix-blend-overlay opacity-10 animate-pulse"></div>
    <div class="absolute bottom-0 right-0 w-96 h-96 bg-white rounded-full mix-blend-overlay opacity-10 animate-pulse animation-delay-1000"></div>
  </div>
  
  <div class="relative max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
    <h2 class="text-4xl lg:text-5xl font-bold mb-6">
      Start Your Free Trial Today
    </h2>
    <p class="text-xl lg:text-2xl text-blue-100 mb-8">
      No credit card required. Cancel anytime. Get up and running in under 5 minutes.
    </p>
    
    <div class="flex flex-col sm:flex-row gap-4 justify-center mb-8">
      <%= link_to new_user_registration_path, class: "group relative inline-flex items-center justify-center px-8 py-4 text-lg font-bold text-blue-600 bg-white rounded-full shadow-2xl hover:shadow-white/25 transform hover:-translate-y-1 transition-all duration-300" do %>
        <span class="relative flex items-center space-x-2">
          <span>Start Free Trial</span>
          <svg class="w-5 h-5 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
          </svg>
        </span>
      <% end %>
      
      <a href="#" class="group inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-white bg-white/10 border-2 border-white/30 rounded-full hover:bg-white/20 transition-all duration-300">
        <svg class="w-5 h-5 mr-2 group-hover:scale-110 transition-transform" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path>
        </svg>
        Contact Sales
      </a>
    </div>
    
    <!-- Stats -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-3xl mx-auto">
      <div class="text-center">
        <div class="text-3xl font-bold text-white mb-2">10,000+</div>
        <div class="text-blue-200">Happy Customers</div>
      </div>
      <div class="text-center">
        <div class="text-3xl font-bold text-white mb-2">312%</div>
        <div class="text-blue-200">Average ROI Increase</div>
      </div>
      <div class="text-center">
        <div class="text-3xl font-bold text-white mb-2">99.9%</div>
        <div class="text-blue-200">Uptime Guarantee</div>
      </div>
    </div>
  </div>
</section>

<!-- JavaScript for Billing Toggle -->
<script>
document.addEventListener('DOMContentLoaded', function() {
  const monthlyBtn = document.getElementById('monthly-btn');
  const annualBtn = document.getElementById('annual-btn');
  const monthlyPrices = document.querySelectorAll('.monthly-price');
  const annualPrices = document.querySelectorAll('.annual-price');
  
  function showMonthly() {
    monthlyBtn.classList.add('bg-blue-600', 'text-white');
    monthlyBtn.classList.remove('text-gray-600', 'hover:text-gray-900');
    annualBtn.classList.remove('bg-blue-600', 'text-white');
    annualBtn.classList.add('text-gray-600', 'hover:text-gray-900');
    
    monthlyPrices.forEach(price => price.classList.remove('hidden'));
    annualPrices.forEach(price => price.classList.add('hidden'));
  }
  
  function showAnnual() {
    annualBtn.classList.add('bg-blue-600', 'text-white');
    annualBtn.classList.remove('text-gray-600', 'hover:text-gray-900');
    monthlyBtn.classList.remove('bg-blue-600', 'text-white');
    monthlyBtn.classList.add('text-gray-600', 'hover:text-gray-900');
    
    monthlyPrices.forEach(price => price.classList.add('hidden'));
    annualPrices.forEach(price => price.classList.remove('hidden'));
  }
  
  monthlyBtn.addEventListener('click', showMonthly);
  annualBtn.addEventListener('click', showAnnual);
});
</script>