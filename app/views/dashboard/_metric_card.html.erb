<%
  # Metric card partial for displaying key performance indicators
  # Parameters:
  # - title: The metric title/label
  # - value: The metric value to display
  # - icon: Heroicon name for the metric
  # - color: Tailwind color name (blue, green, purple, etc.)
  # - trend: Optional trend information (can be nil)
%>

<div class="metric-card bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-shadow">
  <div class="flex items-center justify-between mb-4">
    <div class="flex items-center space-x-3">
      <div class="w-12 h-12 bg-<%= color %>-100 rounded-lg flex items-center justify-center">
        <% case icon %>
        <% when 'chart-bar-square' %>
          <svg class="w-6 h-6 text-<%= color %>-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
          </svg>
        <% when 'currency-dollar' %>
          <svg class="w-6 h-6 text-<%= color %>-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
          </svg>
        <% when 'eye' %>
          <svg class="w-6 h-6 text-<%= color %>-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
          </svg>
        <% when 'cursor-arrow-rays' %>
          <svg class="w-6 h-6 text-<%= color %>-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122"/>
          </svg>
        <% when 'arrow-trending-up' %>
          <svg class="w-6 h-6 text-<%= color %>-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.25 18L9 11.25l4.306 4.307a11.95 11.95 0 015.814-5.519l2.74-1.22m0 0l-5.94-2.28m5.94 2.28l-2.28 5.941"/>
          </svg>
        <% when 'heart' %>
          <svg class="w-6 h-6 text-<%= color %>-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
          </svg>
        <% when 'share' %>
          <svg class="w-6 h-6 text-<%= color %>-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z"/>
          </svg>
        <% when 'users' %>
          <svg class="w-6 h-6 text-<%= color %>-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"/>
          </svg>
        <% else %>
          <!-- Default icon if none matches -->
          <svg class="w-6 h-6 text-<%= color %>-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
          </svg>
        <% end %>
      </div>
      <div>
        <h3 class="text-sm font-medium text-gray-600"><%= title %></h3>
        <p class="text-2xl font-bold text-gray-900"><%= value %></p>
      </div>
    </div>
    
    <% if trend.present? %>
      <div class="text-right">
        <% if trend.to_s.start_with?('+') %>
          <div class="flex items-center text-green-600">
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"/>
            </svg>
            <span class="text-sm font-medium"><%= trend %></span>
          </div>
        <% elsif trend.to_s.start_with?('-') %>
          <div class="flex items-center text-red-600">
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6"/>
            </svg>
            <span class="text-sm font-medium"><%= trend %></span>
          </div>
        <% else %>
          <div class="flex items-center text-gray-600">
            <span class="text-sm font-medium"><%= trend %></span>
          </div>
        <% end %>
      </div>
    <% end %>
  </div>

  <% if trend.present? %>
    <div class="mt-4 pt-4 border-t border-gray-100">
      <div class="text-xs text-gray-500">
        <% if trend.to_s.start_with?('+') %>
          Improvement from last period
        <% elsif trend.to_s.start_with?('-') %>
          Decrease from last period
        <% else %>
          Compared to last period
        <% end %>
      </div>
    </div>
  <% end %>
</div>