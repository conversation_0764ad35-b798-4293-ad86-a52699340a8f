<!-- AI Agent Orchestration Dashboard Section -->
<div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
  <div class="flex items-center justify-between mb-6">
    <div>
      <h3 class="text-lg font-semibold text-gray-900 flex items-center">
        <span class="w-8 h-8 bg-gradient-to-br from-cyan-500 to-blue-600 rounded-lg flex items-center justify-center mr-3">
          <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
          </svg>
        </span>
        AI Agent Orchestration
      </h3>
      <p class="text-sm text-gray-600">Real-time multi-agent coordination & execution</p>
    </div>
    <div class="flex items-center space-x-2">
      <span class="text-xs font-medium text-cyan-600 bg-cyan-50 px-3 py-1 rounded-full animate-pulse">
        <%= active_workflows_count %> Active
      </span>
      <%= link_to agent_workflows_path, class: "text-sm text-blue-600 hover:text-blue-800 font-medium" do %>
        View All Workflows →
      <% end %>
    </div>
  </div>

  <!-- Agent Status Grid -->
  <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
    <!-- Marketing Manager Agent -->
    <div class="agent-status-card bg-gradient-to-br from-indigo-50 to-blue-50 border border-indigo-100 rounded-xl p-4 relative overflow-hidden">
      <div class="absolute top-0 right-0 w-16 h-16 bg-gradient-to-br from-indigo-200/20 to-blue-200/20 rounded-full -mr-8 -mt-8"></div>
      <div class="relative">
        <div class="flex items-center justify-between mb-3">
          <div class="w-10 h-10 bg-gradient-to-br from-indigo-500 to-blue-600 rounded-lg flex items-center justify-center">
            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
            </svg>
          </div>
          <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
        </div>
        <h4 class="font-semibold text-gray-900 text-sm">Marketing Manager</h4>
        <p class="text-xs text-gray-600 mb-2">Central Orchestrator</p>
        <div class="flex items-center justify-between">
          <span class="text-xs text-indigo-600 font-medium">
            <%= orchestration_workflows_count %> Active
          </span>
          <span class="text-xs text-green-600">● Operational</span>
        </div>
      </div>
    </div>

    <!-- Email Specialist Agent -->
    <div class="agent-status-card bg-gradient-to-br from-emerald-50 to-green-50 border border-emerald-100 rounded-xl p-4 relative overflow-hidden">
      <div class="absolute top-0 right-0 w-16 h-16 bg-gradient-to-br from-emerald-200/20 to-green-200/20 rounded-full -mr-8 -mt-8"></div>
      <div class="relative">
        <div class="flex items-center justify-between mb-3">
          <div class="w-10 h-10 bg-gradient-to-br from-emerald-500 to-green-600 rounded-lg flex items-center justify-center">
            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
            </svg>
          </div>
          <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
        </div>
        <h4 class="font-semibold text-gray-900 text-sm">Email Specialist</h4>
        <p class="text-xs text-gray-600 mb-2">Content Generation</p>
        <div class="flex items-center justify-between">
          <span class="text-xs text-emerald-600 font-medium">
            <%= email_specialist_workflows_count %> Active
          </span>
          <span class="text-xs text-green-600">● Operational</span>
        </div>
      </div>
    </div>

    <!-- Social Media Agent -->
    <div class="agent-status-card bg-gradient-to-br from-purple-50 to-pink-50 border border-purple-100 rounded-xl p-4 relative overflow-hidden">
      <div class="absolute top-0 right-0 w-16 h-16 bg-gradient-to-br from-purple-200/20 to-pink-200/20 rounded-full -mr-8 -mt-8"></div>
      <div class="relative">
        <div class="flex items-center justify-between mb-3">
          <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg flex items-center justify-center">
            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 2h10a2 2 0 012 2v10a2 2 0 01-2 2H6a2 2 0 01-2-2V8a2 2 0 012-2z"/>
            </svg>
          </div>
          <div class="w-2 h-2 bg-yellow-400 rounded-full animate-pulse"></div>
        </div>
        <h4 class="font-semibold text-gray-900 text-sm">Social Media</h4>
        <p class="text-xs text-gray-600 mb-2">Multi-Platform</p>
        <div class="flex items-center justify-between">
          <span class="text-xs text-purple-600 font-medium">
            <%= social_specialist_workflows_count %> Queued
          </span>
          <span class="text-xs text-yellow-600">● Developing</span>
        </div>
      </div>
    </div>

    <!-- SEO Specialist Agent -->
    <div class="agent-status-card bg-gradient-to-br from-orange-50 to-red-50 border border-orange-100 rounded-xl p-4 relative overflow-hidden">
      <div class="absolute top-0 right-0 w-16 h-16 bg-gradient-to-br from-orange-200/20 to-red-200/20 rounded-full -mr-8 -mt-8"></div>
      <div class="relative">
        <div class="flex items-center justify-between mb-3">
          <div class="w-10 h-10 bg-gradient-to-br from-orange-500 to-red-600 rounded-lg flex items-center justify-center">
            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
            </svg>
          </div>
          <div class="w-2 h-2 bg-yellow-400 rounded-full animate-pulse"></div>
        </div>
        <h4 class="font-semibold text-gray-900 text-sm">SEO Specialist</h4>
        <p class="text-xs text-gray-600 mb-2">Search Optimization</p>
        <div class="flex items-center justify-between">
          <span class="text-xs text-orange-600 font-medium">
            <%= seo_specialist_workflows_count %> Queued
          </span>
          <span class="text-xs text-yellow-600">● Developing</span>
        </div>
      </div>
    </div>
  </div>

  <!-- Active Workflows Stream -->
  <div id="active-workflows-stream" data-controller="workflow-monitor" data-turbo-permanent>
    <div class="flex items-center justify-between mb-4">
      <h4 class="text-sm font-semibold text-gray-900">Active Workflows</h4>
      <div class="flex items-center space-x-2">
        <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
        <span class="text-xs text-green-600 font-medium">Live Updates</span>
      </div>
    </div>

    <div class="space-y-3 max-h-64 overflow-y-auto">
      <% if active_workflows.any? %>
        <% active_workflows.each do |workflow| %>
          <div class="workflow-item bg-gray-50 rounded-xl p-4 hover:bg-gray-100 transition-colors border border-gray-200"
               id="workflow-item-<%= workflow.id %>"
               data-workflow-id="<%= workflow.id %>">
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-3">
                <div class="flex-shrink-0">
                  <% case workflow.workflow_type %>
                  <% when 'marketing_orchestration' %>
                    <div class="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center">
                      <svg class="w-4 h-4 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                      </svg>
                    </div>
                  <% when 'email_specialist' %>
                    <div class="w-8 h-8 bg-emerald-100 rounded-lg flex items-center justify-center">
                      <svg class="w-4 h-4 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                      </svg>
                    </div>
                  <% when 'social_specialist' %>
                    <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                      <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 2h10a2 2 0 012 2v10a2 2 0 01-2 2H6a2 2 0 01-2-2V8a2 2 0 012-2z"/>
                      </svg>
                    </div>
                  <% when 'seo_specialist' %>
                    <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                      <svg class="w-4 h-4 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                      </svg>
                    </div>
                  <% else %>
                    <div class="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center">
                      <svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
                      </svg>
                    </div>
                  <% end %>
                </div>
                
                <div>
                  <h5 class="text-sm font-medium text-gray-900">
                    <%= workflow.workflow_type.humanize %>
                  </h5>
                  <p class="text-xs text-gray-600">
                    Campaign: <%= link_to truncate(workflow.campaign.name, length: 25), 
                                         campaign_path(workflow.campaign), 
                                         class: "text-blue-600 hover:text-blue-800" %>
                  </p>
                  <div class="flex items-center space-x-2 mt-1">
                    <span class="status-indicator status-<%= workflow.status %>">
                      <%= workflow.status.humanize %>
                    </span>
                    <% if workflow.current_step.present? %>
                      <span class="text-xs text-gray-500">
                        • <%= workflow.current_step.humanize %>
                      </span>
                    <% end %>
                  </div>
                </div>
              </div>
              
              <div class="flex items-center space-x-4">
                <!-- Progress Bar -->
                <div class="flex items-center space-x-2">
                  <div class="w-24 bg-gray-200 rounded-full h-2">
                    <div class="h-2 rounded-full transition-all duration-500 <%= workflow.status == 'running' ? 'bg-gradient-to-r from-blue-500 to-cyan-500' : 'bg-gray-400' %>" 
                         style="width: <%= workflow.progress_percent %>%"></div>
                  </div>
                  <span class="text-xs text-gray-500 font-medium w-8">
                    <%= workflow.progress_percent %>%
                  </span>
                </div>
                
                <!-- Timing Info -->
                <div class="text-right">
                  <% if workflow.started_at %>
                    <div class="text-xs text-gray-500">
                      <%= distance_of_time_in_words(workflow.started_at, Time.current) %>
                    </div>
                  <% end %>
                  <div class="text-xs text-gray-400">
                    <%= time_ago_in_words(workflow.created_at) %> ago
                  </div>
                </div>
                
                <!-- Quick Actions -->
                <div class="flex items-center space-x-1">
                  <%= link_to agent_workflow_path(workflow), 
                              class: "text-blue-600 hover:text-blue-800 p-1", 
                              title: "View Details" do %>
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                    </svg>
                  <% end %>
                  
                  <% if workflow.running? || workflow.pending? %>
                    <%= link_to pause_agent_workflow_path(workflow), 
                                method: :patch, 
                                data: { confirm: "Pause this workflow?", turbo: true },
                                class: "text-orange-600 hover:text-orange-800 p-1", 
                                title: "Pause Workflow" do %>
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                      </svg>
                    <% end %>
                  <% end %>
                </div>
              </div>
            </div>
            
            <!-- Workflow Details Expansion -->
            <% if workflow.results.any? || workflow.error_details.any? %>
              <div class="mt-3 pt-3 border-t border-gray-200">
                <% if workflow.results.any? %>
                  <div class="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs">
                    <% workflow.results.each do |key, value| %>
                      <div class="bg-white rounded-lg p-2 border border-gray-100">
                        <div class="text-gray-500 truncate"><%= key.humanize %></div>
                        <div class="text-gray-900 font-medium truncate">
                          <%= value.is_a?(Hash) ? value.keys.count : value.to_s.truncate(15) %>
                        </div>
                      </div>
                    <% end %>
                  </div>
                <% end %>
                
                <% if workflow.error_details.any? && workflow.failed? %>
                  <div class="mt-2 p-2 bg-red-50 border border-red-200 rounded-lg">
                    <div class="flex items-center space-x-2">
                      <svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                      </svg>
                      <span class="text-xs text-red-700 font-medium">
                        <%= workflow.error_details['message'] || 'Workflow failed' %>
                      </span>
                    </div>
                  </div>
                <% end %>
              </div>
            <% end %>
          </div>
        <% end %>
      <% else %>
        <div class="text-center py-8">
          <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
            </svg>
          </div>
          <h4 class="text-sm font-medium text-gray-900 mb-1">No Active Workflows</h4>
          <p class="text-xs text-gray-600 mb-4">Start a new campaign to see AI agents in action</p>
          <%= link_to new_campaign_path, 
                      class: "inline-flex items-center px-3 py-2 border border-transparent text-xs font-medium rounded-lg text-white bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200" do %>
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
            </svg>
            Create Campaign
          <% end %>
        </div>
      <% end %>
    </div>
  </div>

  <!-- Performance Metrics -->
  <div class="mt-6 pt-6 border-t border-gray-100">
    <h4 class="text-sm font-semibold text-gray-900 mb-4">Agent Performance Metrics</h4>
    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
      <div class="text-center p-3 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl">
        <div class="text-lg font-bold text-blue-600">
          <%= total_workflows_count %>
        </div>
        <div class="text-xs text-gray-600">Total Workflows</div>
        <div class="text-xs text-blue-500 mt-1">All time</div>
      </div>
      
      <div class="text-center p-3 bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl">
        <div class="text-lg font-bold text-green-600">
          <%= workflow_success_rate %>%
        </div>
        <div class="text-xs text-gray-600">Success Rate</div>
        <div class="text-xs text-green-500 mt-1">Last 30 days</div>
      </div>
      
      <div class="text-center p-3 bg-gradient-to-br from-purple-50 to-pink-50 rounded-xl">
        <div class="text-lg font-bold text-purple-600">
          <%= average_workflow_duration %>
        </div>
        <div class="text-xs text-gray-600">Avg Duration</div>
        <div class="text-xs text-purple-500 mt-1">Per workflow</div>
      </div>
      
      <div class="text-center p-3 bg-gradient-to-br from-orange-50 to-red-50 rounded-xl">
        <div class="text-lg font-bold text-orange-600">
          <%= parallel_workflows_count %>
        </div>
        <div class="text-xs text-gray-600">Parallel Jobs</div>
        <div class="text-xs text-orange-500 mt-1">Currently running</div>
      </div>
    </div>
  </div>
</div>

<!-- Real-time Updates Styling -->
<style>
.agent-status-card {
  transition: all 0.3s ease;
}

.agent-status-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.workflow-item {
  transition: all 0.2s ease;
}

.workflow-item:hover {
  transform: translateX(2px);
}

.status-indicator {
  @apply inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium;
}

.status-indicator.status-pending {
  @apply bg-yellow-100 text-yellow-800;
}

.status-indicator.status-running {
  @apply bg-blue-100 text-blue-800;
}

.status-indicator.status-completed {
  @apply bg-green-100 text-green-800;
}

.status-indicator.status-failed {
  @apply bg-red-100 text-red-800;
}

.status-indicator.status-cancelled {
  @apply bg-gray-100 text-gray-800;
}

/* Animate progress bars */
@keyframes progress-pulse {
  0%, 100% { 
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
  }
  50% { 
    box-shadow: 0 0 0 8px rgba(59, 130, 246, 0);
  }
}

.status-running .bg-gradient-to-r {
  animation: progress-pulse 2s infinite;
}
</style>

<!-- Turbo Stream for Real-time Updates -->
<%= turbo_stream_from "agent_workflows_#{current_tenant.id}" %>
