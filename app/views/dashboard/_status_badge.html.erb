<%
  # Status badge partial
  # Parameters:
  # - status: The status value (required)
  # - text: The text to display (optional, defaults to titleized status)
  # - variant: Badge variant - 'solid', 'soft', 'outline' (optional, defaults to 'soft')
  # - size: Badge size - 'xs', 'sm', 'md', 'lg' (optional, defaults to 'sm')

  text ||= status&.titleize || 'Unknown'
  variant ||= 'soft'
  size ||= 'sm'
  
  # Define size classes
  size_classes = {
    'xs' => 'px-1.5 py-0.5 text-xs',
    'sm' => 'px-2 py-0.5 text-xs',
    'md' => 'px-2.5 py-1 text-sm',
    'lg' => 'px-3 py-1.5 text-sm'
  }
  
  # Define status-specific classes based on variant
  case variant
  when 'solid'
    status_classes = {
      'active' => 'bg-green-600 text-white',
      'draft' => 'bg-yellow-600 text-white',
      'paused' => 'bg-orange-600 text-white',
      'completed' => 'bg-blue-600 text-white',
      'cancelled' => 'bg-red-600 text-white',
      'configured' => 'bg-green-600 text-white',
      'setup_required' => 'bg-yellow-600 text-white'
    }
  when 'outline'
    status_classes = {
      'active' => 'border border-green-500 text-green-700 bg-white',
      'draft' => 'border border-yellow-500 text-yellow-700 bg-white',
      'paused' => 'border border-orange-500 text-orange-700 bg-white',
      'completed' => 'border border-blue-500 text-blue-700 bg-white',
      'cancelled' => 'border border-red-500 text-red-700 bg-white',
      'configured' => 'border border-green-500 text-green-700 bg-white',
      'setup_required' => 'border border-yellow-500 text-yellow-700 bg-white'
    }
  else # 'soft' variant (default)
    status_classes = {
      'active' => 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
      'draft' => 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
      'paused' => 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200',
      'completed' => 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
      'cancelled' => 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
      'configured' => 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
      'setup_required' => 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
    }
  end
  
  # Get the appropriate classes
  size_class = size_classes[size] || size_classes['sm']
  status_class = status_classes[status] || status_classes['draft']
  
  # Base classes
  base_classes = 'inline-flex items-center font-medium rounded-full'
  
  # Combine all classes
  all_classes = "#{base_classes} #{size_class} #{status_class}"
%>

<span class="<%= all_classes %>" data-status="<%= status %>">
  <%= text %>
</span>
