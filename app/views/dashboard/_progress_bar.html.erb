<%
  # Progress Bar Partial
  # Parameters:
  # - value: Current progress value (required)
  # - max_value: Maximum value (default: 100)
  # - color: Color scheme - 'primary', 'success', 'warning', 'danger' (default: 'primary')
  # - size: Size variant - 'sm', 'md', 'lg', 'xl' (default: 'md')
  # - show_percentage: Whether to show percentage text (default: false)
  # - css_class: Additional CSS classes (optional)
  # - animate: Whether to animate the progress bar (default: true)

  # Set defaults
  value = local_assigns[:value] || 0
  max_value = local_assigns[:max_value] || 100
  color = local_assigns[:color] || 'primary'
  size = local_assigns[:size] || 'md'
  show_percentage = local_assigns[:show_percentage] || false
  css_class = local_assigns[:css_class] || ''
  animate = local_assigns.fetch(:animate, true)

  # Calculate percentage
  percentage = max_value > 0 ? (value.to_f / max_value.to_f * 100).round(1) : 0
  percentage = [percentage, 100].min # Cap at 100%

  # Color classes
  color_classes = {
    'primary' => 'bg-blue-600',
    'success' => 'bg-green-600',
    'warning' => 'bg-yellow-500',
    'danger' => 'bg-red-600',
    'purple' => 'bg-purple-600',
    'indigo' => 'bg-indigo-600'
  }

  # Size classes
  size_classes = {
    'sm' => 'h-1',
    'md' => 'h-2',
    'lg' => 'h-3',
    'xl' => 'h-4'
  }

  # Build CSS classes
  container_classes = [
    'w-full bg-gray-200 rounded-full overflow-hidden',
    size_classes[size] || size_classes['md'],
    css_class
  ].compact.join(' ')

  progress_classes = [
    color_classes[color] || color_classes['primary'],
    'h-full rounded-full transition-all duration-1000 ease-out',
    ('progress-bar' if animate)
  ].compact.join(' ')

  # Generate unique ID for animation
  progress_id = "progress-#{SecureRandom.hex(4)}"
%>

<div class="progress-bar-container">
  <% if show_percentage %>
    <div class="flex items-center justify-between mb-2">
      <span class="text-sm font-medium text-gray-700">Progress</span>
      <span class="text-sm font-semibold text-gray-900"><%= percentage %>%</span>
    </div>
  <% end %>

  <div class="<%= container_classes %>" role="progressbar" 
       aria-valuenow="<%= value %>" 
       aria-valuemin="0" 
       aria-valuemax="<%= max_value %>"
       aria-label="Progress: <%= percentage %>%">
    <div id="<%= progress_id %>" 
         class="<%= progress_classes %>" 
         style="width: <%= animate ? '0%' : "#{percentage}%" %>"
         data-width="<%= percentage %>">
    </div>
  </div>

  <% if animate %>
    <script>
      document.addEventListener('DOMContentLoaded', function() {
        const progressBar = document.getElementById('<%= progress_id %>');
        if (progressBar) {
          // Small delay to ensure smooth animation
          setTimeout(() => {
            progressBar.style.width = '<%= percentage %>%';
          }, 100);
        }
      });
    </script>
  <% end %>
</div>
