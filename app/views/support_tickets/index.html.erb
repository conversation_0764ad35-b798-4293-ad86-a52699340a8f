<% content_for :title, "Support Tickets" %>

<div class="max-w-7xl mx-auto space-y-8">
  <!-- <PERSON> Header -->
  <div class="bg-white rounded-xl shadow-sm p-8">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold text-gray-900 mb-2">Support Tickets</h1>
        <p class="text-gray-600">Track and manage your support requests</p>
      </div>
      <%= link_to contact_help_index_path, 
          class: "inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors" do %>
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
        </svg>
        New Support Request
      <% end %>
    </div>
  </div>

  <!-- Ticket Stats -->
  <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
    <div class="bg-white rounded-xl shadow-sm p-6">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-sm font-medium text-gray-600">Total Tickets</p>
          <p class="text-3xl font-bold text-gray-900"><%= @ticket_stats[:total] %></p>
        </div>
        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
          <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"></path>
          </svg>
        </div>
      </div>
    </div>

    <div class="bg-white rounded-xl shadow-sm p-6">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-sm font-medium text-gray-600">Open</p>
          <p class="text-3xl font-bold text-green-600"><%= @ticket_stats[:open] %></p>
        </div>
        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
          <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
      </div>
    </div>

    <div class="bg-white rounded-xl shadow-sm p-6">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-sm font-medium text-gray-600">Waiting for Support</p>
          <p class="text-3xl font-bold text-yellow-600"><%= @ticket_stats[:waiting_for_support] %></p>
        </div>
        <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
          <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
      </div>
    </div>

    <div class="bg-white rounded-xl shadow-sm p-6">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-sm font-medium text-gray-600">Closed</p>
          <p class="text-3xl font-bold text-gray-600"><%= @ticket_stats[:closed] %></p>
        </div>
        <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
          <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
          </svg>
        </div>
      </div>
    </div>
  </div>

  <!-- Filters and Tickets List -->
  <div class="bg-white rounded-xl shadow-sm">
    <!-- Filter Tabs -->
    <div class="border-b border-gray-200">
      <nav class="flex space-x-8 px-6" aria-label="Tabs">
        <%= link_to support_tickets_path(status: 'all'), 
            class: "py-4 px-1 border-b-2 font-medium text-sm #{ @filter_status == 'all' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' }" do %>
          All Tickets
        <% end %>
        
        <%= link_to support_tickets_path(status: 'open'), 
            class: "py-4 px-1 border-b-2 font-medium text-sm #{ @filter_status == 'open' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' }" do %>
          Open
        <% end %>
        
        <%= link_to support_tickets_path(status: 'waiting_for_support'), 
            class: "py-4 px-1 border-b-2 font-medium text-sm #{ @filter_status == 'waiting_for_support' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' }" do %>
          Waiting for Support
        <% end %>
        
        <%= link_to support_tickets_path(status: 'closed'), 
            class: "py-4 px-1 border-b-2 font-medium text-sm #{ @filter_status == 'closed' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' }" do %>
          Closed
        <% end %>
      </nav>
    </div>

    <!-- Tickets List -->
    <div class="p-6">
      <% if @support_tickets.any? %>
        <div class="space-y-4">
          <% @support_tickets.each do |ticket| %>
            <div class="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow duration-200">
              <div class="flex items-start justify-between">
                <div class="flex-1">
                  <div class="flex items-center space-x-3 mb-2">
                    <h3 class="text-lg font-semibold text-gray-900">
                      <%= link_to ticket.subject, support_ticket_path(ticket), 
                          class: "hover:text-blue-600 transition-colors" %>
                    </h3>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= ticket.priority_color %>">
                      <%= ticket.priority_display_name %>
                    </span>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= ticket.status_color %>">
                      <%= ticket.status_display_name %>
                    </span>
                  </div>
                  
                  <p class="text-gray-600 text-sm mb-3">
                    <%= truncate(ticket.description, length: 150) %>
                  </p>
                  
                  <div class="flex items-center space-x-6 text-sm text-gray-500">
                    <div class="flex items-center space-x-1">
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                      </svg>
                      <span><%= ticket.ticket_number %></span>
                    </div>
                    
                    <div class="flex items-center space-x-1">
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                      </svg>
                      <span><%= ticket.category_display_name %></span>
                    </div>
                    
                    <div class="flex items-center space-x-1">
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                      </svg>
                      <span>Created <%= time_ago_in_words(ticket.created_at) %> ago</span>
                    </div>
                    
                    <div class="flex items-center space-x-1">
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                      </svg>
                      <span><%= ticket.support_messages.count %> messages</span>
                    </div>
                  </div>
                </div>
                
                <div class="flex items-center space-x-3 ml-6">
                  <%= link_to support_ticket_path(ticket), 
                      class: "inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors" do %>
                    View Details
                  <% end %>
                  
                  <% if ticket.can_be_closed? %>
                    <%= link_to close_support_ticket_path(ticket),
                        data: { turbo_method: :patch, turbo_confirm: "Are you sure you want to close this ticket?" },
                        class: "inline-flex items-center px-4 py-2 border border-gray-300 text-gray-700 text-sm font-medium rounded-lg hover:bg-gray-50 transition-colors" do %>
                      Close
                    <% end %>
                  <% end %>
                </div>
              </div>
            </div>
          <% end %>
        </div>

        <!-- Pagination -->
        <% if respond_to?(:paginate) %>
          <div class="mt-8 flex justify-center">
            <%= paginate @support_tickets, theme: 'twitter-bootstrap-4' %>
          </div>
        <% end %>
      <% else %>
        <!-- Empty State -->
        <div class="text-center py-12">
          <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"></path>
            </svg>
          </div>
          
          <h3 class="text-xl font-semibold text-gray-900 mb-3">
            <% if @filter_status == 'all' %>
              No Support Tickets Yet
            <% else %>
              No <%= @filter_status.humanize %> Tickets
            <% end %>
          </h3>
          
          <p class="text-gray-600 mb-6 max-w-md mx-auto">
            <% if @filter_status == 'all' %>
              You haven't created any support tickets yet. If you need help, don't hesitate to reach out to our support team.
            <% else %>
              You don't have any <%= @filter_status.humanize.downcase %> tickets at the moment.
            <% end %>
          </p>
          
          <div class="flex items-center justify-center space-x-4">
            <%= link_to contact_help_index_path, 
                class: "inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors" do %>
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
              </svg>
              Create Support Ticket
            <% end %>
            
            <%= link_to help_index_path, 
                class: "inline-flex items-center px-6 py-3 border border-gray-300 text-gray-700 font-medium rounded-lg hover:bg-gray-50 transition-colors" do %>
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              Browse Help Articles
            <% end %>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</div>
