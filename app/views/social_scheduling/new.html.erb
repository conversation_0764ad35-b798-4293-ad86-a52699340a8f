<%# New Scheduled Post Form %>
<div class="bg-gray-50 min-h-screen">
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    
    <!-- Header -->
    <div class="bg-white rounded-xl shadow-sm p-8 mb-8">
      <div class="flex items-center space-x-4">
        <%= link_to campaign_social_scheduling_index_path(@campaign), 
            class: "flex items-center text-gray-500 hover:text-gray-700" do %>
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
          </svg>
          Back to Calendar
        <% end %>
      </div>
      
      <div class="mt-4">
        <h1 class="text-3xl font-bold text-gray-900">Schedule New Post</h1>
        <p class="text-gray-600">Create and schedule content for your social media platforms</p>
      </div>
    </div>

    <!-- Scheduling Form -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100">
      <%= form_with model: [@campaign, @scheduled_post], 
          url: campaign_social_scheduling_index_path(@campaign), 
          local: true, html: { class: "p-8 space-y-8" } do |form| %>
        
        <!-- Platform Selection -->
        <div class="border-b border-gray-200 pb-8">
          <h2 class="text-xl font-semibold text-gray-900 mb-4">Select Platform</h2>
          
          <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
            <% @available_platforms.each do |platform| %>
              <label class="relative flex items-center p-4 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50 focus-within:ring-2 focus-within:ring-blue-500">
                <%= form.radio_button :platform, platform, 
                    class: "sr-only peer", 
                    required: true %>
                <div class="flex items-center space-x-3 peer-checked:text-blue-600">
                  <%= render "shared/platform_icons/#{platform}", css_class: "w-6 h-6" %>
                  <span class="font-medium"><%= platform.titleize %></span>
                </div>
                <div class="absolute top-2 right-2 hidden peer-checked:block">
                  <svg class="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                  </svg>
                </div>
              </label>
            <% end %>
          </div>
          
          <% if form.object.errors[:platform].any? %>
            <p class="mt-2 text-sm text-red-600"><%= form.object.errors[:platform].first %></p>
          <% end %>
        </div>

        <!-- Content -->
        <div class="border-b border-gray-200 pb-8">
          <h2 class="text-xl font-semibold text-gray-900 mb-4">Content</h2>
          
          <div class="space-y-4">
            <div>
              <%= form.label :content, "Post Content", class: "block text-sm font-medium text-gray-700 mb-2" %>
              <%= form.text_area :content, 
                  class: "w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",
                  rows: 4,
                  placeholder: "Write your post content here...",
                  required: true %>
              <div class="flex justify-between mt-1">
                <div id="characterCount" class="text-sm text-gray-500">0 characters</div>
                <div id="platformLimit" class="text-sm text-gray-500"></div>
              </div>
              <% if form.object.errors[:content].any? %>
                <p class="mt-1 text-sm text-red-600"><%= form.object.errors[:content].first %></p>
              <% end %>
            </div>

            <!-- Content Variants -->
            <div id="contentVariants" class="hidden">
              <label class="block text-sm font-medium text-gray-700 mb-2">
                Use existing content variant
              </label>
              <select id="contentVariantSelect" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                <option value="">Select a content variant...</option>
                <% @content_variants.each do |platform, content| %>
                  <% 
                    # Extract content text from either string or hash structure
                    content_text = case content
                                   when Hash
                                     content['content'] || content[:content] || ''
                                   when String
                                     content || ''
                                   else
                                     ''
                                   end
                  %>
                  <option value="<%= content_text %>" data-platform="<%= platform %>">
                    <%= platform.titleize %>: <%= truncate(content_text, length: 100) %>
                  </option>
                <% end %>
              </select>
            </div>

            <!-- Hashtags -->
            <div>
              <%= form.label :hashtags, "Hashtags", class: "block text-sm font-medium text-gray-700 mb-2" %>
              <div class="flex space-x-2">
                <%= form.text_field :hashtags, 
                    class: "flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",
                    placeholder: "#marketing #socialmedia #content",
                    value: form.object.hashtags&.join(' ') %>
                <button type="button" 
                        onclick="generateHashtags()"
                        class="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200">
                  Generate
                </button>
              </div>
              <p class="mt-1 text-sm text-gray-500">Separate hashtags with spaces</p>
            </div>
          </div>
        </div>

        <!-- Scheduling -->
        <div class="border-b border-gray-200 pb-8">
          <h2 class="text-xl font-semibold text-gray-900 mb-4">Schedule</h2>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <%= form.label :scheduled_at, "Date & Time", class: "block text-sm font-medium text-gray-700 mb-2" %>
              <%= form.datetime_local_field :scheduled_at, 
                  class: "w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",
                  min: Time.current.strftime('%Y-%m-%dT%H:%M'),
                  value: (params[:date] ? Date.parse(params[:date]).strftime('%Y-%m-%dT09:00') : nil),
                  required: true %>
              <% if form.object.errors[:scheduled_at].any? %>
                <p class="mt-1 text-sm text-red-600"><%= form.object.errors[:scheduled_at].first %></p>
              <% end %>
            </div>
            
            <div>
              <%= form.label :timezone, "Timezone", class: "block text-sm font-medium text-gray-700 mb-2" %>
              <%= form.select :timezone, @timezone_options, 
                  { selected: 'America/New_York' },
                  { class: "w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" } %>
            </div>
          </div>

          <!-- Optimal Times Suggestion -->
          <div id="optimalTimesSuggestion" class="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg hidden">
            <div class="flex items-start space-x-3">
              <svg class="w-5 h-5 text-blue-600 flex-shrink-0 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              <div>
                <h4 class="text-sm font-medium text-blue-900">Optimal Posting Times</h4>
                <p class="text-sm text-blue-800 mt-1">Based on your audience data, the best times to post are:</p>
                <div id="suggestedTimes" class="mt-2 flex flex-wrap gap-2"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- Advanced Options -->
        <div class="border-b border-gray-200 pb-8">
          <h2 class="text-xl font-semibold text-gray-900 mb-4">Advanced Options</h2>
          
          <div class="space-y-4">
            <div class="flex items-center">
              <%= form.check_box :auto_hashtags, 
                  class: "h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" %>
              <%= form.label :auto_hashtags, "Automatically add trending hashtags", 
                  class: "ml-2 block text-sm text-gray-900" %>
            </div>
            
            <div class="flex items-center">
              <%= form.check_box :include_link, 
                  class: "h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" %>
              <%= form.label :include_link, "Include campaign link", 
                  class: "ml-2 block text-sm text-gray-900" %>
            </div>
          </div>
        </div>

        <!-- Media Upload -->
        <div class="border-b border-gray-200 pb-8">
          <h2 class="text-xl font-semibold text-gray-900 mb-4">Media (Optional)</h2>
          
          <div class="mt-2 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
            <div class="space-y-1 text-center">
              <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
              </svg>
              <div class="flex text-sm text-gray-600">
                <label for="media_upload" class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500">
                  <span>Upload images or videos</span>
                  <input id="media_upload" name="media_upload" type="file" class="sr-only" multiple accept="image/*,video/*">
                </label>
                <p class="pl-1">or drag and drop</p>
              </div>
              <p class="text-xs text-gray-500">PNG, JPG, GIF, MP4 up to 10MB</p>
            </div>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="flex items-center justify-between pt-6">
          <%= link_to campaign_social_scheduling_index_path(@campaign), 
              class: "px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50" do %>
            Cancel
          <% end %>
          
          <div class="flex space-x-3">
            <%= form.submit "Save as Draft", 
                name: "draft",
                class: "px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50" %>
            <%= form.submit "Schedule Post", 
                class: "inline-flex items-center px-6 py-2 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" %>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</div>

<!-- JavaScript for dynamic form behavior -->
<script>
document.addEventListener('DOMContentLoaded', function() {
  const contentTextarea = document.querySelector('textarea[name="scheduled_post[content]"]');
  const characterCount = document.getElementById('characterCount');
  const platformLimit = document.getElementById('platformLimit');
  const contentVariantSelect = document.getElementById('contentVariantSelect');
  const optimalTimesSuggestion = document.getElementById('optimalTimesSuggestion');
  
  // Platform character limits
  const platformLimits = {
    'twitter': 280,
    'facebook': 63206,
    'instagram': 2200,
    'linkedin': 3000,
    'tiktok': 150,
    'youtube': 5000
  };
  
  // Optimal times data
  const optimalTimes = <%= @optimal_times.to_json.html_safe %>;
  
  // Update character count
  function updateCharacterCount() {
    const count = contentTextarea.value.length;
    characterCount.textContent = `${count} characters`;
    
    const selectedPlatform = document.querySelector('input[name="scheduled_post[platform]"]:checked')?.value;
    if (selectedPlatform && platformLimits[selectedPlatform]) {
      const limit = platformLimits[selectedPlatform];
      platformLimit.textContent = `${limit} max`;
      
      if (count > limit) {
        characterCount.classList.add('text-red-600');
        platformLimit.classList.add('text-red-600');
      } else {
        characterCount.classList.remove('text-red-600');
        platformLimit.classList.remove('text-red-600');
      }
    }
  }
  
  // Handle platform selection
  function handlePlatformChange() {
    const selectedPlatform = document.querySelector('input[name="scheduled_post[platform]"]:checked')?.value;
    
    if (selectedPlatform) {
      // Show content variants for selected platform
      updateContentVariants(selectedPlatform);
      
      // Show optimal times
      showOptimalTimes(selectedPlatform);
      
      // Update character limit
      updateCharacterCount();
    }
  }
  
  function updateContentVariants(platform) {
    const variants = Array.from(contentVariantSelect.options);
    variants.forEach(option => {
      if (option.value === '' || option.dataset.platform === platform) {
        option.style.display = 'block';
      } else {
        option.style.display = 'none';
      }
    });
    
    document.getElementById('contentVariants').classList.remove('hidden');
  }
  
  function showOptimalTimes(platform) {
    if (optimalTimes[platform]) {
      const suggestedTimesContainer = document.getElementById('suggestedTimes');
      suggestedTimesContainer.innerHTML = '';
      
      optimalTimes[platform].forEach(time => {
        const button = document.createElement('button');
        button.type = 'button';
        button.className = 'px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm hover:bg-blue-200';
        button.textContent = time;
        button.onclick = () => setOptimalTime(time);
        suggestedTimesContainer.appendChild(button);
      });
      
      optimalTimesSuggestion.classList.remove('hidden');
    }
  }
  
  function setOptimalTime(time) {
    const scheduledAtInput = document.querySelector('input[name="scheduled_post[scheduled_at]"]');
    const currentDate = scheduledAtInput.value.split('T')[0] || new Date().toISOString().split('T')[0];
    scheduledAtInput.value = `${currentDate}T${time}`;
  }
  
  // Event listeners
  contentTextarea.addEventListener('input', updateCharacterCount);
  
  document.querySelectorAll('input[name="scheduled_post[platform]"]').forEach(radio => {
    radio.addEventListener('change', handlePlatformChange);
  });
  
  contentVariantSelect.addEventListener('change', function() {
    if (this.value) {
      contentTextarea.value = this.value;
      updateCharacterCount();
    }
  });
  
  // Initialize
  updateCharacterCount();
});

function generateHashtags() {
  const content = document.querySelector('textarea[name="scheduled_post[content]"]').value;
  const platform = document.querySelector('input[name="scheduled_post[platform]"]:checked')?.value;
  
  if (!content || !platform) {
    alert('Please select a platform and enter some content first.');
    return;
  }
  
  // This would call the AI hashtag generation API
  alert('Hashtag generation would be implemented here');
}
</script>
