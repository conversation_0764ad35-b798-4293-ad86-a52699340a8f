<%# Social Scheduling Calendar Index %>
<div class="bg-gray-50 min-h-screen">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    
    <!-- Header -->
    <div class="bg-white rounded-xl shadow-sm p-8 mb-8">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-3xl font-bold text-gray-900 mb-2">Social Media Scheduling</h1>
          <p class="text-gray-600">Plan and schedule your social media content across all platforms</p>
        </div>
        
        <div class="flex items-center space-x-3">
          <%= link_to campaign_social_scheduling_path(@campaign, 'new'), 
              class: "inline-flex items-center px-4 py-2 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors" do %>
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Schedule Post
          <% end %>
          
          <button type="button" 
                  onclick="openBulkScheduleModal()"
                  class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
            </svg>
            Bulk Schedule
          </button>
        </div>
      </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
      <!-- Calendar -->
      <div class="lg:col-span-3">
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
          <div class="p-6 border-b border-gray-200">
            <div class="flex items-center justify-between">
              <h2 class="text-xl font-semibold text-gray-900">Content Calendar</h2>
              
              <!-- Calendar Controls -->
              <div class="flex items-center space-x-3">
                <div class="flex items-center space-x-2">
                  <button type="button" id="prevMonth" class="p-2 text-gray-400 hover:text-gray-600">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                  </button>
                  <h3 id="currentMonth" class="text-lg font-medium text-gray-900"></h3>
                  <button type="button" id="nextMonth" class="p-2 text-gray-400 hover:text-gray-600">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                  </button>
                </div>
                
                <div class="flex items-center space-x-2">
                  <button type="button" id="todayBtn" class="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200">Today</button>
                  <select id="viewSelect" class="text-sm border-gray-300 rounded">
                    <option value="month">Month</option>
                    <option value="week">Week</option>
                    <option value="day">Day</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Calendar Grid -->
          <div id="calendar" class="p-6"></div>
        </div>
      </div>

      <!-- Sidebar -->
      <div class="space-y-6">
        
        <!-- Platform Statistics -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Platform Overview</h3>
          
          <div class="space-y-4">
            <% @platform_stats.each do |platform, stats| %>
              <div class="border border-gray-200 rounded-lg p-4">
                <div class="flex items-center justify-between mb-2">
                  <div class="flex items-center space-x-2">
                    <%= render "shared/platform_icons/#{platform}", css_class: "w-5 h-5" %>
                    <span class="font-medium text-gray-900"><%= platform.titleize %></span>
                  </div>
                  <span class="text-sm text-gray-500"><%= stats[:total_scheduled] %> scheduled</span>
                </div>
                
                <div class="grid grid-cols-3 gap-2 text-xs">
                  <div class="text-center">
                    <div class="text-green-600 font-medium"><%= stats[:posted] %></div>
                    <div class="text-gray-500">Posted</div>
                  </div>
                  <div class="text-center">
                    <div class="text-blue-600 font-medium"><%= stats[:pending] %></div>
                    <div class="text-gray-500">Pending</div>
                  </div>
                  <div class="text-center">
                    <div class="text-red-600 font-medium"><%= stats[:failed] %></div>
                    <div class="text-gray-500">Failed</div>
                  </div>
                </div>
                
                <% if stats[:next_post] %>
                  <div class="mt-2 pt-2 border-t border-gray-200">
                    <div class="text-xs text-gray-500">Next post:</div>
                    <div class="text-xs font-medium text-gray-900">
                      <%= stats[:next_post].strftime('%b %d, %I:%M %p') %>
                    </div>
                  </div>
                <% end %>
              </div>
            <% end %>
          </div>
        </div>

        <!-- Optimal Times -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Optimal Posting Times</h3>
          
          <div class="space-y-3">
            <% @optimal_times.each do |platform, times| %>
              <div>
                <div class="flex items-center space-x-2 mb-1">
                  <%= render "shared/platform_icons/#{platform}", css_class: "w-4 h-4" %>
                  <span class="text-sm font-medium text-gray-900"><%= platform.titleize %></span>
                </div>
                <div class="text-xs text-gray-600 pl-6">
                  <%= times.map { |time| Time.parse(time).strftime('%I:%M %p') }.join(', ') %>
                </div>
              </div>
            <% end %>
          </div>
          
          <div class="mt-4 pt-4 border-t border-gray-200">
            <p class="text-xs text-gray-500">
              Based on your audience engagement patterns
            </p>
          </div>
        </div>

        <!-- Quick Actions -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
          
          <div class="space-y-3">
            <button type="button" 
                    onclick="openOptimalScheduleModal()"
                    class="w-full flex items-center px-3 py-2 text-sm text-left text-gray-700 hover:bg-gray-50 rounded-md">
              <svg class="w-4 h-4 mr-3 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
              </svg>
              Auto-schedule at optimal times
            </button>
            
            <button type="button" 
                    onclick="duplicateWeekSchedule()"
                    class="w-full flex items-center px-3 py-2 text-sm text-left text-gray-700 hover:bg-gray-50 rounded-md">
              <svg class="w-4 h-4 mr-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
              </svg>
              Duplicate this week's schedule
            </button>
            
            <button type="button" 
                    onclick="exportSchedule()"
                    class="w-full flex items-center px-3 py-2 text-sm text-left text-gray-700 hover:bg-gray-50 rounded-md">
              <svg class="w-4 h-4 mr-3 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
              Export schedule to CSV
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Include FullCalendar CSS and JS -->
<link href='https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.css' rel='stylesheet' />
<script src='https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.js'></script>

<!-- Calendar JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
  var calendarEl = document.getElementById('calendar');
  var calendar = new FullCalendar.Calendar(calendarEl, {
    initialView: 'dayGridMonth',
    headerToolbar: false, // We're using custom controls
    height: 'auto',
    events: '<%= campaign_social_scheduling_calendar_data_path(@campaign) %>',
    
    eventClick: function(info) {
      window.location.href = info.event.extendedProps.url;
    },
    
    eventDidMount: function(info) {
      // Color code events by platform
      const platformColors = {
        'twitter': 'bg-blue-100 border-blue-500 text-blue-800',
        'facebook': 'bg-blue-100 border-blue-600 text-blue-900',
        'instagram': 'bg-pink-100 border-pink-500 text-pink-800',
        'linkedin': 'bg-indigo-100 border-indigo-500 text-indigo-800',
        'tiktok': 'bg-gray-100 border-gray-500 text-gray-800',
        'youtube': 'bg-red-100 border-red-500 text-red-800'
      };
      
      const platform = info.event.extendedProps.platform;
      const colorClass = platformColors[platform] || 'bg-gray-100 border-gray-500 text-gray-800';
      
      info.el.className = `fc-event ${colorClass} border-l-4 px-2 py-1 text-xs rounded-r`;
    },
    
    dateClick: function(info) {
      // Quick schedule on date click
      openQuickScheduleModal(info.dateStr);
    }
  });
  
  calendar.render();
  
  // Custom navigation controls
  document.getElementById('prevMonth').addEventListener('click', function() {
    calendar.prev();
    updateMonthDisplay();
  });
  
  document.getElementById('nextMonth').addEventListener('click', function() {
    calendar.next();
    updateMonthDisplay();
  });
  
  document.getElementById('todayBtn').addEventListener('click', function() {
    calendar.today();
    updateMonthDisplay();
  });
  
  document.getElementById('viewSelect').addEventListener('change', function() {
    calendar.changeView(this.value === 'month' ? 'dayGridMonth' : 
                       this.value === 'week' ? 'timeGridWeek' : 'timeGridDay');
  });
  
  function updateMonthDisplay() {
    const currentDate = calendar.getDate();
    document.getElementById('currentMonth').textContent = 
      currentDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
  }
  
  updateMonthDisplay();
});

// Modal and action functions
function openBulkScheduleModal() {
  // Implement bulk schedule modal
  alert('Bulk schedule modal would open here');
}

function openOptimalScheduleModal() {
  // Implement optimal schedule modal
  alert('Optimal schedule modal would open here');
}

function openQuickScheduleModal(dateStr) {
  // Implement quick schedule modal for specific date
  const url = '<%= campaign_social_scheduling_path(@campaign, "new") %>?date=' + dateStr;
  window.location.href = url;
}

function duplicateWeekSchedule() {
  if (confirm('Duplicate this week\'s schedule for next week?')) {
    // Implement week duplication
    alert('Week duplication would be implemented here');
  }
}

function exportSchedule() {
  // Implement schedule export
  alert('Schedule export would be implemented here');
}
</script>

<!-- Platform color legend -->
<div class="fixed bottom-4 right-4 bg-white rounded-lg shadow-lg border border-gray-200 p-4">
  <h4 class="text-sm font-medium text-gray-900 mb-2">Platform Legend</h4>
  <div class="grid grid-cols-2 gap-2 text-xs">
    <div class="flex items-center space-x-2">
      <div class="w-3 h-3 bg-blue-500 rounded"></div>
      <span>Twitter</span>
    </div>
    <div class="flex items-center space-x-2">
      <div class="w-3 h-3 bg-blue-600 rounded"></div>
      <span>Facebook</span>
    </div>
    <div class="flex items-center space-x-2">
      <div class="w-3 h-3 bg-pink-500 rounded"></div>
      <span>Instagram</span>
    </div>
    <div class="flex items-center space-x-2">
      <div class="w-3 h-3 bg-indigo-500 rounded"></div>
      <span>LinkedIn</span>
    </div>
    <div class="flex items-center space-x-2">
      <div class="w-3 h-3 bg-gray-500 rounded"></div>
      <span>TikTok</span>
    </div>
    <div class="flex items-center space-x-2">
      <div class="w-3 h-3 bg-red-500 rounded"></div>
      <span>YouTube</span>
    </div>
  </div>
</div>
