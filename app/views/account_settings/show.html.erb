<% content_for :title, "Account Settings" %>

<div class="max-w-7xl mx-auto space-y-8">
  <!-- Page Header -->
  <div class="bg-white rounded-xl shadow-sm p-8">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold text-gray-900 mb-2">Account Settings</h1>
        <p class="text-gray-600">Manage your account security, notifications, and billing</p>
      </div>
      <div class="flex items-center space-x-3">
        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
          <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
          </svg>
          Account Active
        </span>
      </div>
    </div>
  </div>

  <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
    <!-- Main Content -->
    <div class="lg:col-span-2 space-y-8">
      
      <!-- Security Settings -->
      <div class="bg-white rounded-xl shadow-sm">
        <div class="p-6 border-b border-gray-200">
          <div class="flex items-center justify-between">
            <div>
              <h2 class="text-xl font-semibold text-gray-900">Security Settings</h2>
              <p class="text-gray-600 text-sm">Manage your password and security preferences</p>
            </div>
            <svg class="w-6 h-6 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
            </svg>
          </div>
        </div>
        
        <div class="p-6 space-y-6">
          <!-- Password Change -->
          <div class="border border-gray-200 rounded-lg p-4">
            <div class="flex items-center justify-between mb-4">
              <div>
                <h3 class="text-lg font-medium text-gray-900">Password</h3>
                <p class="text-sm text-gray-600">Last changed <%= time_ago_in_words(@security_settings[:last_password_change]) %> ago</p>
              </div>
              <button type="button" onclick="togglePasswordForm()" 
                      class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors">
                Change Password
              </button>
            </div>
            
            <!-- Password Change Form (Hidden by default) -->
            <div id="password-form" class="hidden mt-4 pt-4 border-t border-gray-200">
              <%= form_with url: update_password_account_settings_path, method: :patch, local: true, class: "space-y-4" do |form| %>
                <div>
                  <%= form.label :current_password, class: "block text-sm font-medium text-gray-700 mb-2" %>
                  <%= form.password_field :current_password, 
                      class: "w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500",
                      placeholder: "Enter current password" %>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <%= form.label "user[password]", "New Password", class: "block text-sm font-medium text-gray-700 mb-2" %>
                    <%= form.password_field "user[password]", 
                        class: "w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500",
                        placeholder: "Enter new password" %>
                  </div>
                  
                  <div>
                    <%= form.label "user[password_confirmation]", "Confirm Password", class: "block text-sm font-medium text-gray-700 mb-2" %>
                    <%= form.password_field "user[password_confirmation]", 
                        class: "w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500",
                        placeholder: "Confirm new password" %>
                  </div>
                </div>
                
                <div class="flex items-center space-x-4">
                  <%= form.submit "Update Password", 
                      class: "px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors" %>
                  <button type="button" onclick="togglePasswordForm()" 
                          class="px-4 py-2 border border-gray-300 text-gray-700 text-sm font-medium rounded-lg hover:bg-gray-50 transition-colors">
                    Cancel
                  </button>
                </div>
              <% end %>
            </div>
          </div>

          <!-- Two-Factor Authentication -->
          <div class="border border-gray-200 rounded-lg p-4">
            <div class="flex items-center justify-between">
              <div>
                <h3 class="text-lg font-medium text-gray-900">Two-Factor Authentication</h3>
                <p class="text-sm text-gray-600">Add an extra layer of security to your account</p>
              </div>
              <div class="flex items-center space-x-3">
                <% if @security_settings[:two_factor_enabled] %>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    Enabled
                  </span>
                  <%= link_to update_security_account_settings_path(enable_2fa: false),
                      data: { turbo_method: :patch },
                      class: "text-sm text-red-600 hover:text-red-700 font-medium" do %>
                    Disable
                  <% end %>
                <% else %>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                    Disabled
                  </span>
                  <%= link_to update_security_account_settings_path(enable_2fa: true),
                      data: { turbo_method: :patch },
                      class: "inline-flex items-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700 transition-colors" do %>
                    Enable 2FA
                  <% end %>
                <% end %>
              </div>
            </div>
          </div>

          <!-- Active Sessions -->
          <div class="border border-gray-200 rounded-lg p-4">
            <div class="flex items-center justify-between mb-4">
              <div>
                <h3 class="text-lg font-medium text-gray-900">Active Sessions</h3>
                <p class="text-sm text-gray-600">Manage your active login sessions</p>
              </div>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                <%= @security_settings[:active_sessions] %> Active
              </span>
            </div>
            
            <div class="space-y-3">
              <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div class="flex items-center space-x-3">
                  <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                  </svg>
                  <div>
                    <p class="text-sm font-medium text-gray-900">Current Session</p>
                    <p class="text-xs text-gray-500">Chrome on macOS • <%= request.remote_ip %></p>
                  </div>
                </div>
                <span class="text-xs text-green-600 font-medium">Active Now</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Notification Settings -->
      <div class="bg-white rounded-xl shadow-sm">
        <div class="p-6 border-b border-gray-200">
          <h2 class="text-xl font-semibold text-gray-900">Notification Settings</h2>
          <p class="text-gray-600 text-sm">Choose how you want to be notified</p>
        </div>
        
        <div class="p-6">
          <%= form_with model: @notification_settings, url: update_notifications_account_settings_path, method: :patch, local: true, class: "space-y-6" do |form| %>
            
            <!-- Email Notifications -->
            <div>
              <h3 class="text-lg font-medium text-gray-900 mb-4">Email Notifications</h3>
              <div class="space-y-4">
                <div class="flex items-center justify-between">
                  <div>
                    <label class="text-sm font-medium text-gray-700">Campaign Updates</label>
                    <p class="text-sm text-gray-500">Get notified about campaign status changes</p>
                  </div>
                  <%= form.check_box :email_campaigns, class: "rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500" %>
                </div>
                
                <div class="flex items-center justify-between">
                  <div>
                    <label class="text-sm font-medium text-gray-700">System Notifications</label>
                    <p class="text-sm text-gray-500">Important system updates and maintenance</p>
                  </div>
                  <%= form.check_box :email_system, class: "rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500" %>
                </div>
                
                <div class="flex items-center justify-between">
                  <div>
                    <label class="text-sm font-medium text-gray-700">Marketing Updates</label>
                    <p class="text-sm text-gray-500">Product updates, tips, and best practices</p>
                  </div>
                  <%= form.check_box :email_marketing, class: "rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500" %>
                </div>
              </div>
            </div>

            <!-- Push Notifications -->
            <div class="border-t border-gray-200 pt-6">
              <h3 class="text-lg font-medium text-gray-900 mb-4">Push Notifications</h3>
              <div class="space-y-4">
                <div class="flex items-center justify-between">
                  <div>
                    <label class="text-sm font-medium text-gray-700">Campaign Alerts</label>
                    <p class="text-sm text-gray-500">Real-time campaign performance alerts</p>
                  </div>
                  <%= form.check_box :push_campaigns, class: "rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500" %>
                </div>
                
                <div class="flex items-center justify-between">
                  <div>
                    <label class="text-sm font-medium text-gray-700">System Alerts</label>
                    <p class="text-sm text-gray-500">Critical system notifications</p>
                  </div>
                  <%= form.check_box :push_system, class: "rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500" %>
                </div>
              </div>
            </div>

            <!-- Notification Frequency -->
            <div class="border-t border-gray-200 pt-6">
              <h3 class="text-lg font-medium text-gray-900 mb-4">Notification Frequency</h3>
              <div>
                <%= form.label :digest_frequency, class: "block text-sm font-medium text-gray-700 mb-2" %>
                <%= form.select :digest_frequency,
                    options_for_select(NotificationSetting.digest_frequency_options, @notification_settings.digest_frequency),
                    {},
                    { class: "w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" } %>
                <p class="mt-1 text-sm text-gray-500">Choose how often you want to receive notification summaries</p>
              </div>
            </div>
            
            <div class="flex justify-end">
              <%= form.submit "Save Notification Settings", 
                  class: "inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors" %>
            </div>
          <% end %>
        </div>
      </div>
    </div>

    <!-- Sidebar -->
    <div class="space-y-8">
      
      <!-- Account Overview -->
      <div class="bg-white rounded-xl shadow-sm p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Account Overview</h3>
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Plan</label>
            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-900"><%= @billing_info[:plan] %></span>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                Active
              </span>
            </div>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Billing Cycle</label>
            <p class="text-sm text-gray-900 capitalize"><%= @billing_info[:billing_cycle] %></p>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Next Billing Date</label>
            <p class="text-sm text-gray-900"><%= @billing_info[:next_billing_date].strftime("%B %d, %Y") %></p>
          </div>
          
          <div class="pt-4 border-t border-gray-200">
            <button class="w-full text-center text-sm text-blue-600 hover:text-blue-700 font-medium">
              Manage Billing
            </button>
          </div>
        </div>
      </div>

      <!-- Team Members (for admin/owner) -->
      <% if current_user.admin_or_owner? && @team_members %>
        <div class="bg-white rounded-xl shadow-sm p-6">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">Team Members</h3>
            <span class="text-sm text-gray-500"><%= @team_members.count %> members</span>
          </div>
          
          <div class="space-y-3">
            <% @team_members.first(5).each do |member| %>
              <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white text-sm font-bold">
                  <%= member.first_name&.first || "U" %>
                </div>
                <div class="flex-1 min-w-0">
                  <p class="text-sm font-medium text-gray-900 truncate">
                    <%= member.full_name %>
                  </p>
                  <p class="text-xs text-gray-500 capitalize"><%= member.role %></p>
                </div>
              </div>
            <% end %>
          </div>
          
          <div class="pt-4 border-t border-gray-200 mt-4">
            <button class="w-full text-center text-sm text-blue-600 hover:text-blue-700 font-medium">
              Manage Team
            </button>
          </div>
        </div>
      <% end %>

      <!-- Quick Actions -->
      <div class="bg-white rounded-xl shadow-sm p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
        <div class="space-y-3">
          <%= link_to profile_path, 
              class: "flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors group" do %>
            <svg class="w-5 h-5 text-gray-400 group-hover:text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
            </svg>
            <span class="text-sm font-medium text-gray-700 group-hover:text-gray-900">Profile Settings</span>
          <% end %>
          
          <%= link_to help_index_path, 
              class: "flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors group" do %>
            <svg class="w-5 h-5 text-gray-400 group-hover:text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <span class="text-sm font-medium text-gray-700 group-hover:text-gray-900">Help & Support</span>
          <% end %>
        </div>
      </div>

      <!-- Danger Zone -->
      <div class="bg-red-50 border border-red-200 rounded-xl p-6">
        <h3 class="text-lg font-semibold text-red-900 mb-4">Danger Zone</h3>
        <p class="text-sm text-red-700 mb-4">
          Once you delete your account, there is no going back. Please be certain.
        </p>
        <button onclick="showDeleteConfirmation()"
                class="w-full px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-lg hover:bg-red-700 transition-colors">
          Delete Account
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Delete Account Modal (Hidden by default) -->
<div id="delete-modal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
  <div class="bg-white rounded-xl max-w-md w-full p-6">
    <div class="flex items-center space-x-3 mb-4">
      <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
      </svg>
      <h3 class="text-lg font-semibold text-gray-900">Delete Account</h3>
    </div>
    
    <p class="text-sm text-gray-600 mb-4">
      This action cannot be undone. This will permanently delete your account and remove all associated data.
    </p>
    
    <%= form_with url: delete_account_account_settings_path, method: :delete, local: true, class: "space-y-4" do |form| %>
      <div>
        <%= form.label :confirmation, "Type your email to confirm:", class: "block text-sm font-medium text-gray-700 mb-2" %>
        <%= form.text_field :confirmation, 
            class: "w-full rounded-lg border-gray-300 shadow-sm focus:border-red-500 focus:ring-red-500",
            placeholder: current_user.email %>
      </div>
      
      <div class="flex items-center space-x-4">
        <%= form.submit "Delete Account", 
            class: "flex-1 px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-lg hover:bg-red-700 transition-colors" %>
        <button type="button" onclick="hideDeleteConfirmation()" 
                class="flex-1 px-4 py-2 border border-gray-300 text-gray-700 text-sm font-medium rounded-lg hover:bg-gray-50 transition-colors">
          Cancel
        </button>
      </div>
    <% end %>
  </div>
</div>

<script>
  function togglePasswordForm() {
    const form = document.getElementById('password-form');
    form.classList.toggle('hidden');
  }
  
  function showDeleteConfirmation() {
    document.getElementById('delete-modal').classList.remove('hidden');
  }
  
  function hideDeleteConfirmation() {
    document.getElementById('delete-modal').classList.add('hidden');
  }
</script>
