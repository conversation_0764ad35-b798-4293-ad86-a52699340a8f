<%
  # User Avatar Component
  # Parameters:
  # - user: User object (required)
  # - size: Size variant - 'xs', 'sm', 'md', 'lg', 'xl', '2xl' (default: 'md')
  # - css_class: Additional CSS classes (optional)
  # - show_status: Whether to show online status indicator (default: false)
  # - clickable: Whether the avatar should be clickable (default: false)
  # - link_to: URL to link to if clickable (optional)

  # Set defaults
  user = local_assigns[:user]
  size = local_assigns[:size] || 'md'
  css_class = local_assigns[:css_class] || ''
  show_status = local_assigns[:show_status] || false
  clickable = local_assigns[:clickable] || false
  link_to_url = local_assigns[:link_to]

  # Size classes
  size_classes = {
    'xs' => 'w-6 h-6 text-xs',
    'sm' => 'w-8 h-8 text-sm',
    'md' => 'w-10 h-10 text-base',
    'lg' => 'w-16 h-16 text-xl',
    'xl' => 'w-20 h-20 text-2xl',
    '2xl' => 'w-24 h-24 text-3xl'
  }

  # Status indicator size classes
  status_size_classes = {
    'xs' => 'w-1.5 h-1.5',
    'sm' => 'w-2 h-2',
    'md' => 'w-2.5 h-2.5',
    'lg' => 'w-3 h-3',
    'xl' => 'w-4 h-4',
    '2xl' => 'w-5 h-5'
  }

  # Build CSS classes
  avatar_classes = [
    size_classes[size] || size_classes['md'],
    'rounded-full flex items-center justify-center font-bold overflow-hidden',
    css_class
  ].compact.join(' ')

  # Generate user initials
  initials = if user&.first_name.present? && user&.last_name.present?
               "#{user.first_name.first}#{user.last_name.first}".upcase
             elsif user&.first_name.present?
               user.first_name.first.upcase
             elsif user&.last_name.present?
               user.last_name.first.upcase
             elsif user&.email.present?
               user.email.first.upcase
             else
               "U"
             end

  # Generate consistent color based on user ID or email
  color_seed = user&.id || user&.email&.sum || 0
  colors = [
    'bg-gradient-to-br from-blue-500 to-purple-500',
    'bg-gradient-to-br from-green-500 to-teal-500',
    'bg-gradient-to-br from-purple-500 to-pink-500',
    'bg-gradient-to-br from-orange-500 to-red-500',
    'bg-gradient-to-br from-indigo-500 to-blue-500',
    'bg-gradient-to-br from-pink-500 to-rose-500',
    'bg-gradient-to-br from-teal-500 to-cyan-500',
    'bg-gradient-to-br from-yellow-500 to-orange-500'
  ]
  background_color = colors[color_seed % colors.length]

  # Check if user has an avatar attached
  has_avatar = user&.respond_to?(:avatar) && user.avatar.attached?
%>

<div class="relative inline-block">
  <% if clickable && link_to_url %>
    <%= link_to link_to_url, class: "block" do %>
      <div class="<%= avatar_classes %> <%= has_avatar ? 'bg-gray-200' : "#{background_color} text-white" %>">
        <% if has_avatar %>
          <%= image_tag user.avatar, 
              alt: "#{user.full_name}'s avatar",
              class: "w-full h-full object-cover" %>
        <% else %>
          <%= initials %>
        <% end %>
      </div>
    <% end %>
  <% else %>
    <div class="<%= avatar_classes %> <%= has_avatar ? 'bg-gray-200' : "#{background_color} text-white" %>">
      <% if has_avatar %>
        <%= image_tag user.avatar, 
            alt: "#{user.full_name}'s avatar",
            class: "w-full h-full object-cover" %>
      <% else %>
        <%= initials %>
      <% end %>
    </div>
  <% end %>

  <% if show_status %>
    <!-- Online status indicator -->
    <div class="absolute bottom-0 right-0 <%= status_size_classes[size] || status_size_classes['md'] %> bg-green-400 border-2 border-white rounded-full"></div>
  <% end %>
</div>
