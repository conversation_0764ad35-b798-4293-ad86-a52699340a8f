<!-- Audience Insights Modal -->
<div data-controller="audience-insights"
     data-audience-insights-target="modal"
     class="hidden fixed inset-0 bg-slate-900 bg-opacity-50 z-50 items-center justify-center"
     data-action="click->audience-insights#closeOnBackdrop keydown.esc->audience-insights#close"
     tabindex="-1"
     role="dialog"
     aria-labelledby="audience-insights-title"
     aria-modal="true">
  
  <div class="bg-white backdrop-blur-sm rounded-2xl shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden"
       data-action="click->audience-insights#stopPropagation">
    
    <!-- Modal Header -->
    <div class="px-6 py-4 bg-gray-50 border-b border-gray-200">
      <div class="flex items-center justify-between">
        <h2 id="audience-insights-title" class="text-xl font-semibold text-slate-800 flex items-center">
          <div class="h-8 w-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center mr-3 shadow-md">
            <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v4a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
          </div>
          Audience Insights
        </h2>
        
        <button type="button"
                data-action="click->audience-insights#close"
                class="rounded-lg p-2 text-slate-400 hover:text-slate-600 hover:bg-slate-100 transition-colors"
                aria-label="Close modal">
          <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
    </div>
    
    <!-- Modal Content -->
    <div class="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
      
      <!-- Loading State -->
      <div data-audience-insights-target="loading" class="hidden text-center py-8">
        <svg class="animate-spin h-8 w-8 text-indigo-600 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
        </svg>
        <p class="text-slate-600">Loading audience insights...</p>
      </div>
      
      <!-- Content Area -->
      <div data-audience-insights-target="content" class="space-y-6">
        
        <!-- Audience Overview -->
        <div class="bg-gradient-to-r from-slate-50 to-white rounded-xl p-6 shadow-sm">
          <h3 class="text-lg font-semibold text-slate-800 mb-4 flex items-center">
            <svg class="h-5 w-5 text-slate-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
            </svg>
            Audience Overview
          </h3>
          
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="text-center p-4 bg-white rounded-lg shadow-sm">
              <div class="text-2xl font-bold text-indigo-600" data-audience-insights-target="totalSubscribers">
                --
              </div>
              <div class="text-sm text-slate-600">Total Subscribers</div>
            </div>
            
            <div class="text-center p-4 bg-white rounded-lg shadow-sm">
              <div class="text-2xl font-bold text-green-600" data-audience-insights-target="activeSubscribers">
                --
              </div>
              <div class="text-sm text-slate-600">Active Subscribers</div>
            </div>
            
            <div class="text-center p-4 bg-white rounded-lg shadow-sm">
              <div class="text-2xl font-bold text-blue-600" data-audience-insights-target="avgEngagement">
                --
              </div>
              <div class="text-sm text-slate-600">Avg. Engagement</div>
            </div>
          </div>
        </div>
        
        <!-- Demographics -->
        <div class="bg-gradient-to-r from-slate-50 to-white rounded-xl p-6 shadow-sm">
          <h3 class="text-lg font-semibold text-slate-800 mb-4 flex items-center">
            <svg class="h-5 w-5 text-slate-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v4a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
            Demographics
          </h3>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Age Distribution -->
            <div>
              <h4 class="font-medium text-slate-700 mb-3">Age Distribution</h4>
              <div data-audience-insights-target="ageChart" class="space-y-2">
                <div class="flex justify-between items-center">
                  <span class="text-sm text-slate-600">18-24</span>
                  <div class="flex-1 mx-3 bg-slate-200 rounded-full h-2">
                    <div class="bg-indigo-500 h-2 rounded-full" style="width: 0%"></div>
                  </div>
                  <span class="text-sm font-medium text-slate-700 w-12 text-right">0%</span>
                </div>
                <div class="flex justify-between items-center">
                  <span class="text-sm text-slate-600">25-34</span>
                  <div class="flex-1 mx-3 bg-slate-200 rounded-full h-2">
                    <div class="bg-indigo-500 h-2 rounded-full" style="width: 0%"></div>
                  </div>
                  <span class="text-sm font-medium text-slate-700 w-12 text-right">0%</span>
                </div>
                <div class="flex justify-between items-center">
                  <span class="text-sm text-slate-600">35-44</span>
                  <div class="flex-1 mx-3 bg-slate-200 rounded-full h-2">
                    <div class="bg-indigo-500 h-2 rounded-full" style="width: 0%"></div>
                  </div>
                  <span class="text-sm font-medium text-slate-700 w-12 text-right">0%</span>
                </div>
                <div class="flex justify-between items-center">
                  <span class="text-sm text-slate-600">45+</span>
                  <div class="flex-1 mx-3 bg-slate-200 rounded-full h-2">
                    <div class="bg-indigo-500 h-2 rounded-full" style="width: 0%"></div>
                  </div>
                  <span class="text-sm font-medium text-slate-700 w-12 text-right">0%</span>
                </div>
              </div>
            </div>
            
            <!-- Location Distribution -->
            <div>
              <h4 class="font-medium text-slate-700 mb-3">Top Locations</h4>
              <div data-audience-insights-target="locationList" class="space-y-2">
                <div class="flex justify-between items-center p-2 bg-white rounded-lg">
                  <span class="text-sm text-slate-600">Loading...</span>
                  <span class="text-sm font-medium text-slate-700">--</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Engagement Patterns -->
        <div class="bg-gradient-to-r from-slate-50 to-white rounded-xl p-6 shadow-sm">
          <h3 class="text-lg font-semibold text-slate-800 mb-4 flex items-center">
            <svg class="h-5 w-5 text-slate-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
            </svg>
            Engagement Patterns
          </h3>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Best Send Times -->
            <div>
              <h4 class="font-medium text-slate-700 mb-3">Best Send Times</h4>
              <div data-audience-insights-target="sendTimes" class="space-y-2">
                <div class="flex justify-between items-center p-3 bg-white rounded-lg shadow-sm">
                  <div>
                    <span class="text-sm font-medium text-slate-700">Tuesday, 10:00 AM</span>
                    <p class="text-xs text-slate-500">Highest open rate</p>
                  </div>
                  <span class="text-sm font-semibold text-green-600">78%</span>
                </div>
                <div class="flex justify-between items-center p-3 bg-white rounded-lg shadow-sm">
                  <div>
                    <span class="text-sm font-medium text-slate-700">Thursday, 2:00 PM</span>
                    <p class="text-xs text-slate-500">Best click-through rate</p>
                  </div>
                  <span class="text-sm font-semibold text-blue-600">12%</span>
                </div>
              </div>
            </div>
            
            <!-- Content Preferences -->
            <div>
              <h4 class="font-medium text-slate-700 mb-3">Content Preferences</h4>
              <div data-audience-insights-target="contentPreferences" class="space-y-2">
                <div class="flex justify-between items-center">
                  <span class="text-sm text-slate-600">Educational</span>
                  <div class="flex-1 mx-3 bg-slate-200 rounded-full h-2">
                    <div class="bg-green-500 h-2 rounded-full" style="width: 85%"></div>
                  </div>
                  <span class="text-sm font-medium text-slate-700">85%</span>
                </div>
                <div class="flex justify-between items-center">
                  <span class="text-sm text-slate-600">Promotional</span>
                  <div class="flex-1 mx-3 bg-slate-200 rounded-full h-2">
                    <div class="bg-blue-500 h-2 rounded-full" style="width: 65%"></div>
                  </div>
                  <span class="text-sm font-medium text-slate-700">65%</span>
                </div>
                <div class="flex justify-between items-center">
                  <span class="text-sm text-slate-600">Newsletter</span>
                  <div class="flex-1 mx-3 bg-slate-200 rounded-full h-2">
                    <div class="bg-purple-500 h-2 rounded-full" style="width: 72%"></div>
                  </div>
                  <span class="text-sm font-medium text-slate-700">72%</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- AI Recommendations -->
        <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 shadow-sm border border-blue-200">
          <h3 class="text-lg font-semibold text-slate-800 mb-4 flex items-center">
            <svg class="h-5 w-5 text-blue-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
            </svg>
            AI Recommendations
          </h3>
          
          <div data-audience-insights-target="recommendations" class="space-y-3">
            <div class="flex items-start space-x-3 p-4 bg-white rounded-lg shadow-sm">
              <svg class="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              <div>
                <p class="text-sm font-medium text-slate-800">Optimal Send Time</p>
                <p class="text-sm text-slate-600 mt-1">Based on your audience's engagement patterns, Tuesday at 10:00 AM shows the highest open rates.</p>
              </div>
            </div>
            
            <div class="flex items-start space-x-3 p-4 bg-white rounded-lg shadow-sm">
              <svg class="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              <div>
                <p class="text-sm font-medium text-slate-800">Content Strategy</p>
                <p class="text-sm text-slate-600 mt-1">Your audience responds well to educational content. Consider including tips, tutorials, or industry insights.</p>
              </div>
            </div>
          </div>
        </div>
        
      </div>
      
      <!-- Error State -->
      <div data-audience-insights-target="error" class="hidden text-center py-8">
        <svg class="h-12 w-12 text-red-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <p class="text-slate-600 mb-4">Unable to load audience insights</p>
        <button type="button"
                data-action="click->audience-insights#retry"
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors">
          Try Again
        </button>
      </div>
    </div>
    
    <!-- Modal Footer -->
    <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-end space-x-3">
      <button type="button"
              data-action="click->audience-insights#close"
              class="inline-flex items-center px-4 py-2 border border-slate-300 shadow-sm text-sm font-medium rounded-lg text-slate-700 bg-white hover:bg-slate-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors">
        Close
      </button>
      
      <button type="button"
              data-action="click->audience-insights#exportData"
              class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors">
        <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
        </svg>
        Export Report
      </button>
    </div>
  </div>
</div>
