<!-- Flash Messages / Toast Notifications -->
<div id="flash-messages-container" class="fixed top-20 right-4 z-50 space-y-3 max-w-sm">
  <!-- Success Messages -->
  <% if notice %>
    <div class="flash-message success animate-slide-in-right" data-auto-dismiss="5000">
      <div class="bg-white rounded-lg shadow-lg border-l-4 border-green-500 p-4">
        <div class="flex items-start">
          <div class="flex-shrink-0">
            <svg class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>
          </div>
          <div class="ml-3 flex-1">
            <h3 class="text-sm font-medium text-green-800">Success</h3>
            <div class="text-sm text-green-700 mt-1">
              <%= notice %>
            </div>
          </div>
          <div class="ml-4 flex-shrink-0">
            <button type="button" class="flash-close-btn bg-white rounded-md inline-flex text-green-400 hover:text-green-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500" data-dismiss="parent">
              <span class="sr-only">Close</span>
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
              </svg>
            </button>
          </div>
        </div>
        
        <!-- Progress bar for auto-dismiss -->
        <div class="mt-3">
          <div class="w-full bg-green-200 rounded-full h-1">
            <div class="flash-progress bg-green-500 h-1 rounded-full transition-all duration-5000 ease-linear" style="width: 100%"></div>
          </div>
        </div>
      </div>
    </div>
  <% end %>
  
  <!-- Error/Alert Messages -->
  <% if alert %>
    <div class="flash-message error animate-slide-in-right" data-auto-dismiss="7000">
      <div class="bg-white rounded-lg shadow-lg border-l-4 border-red-500 p-4">
        <div class="flex items-start">
          <div class="flex-shrink-0">
            <svg class="w-5 h-5 text-red-500" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
            </svg>
          </div>
          <div class="ml-3 flex-1">
            <h3 class="text-sm font-medium text-red-800">Error</h3>
            <div class="text-sm text-red-700 mt-1">
              <%= alert %>
            </div>
          </div>
          <div class="ml-4 flex-shrink-0">
            <button type="button" class="flash-close-btn bg-white rounded-md inline-flex text-red-400 hover:text-red-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500" data-dismiss="parent">
              <span class="sr-only">Close</span>
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
              </svg>
            </button>
          </div>
        </div>
        
        <!-- Progress bar for auto-dismiss -->
        <div class="mt-3">
          <div class="w-full bg-red-200 rounded-full h-1">
            <div class="flash-progress bg-red-500 h-1 rounded-full transition-all duration-7000 ease-linear" style="width: 100%"></div>
          </div>
        </div>
      </div>
    </div>
  <% end %>
  
  <!-- Info Messages (for other flash types) -->
  <% flash.each do |type, message| %>
    <% next if %w[notice alert].include?(type.to_s) %>
    <% 
      case type.to_s
      when 'info', 'warning'
        color_class = type.to_s == 'info' ? 'blue' : 'yellow'
        icon_svg = if type.to_s == 'info'
          '<path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>'
        else
          '<path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>'
        end
      else
        color_class = 'gray'
        icon_svg = '<path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>'
      end
    %>
    
    <div class="flash-message <%= type %> animate-slide-in-right" data-auto-dismiss="6000">
      <div class="bg-white rounded-lg shadow-lg border-l-4 border-<%= color_class %>-500 p-4">
        <div class="flex items-start">
          <div class="flex-shrink-0">
            <svg class="w-5 h-5 text-<%= color_class %>-500" fill="currentColor" viewBox="0 0 20 20">
              <%= icon_svg.html_safe %>
            </svg>
          </div>
          <div class="ml-3 flex-1">
            <h3 class="text-sm font-medium text-<%= color_class %>-800 capitalize"><%= type.to_s.humanize %></h3>
            <div class="text-sm text-<%= color_class %>-700 mt-1">
              <%= message %>
            </div>
          </div>
          <div class="ml-4 flex-shrink-0">
            <button type="button" class="flash-close-btn bg-white rounded-md inline-flex text-<%= color_class %>-400 hover:text-<%= color_class %>-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-<%= color_class %>-500" data-dismiss="parent">
              <span class="sr-only">Close</span>
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
              </svg>
            </button>
          </div>
        </div>
        
        <!-- Progress bar for auto-dismiss -->
        <div class="mt-3">
          <div class="w-full bg-<%= color_class %>-200 rounded-full h-1">
            <div class="flash-progress bg-<%= color_class %>-500 h-1 rounded-full transition-all duration-6000 ease-linear" style="width: 100%"></div>
          </div>
        </div>
      </div>
    </div>
  <% end %>
</div>

<!-- CSS Animations and Styles -->
<style>
  @keyframes slideInRight {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }
  
  @keyframes slideOutRight {
    from {
      transform: translateX(0);
      opacity: 1;
    }
    to {
      transform: translateX(100%);
      opacity: 0;
    }
  }
  
  @keyframes fadeOut {
    from {
      opacity: 1;
    }
    to {
      opacity: 0;
    }
  }
  
  .animate-slide-in-right {
    animation: slideInRight 0.3s ease-out;
  }
  
  .animate-slide-out-right {
    animation: slideOutRight 0.3s ease-in;
  }
  
  .animate-fade-out {
    animation: fadeOut 0.3s ease-in;
  }
  
  .flash-message {
    transition: all 0.3s ease;
  }
  
  .flash-message:hover .flash-progress {
    animation-play-state: paused !important;
  }
  
  /* Progress bar animations */
  .flash-message[data-auto-dismiss="5000"] .flash-progress {
    animation: progressShrink 5s linear forwards;
  }
  
  .flash-message[data-auto-dismiss="6000"] .flash-progress {
    animation: progressShrink 6s linear forwards;
  }
  
  .flash-message[data-auto-dismiss="7000"] .flash-progress {
    animation: progressShrink 7s linear forwards;
  }
  
  @keyframes progressShrink {
    from {
      width: 100%;
    }
    to {
      width: 0%;
    }
  }
</style>

<!-- JavaScript for Flash Message Behavior -->
<script>
document.addEventListener('DOMContentLoaded', function() {
  // Auto-dismiss functionality
  document.querySelectorAll('.flash-message[data-auto-dismiss]').forEach(function(message) {
    const dismissTime = parseInt(message.getAttribute('data-auto-dismiss'));
    
    setTimeout(function() {
      dismissFlashMessage(message);
    }, dismissTime);
  });
  
  // Manual close button functionality
  document.querySelectorAll('.flash-close-btn').forEach(function(button) {
    button.addEventListener('click', function() {
      const message = button.closest('.flash-message');
      dismissFlashMessage(message);
    });
  });
  
  // Pause auto-dismiss on hover
  document.querySelectorAll('.flash-message').forEach(function(message) {
    const progressBar = message.querySelector('.flash-progress');
    
    message.addEventListener('mouseenter', function() {
      if (progressBar) {
        progressBar.style.animationPlayState = 'paused';
      }
    });
    
    message.addEventListener('mouseleave', function() {
      if (progressBar) {
        progressBar.style.animationPlayState = 'running';
      }
    });
  });
  
  function dismissFlashMessage(message) {
    message.classList.add('animate-slide-out-right');
    
    setTimeout(function() {
      if (message.parentNode) {
        message.parentNode.removeChild(message);
      }
    }, 300);
  }
  
  // Function to programmatically add flash messages (for AJAX requests)
  window.addFlashMessage = function(type, message, duration = 5000) {
    const container = document.getElementById('flash-messages-container');
    if (!container) return;
    
    const colors = {
      success: { bg: 'green', icon: 'check-circle' },
      error: { bg: 'red', icon: 'x-circle' },
      alert: { bg: 'red', icon: 'x-circle' },
      warning: { bg: 'yellow', icon: 'exclamation-triangle' },
      info: { bg: 'blue', icon: 'information-circle' }
    };
    
    const config = colors[type] || colors.info;
    
    const messageHTML = `
      <div class="flash-message ${type} animate-slide-in-right" data-auto-dismiss="${duration}">
        <div class="bg-white rounded-lg shadow-lg border-l-4 border-${config.bg}-500 p-4">
          <div class="flex items-start">
            <div class="flex-shrink-0">
              <svg class="w-5 h-5 text-${config.bg}-500" fill="currentColor" viewBox="0 0 20 20">
                ${getIconSVG(config.icon)}
              </svg>
            </div>
            <div class="ml-3 flex-1">
              <h3 class="text-sm font-medium text-${config.bg}-800 capitalize">${type}</h3>
              <div class="text-sm text-${config.bg}-700 mt-1">${message}</div>
            </div>
            <div class="ml-4 flex-shrink-0">
              <button type="button" class="flash-close-btn bg-white rounded-md inline-flex text-${config.bg}-400 hover:text-${config.bg}-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-${config.bg}-500">
                <span class="sr-only">Close</span>
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
              </button>
            </div>
          </div>
          <div class="mt-3">
            <div class="w-full bg-${config.bg}-200 rounded-full h-1">
              <div class="flash-progress bg-${config.bg}-500 h-1 rounded-full" style="width: 100%; animation: progressShrink ${duration}ms linear forwards;"></div>
            </div>
          </div>
        </div>
      </div>
    `;
    
    container.insertAdjacentHTML('beforeend', messageHTML);
    
    // Set up auto-dismiss and click handlers for the new message
    const newMessage = container.lastElementChild;
    
    setTimeout(function() {
      dismissFlashMessage(newMessage);
    }, duration);
    
    const closeBtn = newMessage.querySelector('.flash-close-btn');
    if (closeBtn) {
      closeBtn.addEventListener('click', function() {
        dismissFlashMessage(newMessage);
      });
    }
  };
  
  function getIconSVG(iconType) {
    const icons = {
      'check-circle': '<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>',
      'x-circle': '<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>',
      'exclamation-triangle': '<path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>',
      'information-circle': '<path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>'
    };
    
    return icons[iconType] || icons['information-circle'];
  }
});
</script>