<%# AI Provider Status Component %>
<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
  <div class="flex items-center justify-between mb-3">
    <h3 class="text-sm font-medium text-gray-900">AI Providers Status</h3>
    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
      <%= pluralize(available_providers_count, 'provider') %> available
    </span>
  </div>

  <div class="space-y-2">
    <% ai_providers.each do |provider_key, config| %>
      <% is_available = provider_available?(provider_key) %>
      <div class="flex items-center justify-between py-1">
        <div class="flex items-center space-x-2">
          <div class="flex-shrink-0">
            <% if is_available %>
              <div class="w-2 h-2 bg-green-400 rounded-full"></div>
            <% else %>
              <div class="w-2 h-2 bg-gray-300 rounded-full"></div>
            <% end %>
          </div>
          <span class="text-sm <%= is_available ? 'text-gray-900' : 'text-gray-500' %>">
            <%= config[:name] %>
          </span>
        </div>
        
        <div class="flex items-center space-x-1">
          <% if is_available %>
            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
              Active
            </span>
          <% else %>
            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-600">
              Not configured
            </span>
          <% end %>
        </div>
      </div>
    <% end %>
  </div>

  <% if available_providers_count == 0 %>
    <div class="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z"/>
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-yellow-800">
            No AI providers configured
          </h3>
          <div class="mt-2 text-sm text-yellow-700">
            <p>Configure at least one AI provider to enable AI features.</p>
          </div>
          <div class="mt-3">
            <%= link_to "Setup Guide", "#", 
                class: "text-sm font-medium text-yellow-800 hover:text-yellow-900 underline" %>
          </div>
        </div>
      </div>
    </div>
  <% end %>

  <% if show_provider_selection? %>
    <div class="mt-4 pt-3 border-t border-gray-200">
      <div class="flex items-center justify-between">
        <span class="text-xs text-gray-500">Provider Strategy</span>
        <span class="text-xs font-medium text-gray-700 capitalize">
          <%= current_provider_strategy %>
        </span>
      </div>
      
      <div class="mt-2">
        <div class="text-xs text-gray-500">
          Optimizing for: 
          <span class="font-medium text-gray-700">
            <%= strategy_description(current_provider_strategy) %>
          </span>
        </div>
      </div>
    </div>
  <% end %>
</div>

<%# Helper methods - these would typically be in a helper file %>
<% content_for :page_helpers do %>
  <script>
    // Helper functions for AI provider status
    window.AIProviderHelpers = {
      providers: <%= ai_providers.to_json.html_safe %>,
      
      refreshStatus: function() {
        // This could make an AJAX call to refresh provider status
        console.log('Refreshing AI provider status...');
      },
      
      showProviderDetails: function(providerKey) {
        // Show detailed information about a specific provider
        console.log('Showing details for provider:', providerKey);
      }
    };
  </script>
<% end %>
