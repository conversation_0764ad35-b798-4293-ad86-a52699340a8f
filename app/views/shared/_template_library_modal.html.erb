<!-- Template Library Modal -->
<div data-controller="template-library"
     data-template-library-target="modal"
     class="hidden fixed inset-0 bg-slate-900 bg-opacity-50 z-50 items-center justify-center"
     data-action="click->template-library#closeOnBackdrop keydown.esc->template-library#close"
     tabindex="-1"
     role="dialog"
     aria-labelledby="template-library-title"
     aria-modal="true">
  
  <div class="bg-white backdrop-blur-sm rounded-2xl shadow-xl max-w-6xl w-full mx-4 max-h-[90vh] overflow-hidden"
       data-action="click->template-library#stopPropagation">
    
    <!-- Modal Header -->
    <div class="px-6 py-4 bg-gray-50 border-b border-gray-200">
      <div class="flex items-center justify-between">
        <h2 id="template-library-title" class="text-xl font-semibold text-slate-800 flex items-center">
          <div class="h-8 w-8 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg flex items-center justify-center mr-3 shadow-md">
            <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
          </div>
          Template Library
        </h2>
        
        <button type="button"
                data-action="click->template-library#close"
                class="rounded-lg p-2 text-slate-400 hover:text-slate-600 hover:bg-slate-100 transition-colors"
                aria-label="Close modal">
          <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
      
      <!-- Search and Filter Bar -->
      <div class="mt-4 flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-4">
        <div class="flex-1 relative">
          <input type="text"
                 data-template-library-target="searchInput"
                 data-action="input->template-library#filterTemplates"
                 placeholder="Search templates..."
                 class="w-full pl-10 pr-4 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all">
          <svg class="absolute left-3 top-2.5 h-5 w-5 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
          </svg>
        </div>
        
        <select data-template-library-target="categoryFilter"
                data-action="change->template-library#filterTemplates"
                class="px-4 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all">
          <option value="">All Categories</option>
          <option value="newsletter">Newsletter</option>
          <option value="promotional">Promotional</option>
          <option value="welcome">Welcome Series</option>
          <option value="product">Product Updates</option>
          <option value="event">Event Invites</option>
          <option value="survey">Surveys</option>
        </select>
        
        <select data-template-library-target="industryFilter"
                data-action="change->template-library#filterTemplates"
                class="px-4 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all">
          <option value="">All Industries</option>
          <option value="technology">Technology</option>
          <option value="ecommerce">E-commerce</option>
          <option value="finance">Finance</option>
          <option value="healthcare">Healthcare</option>
          <option value="education">Education</option>
          <option value="nonprofit">Non-profit</option>
        </select>
      </div>
    </div>
    
    <!-- Modal Content -->
    <div class="flex h-[calc(90vh-180px)]">
      
      <!-- Template Grid -->
      <div class="flex-1 p-6 overflow-y-auto">
        
        <!-- Loading State -->
        <div data-template-library-target="loading" class="hidden text-center py-12">
          <svg class="animate-spin h-8 w-8 text-purple-600 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
          </svg>
          <p class="text-slate-600">Loading templates...</p>
        </div>
        
        <!-- Templates Grid -->
        <div data-template-library-target="templatesGrid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          
          <!-- Newsletter Template -->
          <div class="template-card bg-white rounded-xl shadow-sm border border-slate-200 hover:shadow-md transition-all duration-300 cursor-pointer"
               data-template-library-target="templateCard"
               data-template-id="newsletter-modern"
               data-category="newsletter"
               data-industry="technology"
               data-action="click->template-library#selectTemplate">
            
            <div class="aspect-[4/3] bg-gradient-to-br from-blue-50 to-indigo-100 rounded-t-xl relative overflow-hidden">
              <div class="absolute inset-0 p-4">
                <div class="bg-white rounded-lg shadow-sm p-3 mb-2">
                  <div class="h-2 bg-slate-200 rounded mb-2"></div>
                  <div class="h-1.5 bg-slate-100 rounded mb-1"></div>
                  <div class="h-1.5 bg-slate-100 rounded w-3/4"></div>
                </div>
                <div class="space-y-2">
                  <div class="h-1 bg-white/60 rounded"></div>
                  <div class="h-1 bg-white/60 rounded w-4/5"></div>
                  <div class="h-1 bg-white/60 rounded w-3/5"></div>
                </div>
              </div>
              <span class="absolute top-3 right-3 bg-blue-600 text-white text-xs px-2 py-1 rounded-full">Newsletter</span>
            </div>
            
            <div class="p-4">
              <h3 class="font-semibold text-slate-800 mb-1">Modern Newsletter</h3>
              <p class="text-sm text-slate-600 mb-3">Clean, professional newsletter template with header, content sections, and footer.</p>
              
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-2">
                  <span class="text-xs bg-slate-100 text-slate-600 px-2 py-1 rounded">Technology</span>
                  <div class="flex items-center">
                    <svg class="h-3 w-3 text-yellow-400 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                    </svg>
                    <span class="text-xs text-slate-500">4.8</span>
                  </div>
                </div>
                <button class="text-purple-600 hover:text-purple-700 text-sm font-medium">Preview</button>
              </div>
            </div>
          </div>
          
          <!-- Promotional Template -->
          <div class="template-card bg-white rounded-xl shadow-sm border border-slate-200 hover:shadow-md transition-all duration-300 cursor-pointer"
               data-template-library-target="templateCard"
               data-template-id="promo-sale"
               data-category="promotional"
               data-industry="ecommerce"
               data-action="click->template-library#selectTemplate">
            
            <div class="aspect-[4/3] bg-gradient-to-br from-red-50 to-pink-100 rounded-t-xl relative overflow-hidden">
              <div class="absolute inset-0 p-4">
                <div class="bg-white rounded-lg shadow-sm p-3 mb-2 text-center">
                  <div class="h-3 bg-red-500 rounded mb-2 w-1/2 mx-auto"></div>
                  <div class="h-1.5 bg-slate-100 rounded mb-1"></div>
                  <div class="h-1.5 bg-slate-100 rounded w-3/4 mx-auto"></div>
                </div>
                <div class="text-center space-y-1">
                  <div class="h-1 bg-white/60 rounded w-2/3 mx-auto"></div>
                  <div class="h-1 bg-white/60 rounded w-1/2 mx-auto"></div>
                </div>
              </div>
              <span class="absolute top-3 right-3 bg-red-600 text-white text-xs px-2 py-1 rounded-full">Promotional</span>
            </div>
            
            <div class="p-4">
              <h3 class="font-semibold text-slate-800 mb-1">Sale Announcement</h3>
              <p class="text-sm text-slate-600 mb-3">Eye-catching promotional template perfect for sales, discounts, and special offers.</p>
              
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-2">
                  <span class="text-xs bg-slate-100 text-slate-600 px-2 py-1 rounded">E-commerce</span>
                  <div class="flex items-center">
                    <svg class="h-3 w-3 text-yellow-400 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                    </svg>
                    <span class="text-xs text-slate-500">4.9</span>
                  </div>
                </div>
                <button class="text-purple-600 hover:text-purple-700 text-sm font-medium">Preview</button>
              </div>
            </div>
          </div>
          
          <!-- Welcome Template -->
          <div class="template-card bg-white rounded-xl shadow-sm border border-slate-200 hover:shadow-md transition-all duration-300 cursor-pointer"
               data-template-library-target="templateCard"
               data-template-id="welcome-series"
               data-category="welcome"
               data-industry="finance"
               data-action="click->template-library#selectTemplate">
            
            <div class="aspect-[4/3] bg-gradient-to-br from-green-50 to-emerald-100 rounded-t-xl relative overflow-hidden">
              <div class="absolute inset-0 p-4">
                <div class="bg-white rounded-lg shadow-sm p-3 mb-2">
                  <div class="h-2 bg-green-500 rounded mb-2 w-1/3"></div>
                  <div class="h-1.5 bg-slate-100 rounded mb-1"></div>
                  <div class="h-1.5 bg-slate-100 rounded w-4/5"></div>
                </div>
                <div class="space-y-1">
                  <div class="h-1 bg-white/60 rounded"></div>
                  <div class="h-1 bg-white/60 rounded w-3/4"></div>
                  <div class="h-1 bg-white/60 rounded w-1/2"></div>
                </div>
              </div>
              <span class="absolute top-3 right-3 bg-green-600 text-white text-xs px-2 py-1 rounded-full">Welcome</span>
            </div>
            
            <div class="p-4">
              <h3 class="font-semibold text-slate-800 mb-1">Welcome Series</h3>
              <p class="text-sm text-slate-600 mb-3">Warm welcome template for new subscribers with onboarding information.</p>
              
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-2">
                  <span class="text-xs bg-slate-100 text-slate-600 px-2 py-1 rounded">Finance</span>
                  <div class="flex items-center">
                    <svg class="h-3 w-3 text-yellow-400 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                    </svg>
                    <span class="text-xs text-slate-500">4.7</span>
                  </div>
                </div>
                <button class="text-purple-600 hover:text-purple-700 text-sm font-medium">Preview</button>
              </div>
            </div>
          </div>
          
        </div>
        
        <!-- No Results State -->
        <div data-template-library-target="noResults" class="hidden text-center py-12">
          <svg class="h-12 w-12 text-slate-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
          </svg>
          <p class="text-slate-600 mb-2">No templates found</p>
          <p class="text-sm text-slate-500">Try adjusting your search or filter criteria</p>
          <button type="button"
                  data-action="click->template-library#clearFilters"
                  class="mt-4 text-purple-600 hover:text-purple-700 text-sm font-medium">
            Clear all filters
          </button>
        </div>
        
        <!-- Error State -->
        <div data-template-library-target="error" class="hidden text-center py-12">
          <svg class="h-12 w-12 text-red-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          <p class="text-slate-600 mb-4">Unable to load templates</p>
          <button type="button"
                  data-action="click->template-library#retry"
                  class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors">
            Try Again
          </button>
        </div>
      </div>
      
      <!-- Template Preview Sidebar -->
      <div data-template-library-target="previewSidebar" 
           class="hidden w-80 bg-slate-50 border-l border-slate-200 overflow-y-auto">
        
        <div class="p-6">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-slate-800" data-template-library-target="previewTitle">
              Template Preview
            </h3>
            <button type="button"
                    data-action="click->template-library#closePreview"
                    class="rounded-lg p-1 text-slate-400 hover:text-slate-600 hover:bg-slate-200 transition-colors">
              <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>
          
          <!-- Template Preview Content -->
          <div data-template-library-target="previewContent" class="space-y-4">
            <!-- Preview content will be populated dynamically -->
          </div>
          
          <!-- Template Actions -->
          <div class="mt-6 pt-6 border-t border-slate-200 space-y-3">
            <button type="button"
                    data-action="click->template-library#useTemplate"
                    class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors">
              <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              Use This Template
            </button>
            
            <button type="button"
                    data-action="click->template-library#customizeTemplate"
                    class="w-full inline-flex justify-center items-center px-4 py-2 border border-slate-300 shadow-sm text-sm font-medium rounded-lg text-slate-700 bg-white hover:bg-slate-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors">
              <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
              </svg>
              Customize & Use
            </button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Modal Footer -->
    <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-between items-center">
      <div class="text-sm text-slate-600">
        <span data-template-library-target="templateCount">--</span> templates available
      </div>
      
      <div class="flex space-x-3">
        <button type="button"
                data-action="click->template-library#close"
                class="inline-flex items-center px-4 py-2 border border-slate-300 shadow-sm text-sm font-medium rounded-lg text-slate-700 bg-white hover:bg-slate-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors">
          Cancel
        </button>
        
        <button type="button"
                data-template-library-target="useSelectedButton"
                data-action="click->template-library#useSelectedTemplate"
                disabled
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
          <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          Use Template
        </button>
      </div>
    </div>
  </div>
</div>
