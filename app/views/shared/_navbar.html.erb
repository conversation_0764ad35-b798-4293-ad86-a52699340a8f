<!-- Professional Navigation Bar -->
<nav class="bg-white/95 backdrop-blur-lg shadow-sm fixed top-0 w-full z-50">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex justify-between items-center h-16">
      
      <!-- Logo Section -->
      <div class="flex items-center">
        <%= link_to (user_signed_in? ? dashboard_path : root_path), class: "flex items-center space-x-3 group" do %>
          <!-- Logo Icon -->
          <div class="relative">
            <div class="w-10 h-10 bg-gradient-to-br from-blue-600 to-purple-600 rounded-xl flex items-center justify-center transform group-hover:scale-105 transition-transform duration-200">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
              </svg>
            </div>
            <!-- Status indicator for authenticated users -->
            <% if user_signed_in? %>
              <div class="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full shadow-sm"></div>
            <% end %>
          </div>
          
          <!-- Brand Text -->
          <div class="hidden sm:block">
            <h1 class="text-xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
              AI Marketing Hub
            </h1>
            <% if user_signed_in? %>
              <p class="text-xs text-gray-500 leading-none">
                <%= current_user.tenant&.name || "Dashboard" %>
              </p>
            <% end %>
          </div>
        <% end %>
      </div>
      
      <!-- Desktop Navigation Links -->
      <div class="hidden md:flex items-center space-x-8">
        <% unless user_signed_in? %>
          <!-- Marketing Navigation -->
          <%= link_to  features_path, 
              class: "text-gray-600 hover:text-blue-600 font-medium transition-colors duration-200 relative group" do %>
            Features
            <span class="absolute bottom-0 left-0 w-0 h-0.5 bg-blue-600 group-hover:w-full transition-all duration-200"></span>
          <% end %>
          
          <%= link_to  pricing_path, 
              class: "text-gray-600 hover:text-blue-600 font-medium transition-colors duration-200 relative group" do %>
            Pricing
            <span class="absolute bottom-0 left-0 w-0 h-0.5 bg-blue-600 group-hover:w-full transition-all duration-200"></span>
          <% end %>
          
          <%= link_to  about_path,
              class: "text-gray-600 hover:text-blue-600 font-medium transition-colors duration-200 relative group" do %>
            About
            <span class="absolute bottom-0 left-0 w-0 h-0.5 bg-blue-600 group-hover:w-full transition-all duration-200"></span>
          <% end %>

          <%= link_to  contact_path,
              class: "text-gray-600 hover:text-blue-600 font-medium transition-colors duration-200 relative group" do %>
            Contact
            <span class="absolute bottom-0 left-0 w-0 h-0.5 bg-blue-600 group-hover:w-full transition-all duration-200"></span>
          <% end %>
        <% else %>
          <!-- Dashboard Navigation -->
          <%= link_to dashboard_path, 
              class: "flex items-center space-x-2 text-gray-600 hover:text-blue-600 font-medium transition-colors duration-200 #{'text-blue-600' if current_page?(dashboard_path)}" do %>
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"></path>
            </svg>
            <span>Dashboard</span>
          <% end %>
          
          <%= link_to campaigns_path,
              class: "flex items-center space-x-2 font-medium transition-colors duration-200 #{ controller_name == 'campaigns' ? 'text-blue-600' : 'text-gray-600 hover:text-blue-600' }" do %>
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
            <span>Campaigns</span>
          <% end %>
          
          <!-- Vibe Analytics -->
          <%= link_to vibe_analytics_path,
              class: "flex items-center space-x-2 font-medium transition-colors duration-200 #{ controller_name == 'vibe_analytics' ? 'text-blue-600' : 'text-gray-600 hover:text-blue-600' }" do %>
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
              <circle cx="12" cy="12" r="2" fill="currentColor"/>
            </svg>
            <span>Vibe Analytics</span>
            <span class="text-xs bg-gradient-to-r from-purple-500 to-pink-500 text-white px-2 py-1 rounded-full animate-pulse">New</span>
          <% end %>

          <!-- Audiences -->
          <%= link_to audiences_path,
              class: "flex items-center space-x-2 font-medium transition-colors duration-200 #{ controller_name == 'audiences' ? 'text-blue-600' : 'text-gray-600 hover:text-blue-600' }" do %>
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
            </svg>
            <span>Audiences</span>
            <span class="text-xs bg-gradient-to-r from-green-500 to-emerald-500 text-white px-2 py-1 rounded-full animate-pulse">Live</span>
          <% end %>
        <% end %>
      </div>
      
      <!-- User Section -->
      <div class="flex items-center space-x-4">
        <% if user_signed_in? %>
          <!-- User Menu -->
          <div class="relative group">
            <button class="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-50 transition-colors duration-200">
              <!-- User Avatar -->
              <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white text-sm font-bold">
                <%= current_user.first_name&.first || "U" %>
              </div>
              
              <!-- User Info (Desktop only) -->
              <div class="hidden sm:block text-left">
                <p class="text-sm font-medium text-gray-900">
                  <%= current_user.first_name %> <%= current_user.last_name %>
                </p>
                <p class="text-xs text-gray-500 capitalize">
                  <%= current_user.role %>
                </p>
              </div>
              
              <!-- Dropdown Arrow -->
              <svg class="w-4 h-4 text-gray-400 group-hover:text-gray-600 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </button>
            
            <!-- Dropdown Menu -->
            <div class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
              <div class="py-2">
                <!-- User Info (Mobile) -->
                <div class="sm:hidden px-4 py-2 bg-gray-50">
                  <p class="text-sm font-medium text-gray-900">
                    <%= current_user.first_name %> <%= current_user.last_name %>
                  </p>
                  <p class="text-xs text-gray-500">
                    <%= current_user.email %>
                  </p>
                </div>
                
                <!-- Menu Items -->
                <a href="#" class="flex items-center space-x-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors">
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                  </svg>
                  <span>Profile Settings</span>
                </a>
                
                <a href="#" class="flex items-center space-x-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors">
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                  </svg>
                  <span>Account Settings</span>
                </a>
                
                <a href="#" class="flex items-center space-x-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors">
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                  <span>Help & Support</span>
                </a>
                
                <div class="bg-gray-50 mt-2 pt-2">
                  <%= link_to destroy_user_session_path,
                      class: "flex items-center space-x-2 px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors",
                      data: { turbo_method: :delete, turbo_confirm: "Are you sure you want to sign out?" } do %>
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                    </svg>
                    <span>Sign Out</span>
                  <% end %>
                </div>
              </div>
            </div>
          </div>
          
        <% else %>
          <!-- Guest User Actions -->
          <%= link_to "Sign In", new_user_session_path, 
              class: "text-gray-600 hover:text-gray-900 font-medium transition-colors duration-200" %>
          
          <%= link_to new_user_registration_path, 
              class: "group relative inline-flex items-center justify-center px-6 py-2.5 text-sm font-semibold text-white bg-gradient-to-r from-blue-600 to-purple-600 rounded-full shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200" do %>
            <span class="absolute inset-0 w-full h-full bg-gradient-to-r from-blue-700 to-purple-700 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200"></span>
            <span class="relative flex items-center space-x-2">
              <span>Start Free Trial</span>
              <svg class="w-4 h-4 group-hover:translate-x-0.5 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
              </svg>
            </span>
          <% end %>
        <% end %>
        
        <% if user_signed_in? %>
          <!-- Sidebar Toggle Button (Mobile) -->
          <button id="sidebar-toggle" class="lg:hidden p-2 rounded-lg hover:bg-gray-50 transition-colors duration-200">
            <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
            </svg>
          </button>
        <% else %>
          <!-- Mobile Menu Button -->
          <button id="mobile-menu-button" class="md:hidden p-2 rounded-lg hover:bg-gray-50 transition-colors duration-200">
            <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
            </svg>
          </button>
        <% end %>
      </div>
    </div>
  </div>
  
  <!-- Mobile Menu -->
  <div id="mobile-menu" class="md:hidden bg-white shadow-sm hidden">
    <div class="px-4 py-4 space-y-4">
      <% unless user_signed_in? %>
        <!-- Marketing Navigation -->
        <%= link_to "Features", features_path, 
            class: "block py-2 text-gray-600 hover:text-blue-600 font-medium transition-colors duration-200" %>
        <%= link_to "Pricing", pricing_path, 
            class: "block py-2 text-gray-600 hover:text-blue-600 font-medium transition-colors duration-200" %>
        <%= link_to "About", about_path,
            class: "block py-2 text-gray-600 hover:text-blue-600 font-medium transition-colors duration-200" %>
        <%= link_to "Contact", contact_path,
            class: "block py-2 text-gray-600 hover:text-blue-600 font-medium transition-colors duration-200" %>
            
        <!-- Mobile CTA -->
        <div class="pt-4 bg-gray-50">
          <%= link_to new_user_registration_path, 
              class: "block w-full text-center px-6 py-3 text-white bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg font-semibold shadow-lg" do %>
            Start Free Trial
          <% end %>
        </div>
      <% else %>
        <!-- Dashboard Navigation -->
        <%= link_to dashboard_path, 
            class: "flex items-center space-x-2 py-2 text-gray-600 hover:text-blue-600 font-medium transition-colors duration-200" do %>
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
          </svg>
          <span>Dashboard</span>
        <% end %>
        
        <%= link_to campaigns_path,
            class: "flex items-center space-x-2 py-2 font-medium transition-colors duration-200 #{ controller_name == 'campaigns' ? 'text-blue-600' : 'text-gray-600 hover:text-blue-600' }" do %>
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
          </svg>
          <span>Campaigns</span>
        <% end %>

        <%= link_to vibe_analytics_path,
            class: "flex items-center space-x-2 py-2 font-medium transition-colors duration-200 #{ controller_name == 'vibe_analytics' ? 'text-blue-600' : 'text-gray-600 hover:text-blue-600' }" do %>
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            <circle cx="12" cy="12" r="2" fill="currentColor"/>
          </svg>
          <span>Vibe Analytics</span>
        <% end %>

        <%= link_to audiences_path,
            class: "flex items-center space-x-2 py-2 font-medium transition-colors duration-200 #{ controller_name == 'audiences' ? 'text-blue-600' : 'text-gray-600 hover:text-blue-600' }" do %>
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
          </svg>
          <span>Audiences</span>
        <% end %>
      <% end %>
    </div>
  </div>
</nav>

<!-- JavaScript for Mobile Menu and Sidebar -->
<script>
document.addEventListener('DOMContentLoaded', function() {
  // Mobile Menu Toggle
  const mobileMenuButton = document.getElementById('mobile-menu-button');
  const mobileMenu = document.getElementById('mobile-menu');

  if (mobileMenuButton && mobileMenu) {
    mobileMenuButton.addEventListener('click', function() {
      mobileMenu.classList.toggle('hidden');
    });
  }

  // Sidebar Toggle for Authenticated Users
  const sidebarToggle = document.getElementById('sidebar-toggle');
  const sidebar = document.getElementById('sidebar');
  const sidebarOverlay = document.getElementById('sidebar-overlay');
  const sidebarClose = document.getElementById('sidebar-close');

  function openSidebar() {
    if (sidebar && sidebarOverlay) {
      sidebar.classList.remove('-translate-x-full');
      sidebarOverlay.classList.remove('hidden');
      document.body.classList.add('overflow-hidden');
    }
  }

  function closeSidebar() {
    if (sidebar && sidebarOverlay) {
      sidebar.classList.add('-translate-x-full');
      sidebarOverlay.classList.add('hidden');
      document.body.classList.remove('overflow-hidden');
    }
  }

  if (sidebarToggle) {
    sidebarToggle.addEventListener('click', openSidebar);
  }

  if (sidebarClose) {
    sidebarClose.addEventListener('click', closeSidebar);
  }

  if (sidebarOverlay) {
    sidebarOverlay.addEventListener('click', closeSidebar);
  }

  // Close sidebar on escape key
  document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
      closeSidebar();
    }
  });

  // Close sidebar when clicking on sidebar links (mobile)
  const sidebarLinks = sidebar?.querySelectorAll('a');
  if (sidebarLinks) {
    sidebarLinks.forEach(link => {
      link.addEventListener('click', function() {
        if (window.innerWidth < 1024) { // lg breakpoint
          closeSidebar();
        }
      });
    });
  }
});
</script>