<%# Instagram Icon with Brand Colors %>
<%
  css_class = local_assigns[:css_class] || 'w-6 h-6'
  container_class = local_assigns[:container_class] || 'platform-icon-container bg-instagram-gradient'
  icon_class = local_assigns[:icon_class] || 'text-white'
%>

<div class="<%= container_class %>">
  <svg class="<%= css_class %> <%= icon_class %>" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
    <path d="M12.017 0C8.396 0 7.989.013 7.041.048 6.021.088 5.347.222 4.73.42a5.978 5.978 0 00-2.188 1.423A5.994 5.994 0 00.42 4.73C.222 5.347.087 6.021.048 7.041.013 7.989 0 8.396 0 12.017c0 3.624.013 4.09.048 5.014.04 1.017.174 1.693.372 2.31a5.993 5.993 0 001.423 2.188 6.007 6.007 0 002.188 1.423c.618.198 1.293.333 2.31.372C7.989 23.987 8.396 24 12.017 24c3.624 0 4.09-.013 5.014-.048 1.017-.04 1.693-.174 2.31-.372a5.994 5.994 0 002.188-1.423A6.007 6.007 0 0023.35 19.34c.198-.618.333-1.293.372-2.31C23.987 16.09 24 15.624 24 12.017c0-3.624-.013-4.09-.048-5.014-.04-1.017-.174-1.693-.372-2.31a5.994 5.994 0 00-1.423-2.188A6.007 6.007 0 0019.34.67c-.618-.198-1.293-.333-2.31-.372C16.09.013 15.624 0 12.017 0zm0 2.162c3.558 0 3.982.013 5.389.048.868.04 1.338.185 1.652.307.415.161.71.353 1.021.664s.503.606.664 1.021c.123.314.267.784.307 1.652.035 1.407.048 1.831.048 5.389s-.013 3.982-.048 5.389c-.04.868-.184 1.338-.307 1.652-.161.415-.353.71-.664 1.021a2.75 2.75 0 01-1.021.664c-.314.123-.784.267-1.652.307-1.407.035-1.831.048-5.389.048s-3.982-.013-5.389-.048c-.868-.04-1.338-.184-1.652-.307a2.744 2.744 0 01-1.021-.664 2.75 2.75 0 01-.664-1.021c-.123-.314-.267-.784-.307-1.652C2.175 15.999 2.162 15.575 2.162 12.017s.013-3.982.048-5.389c.04-.868.184-1.338.307-1.652.161-.415.353-.71.664-1.021a2.75 2.75 0 011.021-.664c.314-.123.784-.267 1.652-.307C8.035 2.175 8.459 2.162 12.017 2.162z"/>
    <path d="M12.017 5.838a6.179 6.179 0 100 12.358 6.179 6.179 0 000-12.358zm0 10.196a4.017 4.017 0 110-8.034 4.017 4.017 0 010 8.034z"/>
    <circle cx="18.406" cy="5.594" r="1.44"/>
  </svg>
</div>
