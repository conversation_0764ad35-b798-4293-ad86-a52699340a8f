<%# Campaign Statistics Cards %>
<% active_count = campaigns.where(status: 'active').count %>
<% total_budget = campaigns.sum(:budget_cents) / 100 %>

<div class="campaigns-library-stats grid grid-cols-1 sm:grid-cols-3 gap-4 sm:gap-6">
  <!-- Total Campaigns Stat Card -->
  <div class="campaigns-stat-card bg-white/80 backdrop-blur-md rounded-xl p-4 border border-white/50 shadow-sm hover:shadow-md transition-all duration-300 group">
    <div class="flex items-center gap-4">
      <div class="campaigns-stat-icon w-12 h-12 bg-gradient-to-br from-indigo-50 to-blue-100 rounded-lg flex items-center justify-center ring-2 ring-blue-100/50 group-hover:scale-105 transition-transform duration-300">
        <svg class="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
        </svg>
      </div>
      <div>
        <p class="text-3xl font-bold text-gray-900"><%= campaigns.count %></p>
        <div class="flex items-center gap-1 mt-1">
          <span class="text-sm text-gray-600">Total Campaigns</span>
          <% if campaigns.count > 10 %>
            <span class="bg-blue-100 text-blue-800 text-xs px-2 py-0.5 rounded-full">Pro</span>
          <% end %>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Active Campaigns Stat Card -->
  <div class="campaigns-stat-card bg-white/80 backdrop-blur-md rounded-xl p-4 border border-white/50 shadow-sm hover:shadow-md transition-all duration-300 group">
    <div class="flex items-center gap-4">
      <div class="campaigns-stat-icon w-12 h-12 bg-gradient-to-br from-green-50 to-emerald-100 rounded-lg flex items-center justify-center ring-2 ring-green-100/50 group-hover:scale-105 transition-transform duration-300">
        <div class="relative">
          <div class="w-4 h-4 bg-green-500 rounded-full"></div>
          <div class="absolute inset-0 w-4 h-4 bg-green-500 rounded-full <%= active_count > 0 ? 'animate-ping opacity-75' : '' %>"></div>
        </div>
      </div>
      <div>
        <p class="text-3xl font-bold text-green-600"><%= active_count %></p>
        <div class="flex items-center gap-1 mt-1">
          <span class="text-sm text-gray-600">Currently Active</span>
          <% if active_count > 0 %>
            <span class="flex items-center text-xs text-green-700">
              <svg class="w-3 h-3 mr-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
              Live
            </span>
          <% end %>
        </div>
      </div>
    </div>
    <% if active_count > 0 && active_count < campaigns.count %>
      <div class="mt-3 pt-3 border-t border-gray-100">
        <div class="w-full bg-gray-200 rounded-full h-1.5">
          <div class="bg-green-500 h-1.5 rounded-full" style="width: <%= (active_count.to_f / campaigns.count * 100).round %>%"></div>
        </div>
        <p class="text-xs text-gray-500 mt-1"><%= (active_count.to_f / campaigns.count * 100).round %>% of campaigns active</p>
      </div>
    <% end %>
  </div>
  
  <!-- Total Budget Stat Card -->
  <div class="campaigns-stat-card bg-white/80 backdrop-blur-md rounded-xl p-4 border border-white/50 shadow-sm hover:shadow-md transition-all duration-300 group">
    <div class="flex items-center gap-4">
      <div class="campaigns-stat-icon w-12 h-12 bg-gradient-to-br from-blue-50 to-purple-100 rounded-lg flex items-center justify-center ring-2 ring-purple-100/50 group-hover:scale-105 transition-transform duration-300">
        <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
        </svg>
      </div>
      <div>
        <p class="text-3xl font-bold text-purple-700">$<%= number_with_delimiter(total_budget, precision: 0) %></p>
        <div class="flex items-center gap-1 mt-1">
          <span class="text-sm text-gray-600">Total Budget</span>
          <% avg_budget = campaigns.count > 0 ? total_budget / campaigns.count : 0 %>
          <span class="text-xs text-gray-500">(~$<%= number_with_delimiter(avg_budget.round) %>/campaign)</span>
        </div>
      </div>
    </div>
    <% if campaigns.where.not(budget_cents: nil).exists? %>
      <div class="mt-3 grid grid-cols-5 gap-1">
        <% campaigns.order(budget_cents: :desc).limit(5).each do |c| %>
          <div class="bg-purple-200 h-6 rounded-md" style="opacity: <%= 0.4 + (c.budget_cents.to_f / campaigns.maximum(:budget_cents) * 0.6) %>"></div>
        <% end %>
      </div>
      <p class="text-xs text-gray-500 mt-1">Top campaign budgets distribution</p>
    <% end %>
  </div>
</div>
