<%# Sort Control %>
<div class="campaigns-sort-controls relative">
  <select class="campaigns-sort-select w-full text-sm border border-gray-200 rounded-xl bg-white backdrop-blur-md px-4 py-2.5 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent hover:shadow-md transition-all appearance-none">
    <option value="">📅 Sort by Latest</option>
    <option value="name" <%= 'selected' if params[:sort] == 'name' %>>🔤 Name A-Z</option>
    <option value="budget_desc" <%= 'selected' if params[:sort] == 'budget_desc' %>>💰 Budget (High-Low)</option>
    <option value="budget_asc" <%= 'selected' if params[:sort] == 'budget_asc' %>>💸 Budget (Low-High)</option>
    <option value="start_date" <%= 'selected' if params[:sort] == 'start_date' %>>📅 Start Date</option>
    <option value="status" <%= 'selected' if params[:sort] == 'status' %>>🏷️ Status</option>
    <option value="performance" <%= 'selected' if params[:sort] == 'performance' %>>📊 Performance</option>
  </select>
  <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-3 text-gray-500">
    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
    </svg>
  </div>
</div>
