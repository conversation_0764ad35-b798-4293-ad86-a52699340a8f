<%# Campaign Library Header with Statistics %>
<div class="campaigns-library-header bg-gradient-to-r from-indigo-50 via-purple-50 to-pink-50 rounded-2xl p-6 mb-8 border border-purple-100/50 backdrop-blur-sm shadow-lg relative overflow-hidden">
  <!-- Background Pattern Elements -->
  <div class="absolute top-0 right-0 w-64 h-64 bg-gradient-to-bl from-blue-200/20 to-purple-300/20 rounded-full -mr-32 -mt-32 opacity-60"></div>
  <div class="absolute bottom-0 left-0 w-40 h-40 bg-gradient-to-tr from-pink-200/20 to-indigo-300/20 rounded-full -ml-20 -mb-20 opacity-60"></div>
  
  <div class="relative flex flex-col lg:flex-row lg:items-center justify-between gap-6 z-10">
    <!-- Left Section: Title & Statistics -->
    <div class="flex-1">
      <%= render "campaigns/_partials/library_title" %>
      <%= render "campaigns/_partials/campaign_stats", campaigns: @campaigns %>
    </div>
    
    <!-- Right Section: Controls -->
    <%= render "campaigns/_partials/campaign_controls" %>
  </div>
</div>
