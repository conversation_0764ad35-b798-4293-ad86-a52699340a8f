<%# Bulk Actions Menu %>
<div class="campaigns-bulk-actions relative">
  <button class="campaigns-bulk-toggle w-full inline-flex items-center justify-center px-5 py-2.5 text-sm font-medium text-gray-700 bg-white backdrop-blur-md border border-gray-200 rounded-xl hover:bg-white hover:shadow-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all">
    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"></path>
    </svg>
    <span>Bulk Actions</span>
    <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
    </svg>
  </button>
  <div class="campaigns-bulk-menu hidden absolute right-0 mt-2 w-64 bg-white rounded-xl shadow-xl border border-gray-100 z-20 overflow-hidden">
    <div class="py-2 px-1">
      <div class="text-xs uppercase text-gray-500 px-4 py-1 mb-1">Selection</div>
      <button class="campaigns-bulk-select-all w-full text-left px-4 py-2.5 text-sm text-gray-700 hover:bg-gray-50 rounded-lg transition-colors">
        <svg class="w-4 h-4 inline mr-3 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        Select All Visible
      </button>
      <div class="my-1 border-t border-gray-100"></div>
      <div class="text-xs uppercase text-gray-500 px-4 py-1 mb-1">Actions</div>
      <button class="campaigns-bulk-activate w-full text-left px-4 py-2.5 text-sm text-green-700 hover:bg-green-50 rounded-lg transition-colors">
        <svg class="w-4 h-4 inline mr-3 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m2-10.586V18a2 2 0 01-2 2H6a2 2 0 01-2-2V7.414a1 1 0 01.293-.707l2-2A1 1 0 017 4h4.586a1 1 0 01.707.293l2 2A1 1 0 0115 7.414z"></path>
        </svg>
        Activate Selected
      </button>
      <button class="campaigns-bulk-pause w-full text-left px-4 py-2.5 text-sm text-orange-700 hover:bg-orange-50 rounded-lg transition-colors">
        <svg class="w-4 h-4 inline mr-3 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        Pause Selected
      </button>
      <div class="my-1 border-t border-gray-100"></div>
      <button class="campaigns-bulk-delete w-full text-left px-4 py-2.5 text-sm text-red-700 hover:bg-red-50 rounded-lg transition-colors">
        <svg class="w-4 h-4 inline mr-3 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
        </svg>
        Delete Selected
      </button>
    </div>
  </div>
</div>
