<%# Campaign Controls Section %>
<div class="campaigns-library-controls flex flex-col md:flex-row md:items-end gap-3">
  <!-- Control Buttons Container -->
  <div class="campaign-control-group grid grid-cols-2 sm:grid-cols-2 md:flex gap-3">
    <!-- Create Campaign Button -->
    <%= link_to new_campaign_path, class: "campaigns-create-btn w-full bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-5 py-2.5 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 flex items-center justify-center gap-2 font-medium" do %>
      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
      </svg>
      <span>Create Campaign</span>
    <% end %>
  
    <!-- Bulk Actions -->
    <% if @campaigns.any? %>
      <%= render "campaigns/_partials/bulk_actions_menu" %>
    <% end %>
  </div>

  <!-- View Options Container -->
  <div class="campaign-view-options grid grid-cols-2 gap-3 md:flex md:ml-auto">
    <%= render "campaigns/_partials/sort_control" %>
    <%= render "campaigns/_partials/view_toggle" %>
  </div>
</div>
