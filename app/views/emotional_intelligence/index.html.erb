<%# Marketing Therapist AI - Emotional Intelligence Dashboard %>
<% content_for :title, @page_title %>

<div class="min-h-screen bg-gray-50" data-controller="emotional-intelligence">
  <!-- Header Section -->
  <div class="bg-gradient-to-r from-purple-600 to-indigo-600 text-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-3xl font-bold">🧠 Marketing Therapist AI</h1>
          <p class="text-purple-100 mt-2">Real-time emotional intelligence for empathetic marketing</p>
        </div>
        <div class="flex space-x-4">
          <%= link_to emotional_intelligence_real_time_monitor_path, 
                      class: "bg-white/20 hover:bg-white/30 px-4 py-2 rounded-lg transition-colors" do %>
            📊 Real-Time Monitor
          <% end %>
          <%= link_to emotional_intelligence_insights_path, 
                      class: "bg-white/20 hover:bg-white/30 px-4 py-2 rounded-lg transition-colors" do %>
            🔍 Insights
          <% end %>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    
    <!-- Summary Stats -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
      <div class="bg-white rounded-lg shadow-sm p-6 border-l-4 border-blue-500">
        <div class="flex items-center">
          <div class="text-3xl text-blue-500">👥</div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Total Customers</p>
            <p class="text-2xl font-bold text-gray-900"><%= @emotional_stats[:total_customers] %></p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm p-6 border-l-4 border-green-500">
        <div class="flex items-center">
          <div class="text-3xl text-green-500">😊</div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Receptive to Marketing</p>
            <p class="text-2xl font-bold text-gray-900"><%= @emotional_stats[:receptive_customers] %></p>
            <p class="text-xs text-gray-500">
              <%= (@emotional_stats[:receptive_customers].to_f / [@emotional_stats[:total_customers], 1].max * 100).round(1) %>%
            </p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm p-6 border-l-4 border-purple-500">
        <div class="flex items-center">
          <div class="text-3xl text-purple-500">🎯</div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Avg Confidence</p>
            <p class="text-2xl font-bold text-gray-900"><%= @emotional_stats[:avg_confidence] %>%</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm p-6 border-l-4 border-orange-500">
        <div class="flex items-center">
          <div class="text-3xl text-orange-500">⚡</div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">High Confidence</p>
            <p class="text-2xl font-bold text-gray-900"><%= @emotional_stats[:high_confidence_states] %></p>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Grid Layout -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      
      <!-- Left Column: Emotion Distribution & Recent Customers -->
      <div class="lg:col-span-2 space-y-8">
        
        <!-- Current Emotional Distribution -->
        <div class="bg-white rounded-lg shadow-sm p-6">
          <div class="flex items-center justify-between mb-6">
            <h2 class="text-xl font-semibold text-gray-900">Current Emotional Landscape</h2>
            <div class="text-sm text-gray-500">
              Last updated: <%= Time.current.strftime("%I:%M %p") %>
            </div>
          </div>
          
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <% @emotional_stats[:emotion_distribution].each do |emotion, count| %>
              <div class="text-center p-4 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors">
                <div class="text-2xl mb-2"><%= emotion_emoji(emotion) %></div>
                <div class="text-lg font-semibold text-gray-900"><%= count %></div>
                <div class="text-sm text-gray-600 capitalize"><%= emotion.humanize %></div>
                <div class="text-xs text-gray-500 mt-1">
                  <%= (count.to_f / [@emotional_stats[:total_customers], 1].max * 100).round(1) %>%
                </div>
              </div>
            <% end %>
          </div>
          
          <% if @emotional_stats[:emotion_distribution].empty? %>
            <div class="text-center py-8 text-gray-500">
              <div class="text-6xl mb-4">🤖</div>
              <p class="text-lg">No emotional data yet</p>
              <p class="text-sm">Start collecting customer behavioral signals to see emotional insights</p>
            </div>
          <% end %>
        </div>

        <!-- Recent Customer States -->
        <div class="bg-white rounded-lg shadow-sm p-6">
          <div class="flex items-center justify-between mb-6">
            <h2 class="text-xl font-semibold text-gray-900">Recent Customer Interactions</h2>
            <%= link_to emotional_intelligence_real_time_monitor_path, 
                        class: "text-sm text-indigo-600 hover:text-indigo-700" do %>
              View All →
            <% end %>
          </div>
          
          <div class="space-y-4">
            <% @recent_emotional_states.first(8).each do |state| %>
              <div class="flex items-center justify-between p-4 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors">
                <div class="flex items-center space-x-4">
                  <div class="text-2xl"><%= emotion_emoji(state.current_emotion) %></div>
                  <div>
                    <div class="font-medium text-gray-900">
                      Customer <%= state.customer_identifier.truncate(15) %>
                    </div>
                    <div class="text-sm text-gray-600">
                      <%= state.current_emotion.humanize %> • 
                      <%= state.emotional_intensity.humanize %> intensity •
                      <%= state.confidence_score.round(1) %>% confidence
                    </div>
                  </div>
                </div>
                <div class="flex items-center space-x-3">
                  <% if state.receptive_to_marketing? %>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      Receptive
                    </span>
                  <% else %>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                      Not Receptive
                    </span>
                  <% end %>
                  <%= link_to emotional_intelligence_customer_path(state.customer_identifier), 
                              class: "text-indigo-600 hover:text-indigo-700 text-sm" do %>
                    View →
                  <% end %>
                </div>
              </div>
            <% end %>
          </div>
          
          <% if @recent_emotional_states.empty? %>
            <div class="text-center py-8 text-gray-500">
              <div class="text-4xl mb-2">📭</div>
              <p>No recent customer interactions</p>
            </div>
          <% end %>
        </div>
      </div>

      <!-- Right Column: Insights & Actions -->
      <div class="space-y-8">
        
        <!-- Emotional Intelligence Insights -->
        <div class="bg-white rounded-lg shadow-sm p-6">
          <h2 class="text-xl font-semibold text-gray-900 mb-6">🧠 AI Insights</h2>
          
          <div class="space-y-4">
            <% @insights.each do |insight| %>
              <div class="p-4 rounded-lg border border-gray-200 <%= insight_border_color(insight[:type]) %>">
                <div class="flex items-start space-x-3">
                  <div class="text-xl"><%= insight_icon(insight[:type]) %></div>
                  <div class="flex-1">
                    <h3 class="font-medium text-gray-900 mb-1"><%= insight[:title] %></h3>
                    <p class="text-sm text-gray-600 mb-2"><%= insight[:description] %></p>
                    <p class="text-xs text-indigo-600 font-medium"><%= insight[:action] %></p>
                  </div>
                </div>
              </div>
            <% end %>
          </div>
          
          <% if @insights.empty? %>
            <div class="text-center py-6 text-gray-500">
              <div class="text-3xl mb-2">🔍</div>
              <p class="text-sm">Gathering insights...</p>
            </div>
          <% end %>
        </div>

        <!-- Campaigns Needing Review -->
        <div class="bg-white rounded-lg shadow-sm p-6">
          <h2 class="text-xl font-semibold text-gray-900 mb-6">⚠️ Needs Emotional Review</h2>
          
          <div class="space-y-3">
            <% @campaigns_needing_review.each do |campaign| %>
              <div class="flex items-center justify-between p-3 rounded-lg border border-yellow-200 bg-yellow-50">
                <div>
                  <div class="font-medium text-gray-900"><%= campaign.name %></div>
                  <div class="text-sm text-gray-600 capitalize"><%= campaign.campaign_type %> Campaign</div>
                </div>
                <%= link_to emotional_intelligence_campaign_emotional_analysis_path(campaign), 
                            class: "text-yellow-700 hover:text-yellow-800 text-sm font-medium" do %>
                  Review →
                <% end %>
              </div>
            <% end %>
          </div>
          
          <% if @campaigns_needing_review.empty? %>
            <div class="text-center py-6 text-gray-500">
              <div class="text-3xl mb-2 text-green-500">✅</div>
              <p class="text-sm">All campaigns have emotional analysis</p>
            </div>
          <% end %>
        </div>

        <!-- Recent Adaptations -->
        <div class="bg-white rounded-lg shadow-sm p-6">
          <h2 class="text-xl font-semibold text-gray-900 mb-6">⚡ Recent Adaptations</h2>
          
          <div class="space-y-3">
            <% @recent_adaptations.first(5).each do |adaptation| %>
              <div class="p-3 rounded-lg border border-gray-200">
                <div class="flex items-center justify-between mb-1">
                  <span class="text-xs font-medium text-gray-500 uppercase tracking-wide">
                    <%= adaptation[:type].humanize %>
                  </span>
                  <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium
                         <%= adaptation[:severity] == 'high' ? 'bg-red-100 text-red-800' : 'bg-blue-100 text-blue-800' %>">
                    <%= adaptation[:severity].capitalize %>
                  </span>
                </div>
                <p class="text-sm text-gray-900"><%= adaptation[:message] %></p>
                <p class="text-xs text-gray-500 mt-1">
                  <%= time_ago_in_words(adaptation[:timestamp]) %> ago
                </p>
              </div>
            <% end %>
          </div>
          
          <% if @recent_adaptations.empty? %>
            <div class="text-center py-6 text-gray-500">
              <div class="text-3xl mb-2">🔧</div>
              <p class="text-sm">No recent adaptations</p>
            </div>
          <% end %>
        </div>

        <!-- Quick Actions -->
        <div class="bg-white rounded-lg shadow-sm p-6">
          <h2 class="text-xl font-semibold text-gray-900 mb-6">🚀 Quick Actions</h2>
          
          <div class="space-y-3">
            <%= button_to "Analyze All Customers", 
                          emotional_intelligence_bulk_analyze_path,
                          params: { 
                            customer_identifiers: @recent_emotional_states.pluck(:customer_identifier),
                            campaign_ids: current_tenant.campaigns.active.pluck(:id) 
                          },
                          method: :post,
                          class: "w-full bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-2 px-4 rounded-lg transition-colors",
                          data: { confirm: "This will analyze all customers against active campaigns. Continue?" } %>
            
            <%= link_to emotional_intelligence_real_time_monitor_path, 
                        class: "block w-full bg-gray-100 hover:bg-gray-200 text-gray-900 font-medium py-2 px-4 rounded-lg transition-colors text-center" do %>
              📊 Monitor Real-Time
            <% end %>
            
            <%= link_to emotional_intelligence_insights_path, 
                        class: "block w-full bg-purple-100 hover:bg-purple-200 text-purple-900 font-medium py-2 px-4 rounded-lg transition-colors text-center" do %>
              🔍 View Insights
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<%# Auto-refresh functionality %>
<script>
  // Auto-refresh the dashboard every 30 seconds
  setInterval(function() {
    if (document.hidden) return; // Don't refresh if tab is hidden
    
    fetch(window.location.href + '.json')
      .then(response => response.json())
      .then(data => {
        // Update the stats without full page reload
        console.log('Dashboard data updated', data.last_updated);
        // In production, this would update specific DOM elements
      })
      .catch(error => console.log('Auto-refresh failed:', error));
  }, 30000);
</script>
