<%# Customer Emotional Profile Detail View %>
<% content_for :title, @page_title %>

<div class="min-h-screen bg-gray-50" data-controller="emotional-customer-detail">
  <!-- Header -->
  <div class="bg-white border-b border-gray-200">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <%= link_to emotional_intelligence_path, 
                      class: "text-gray-500 hover:text-gray-700" do %>
            ← Back to Dashboard
          <% end %>
          <div>
            <h1 class="text-2xl font-bold text-gray-900">
              <%= emotion_emoji(@customer_emotional_state.current_emotion) %>
              Customer <%= @customer_emotional_state.customer_identifier.truncate(20) %>
            </h1>
            <p class="text-sm text-gray-600 mt-1">
              Emotional Intelligence Profile • Last updated <%= time_ago_in_words(@customer_emotional_state.last_interaction_at) %> ago
            </p>
          </div>
        </div>
        
        <div class="flex space-x-3">
          <button data-action="click->emotional-customer-detail#refreshState"
                  class="bg-white border border-gray-300 rounded-lg px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors">
            🔄 Refresh State
          </button>
          <button data-action="click->emotional-customer-detail#showUpdateModal"
                  class="bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg px-4 py-2 text-sm font-medium transition-colors">
            📊 Update Signals
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    
    <!-- Current Emotional State -->
    <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
      <h2 class="text-xl font-semibold text-gray-900 mb-6">Current Emotional State</h2>
      
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <!-- Primary Emotion -->
        <div class="text-center p-6 rounded-lg border-2 <%= emotion_border_color(@customer_emotional_state.current_emotion) %>">
          <div class="text-4xl mb-3"><%= emotion_emoji(@customer_emotional_state.current_emotion) %></div>
          <div class="font-semibold text-gray-900 text-lg capitalize">
            <%= @customer_emotional_state.current_emotion.humanize %>
          </div>
          <div class="text-sm text-gray-600 mt-1">Primary Emotion</div>
        </div>

        <!-- Intensity -->
        <div class="text-center p-6 rounded-lg border border-gray-200">
          <div class="text-3xl mb-3"><%= intensity_emoji(@customer_emotional_state.emotional_intensity) %></div>
          <div class="font-semibold text-gray-900 text-lg capitalize">
            <%= @customer_emotional_state.emotional_intensity.humanize %>
          </div>
          <div class="text-sm text-gray-600 mt-1">Intensity Level</div>
        </div>

        <!-- Confidence -->
        <div class="text-center p-6 rounded-lg border border-gray-200">
          <div class="text-3xl mb-3"><%= confidence_emoji(@customer_emotional_state.confidence_score) %></div>
          <div class="font-semibold text-gray-900 text-lg">
            <%= @customer_emotional_state.confidence_score.round(1) %>%
          </div>
          <div class="text-sm text-gray-600 mt-1">Confidence Score</div>
        </div>

        <!-- Receptivity -->
        <div class="text-center p-6 rounded-lg border border-gray-200">
          <div class="text-3xl mb-3">
            <%= @customer_emotional_state.receptive_to_marketing? ? "✅" : "❌" %>
          </div>
          <div class="font-semibold text-gray-900 text-lg">
            <%= @customer_emotional_state.receptive_to_marketing? ? "Receptive" : "Not Receptive" %>
          </div>
          <div class="text-sm text-gray-600 mt-1">Marketing Readiness</div>
        </div>
      </div>
    </div>

    <!-- Main Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      
      <!-- Left Column: Journey & Trajectory -->
      <div class="lg:col-span-2 space-y-8">
        
        <!-- Emotional Journey -->
        <div class="bg-white rounded-lg shadow-sm p-6">
          <h2 class="text-xl font-semibold text-gray-900 mb-6">Emotional Journey (30 Days)</h2>
          
          <% if @emotional_journey[:journey_points].any? %>
            <div class="space-y-4 mb-6">
              <% @emotional_journey[:journey_points].reverse.first(10).each_with_index do |point, index| %>
                <div class="flex items-center space-x-4 p-3 rounded-lg <%= index == 0 ? 'bg-blue-50 border-2 border-blue-200' : 'bg-gray-50' %>">
                  <div class="text-xl"><%= emotion_emoji(point[:emotion]) %></div>
                  <div class="flex-1">
                    <div class="flex items-center justify-between">
                      <span class="font-medium capitalize"><%= point[:emotion].humanize %></span>
                      <span class="text-sm text-gray-500">
                        <%= Time.parse(point[:timestamp]).strftime("%b %d, %I:%M %p") rescue point[:timestamp] %>
                      </span>
                    </div>
                    <div class="text-sm text-gray-600">
                      <%= point[:intensity].humanize %> intensity • 
                      <%= point[:confidence].round(1) %>% confidence
                      <% if point[:trigger].present? %>
                        • Triggered by <%= point[:trigger].humanize %>
                      <% end %>
                    </div>
                  </div>
                  <% if index == 0 %>
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      Current
                    </span>
                  <% end %>
                </div>
              <% end %>
            </div>

            <!-- Journey Analytics -->
            <div class="grid grid-cols-2 gap-4 pt-4 border-t border-gray-200">
              <div class="text-center">
                <div class="text-2xl font-bold text-gray-900">
                  <%= (@emotional_journey[:emotional_stability] * 100).round(1) %>%
                </div>
                <div class="text-sm text-gray-600">Emotional Stability</div>
              </div>
              <div class="text-center">
                <div class="text-2xl font-bold text-gray-900">
                  <%= @emotional_journey[:dominant_emotions].first&.first&.humanize || "N/A" %>
                </div>
                <div class="text-sm text-gray-600">Dominant Emotion</div>
              </div>
            </div>
          <% else %>
            <div class="text-center py-8 text-gray-500">
              <div class="text-4xl mb-4">📈</div>
              <p class="text-lg">No historical data yet</p>
              <p class="text-sm">Emotional journey will appear as more interactions are recorded</p>
            </div>
          <% end %>
        </div>

        <!-- Campaign Compatibility Analysis -->
        <div class="bg-white rounded-lg shadow-sm p-6">
          <h2 class="text-xl font-semibold text-gray-900 mb-6">Campaign Compatibility</h2>
          
          <div class="space-y-4">
            <% @campaign_compatibility.each do |compatibility| %>
              <div class="p-4 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors">
                <div class="flex items-center justify-between mb-3">
                  <div>
                    <h3 class="font-medium text-gray-900">
                      <%= compatibility[:campaign][:name] %>
                    </h3>
                    <p class="text-sm text-gray-600 capitalize">
                      <%= compatibility[:campaign][:type] %> Campaign
                    </p>
                  </div>
                  <div class="text-right">
                    <div class="text-lg font-semibold <%= compatibility_score_color(compatibility[:compatibility_score]) %>">
                      <%= compatibility[:compatibility_score].round(1) %>%
                    </div>
                    <div class="text-xs text-gray-500">Compatibility</div>
                  </div>
                </div>
                
                <div class="flex items-center justify-between">
                  <span class="text-sm text-gray-700">
                    <%= compatibility[:recommendation] %>
                  </span>
                  <button data-action="click->emotional-customer-detail#predictResponse"
                          data-campaign-id="<%= compatibility[:campaign][:id] %>"
                          class="text-indigo-600 hover:text-indigo-700 text-sm font-medium">
                    Predict Response →
                  </button>
                </div>
              </div>
            <% end %>
          </div>
          
          <% if @campaign_compatibility.empty? %>
            <div class="text-center py-8 text-gray-500">
              <div class="text-4xl mb-4">🎯</div>
              <p class="text-lg">No active campaigns</p>
              <p class="text-sm">Campaign compatibility will appear when campaigns are active</p>
            </div>
          <% end %>
        </div>
      </div>

      <!-- Right Column: Recommendations & Actions -->
      <div class="space-y-8">
        
        <!-- AI Recommendations -->
        <div class="bg-white rounded-lg shadow-sm p-6">
          <h2 class="text-xl font-semibold text-gray-900 mb-6">🤖 AI Recommendations</h2>
          
          <div class="space-y-4">
            <% @recommendations.each do |rec| %>
              <div class="p-4 rounded-lg border border-gray-200 <%= recommendation_bg_color(rec[:priority]) %>">
                <div class="flex items-start space-x-3">
                  <div class="text-lg"><%= recommendation_icon(rec[:type]) %></div>
                  <div class="flex-1">
                    <div class="flex items-center justify-between mb-1">
                      <span class="font-medium text-gray-900 capitalize"><%= rec[:type].humanize %></span>
                      <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium
                             <%= priority_badge_color(rec[:priority]) %>">
                        <%= rec[:priority].capitalize %>
                      </span>
                    </div>
                    <p class="text-sm text-gray-700 mb-2"><%= rec[:action] %></p>
                    <p class="text-xs text-gray-600"><%= rec[:details] %></p>
                  </div>
                </div>
              </div>
            <% end %>
          </div>
          
          <% if @recommendations.empty? %>
            <div class="text-center py-6 text-gray-500">
              <div class="text-3xl mb-2">💡</div>
              <p class="text-sm">No specific recommendations at this time</p>
            </div>
          <% end %>
        </div>

        <!-- Trajectory Analysis -->
        <% if @trajectory_analysis.present? && @trajectory_analysis.any? %>
          <div class="bg-white rounded-lg shadow-sm p-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-6">📈 Trajectory Analysis</h2>
            
            <div class="space-y-4">
              <% if @trajectory_analysis[:trend] %>
                <div class="flex items-center justify-between p-3 rounded-lg bg-gray-50">
                  <span class="text-sm font-medium text-gray-700">Emotional Trend</span>
                  <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                         <%= trend_badge_color(@trajectory_analysis[:trend]) %>">
                    <%= @trajectory_analysis[:trend].humanize %>
                  </span>
                </div>
              <% end %>
              
              <% if @trajectory_analysis[:volatility] %>
                <div class="flex items-center justify-between p-3 rounded-lg bg-gray-50">
                  <span class="text-sm font-medium text-gray-700">Volatility</span>
                  <span class="text-sm text-gray-900">
                    <%= (@trajectory_analysis[:volatility] * 100).round(1) %>%
                  </span>
                </div>
              <% end %>
              
              <% if @trajectory_analysis[:stability_score] %>
                <div class="flex items-center justify-between p-3 rounded-lg bg-gray-50">
                  <span class="text-sm font-medium text-gray-700">Stability Score</span>
                  <span class="text-sm text-gray-900">
                    <%= (@trajectory_analysis[:stability_score] * 100).round(1) %>%
                  </span>
                </div>
              <% end %>
            </div>
          </div>
        <% end %>

        <!-- Quick Actions -->
        <div class="bg-white rounded-lg shadow-sm p-6">
          <h2 class="text-xl font-semibold text-gray-900 mb-6">⚡ Quick Actions</h2>
          
          <div class="space-y-3">
            <button data-action="click->emotional-customer-detail#showUpdateModal"
                    class="w-full bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-2 px-4 rounded-lg transition-colors">
              📊 Update Behavioral Signals
            </button>
            
            <button data-action="click->emotional-customer-detail#generateReport"
                    class="w-full bg-gray-100 hover:bg-gray-200 text-gray-900 font-medium py-2 px-4 rounded-lg transition-colors">
              📄 Generate Report
            </button>
            
            <button data-action="click->emotional-customer-detail#exportData"
                    class="w-full bg-purple-100 hover:bg-purple-200 text-purple-900 font-medium py-2 px-4 rounded-lg transition-colors">
              💾 Export Data
            </button>
          </div>
        </div>

        <!-- Emotional Context -->
        <div class="bg-white rounded-lg shadow-sm p-6">
          <h2 class="text-xl font-semibold text-gray-900 mb-6">📋 Context Details</h2>
          
          <div class="space-y-3 text-sm">
            <div class="flex justify-between">
              <span class="text-gray-600">Last Interaction:</span>
              <span class="text-gray-900">
                <%= @customer_emotional_state.last_interaction_at&.strftime("%b %d, %I:%M %p") || "Never" %>
              </span>
            </div>
            
            <div class="flex justify-between">
              <span class="text-gray-600">Emotional Changes:</span>
              <span class="text-gray-900">
                <%= @customer_emotional_state.significant_emotional_change? ? "Recent changes" : "Stable" %>
              </span>
            </div>
            
            <div class="flex justify-between">
              <span class="text-gray-600">Data Sources:</span>
              <span class="text-gray-900">
                <%= @customer_emotional_state.behavioral_signals.keys.count %> sources
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Update Behavioral Signals Modal -->
<div id="updateSignalsModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
  <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
    <div class="mt-3">
      <h3 class="text-lg font-medium text-gray-900 mb-4">Update Behavioral Signals</h3>
      
      <%= form_with url: emotional_intelligence_customer_update_state_path(@customer_emotional_state.customer_identifier),
                    method: :post, local: false, id: "updateSignalsForm" do |form| %>
        
        <div class="space-y-4">
          <!-- Email Engagement -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Email Engagement</label>
            <div class="grid grid-cols-2 gap-2">
              <%= form.number_field "behavioral_signals[email_engagement][open_rate]", 
                                    placeholder: "Open Rate %", min: 0, max: 100,
                                    class: "block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" %>
              <%= form.number_field "behavioral_signals[email_engagement][click_rate]", 
                                    placeholder: "Click Rate %", min: 0, max: 100,
                                    class: "block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" %>
            </div>
          </div>

          <!-- Website Behavior -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Website Behavior</label>
            <div class="grid grid-cols-2 gap-2">
              <%= form.number_field "behavioral_signals[website_behavior][session_duration]", 
                                    placeholder: "Session Duration (sec)", min: 0,
                                    class: "block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" %>
              <%= form.number_field "behavioral_signals[website_behavior][pages_per_session]", 
                                    placeholder: "Pages per Session", min: 0,
                                    class: "block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" %>
            </div>
          </div>

          <!-- Source -->
          <%= form.hidden_field :source, value: "manual_update" %>
        </div>

        <div class="flex justify-end space-x-3 mt-6">
          <button type="button" 
                  data-action="click->emotional-customer-detail#hideUpdateModal"
                  class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-medium py-2 px-4 rounded-lg transition-colors">
            Cancel
          </button>
          <%= form.submit "Update Signals", 
                          class: "bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-2 px-4 rounded-lg transition-colors" %>
        </div>
      <% end %>
    </div>
  </div>
</div>

<!-- Response Prediction Modal -->
<div id="responsePredictionModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
  <div class="relative top-20 mx-auto p-5 border w-2xl shadow-lg rounded-md bg-white max-w-2xl">
    <div class="mt-3">
      <h3 class="text-lg font-medium text-gray-900 mb-4">Campaign Response Prediction</h3>
      <div id="predictionResults" class="space-y-4">
        <!-- Prediction results will be loaded here -->
      </div>
      <div class="flex justify-end mt-6">
        <button type="button" 
                data-action="click->emotional-customer-detail#hidePredictionModal"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-medium py-2 px-4 rounded-lg transition-colors">
          Close
        </button>
      </div>
    </div>
  </div>
</div>
