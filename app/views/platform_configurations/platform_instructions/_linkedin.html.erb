<p class="text-blue-800 mb-4">
  To connect LinkedIn, you'll need to create a LinkedIn Developer application.
</p>

<div class="space-y-3 text-sm text-blue-800">
  <div>
    <strong>Step 1:</strong> Go to <a href="https://www.linkedin.com/developers" target="_blank" class="underline">LinkedIn Developer Portal</a>
  </div>
  <div>
    <strong>Step 2:</strong> Create a new app with these settings:
    <ul class="list-disc list-inside ml-4 mt-2 space-y-1">
      <li>App name and description</li>
      <li>Associated LinkedIn Page (required)</li>
      <li>Redirect URLs: <code class="bg-blue-100 px-1 rounded"><%= "#{request.base_url}/platform_configurations/oauth_callback/linkedin" %></code></li>
    </ul>
  </div>
  <div>
    <strong>Step 3:</strong> Request access to Share on LinkedIn product</li>
  </div>
  <div>
    <strong>Step 4:</strong> Copy your Client ID and Client Secret from Auth tab
  </div>
</div>
