<p class="text-blue-800 mb-4">
  To connect Twitter/X, you'll need to create a Twitter Developer account and application.
</p>

<div class="space-y-3 text-sm text-blue-800">
  <div>
    <strong>Step 1:</strong> Go to <a href="https://developer.twitter.com" target="_blank" class="underline">Twitter Developer Portal</a>
  </div>
  <div>
    <strong>Step 2:</strong> Create a new app with these settings:
    <ul class="list-disc list-inside ml-4 mt-2 space-y-1">
      <li>App permissions: Read and Write</li>
      <li>Type of App: Web App</li>
      <li>Callback URL: <code class="bg-blue-100 px-1 rounded"><%= "#{request.base_url}/platform_configurations/oauth_callback/twitter" %></code></li>
    </ul>
  </div>
  <div>
    <strong>Step 3:</strong> Copy your Client ID and Client Secret from the app settings
  </div>
  <div>
    <strong>Step 4:</strong> Enable OAuth 2.0 in your app settings
  </div>
</div>
