<p class="text-blue-800 mb-4">
  To connect Instagram, you'll need to use the Instagram Basic Display API.
</p>

<div class="space-y-3 text-sm text-blue-800">
  <div>
    <strong>Step 1:</strong> Go to <a href="https://developers.facebook.com" target="_blank" class="underline">Facebook for Developers</a>
  </div>
  <div>
    <strong>Step 2:</strong> Create a new app and add Instagram Basic Display:
    <ul class="list-disc list-inside ml-4 mt-2 space-y-1">
      <li>Add Instagram Basic Display product</li>
      <li>Valid OAuth Redirect URIs: <code class="bg-blue-100 px-1 rounded"><%= "#{request.base_url}/platform_configurations/oauth_callback/instagram" %></code></li>
      <li>Deauthorize Callback URL: <code class="bg-blue-100 px-1 rounded"><%= "#{request.base_url}/webhooks/instagram/deauth" %></code></li>
    </ul>
  </div>
  <div>
    <strong>Step 3:</strong> Copy your Instagram App ID and App Secret
  </div>
  <div>
    <strong>Step 4:</strong> Add test users in the Roles section
  </div>
</div>
