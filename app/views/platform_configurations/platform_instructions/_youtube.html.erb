<p class="text-blue-800 mb-4">
  To connect YouTube, you'll need to create a Google Cloud Platform project and enable the YouTube API.
</p>

<div class="space-y-3 text-sm text-blue-800">
  <div>
    <strong>Step 1:</strong> Go to <a href="https://console.cloud.google.com" target="_blank" class="underline">Google Cloud Console</a>
  </div>
  <div>
    <strong>Step 2:</strong> Create a new project and enable YouTube Data API v3:
    <ul class="list-disc list-inside ml-4 mt-2 space-y-1">
      <li>Enable YouTube Data API v3</li>
      <li>Create OAuth 2.0 credentials</li>
      <li>Authorized redirect URIs: <code class="bg-blue-100 px-1 rounded"><%= "#{request.base_url}/platform_configurations/oauth_callback/youtube" %></code></li>
    </ul>
  </div>
  <div>
    <strong>Step 3:</strong> Configure OAuth consent screen</li>
  </div>
  <div>
    <strong>Step 4:</strong> Copy your Client ID and Client Secret from Credentials page
  </div>
</div>
