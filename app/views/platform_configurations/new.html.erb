<%# New Platform Configuration Form %>
<div class="bg-gray-50 min-h-screen">
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    
    <!-- Header -->
    <div class="bg-white rounded-xl shadow-sm p-8 mb-8">
      <div class="flex items-center space-x-4">
        <%= link_to platform_configurations_path, 
            class: "flex items-center text-gray-500 hover:text-gray-700" do %>
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
          </svg>
          Back to Platforms
        <% end %>
      </div>
      
      <div class="mt-4">
        <div class="flex items-center space-x-4">
          <%= render "shared/platform_icons/#{@platform_name}", css_class: "w-12 h-12" %>
          <div>
            <h1 class="text-3xl font-bold text-gray-900">Connect <%= @platform_name.titleize %></h1>
            <p class="text-gray-600">Set up OAuth integration to publish content automatically</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Configuration Form -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100">
      <%= form_with model: @platform_configuration, url: platform_configurations_path, local: true, 
          html: { class: "p-8 space-y-8" } do |form| %>
        
        <%= form.hidden_field :platform_name, value: @platform_name %>
        
        <!-- Platform-Specific Instructions -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <div class="flex items-start space-x-3">
            <svg class="w-6 h-6 text-blue-600 flex-shrink-0 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <div>
              <h3 class="text-lg font-medium text-blue-900 mb-2">
                Before you begin - <%= @platform_name.titleize %> Setup
              </h3>
              
              <%= render partial: "platform_configurations/platform_instructions/#{@platform_name}" %>
            </div>
          </div>
        </div>

        <!-- OAuth Credentials -->
        <div class="border-b border-gray-200 pb-8">
          <h2 class="text-xl font-semibold text-gray-900 mb-4">OAuth Credentials</h2>
          <p class="text-gray-600 mb-6">
            Enter the API credentials from your <%= @platform_name.titleize %> developer application.
          </p>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <%= form.label :client_id, "Client ID", class: "block text-sm font-medium text-gray-700 mb-2" %>
              <%= form.text_field :client_id, 
                  class: "w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",
                  placeholder: "Enter your Client ID",
                  required: true %>
              <% if form.object.errors[:client_id].any? %>
                <p class="mt-1 text-sm text-red-600"><%= form.object.errors[:client_id].first %></p>
              <% end %>
            </div>
            
            <div>
              <%= form.label :client_secret, "Client Secret", class: "block text-sm font-medium text-gray-700 mb-2" %>
              <%= form.password_field :client_secret, 
                  class: "w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",
                  placeholder: "Enter your Client Secret",
                  required: true %>
              <% if form.object.errors[:client_secret].any? %>
                <p class="mt-1 text-sm text-red-600"><%= form.object.errors[:client_secret].first %></p>
              <% end %>
            </div>
          </div>
        </div>

        <!-- Webhook Configuration -->
        <div class="border-b border-gray-200 pb-8">
          <h2 class="text-xl font-semibold text-gray-900 mb-4">Webhook Configuration</h2>
          <p class="text-gray-600 mb-6">
            Configure webhook URL for real-time updates (optional but recommended).
          </p>
          
          <div>
            <%= form.label :webhook_url, "Webhook URL", class: "block text-sm font-medium text-gray-700 mb-2" %>
            <%= form.url_field :webhook_url, 
                class: "w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",
                placeholder: "https://your-domain.com/webhooks/#{@platform_name}",
                value: "#{request.base_url}/webhooks/#{@platform_name}" %>
            <p class="mt-1 text-sm text-gray-500">
              This URL will receive real-time updates from <%= @platform_name.titleize %>.
            </p>
            <% if form.object.errors[:webhook_url].any? %>
              <p class="mt-1 text-sm text-red-600"><%= form.object.errors[:webhook_url].first %></p>
            <% end %>
          </div>
        </div>

        <!-- Platform Settings -->
        <div class="border-b border-gray-200 pb-8">
          <h2 class="text-xl font-semibold text-gray-900 mb-4">Platform Settings</h2>
          <p class="text-gray-600 mb-6">
            Configure how this platform integration will behave.
          </p>
          
          <div class="space-y-4">
            <div class="flex items-center">
              <%= form.check_box :auto_sync_enabled, 
                  class: "h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded",
                  checked: true %>
              <%= form.label :auto_sync_enabled, "Enable automatic synchronization", 
                  class: "ml-2 block text-sm text-gray-900" %>
              <p class="ml-6 text-sm text-gray-500">
                Automatically sync content and analytics data every hour.
              </p>
            </div>
            
            <div class="flex items-center">
              <%= form.check_box :posting_enabled, 
                  class: "h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded",
                  checked: true %>
              <%= form.label :posting_enabled, "Enable content publishing", 
                  class: "ml-2 block text-sm text-gray-900" %>
              <p class="ml-6 text-sm text-gray-500">
                Allow AI Marketing Hub to publish content to this platform.
              </p>
            </div>
            
            <div class="flex items-center">
              <%= form.check_box :analytics_enabled, 
                  class: "h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded",
                  checked: true %>
              <%= form.label :analytics_enabled, "Enable analytics collection", 
                  class: "ml-2 block text-sm text-gray-900" %>
              <p class="ml-6 text-sm text-gray-500">
                Collect performance metrics and engagement data.
              </p>
            </div>
          </div>
        </div>

        <!-- OAuth Scopes Information -->
        <div class="bg-gray-50 rounded-lg p-6">
          <h3 class="text-lg font-medium text-gray-900 mb-3">
            Requested Permissions
          </h3>
          <p class="text-gray-600 mb-4">
            When you connect your account, we'll request these permissions:
          </p>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <% @oauth_settings[:scopes].each do |scope| %>
              <div class="flex items-center space-x-2">
                <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span class="text-sm text-gray-700"><%= scope.humanize %></span>
              </div>
            <% end %>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="flex items-center justify-between pt-6 border-t border-gray-200">
          <%= link_to platform_configurations_path, 
              class: "px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50" do %>
            Cancel
          <% end %>
          
          <%= form.submit "Connect to #{@platform_name.titleize}", 
              class: "inline-flex items-center px-6 py-2 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" %>
        </div>
      <% end %>
    </div>
  </div>
</div>
