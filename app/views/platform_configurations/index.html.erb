<%# Platform Configurations Index - Social Media OAuth Management %>
<div class="bg-gray-50 min-h-screen">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    
    <!-- Page Header -->
    <div class="bg-white rounded-xl shadow-sm p-8 mb-8">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-3xl font-bold text-gray-900 mb-2">Social Platform Connections</h1>
          <p class="text-gray-600">Connect and manage your social media accounts for seamless content publishing</p>
        </div>
        <div class="flex items-center space-x-3">
          <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
            <%= @connected_platforms.count %> Connected
          </span>
          <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800">
            <%= @available_platforms.count - @connected_platforms.count %> Available
          </span>
        </div>
      </div>
    </div>

    <!-- Platform Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <% @available_platforms.each do |platform| %>
        <% is_connected = @connected_platforms.include?(platform) %>
        <% config = @platform_configurations.find { |pc| pc.platform_name == platform } %>
        
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden hover:shadow-md transition-shadow">
          <!-- Platform Header -->
          <div class="p-6 border-b border-gray-100">
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-3">
                <div class="flex-shrink-0">
                  <%= render "shared/platform_icons/#{platform}", css_class: "w-10 h-10" %>
                </div>
                <div>
                  <h3 class="text-lg font-semibold text-gray-900"><%= platform.titleize %></h3>
                  <p class="text-sm text-gray-500">
                    <% if is_connected %>
                      Connected as <%= config&.connected_account_name || 'Unknown' %>
                    <% else %>
                      Not connected
                    <% end %>
                  </p>
                </div>
              </div>
              
              <!-- Status Badge -->
              <% if is_connected %>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  Connected
                </span>
              <% else %>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                  Available
                </span>
              <% end %>
            </div>
          </div>

          <!-- Platform Details -->
          <div class="p-6">
            <% if is_connected %>
              <!-- Connected Platform Info -->
              <div class="space-y-3 mb-4">
                <div class="flex justify-between text-sm">
                  <span class="text-gray-500">Last Sync</span>
                  <span class="text-gray-900">
                    <%= config.last_sync_at ? time_ago_in_words(config.last_sync_at) + ' ago' : 'Never' %>
                  </span>
                </div>
                <div class="flex justify-between text-sm">
                  <span class="text-gray-500">Auto Sync</span>
                  <span class="text-gray-900">
                    <%= config.auto_sync_enabled? ? 'Enabled' : 'Disabled' %>
                  </span>
                </div>
                <div class="flex justify-between text-sm">
                  <span class="text-gray-500">Posting</span>
                  <span class="text-gray-900">
                    <%= config.posting_enabled? ? 'Enabled' : 'Disabled' %>
                  </span>
                </div>
              </div>

              <!-- Action Buttons -->
              <div class="flex space-x-2">
                <%= link_to platform_configuration_path(platform), 
                    class: "flex-1 inline-flex items-center justify-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50" do %>
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                  </svg>
                  Manage
                <% end %>
                
                <button type="button" 
                        class="px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                        onclick="testConnection('<%= platform %>')">
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                </button>
              </div>
            <% else %>
              <!-- Not Connected -->
              <div class="text-center py-4">
                <p class="text-gray-500 text-sm mb-4">Connect your <%= platform.titleize %> account to start publishing content automatically.</p>
                
                <%= link_to new_platform_configuration_path(platform: platform), 
                    class: "inline-flex items-center px-4 py-2 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors" do %>
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                  </svg>
                  Connect <%= platform.titleize %>
                <% end %>
              </div>
            <% end %>
          </div>
        </div>
      <% end %>
    </div>

    <!-- Help Section -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-8 mt-8">
      <div class="flex items-start space-x-4">
        <div class="flex-shrink-0">
          <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
        <div>
          <h3 class="text-lg font-medium text-gray-900 mb-2">Need Help Connecting Platforms?</h3>
          <p class="text-gray-600 mb-4">
            Each platform requires you to create a developer application to obtain API credentials. 
            We'll guide you through the process for each platform.
          </p>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
            <div>
              <strong>What you'll need:</strong>
              <ul class="list-disc list-inside mt-1 space-y-1">
                <li>Developer account on each platform</li>
                <li>API credentials (Client ID & Secret)</li>
                <li>Webhook URL configuration</li>
              </ul>
            </div>
            <div>
              <strong>What you'll get:</strong>
              <ul class="list-disc list-inside mt-1 space-y-1">
                <li>Automated content publishing</li>
                <li>Real-time analytics sync</li>
                <li>Centralized social management</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- JavaScript for Connection Testing -->
<script>
function testConnection(platform) {
  const button = event.target;
  const originalContent = button.innerHTML;
  
  // Show loading state
  button.innerHTML = '<svg class="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path></svg>';
  button.disabled = true;
  
  fetch(`/platform_configurations/${platform}/test_connection`, {
    method: 'GET',
    headers: {
      'Accept': 'application/json',
      'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
    }
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      // Show success briefly
      button.innerHTML = '<svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>';
      button.classList.add('text-green-600');
      
      setTimeout(() => {
        button.innerHTML = originalContent;
        button.classList.remove('text-green-600');
        button.disabled = false;
      }, 2000);
    } else {
      // Show error briefly
      button.innerHTML = '<svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>';
      button.classList.add('text-red-600');
      
      setTimeout(() => {
        button.innerHTML = originalContent;
        button.classList.remove('text-red-600');
        button.disabled = false;
      }, 2000);
      
      alert(data.message || 'Connection test failed');
    }
  })
  .catch(error => {
    console.error('Connection test error:', error);
    button.innerHTML = originalContent;
    button.disabled = false;
    alert('Failed to test connection. Please try again.');
  });
}
</script>
