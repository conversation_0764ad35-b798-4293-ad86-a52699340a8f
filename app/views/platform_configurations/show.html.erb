<%# Platform Configuration Show - Individual Platform Management %>
<div class="bg-gray-50 min-h-screen">
  <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    
    <!-- Header -->
    <div class="bg-white rounded-xl shadow-sm p-8 mb-8">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <%= link_to platform_configurations_path, 
              class: "flex items-center text-gray-500 hover:text-gray-700" do %>
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
            Back to Platforms
          <% end %>
        </div>
        
        <div class="flex items-center space-x-3">
          <%= link_to refresh_token_platform_configuration_path(@platform_configuration.platform_name), 
              method: :post,
              class: "#{platform_button_classes(@platform_configuration.platform_name, 'inline-flex items-center px-3 py-2 shadow-sm text-sm font-medium rounded-md transition-colors duration-200')}" do %>
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
            Refresh Token
          <% end %>
          
          <%= link_to platform_configuration_path(@platform_configuration.platform_name), 
              method: :delete,
              data: { 
                confirm: "Are you sure you want to disconnect #{@platform_configuration.platform_name.titleize}? This will stop all automated posting to this platform." 
              },
              class: "inline-flex items-center px-3 py-2 border border-red-300 shadow-sm text-sm font-medium rounded-md text-red-700 bg-white hover:bg-red-50 transition-colors duration-200" do %>
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
            Disconnect
          <% end %>
        </div>
      </div>
      
      <div class="mt-6">
        <div class="flex items-center space-x-4">
          <%= render "shared/platform_icons/#{@platform_configuration.platform_name}", 
              css_class: "w-8 h-8", 
              container_class: "platform-icon-container #{platform_background_classes(@platform_configuration.platform_name)}" %>
          <div>
            <h1 class="text-3xl font-bold text-gray-900"><%= @platform_configuration.platform_name.titleize %></h1>
            <p class="text-gray-600">
              Connected as <%= @platform_configuration.connected_account_name || 'Unknown' %>
            </p>
          </div>
        </div>
      </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- Main Content -->
      <div class="lg:col-span-2 space-y-8">
        
        <!-- Connection Status -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <div class="flex items-center mb-6">
            <div class="w-1 h-6 <%= platform_background_classes(@platform_configuration.platform_name, 'rounded-full mr-3') %>"></div>
            <h2 class="text-xl font-semibold text-gray-900">Connection Status</h2>
          </div>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-4">
              <div class="flex justify-between">
                <span class="text-sm text-gray-500">Status</span>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  Connected
                </span>
              </div>
              
              <div class="flex justify-between">
                <span class="text-sm text-gray-500">Account ID</span>
                <span class="text-sm text-gray-900 font-mono">
                  <%= @platform_configuration.connected_account_id || 'N/A' %>
                </span>
              </div>
              
              <div class="flex justify-between">
                <span class="text-sm text-gray-500">Last Sync</span>
                <span class="text-sm text-gray-900">
                  <%= @platform_configuration.last_sync_at ? time_ago_in_words(@platform_configuration.last_sync_at) + ' ago' : 'Never' %>
                </span>
              </div>
            </div>
            
            <div class="space-y-4">
              <div class="flex justify-between">
                <span class="text-sm text-gray-500">Auto Sync</span>
                <span class="text-sm text-gray-900">
                  <%= @platform_configuration.auto_sync_enabled? ? 'Enabled' : 'Disabled' %>
                </span>
              </div>
              
              <div class="flex justify-between">
                <span class="text-sm text-gray-500">Posting</span>
                <span class="text-sm text-gray-900">
                  <%= @platform_configuration.posting_enabled? ? 'Enabled' : 'Disabled' %>
                </span>
              </div>
              
              <div class="flex justify-between">
                <span class="text-sm text-gray-500">Analytics</span>
                <span class="text-sm text-gray-900">
                  <%= @platform_configuration.analytics_enabled? ? 'Enabled' : 'Disabled' %>
                </span>
              </div>
            </div>
          </div>
          
          <% if @oauth_token %>
            <div class="mt-6 pt-6 border-t border-gray-200">
              <h3 class="text-lg font-medium text-gray-900 mb-4">OAuth Token Information</h3>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div class="flex justify-between">
                  <span class="text-gray-500">Token Type</span>
                  <span class="text-gray-900"><%= @oauth_token.token_type %></span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-500">Expires</span>
                  <span class="text-gray-900">
                    <%= @oauth_token.expires_at ? @oauth_token.expires_at.strftime('%b %d, %Y') : 'Never' %>
                  </span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-500">Scopes</span>
                  <span class="text-gray-900"><%= @oauth_token.scope || 'N/A' %></span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-500">Created</span>
                  <span class="text-gray-900"><%= @oauth_token.created_at.strftime('%b %d, %Y') %></span>
                </div>
              </div>
            </div>
          <% end %>
        </div>

        <!-- Recent Posts -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <div class="flex items-center mb-6">
            <div class="w-1 h-6 <%= platform_background_classes(@platform_configuration.platform_name, 'rounded-full mr-3') %>"></div>
            <h2 class="text-xl font-semibold text-gray-900">Recent Posts</h2>
          </div>
          
          <% if @recent_posts.any? %>
            <div class="space-y-4">
              <% @recent_posts.each do |post| %>
                <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200">
                  <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0">
                      <%= render "shared/platform_icons/#{@platform_configuration.platform_name}", 
                          css_class: "w-6 h-6", 
                          container_class: "platform-icon-container #{platform_background_classes(@platform_configuration.platform_name)} !p-2" %>
                    </div>
                    <div class="flex-1">
                      <p class="text-gray-900 mb-2"><%= truncate(post[:content], length: 150) %></p>
                      <div class="flex items-center justify-between text-sm text-gray-500">
                        <span><%= post[:created_at] %></span>
                        <div class="flex items-center space-x-4">
                          <span class="flex items-center">
                            <span class="mr-1">👍</span>
                            <%= post[:likes] || 0 %>
                          </span>
                          <span class="flex items-center">
                            <span class="mr-1">💬</span>
                            <%= post[:comments] || 0 %>
                          </span>
                          <span class="flex items-center">
                            <span class="mr-1">🔄</span>
                            <%= post[:shares] || 0 %>
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              <% end %>
            </div>
          <% else %>
            <div class="text-center py-8">
              <%= render "shared/platform_icons/#{@platform_configuration.platform_name}", 
                  css_class: "w-8 h-8", 
                  container_class: "platform-icon-container #{platform_background_classes(@platform_configuration.platform_name)} mx-auto mb-4 opacity-50" %>
              <p class="text-gray-500">No recent posts found</p>
            </div>
          <% end %>
        </div>
      </div>

      <!-- Sidebar -->
      <div class="space-y-8">
        
        <!-- Account Metrics -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <div class="flex items-center mb-4">
            <div class="w-1 h-6 <%= platform_background_classes(@platform_configuration.platform_name, 'rounded-full mr-3') %>"></div>
            <h3 class="text-lg font-semibold text-gray-900">Account Metrics</h3>
          </div>
          
          <% if @account_metrics.any? %>
            <div class="space-y-4">
              <% @account_metrics.each do |metric, value| %>
                <div class="flex justify-between">
                  <span class="text-sm text-gray-500"><%= metric.humanize %></span>
                  <span class="text-sm font-medium text-gray-900">
                    <%= number_with_delimiter(value) %>
                  </span>
                </div>
              <% end %>
            </div>
          <% else %>
            <p class="text-gray-500 text-sm">No metrics available</p>
          <% end %>
        </div>

        <!-- Platform Settings -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <div class="flex items-center mb-4">
            <div class="w-1 h-6 <%= platform_background_classes(@platform_configuration.platform_name, 'rounded-full mr-3') %>"></div>
            <h3 class="text-lg font-semibold text-gray-900">Platform Settings</h3>
          </div>
          
          <%= form_with model: @platform_configuration, 
              url: platform_configuration_path(@platform_configuration.platform_name), 
              method: :patch, local: true do |form| %>
            
            <div class="space-y-4">
              <div class="flex items-center justify-between">
                <%= form.label :auto_sync_enabled, "Auto Sync", class: "text-sm font-medium text-gray-700" %>
                <%= form.check_box :auto_sync_enabled, 
                    class: "h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" %>
              </div>
              
              <div class="flex items-center justify-between">
                <%= form.label :posting_enabled, "Publishing", class: "text-sm font-medium text-gray-700" %>
                <%= form.check_box :posting_enabled, 
                    class: "h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" %>
              </div>
              
              <div class="flex items-center justify-between">
                <%= form.label :analytics_enabled, "Analytics", class: "text-sm font-medium text-gray-700" %>
                <%= form.check_box :analytics_enabled, 
                    class: "h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" %>
              </div>
            </div>
            
            <div class="mt-6">
              <%= form.submit "Update Settings", 
                  class: "#{platform_button_classes(@platform_configuration.platform_name, 'w-full inline-flex justify-center items-center px-4 py-2 text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200')}" %>
            </div>
          <% end %>
        </div>

        <!-- Connection Health -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <div class="flex items-center mb-4">
            <div class="w-1 h-6 <%= platform_background_classes(@platform_configuration.platform_name, 'rounded-full mr-3') %>"></div>
            <h3 class="text-lg font-semibold text-gray-900">Connection Health</h3>
          </div>
          
          <button type="button" 
                  id="connectionTestBtn"
                  class="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            Test Connection
          </button>
          
          <div id="connectionResult" class="mt-4 hidden">
            <!-- Results will be populated by JavaScript -->
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- JavaScript for Connection Testing -->
<script>
document.getElementById('connectionTestBtn').addEventListener('click', function() {
  const button = this;
  const resultDiv = document.getElementById('connectionResult');
  const originalContent = button.innerHTML;
  
  // Show loading state
  button.innerHTML = '<svg class="w-4 h-4 mr-2 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path></svg>Testing...';
  button.disabled = true;
  resultDiv.classList.add('hidden');
  
  fetch('<%= test_connection_platform_configuration_path(@platform_configuration.platform_name) %>', {
    method: 'GET',
    headers: {
      'Accept': 'application/json',
      'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
    }
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      resultDiv.innerHTML = `
        <div class="p-3 bg-green-50 border border-green-200 rounded-md">
          <div class="flex items-center">
            <svg class="w-5 h-5 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span class="text-sm text-green-800">Connection successful!</span>
          </div>
          ${data.account_info ? `<p class="text-xs text-green-700 mt-2">${data.account_info}</p>` : ''}
        </div>
      `;
    } else {
      resultDiv.innerHTML = `
        <div class="p-3 bg-red-50 border border-red-200 rounded-md">
          <div class="flex items-center">
            <svg class="w-5 h-5 text-red-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
            <span class="text-sm text-red-800">Connection failed</span>
          </div>
          <p class="text-xs text-red-700 mt-2">${data.message}</p>
        </div>
      `;
    }
    
    resultDiv.classList.remove('hidden');
  })
  .catch(error => {
    console.error('Connection test error:', error);
    resultDiv.innerHTML = `
      <div class="p-3 bg-red-50 border border-red-200 rounded-md">
        <div class="flex items-center">
          <svg class="w-5 h-5 text-red-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
          <span class="text-sm text-red-800">Connection test failed</span>
        </div>
        <p class="text-xs text-red-700 mt-2">Please try again later.</p>
      </div>
    `;
    resultDiv.classList.remove('hidden');
  })
  .finally(() => {
    button.innerHTML = originalContent;
    button.disabled = false;
  });
});
</script>
