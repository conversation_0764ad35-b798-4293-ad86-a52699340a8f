<% content_for :title, "Generate Email Content" %>

<div class="container mt-4">
  <div class="row">
    <div class="col-lg-12">
      <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Generate Email Content</h1>
        <%= link_to "Back to Campaign", campaign_path(@campaign), class: "btn btn-outline-secondary" %>
      </div>
      
      <div class="card mb-4">
        <div class="card-header">
          <h5 class="card-title mb-0">Campaign Information</h5>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <dl class="row">
                <dt class="col-sm-4">Campaign Name:</dt>
                <dd class="col-sm-8"><%= @campaign.name %></dd>
                
                <dt class="col-sm-4">Type:</dt>
                <dd class="col-sm-8"><%= @campaign.type || "Standard" %></dd>
                
                <dt class="col-sm-4">Status:</dt>
                <dd class="col-sm-8">
                  <%= render 'dashboard/status_badge', status: @campaign.status %>
                </dd>
              </dl>
            </div>
            <div class="col-md-6">
              <dl class="row">
                <dt class="col-sm-4">Audience:</dt>
                <dd class="col-sm-8">
                  <% if @campaign.respond_to?(:audience) && @campaign.audience.present? %>
                    <%= link_to @campaign.audience.name, audience_path(@campaign.audience) %>
                    <small class="text-muted">(<%= @campaign.audience.subscriber_count || 0 %> subscribers)</small>
                  <% else %>
                    <span class="text-muted">Not specified</span>
                  <% end %>
                </dd>
                
                <dt class="col-sm-4">Created:</dt>
                <dd class="col-sm-8"><%= @campaign.created_at.strftime("%B %d, %Y") %></dd>
                
                <dt class="col-sm-4">Target Date:</dt>
                <dd class="col-sm-8">
                  <% if @campaign.respond_to?(:target_completion_date) && @campaign.target_completion_date.present? %>
                    <%= @campaign.target_completion_date.strftime("%B %d, %Y") %>
                  <% else %>
                    <span class="text-muted">Not specified</span>
                  <% end %>
                </dd>
              </dl>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Audience Insights Modal Trigger -->
      <% if @campaign.respond_to?(:audience) && @campaign.audience.present? %>
        <div class="text-end mb-4">
          <button type="button" class="btn btn-outline-info me-2" data-controller="audience-insights" data-action="audience-insights#show" data-audience-insights-audience-id-value="<%= @campaign.audience.id %>">
            <i class="bi bi-graph-up"></i> View Audience Insights
          </button>
          
          <button type="button" class="btn btn-outline-primary" data-controller="template-library" data-action="template-library#show">
            <i class="bi bi-collection"></i> Browse Template Library
          </button>
        </div>
      <% end %>
      
      <!-- Content Generation Panel -->
      <%= render 'shared/content_generation_panel', 
        campaign: @campaign,
        endpoint: generate_email_content_path,
        form_id: 'email_generation_form',
        include_version_history: true,
        brand_voice_options: ['professional', 'casual', 'authoritative', 'playful', 'technical', 'inspirational'],
        email_type_options: ['promotional', 'newsletter', 'welcome', 'announcement', 'follow-up', 'abandoned_cart', 're-engagement'],
        custom_prompt_placeholder: 'Enter key message or specific instructions for this email content...'
      %>
      
      <!-- AI Budget Usage Card -->
      <div class="card mt-4">
        <div class="card-header bg-light">
          <h5 class="card-title mb-0">AI Budget Usage</h5>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <h6>Current Month Usage</h6>
              <div class="progress" style="height: 20px;">
                <% 
                  # This would be replaced with actual data in a real implementation
                  usage_percent = 65 
                  usage_class = usage_percent > 80 ? "bg-danger" : (usage_percent > 60 ? "bg-warning" : "bg-success")
                %>
                <div class="progress-bar <%= usage_class %>" role="progressbar" style="width: <%= usage_percent %>%;" aria-valuenow="<%= usage_percent %>" aria-valuemin="0" aria-valuemax="100"><%= usage_percent %>%</div>
              </div>
              <small class="text-muted mt-2 d-block">Estimated cost per generation: $0.05 - $0.15</small>
            </div>
            <div class="col-md-6">
              <h6>Generation Statistics</h6>
              <ul class="list-unstyled">
                <li><i class="bi bi-check-circle-fill text-success"></i> Successful generations: <strong>24</strong></li>
                <li><i class="bi bi-x-circle-fill text-danger"></i> Failed generations: <strong>3</strong></li>
                <li><i class="bi bi-lightning-fill text-warning"></i> Average generation time: <strong>8.2s</strong></li>
              </ul>
            </div>
          </div>
        </div>
        <div class="card-footer text-muted">
          <small>Budget refreshes on the 1st of each month. <a href="#" data-bs-toggle="modal" data-bs-target="#budgetInfoModal">Learn more</a></small>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Budget Info Modal -->
<div class="modal fade" id="budgetInfoModal" tabindex="-1" aria-labelledby="budgetInfoModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="budgetInfoModalLabel">AI Budget Information</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <h6>How AI Budget Works</h6>
        <p>Your subscription includes a monthly AI generation budget that refreshes on the 1st of each month. This budget controls how many AI operations you can perform.</p>
        
        <h6>Cost Factors</h6>
        <ul>
          <li><strong>Model Complexity:</strong> More advanced models cost more per generation</li>
          <li><strong>Content Length:</strong> Longer content requires more tokens and costs more</li>
          <li><strong>Revision Rounds:</strong> Each revision counts as a separate generation</li>
        </ul>
        
        <h6>Optimization Tips</h6>
        <ul>
          <li>Be specific in your prompts to reduce the need for revisions</li>
          <li>Use the template library for common content types</li>
          <li>Consider using simpler models for draft content</li>
        </ul>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
        <button type="button" class="btn btn-primary">Upgrade Plan</button>
      </div>
    </div>
  </div>
</div>

<!-- Render the audience insights modal partial -->
<%= render 'shared/audience_insights_modal' %>

<!-- Render the template library modal partial -->
<%= render 'shared/template_library_modal' %>
