<% content_for :title, "Email Content - #{@campaign.name}" %>

<div class="min-h-screen bg-gray-50">
  <!-- Header -->
  <div class="bg-white shadow-sm border-b">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex items-center justify-between h-16">
        <div class="flex items-center space-x-4">
          <%= link_to campaign_path(@campaign), 
              class: "text-gray-500 hover:text-gray-700 transition-colors",
              data: { turbo_method: :get } do %>
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
            </svg>
          <% end %>
          <h1 class="text-xl font-semibold text-gray-900">Email Content</h1>
          <span class="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
            <%= @campaign.campaign_type.humanize %>
          </span>
        </div>
        
        <div class="flex items-center space-x-3">
          <% if @email_campaign.persisted? %>
            <%= link_to "Edit Content", edit_campaign_email_content_path(@campaign),
                class: "btn-primary" %>
          <% else %>
            <%= link_to "Create Content", new_campaign_email_content_path(@campaign),
                class: "btn-primary" %>
          <% end %>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      
      <!-- Email Content Form/Display -->
      <div class="lg:col-span-2">
        <div class="bg-white rounded-lg shadow-sm border">
          <div class="p-6">
            <div class="flex items-center justify-between mb-6">
              <h2 class="text-lg font-medium text-gray-900">Email Campaign Content</h2>
              <% if @email_campaign.persisted? %>
                <span class="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                  Content Ready
                </span>
              <% else %>
                <span class="px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded-full">
                  Setup Required
                </span>
              <% end %>
            </div>

            <% if @email_campaign.persisted? %>
              <!-- Display Email Content -->
              <div class="space-y-6">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Subject Line</label>
                  <div class="p-3 bg-gray-50 rounded-md border">
                    <%= @email_campaign.subject_line %>
                  </div>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Preview Text</label>
                  <div class="p-3 bg-gray-50 rounded-md border">
                    <%= @email_campaign.preview_text.presence || "No preview text set" %>
                  </div>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">From Name</label>
                  <div class="p-3 bg-gray-50 rounded-md border">
                    <%= @email_campaign.from_name %>
                  </div>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">From Email</label>
                  <div class="p-3 bg-gray-50 rounded-md border">
                    <%= @email_campaign.from_email %>
                  </div>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Email Content</label>
                  <div class="p-4 bg-gray-50 rounded-md border min-h-[200px]">
                    <%= simple_format(@email_campaign.content) %>
                  </div>
                </div>
              </div>
            <% else %>
              <!-- Setup Required Message -->
              <div class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No email content yet</h3>
                <p class="mt-1 text-sm text-gray-500">Get started by creating your email campaign content.</p>
                <div class="mt-6">
                  <%= link_to "Create Email Content", new_campaign_email_content_path(@campaign),
                      class: "btn-primary" %>
                </div>
              </div>
            <% end %>
          </div>
        </div>
      </div>

      <!-- Sidebar -->
      <div class="space-y-6">
        <!-- AI Content Generation -->
        <div class="bg-white rounded-lg shadow-sm border">
          <div class="p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">AI Content Generation</h3>
            
            <%= form_with url: generate_ai_content_campaign_email_content_path(@campaign),
                method: :post, local: true, class: "space-y-4" do |form| %>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Brand Voice</label>
                <%= form.select :brand_voice, 
                    options_for_select([
                      ['Professional', 'professional'],
                      ['Friendly', 'friendly'],
                      ['Casual', 'casual'],
                      ['Authoritative', 'authoritative'],
                      ['Playful', 'playful']
                    ], 'professional'),
                    {}, { class: "form-select" } %>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Email Type</label>
                <%= form.select :email_type,
                    options_for_select([
                      ['Promotional', 'promotional'],
                      ['Newsletter', 'newsletter'],
                      ['Welcome', 'welcome'],
                      ['Follow-up', 'followup'],
                      ['Announcement', 'announcement']
                    ], 'promotional'),
                    {}, { class: "form-select" } %>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Key Message</label>
                <%= form.text_area :key_message, 
                    placeholder: "What's the main message you want to convey?",
                    class: "form-textarea", rows: 3 %>
              </div>

              <%= form.submit "Generate AI Content", 
                  class: "w-full btn-primary",
                  data: { turbo_confirm: "This will replace existing content. Continue?" } %>
            <% end %>
          </div>
        </div>

        <!-- Campaign Info -->
        <div class="bg-white rounded-lg shadow-sm border">
          <div class="p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Campaign Details</h3>
            <dl class="space-y-3">
              <div>
                <dt class="text-sm font-medium text-gray-500">Campaign Name</dt>
                <dd class="text-sm text-gray-900"><%= @campaign.name %></dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500">Target Audience</dt>
                <dd class="text-sm text-gray-900"><%= @campaign.target_audience %></dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500">Budget</dt>
                <dd class="text-sm text-gray-900"><%= number_to_currency(@campaign.budget_cents / 100.0) %></dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500">Status</dt>
                <dd>
                  <span class="px-2 py-1 text-xs font-medium rounded-full
                    <%= case @campaign.status
                        when 'active' then 'bg-green-100 text-green-800'
                        when 'draft' then 'bg-gray-100 text-gray-800'
                        when 'paused' then 'bg-yellow-100 text-yellow-800'
                        when 'completed' then 'bg-blue-100 text-blue-800'
                        else 'bg-gray-100 text-gray-800'
                        end %>">
                    <%= @campaign.status.humanize %>
                  </span>
                </dd>
              </div>
            </dl>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
