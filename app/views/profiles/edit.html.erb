<% content_for :title, "Edit Profile" %>

<div class="max-w-4xl mx-auto space-y-8">
  <!-- <PERSON> Header -->
  <div class="bg-white rounded-xl shadow-sm p-8">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold text-gray-900 mb-2">Edit Profile</h1>
        <p class="text-gray-600">Update your personal information</p>
      </div>
      <%= link_to profile_path, 
          class: "inline-flex items-center px-4 py-2 bg-gray-100 text-gray-700 text-sm font-medium rounded-lg hover:bg-gray-200 transition-colors" do %>
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
        </svg>
        Back to Profile
      <% end %>
    </div>
  </div>

  <!-- Edit Form -->
  <div class="bg-white rounded-xl shadow-sm">
    <div class="p-6 border-b border-gray-200">
      <h2 class="text-xl font-semibold text-gray-900">Personal Information</h2>
      <p class="text-gray-600 text-sm">Update your basic profile information</p>
    </div>
    
    <div class="p-6">
      <%= form_with model: @user, url: profile_path, method: :patch, local: true, class: "space-y-6" do |form| %>
        
        <!-- Avatar Section -->
        <div class="flex items-start space-x-6">
          <div class="flex-shrink-0">
            <div class="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white text-2xl font-bold">
              <%= @user.first_name&.first || "U" %>
            </div>
            <button type="button" class="mt-2 text-sm text-blue-600 hover:text-blue-700 font-medium">
              Change Avatar
            </button>
          </div>
          
          <div class="flex-1">
            <h3 class="text-lg font-medium text-gray-900 mb-2">Profile Picture</h3>
            <p class="text-sm text-gray-600 mb-4">Upload a new avatar or change your existing one. Recommended size: 400x400px.</p>
            
            <!-- File Upload (placeholder for now) -->
            <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors">
              <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
              </svg>
              <div class="mt-4">
                <label for="avatar-upload" class="cursor-pointer">
                  <span class="mt-2 block text-sm font-medium text-gray-900">
                    Click to upload or drag and drop
                  </span>
                  <span class="mt-1 block text-sm text-gray-500">
                    PNG, JPG, GIF up to 10MB
                  </span>
                  <input id="avatar-upload" name="avatar" type="file" class="sr-only" accept="image/*">
                </label>
              </div>
            </div>
          </div>
        </div>

        <!-- Form Fields -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <%= form.label :first_name, class: "block text-sm font-medium text-gray-700 mb-2" %>
            <%= form.text_field :first_name, 
                class: "w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500",
                placeholder: "Enter your first name" %>
            <% if @user.errors[:first_name].any? %>
              <p class="mt-1 text-sm text-red-600"><%= @user.errors[:first_name].first %></p>
            <% end %>
          </div>
          
          <div>
            <%= form.label :last_name, class: "block text-sm font-medium text-gray-700 mb-2" %>
            <%= form.text_field :last_name, 
                class: "w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500",
                placeholder: "Enter your last name" %>
            <% if @user.errors[:last_name].any? %>
              <p class="mt-1 text-sm text-red-600"><%= @user.errors[:last_name].first %></p>
            <% end %>
          </div>
        </div>
        
        <div>
          <%= form.label :email, class: "block text-sm font-medium text-gray-700 mb-2" %>
          <%= form.email_field :email, 
              class: "w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500",
              placeholder: "Enter your email address" %>
          <% if @user.errors[:email].any? %>
            <p class="mt-1 text-sm text-red-600"><%= @user.errors[:email].first %></p>
          <% end %>
          <p class="mt-1 text-sm text-gray-500">
            We'll send a confirmation email if you change your email address.
          </p>
        </div>

        <!-- Account Information (Read-only) -->
        <div class="border-t border-gray-200 pt-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Account Information</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Role</label>
              <div class="bg-gray-50 rounded-lg p-3">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 capitalize">
                  <%= @user.role %>
                </span>
              </div>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Member Since</label>
              <div class="bg-gray-50 rounded-lg p-3">
                <p class="text-gray-900"><%= @user.created_at.strftime("%B %d, %Y") %></p>
              </div>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Organization</label>
              <div class="bg-gray-50 rounded-lg p-3">
                <p class="text-gray-900"><%= @user.tenant.name if @user.tenant %></p>
              </div>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Account Status</label>
              <div class="bg-gray-50 rounded-lg p-3">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                  </svg>
                  Active
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="flex items-center justify-between pt-6 border-t border-gray-200">
          <div class="text-sm text-gray-500">
            <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
            </svg>
            Your information is secure and encrypted
          </div>
          
          <div class="flex items-center space-x-4">
            <%= link_to profile_path, 
                class: "px-6 py-3 border border-gray-300 text-gray-700 font-medium rounded-lg hover:bg-gray-50 transition-colors" do %>
              Cancel
            <% end %>
            
            <%= form.submit "Save Changes", 
                class: "px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors" %>
          </div>
        </div>
      <% end %>
    </div>
  </div>

  <!-- Additional Information -->
  <div class="bg-blue-50 border border-blue-200 rounded-xl p-6">
    <div class="flex items-start space-x-3">
      <svg class="w-6 h-6 text-blue-600 flex-shrink-0 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
      </svg>
      <div>
        <h3 class="text-lg font-medium text-blue-900 mb-2">Important Notes</h3>
        <ul class="text-sm text-blue-800 space-y-1">
          <li>• Changes to your email address will require email verification</li>
          <li>• Your role and organization can only be changed by an administrator</li>
          <li>• Profile changes may take a few minutes to appear across all services</li>
          <li>• For security settings and password changes, visit Account Settings</li>
        </ul>
      </div>
    </div>
  </div>
</div>

<script>
  // Simple file upload preview
  document.getElementById('avatar-upload').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = function(e) {
        // In a real implementation, you'd update the avatar preview here
        console.log('File selected:', file.name);
      };
      reader.readAsDataURL(file);
    }
  });
</script>
