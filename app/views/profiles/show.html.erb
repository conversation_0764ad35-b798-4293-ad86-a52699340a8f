<% content_for :title, "Profile Settings" %>

<div class="max-w-7xl mx-auto space-y-8">
  <!-- Page Header -->
  <div class="bg-white rounded-xl shadow-sm p-8">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold text-gray-900 mb-2">Profile Settings</h1>
        <p class="text-gray-600">Manage your personal information and preferences</p>
      </div>
      <div class="flex items-center space-x-4">
        <!-- Profile Completion -->
        <div class="text-center">
          <div class="text-2xl font-bold text-blue-600"><%= @profile_completion %>%</div>
          <div class="text-sm text-gray-500">Complete</div>
        </div>
        <%= render 'dashboard/progress_bar',
            value: @profile_completion,
            max_value: 100,
            color: 'primary',
            size: 'md',
            show_percentage: false %>
      </div>
    </div>
  </div>

  <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
    <!-- Main Content -->
    <div class="lg:col-span-2 space-y-8">
      
      <!-- Personal Information -->
      <div class="bg-white rounded-xl shadow-sm">
        <div class="p-6 border-b border-gray-200">
          <div class="flex items-center justify-between">
            <div>
              <h2 class="text-xl font-semibold text-gray-900">Personal Information</h2>
              <p class="text-gray-600 text-sm">Update your basic profile information</p>
            </div>
            <%= link_to edit_profile_path, 
                class: "inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors" do %>
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
              </svg>
              Edit Profile
            <% end %>
          </div>
        </div>
        
        <div class="p-6">
          <div class="flex items-start space-x-6">
            <!-- Avatar -->
            <div class="flex-shrink-0">
              <div class="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white text-2xl font-bold">
                <%= @user.first_name&.first || "U" %>
              </div>
              <button class="mt-2 text-sm text-blue-600 hover:text-blue-700 font-medium">
                Change Avatar
              </button>
            </div>
            
            <!-- User Info -->
            <div class="flex-1 grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">First Name</label>
                <div class="bg-gray-50 rounded-lg p-3">
                  <p class="text-gray-900"><%= @user.first_name %></p>
                </div>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Last Name</label>
                <div class="bg-gray-50 rounded-lg p-3">
                  <p class="text-gray-900"><%= @user.last_name %></p>
                </div>
              </div>
              
              <div class="md:col-span-2">
                <label class="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
                <div class="bg-gray-50 rounded-lg p-3">
                  <p class="text-gray-900"><%= @user.email %></p>
                </div>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Role</label>
                <div class="bg-gray-50 rounded-lg p-3">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 capitalize">
                    <%= @user.role %>
                  </span>
                </div>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Member Since</label>
                <div class="bg-gray-50 rounded-lg p-3">
                  <p class="text-gray-900"><%= @user.created_at.strftime("%B %Y") %></p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Preferences -->
      <div class="bg-white rounded-xl shadow-sm">
        <div class="p-6 border-b border-gray-200">
          <h2 class="text-xl font-semibold text-gray-900">Preferences</h2>
          <p class="text-gray-600 text-sm">Customize your experience</p>
        </div>
        
        <div class="p-6">
          <%= form_with model: @user_preferences, url: update_preferences_profile_path, method: :patch, local: true, class: "space-y-6" do |form| %>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <%= form.label :timezone, class: "block text-sm font-medium text-gray-700 mb-2" %>
                <%= form.select :timezone, 
                    options_for_select(UserPreference.available_timezones, @user_preferences.timezone),
                    { prompt: "Select timezone" },
                    { class: "w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" } %>
              </div>
              
              <div>
                <%= form.label :language, class: "block text-sm font-medium text-gray-700 mb-2" %>
                <%= form.select :language,
                    options_for_select(UserPreference.available_languages, @user_preferences.language),
                    { prompt: "Select language" },
                    { class: "w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" } %>
              </div>
              
              <div>
                <%= form.label :date_format, class: "block text-sm font-medium text-gray-700 mb-2" %>
                <%= form.select :date_format,
                    options_for_select(UserPreference.available_date_formats, @user_preferences.date_format),
                    { prompt: "Select date format" },
                    { class: "w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" } %>
              </div>
              
              <div>
                <%= form.label :theme, class: "block text-sm font-medium text-gray-700 mb-2" %>
                <%= form.select :theme,
                    options_for_select(UserPreference.available_themes, @user_preferences.theme),
                    { prompt: "Select theme" },
                    { class: "w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" } %>
              </div>
            </div>
            
            <!-- Notification Preferences -->
            <div class="border-t border-gray-200 pt-6">
              <h3 class="text-lg font-medium text-gray-900 mb-4">Notification Preferences</h3>
              <div class="space-y-4">
                <div class="flex items-center justify-between">
                  <div>
                    <label class="text-sm font-medium text-gray-700">Email Notifications</label>
                    <p class="text-sm text-gray-500">Receive notifications via email</p>
                  </div>
                  <%= form.check_box :email_notifications, class: "rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500" %>
                </div>
                
                <div class="flex items-center justify-between">
                  <div>
                    <label class="text-sm font-medium text-gray-700">Push Notifications</label>
                    <p class="text-sm text-gray-500">Receive push notifications in your browser</p>
                  </div>
                  <%= form.check_box :push_notifications, class: "rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500" %>
                </div>
                
                <div class="flex items-center justify-between">
                  <div>
                    <label class="text-sm font-medium text-gray-700">Marketing Emails</label>
                    <p class="text-sm text-gray-500">Receive product updates and tips</p>
                  </div>
                  <%= form.check_box :marketing_emails, class: "rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500" %>
                </div>
              </div>
            </div>
            
            <div class="flex justify-end">
              <%= form.submit "Save Preferences", 
                  class: "inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors" %>
            </div>
          <% end %>
        </div>
      </div>
    </div>

    <!-- Sidebar -->
    <div class="space-y-8">
      
      <!-- Quick Stats -->
      <div class="bg-white rounded-xl shadow-sm p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Stats</h3>
        <div class="space-y-4">
          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-600">Campaigns Created</span>
            <span class="text-sm font-semibold text-gray-900"><%= @user.created_campaigns.count %></span>
          </div>
          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-600">Audiences Created</span>
            <span class="text-sm font-semibold text-gray-900"><%= @user.created_audiences.count %></span>
          </div>
          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-600">Platforms Connected</span>
            <span class="text-sm font-semibold text-gray-900"><%= @user.platform_configurations.active.count %></span>
          </div>
        </div>
      </div>

      <!-- Recent Activity -->
      <div class="bg-white rounded-xl shadow-sm p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
        <% if @recent_activity.any? %>
          <div class="space-y-3">
            <% @recent_activity.first(5).each do |activity| %>
              <div class="flex items-start space-x-3">
                <div class="flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                <div class="flex-1 min-w-0">
                  <p class="text-sm text-gray-900">
                    <%= activity[:action].humanize %> 
                    <%= activity[:type] %>: 
                    <span class="font-medium"><%= truncate(activity[:item].name, length: 30) %></span>
                  </p>
                  <p class="text-xs text-gray-500"><%= time_ago_in_words(activity[:timestamp]) %> ago</p>
                </div>
              </div>
            <% end %>
          </div>
        <% else %>
          <p class="text-sm text-gray-500">No recent activity</p>
        <% end %>
      </div>

      <!-- Quick Actions -->
      <div class="bg-white rounded-xl shadow-sm p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
        <div class="space-y-3">
          <%= link_to account_settings_path, 
              class: "flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors group" do %>
            <svg class="w-5 h-5 text-gray-400 group-hover:text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            </svg>
            <span class="text-sm font-medium text-gray-700 group-hover:text-gray-900">Account Settings</span>
          <% end %>
          
          <%= link_to help_index_path, 
              class: "flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors group" do %>
            <svg class="w-5 h-5 text-gray-400 group-hover:text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <span class="text-sm font-medium text-gray-700 group-hover:text-gray-900">Help & Support</span>
          <% end %>
          
          <%= link_to support_tickets_path, 
              class: "flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors group" do %>
            <svg class="w-5 h-5 text-gray-400 group-hover:text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"></path>
            </svg>
            <span class="text-sm font-medium text-gray-700 group-hover:text-gray-900">Support Tickets</span>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>
