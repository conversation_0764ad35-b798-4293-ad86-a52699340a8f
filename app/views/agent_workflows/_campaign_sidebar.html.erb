<!-- Campaign Info Sidebar -->
<div class="bg-white rounded-lg shadow">
  <div class="px-6 py-4 bg-gray-50">
    <h2 class="text-lg font-medium text-gray-900">Campaign Overview</h2>
  </div>
  
  <div class="p-6 space-y-6">
    <!-- Campaign Details -->
    <div>
      <h3 class="text-sm font-medium text-gray-900 mb-3">Details</h3>
      <dl class="space-y-2">
        <div class="flex justify-between">
          <dt class="text-sm text-gray-500">Name</dt>
          <dd class="text-sm font-medium text-gray-900"><%= @campaign.name %></dd>
        </div>
        <div class="flex justify-between">
          <dt class="text-sm text-gray-500">Type</dt>
          <dd class="text-sm font-medium text-gray-900">
            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
              <%= @campaign.campaign_type.humanize %>
            </span>
          </dd>
        </div>
        <div class="flex justify-between">
          <dt class="text-sm text-gray-500">Status</dt>
          <dd class="text-sm font-medium text-gray-900">
            <%= render 'campaigns/status_badge', campaign: @campaign %>
          </dd>
        </div>
        <div class="flex justify-between">
          <dt class="text-sm text-gray-500">Budget</dt>
          <dd class="text-sm font-medium text-gray-900">$<%= number_with_delimiter(@campaign.budget_in_dollars) %></dd>
        </div>
      </dl>
    </div>

    <!-- AI Content Status -->
    <div>
      <h3 class="text-sm font-medium text-gray-900 mb-3">AI Content Status</h3>
      <div class="space-y-3">
        
        <!-- Email Content -->
        <% if @campaign.email? || @campaign.multi_channel? %>
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <span class="text-lg mr-2">✉️</span>
              <span class="text-sm text-gray-600">Email</span>
            </div>
            <% if content_generation_status(@campaign, 'email') %>
              <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                ✅ Generated
              </span>
            <% else %>
              <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                ⏳ Pending
              </span>
            <% end %>
          </div>
        <% end %>

        <!-- Social Content -->
        <% if @campaign.social? || @campaign.multi_channel? %>
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <span class="text-lg mr-2">📱</span>
              <span class="text-sm text-gray-600">Social</span>
            </div>
            <% if content_generation_status(@campaign, 'social') %>
              <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                ✅ Generated
              </span>
            <% else %>
              <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                ⏳ Pending
              </span>
            <% end %>
          </div>
        <% end %>

        <!-- SEO Content -->
        <% if @campaign.seo? || @campaign.multi_channel? %>
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <span class="text-lg mr-2">🔍</span>
              <span class="text-sm text-gray-600">SEO</span>
            </div>
            <% if content_generation_status(@campaign, 'seo') %>
              <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                ✅ Generated
              </span>
            <% else %>
              <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                ⏳ Pending
              </span>
            <% end %>
          </div>
        <% end %>
      </div>
    </div>

    <!-- Performance Metrics -->
    <% if @campaign.active? || @campaign.completed? %>
      <div>
        <h3 class="text-sm font-medium text-gray-900 mb-3">Performance</h3>
        <dl class="space-y-2">
          <div class="flex justify-between">
            <dt class="text-sm text-gray-500">Impressions</dt>
            <dd class="text-sm font-medium text-gray-900"><%= number_with_delimiter(@campaign.total_impressions) %></dd>
          </div>
          <div class="flex justify-between">
            <dt class="text-sm text-gray-500">Clicks</dt>
            <dd class="text-sm font-medium text-gray-900"><%= number_with_delimiter(@campaign.total_clicks) %></dd>
          </div>
          <div class="flex justify-between">
            <dt class="text-sm text-gray-500">CTR</dt>
            <dd class="text-sm font-medium text-gray-900"><%= @campaign.click_through_rate %>%</dd>
          </div>
          <div class="flex justify-between">
            <dt class="text-sm text-gray-500">Conversions</dt>
            <dd class="text-sm font-medium text-gray-900"><%= number_with_delimiter(@campaign.total_conversions) %></dd>
          </div>
          <div class="flex justify-between">
            <dt class="text-sm text-gray-500">ROAS</dt>
            <dd class="text-sm font-medium text-gray-900"><%= @campaign.return_on_ad_spend %>%</dd>
          </div>
        </dl>
      </div>
    <% end %>

    <!-- Quick Actions -->
    <div>
      <h3 class="text-sm font-medium text-gray-900 mb-3">Quick Actions</h3>
      <div class="space-y-2">
        <%= link_to campaign_path(@campaign),
                    class: "block w-full text-center px-4 py-2 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50" do %>
          View Campaign
        <% end %>
        
        <% if @campaign.can_be_activated? %>
          <%= link_to activate_campaign_path(@campaign),
                      data: {
                        turbo_method: :patch,
                        turbo_confirm: "Activate this campaign?"
                      },
                      class: "block w-full text-center px-4 py-2 rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700" do %>
            Activate Campaign
          <% end %>
        <% end %>
      </div>
    </div>

    <!-- AI Insights -->
    <% if @campaign.ai_content_generated? %>
      <div>
        <h3 class="text-sm font-medium text-gray-900 mb-3">AI Insights</h3>
        <div class="bg-blue-50 shadow-sm rounded-md p-3">
          <div class="flex">
            <svg class="w-4 h-4 text-blue-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <div class="ml-2">
              <p class="text-sm text-blue-700">
                Your campaign has been optimized with AI-generated content. Consider reviewing and activating when ready.
              </p>
            </div>
          </div>
        </div>
      </div>
    <% end %>
  </div>
</div>
