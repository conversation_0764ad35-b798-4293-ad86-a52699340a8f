<div class="p-6" id="workflow-progress-<%= workflow.id %>">
  <div class="flex items-center justify-between">
    <div class="flex items-center">
      <div class="flex-shrink-0">
        <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
          <% case workflow.status %>
          <% when 'running' %>
            <svg class="w-5 h-5 text-blue-600 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
          <% when 'pending' %>
            <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          <% else %>
            <span class="text-blue-600 text-sm font-medium">🤖</span>
          <% end %>
        </div>
      </div>
      
      <div class="ml-4">
        <h3 class="text-sm font-medium text-gray-900">
          <%= workflow.workflow_type.humanize %>
        </h3>
        <p class="text-sm text-gray-500">
          <% if workflow.current_step.present? %>
            <%= workflow.current_step.humanize %>
          <% else %>
            <%= workflow.status.humanize %>
          <% end %>
        </p>
      </div>
    </div>
    
    <div class="flex items-center space-x-4">
      <!-- Progress Indicator -->
      <div class="flex items-center">
        <div class="w-32">
          <div class="bg-gray-200 rounded-full h-2">
            <div class="bg-blue-600 h-2 rounded-full transition-all duration-300" 
                 style="width: <%= workflow.progress_percent %>%"></div>
          </div>
        </div>
        <span class="ml-2 text-sm text-gray-500"><%= workflow.progress_percent %>%</span>
      </div>
      
      <!-- Actions -->
      <div class="flex items-center space-x-2">
        <%= link_to campaign_agent_workflow_path(@campaign, workflow),
                    class: "text-sm text-blue-600 hover:text-blue-500" do %>
          View Details
        <% end %>
        
        <% if workflow.running? || workflow.pending? %>
          <%= link_to cancel_campaign_agent_workflow_path(@campaign, workflow),
                      method: :patch,
                      data: { confirm: "Are you sure you want to cancel this workflow?", turbo: true },
                      class: "text-sm text-red-600 hover:text-red-500" do %>
            Cancel
          <% end %>
        <% end %>
      </div>
    </div>
  </div>
  
  <!-- Detailed Progress -->
  <% if workflow.total_steps > 0 %>
    <div class="mt-4">
      <div class="flex justify-between text-xs text-gray-500">
        <span>Step <%= workflow.results.keys.count + 1 %> of <%= workflow.total_steps %></span>
        <% if workflow.started_at && workflow.running? %>
          <span>Running for <%= distance_of_time_in_words(workflow.started_at, Time.current) %></span>
        <% end %>
      </div>
    </div>
  <% end %>
  
  <!-- Error Display -->
  <% if workflow.failed? && workflow.error_details.any? %>
    <div class="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
      <div class="flex">
        <svg class="w-4 h-4 text-red-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <div class="ml-2">
          <p class="text-sm text-red-700">
            <%= workflow.error_details['message'] %>
          </p>
        </div>
      </div>
    </div>
  <% end %>
</div>
