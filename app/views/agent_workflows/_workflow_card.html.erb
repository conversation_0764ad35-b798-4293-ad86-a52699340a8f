<div class="p-6" id="workflow-<%= workflow.id %>">
  <div class="flex items-center justify-between">
    <div class="flex items-center">
      <div class="flex-shrink-0">
        <div class="w-8 h-8 rounded-full flex items-center justify-center <%= workflow_status_classes(workflow) %>">
          <%= workflow_status_icon(workflow) %>
        </div>
      </div>
      
      <div class="ml-4">
        <h3 class="text-sm font-medium text-gray-900">
          <%= workflow.workflow_type.humanize %>
        </h3>
        <p class="text-sm text-gray-500">
          <%= workflow_description(workflow) %>
        </p>
      </div>
    </div>
    
    <div class="flex items-center space-x-4">
      <!-- Status Badge -->
      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= workflow_badge_classes(workflow) %>">
        <%= workflow.status.humanize %>
      </span>
      
      <!-- Duration -->
      <% if workflow.duration %>
        <span class="text-sm text-gray-500">
          <%= distance_of_time_in_words(0, workflow.duration) %>
        </span>
      <% end %>
      
      <!-- Actions -->
      <div class="flex items-center space-x-2">
        <%= link_to campaign_agent_workflow_path(@campaign, workflow),
                    class: "text-sm text-blue-600 hover:text-blue-500" do %>
          Details
        <% end %>
        
        <% if workflow.failed? %>
          <%= link_to retry_campaign_agent_workflow_path(@campaign, workflow),
                      method: :patch,
                      data: { confirm: "Retry this workflow?", turbo: true },
                      class: "text-sm text-green-600 hover:text-green-500" do %>
            Retry
          <% end %>
        <% end %>
      </div>
    </div>
  </div>
  
  <!-- Results Summary -->
  <% if workflow.completed? && workflow.results.any? %>
    <div class="mt-4">
      <div class="bg-green-50 shadow-sm rounded-md p-3">
        <div class="flex">
          <svg class="w-4 h-4 text-green-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          <div class="ml-2">
            <p class="text-sm text-green-700">
              <strong>Completed successfully!</strong> 
              Generated content for <%= workflow.results.keys.count %> components.
            </p>
          </div>
        </div>
      </div>
    </div>
  <% end %>
  
  <!-- Error Summary -->
  <% if workflow.failed? && workflow.error_details.any? %>
    <div class="mt-4">
      <div class="bg-red-50 shadow-sm rounded-md p-3">
        <div class="flex">
          <svg class="w-4 h-4 text-red-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          <div class="ml-2">
            <p class="text-sm text-red-700">
              <strong>Failed:</strong> <%= workflow.error_details['message']&.truncate(100) %>
            </p>
          </div>
        </div>
      </div>
    </div>
  <% end %>
  
  <!-- Timestamp -->
  <div class="mt-4 text-xs text-gray-500">
    <% if workflow.completed_at %>
      Completed <%= time_ago_in_words(workflow.completed_at) %> ago
    <% elsif workflow.started_at %>
      Started <%= time_ago_in_words(workflow.started_at) %> ago
    <% else %>
      Created <%= time_ago_in_words(workflow.created_at) %> ago
    <% end %>
  </div>
</div>
