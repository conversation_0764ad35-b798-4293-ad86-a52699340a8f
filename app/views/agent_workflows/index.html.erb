<% content_for :title, "AI Workflows - #{@campaign.name}" %>

<div class="min-h-screen bg-gray-50" data-controller="agent-workflows" 
     data-agent-workflows-campaign-id-value="<%= @campaign.id %>">
  
  <!-- Header Section -->
  <div class="bg-white shadow">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="py-6">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-2xl font-bold text-gray-900">
              🤖 AI Marketing Workflows
            </h1>
            <p class="mt-1 text-sm text-gray-500">
              Automate your marketing with AI-powered agents for <%= @campaign.name %>
            </p>
          </div>
          
          <%= link_to campaign_path(@campaign), 
                      class: "inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50" do %>
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Back to Campaign
          <% end %>
        </div>
      </div>
    </div>
  </div>

  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      
      <!-- AI Agent Controls -->
      <div class="lg:col-span-2">
        <div class="bg-white rounded-lg shadow">
          <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-medium text-gray-900">AI Agent Controls</h2>
            <p class="mt-1 text-sm text-gray-500">
              Generate and optimize content using specialized AI agents
            </p>
          </div>
          
          <div class="p-6">
            <%= render 'agent_controls' %>
          </div>
        </div>

        <!-- Active Workflows -->
        <% if @active_workflows.any? %>
          <div class="mt-8 bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
              <h2 class="text-lg font-medium text-gray-900">Active Workflows</h2>
            </div>
            
            <div class="divide-y divide-gray-200">
              <% @active_workflows.each do |workflow| %>
                <%= render 'workflow_progress', workflow: workflow %>
              <% end %>
            </div>
          </div>
        <% end %>

        <!-- Recent Workflows -->
        <div class="mt-8 bg-white rounded-lg shadow">
          <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-medium text-gray-900">Recent Workflows</h2>
          </div>
          
          <div class="divide-y divide-gray-200" id="recent-workflows">
            <% if @recent_workflows.any? %>
              <% @recent_workflows.each do |workflow| %>
                <%= render 'workflow_card', workflow: workflow %>
              <% end %>
            <% else %>
              <div class="p-6 text-center text-gray-500">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No workflows yet</h3>
                <p class="mt-1 text-sm text-gray-500">Get started by generating AI content for your campaign.</p>
              </div>
            <% end %>
          </div>
        </div>
      </div>

      <!-- Campaign Info Sidebar -->
      <div class="lg:col-span-1">
        <%= render 'campaign_sidebar' %>
      </div>
    </div>
  </div>
</div>

<!-- Subscribe to real-time updates -->
<%= turbo_stream_from "campaign_#{@campaign.id}" %>
<%= turbo_stream_from "agent_workflows" %>
