<!-- AI Agent Control Panel -->
<div class="space-y-6">
  
  <!-- Quick Actions -->
  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
    
    <!-- Generate All Content -->
    <% if @campaign.can_generate_ai_content? %>
      <%= form_with url: campaign_agent_workflows_path(@campaign), 
                    method: :post, 
                    data: { turbo: true },
                    class: "block" do |f| %>
        <%= f.hidden_field :workflow_type, value: 'campaign_generation' %>
        <%= f.submit "🚀 Generate All Content", 
                     class: "w-full flex justify-center items-center px-4 py-3 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",
                     data: { confirm: "This will generate AI content for all channels. Continue?" } %>
      <% end %>
    <% else %>
      <div class="w-full flex justify-center items-center px-4 py-3 border border-gray-300 text-sm font-medium rounded-md text-gray-500 bg-gray-100 cursor-not-allowed">
        🚀 Generate All Content
        <span class="ml-2 text-xs">(Campaign must be in draft status)</span>
      </div>
    <% end %>

    <!-- Optimize Campaign -->
    <% if @campaign.ai_optimization_available? %>
      <%= form_with url: optimize_campaign_campaign_agent_workflows_path(@campaign), 
                    method: :post,
                    data: { turbo: true },
                    class: "block" do |f| %>
        <%= f.submit "📊 AI Optimization", 
                     class: "w-full flex justify-center items-center px-4 py-3 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500" %>
      <% end %>
    <% else %>
      <div class="w-full flex justify-center items-center px-4 py-3 border border-gray-300 text-sm font-medium rounded-md text-gray-500 bg-gray-100 cursor-not-allowed">
        📊 AI Optimization
        <span class="ml-2 text-xs">(Requires active campaign with data)</span>
      </div>
    <% end %>
  </div>

  <!-- Individual Agent Controls -->
  <div class="border-t pt-6">
    <h3 class="text-sm font-medium text-gray-900 mb-4">Individual AI Specialists</h3>
    
    <div class="grid grid-cols-1 gap-4">
      
      <!-- Email Agent -->
      <% if @campaign.email? || @campaign.multi_channel? %>
        <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg" id="email-content-status">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <span class="text-blue-600 text-sm font-medium">✉️</span>
              </div>
            </div>
            <div class="ml-3">
              <p class="text-sm font-medium text-gray-900">Email Specialist</p>
              <p class="text-sm text-gray-500">Generate email campaigns and sequences</p>
            </div>
          </div>
          
          <div class="flex-shrink-0">
            <%= render 'agent_action_button', agent_type: 'email', campaign: @campaign %>
          </div>
        </div>
      <% end %>

      <!-- Social Media Agent -->
      <% if @campaign.social? || @campaign.multi_channel? %>
        <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg" id="social-content-status">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                <span class="text-purple-600 text-sm font-medium">📱</span>
              </div>
            </div>
            <div class="ml-3">
              <p class="text-sm font-medium text-gray-900">Social Media Specialist</p>
              <p class="text-sm text-gray-500">Create platform-optimized social content</p>
            </div>
          </div>
          
          <div class="flex-shrink-0">
            <%= render 'agent_action_button', agent_type: 'social', campaign: @campaign %>
          </div>
        </div>
      <% end %>

      <!-- SEO Agent -->
      <% if @campaign.seo? || @campaign.multi_channel? %>
        <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg" id="seo-content-status">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                <span class="text-green-600 text-sm font-medium">🔍</span>
              </div>
            </div>
            <div class="ml-3">
              <p class="text-sm font-medium text-gray-900">SEO Specialist</p>
              <p class="text-sm text-gray-500">Optimize content for search engines</p>
            </div>
          </div>
          
          <div class="flex-shrink-0">
            <%= render 'agent_action_button', agent_type: 'seo', campaign: @campaign %>
          </div>
        </div>
      <% end %>
    </div>
  </div>

  <!-- Bulk Actions -->
  <% if @campaign.multi_channel? %>
    <div class="border-t pt-6">
      <h3 class="text-sm font-medium text-gray-900 mb-4">Bulk Actions</h3>
      
      <%= form_with url: bulk_generate_campaign_agent_workflows_path(@campaign), 
                    method: :post,
                    data: { turbo: true },
                    class: "space-y-4" do |f| %>
        
        <div class="space-y-2">
          <label class="text-sm font-medium text-gray-700">Select Agent Types:</label>
          <div class="space-y-2">
            <label class="inline-flex items-center">
              <%= f.check_box :agent_types, { multiple: true }, 'email', '' %>
              <span class="ml-2 text-sm text-gray-900">Email Marketing</span>
            </label>
            <label class="inline-flex items-center ml-6">
              <%= f.check_box :agent_types, { multiple: true }, 'social', '' %>
              <span class="ml-2 text-sm text-gray-900">Social Media</span>
            </label>
            <label class="inline-flex items-center ml-6">
              <%= f.check_box :agent_types, { multiple: true }, 'seo', '' %>
              <span class="ml-2 text-sm text-gray-900">SEO Content</span>
            </label>
          </div>
        </div>
        
        <%= f.submit "Generate Selected Content", 
                     class: "w-full flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
      <% end %>
    </div>
  <% end %>

  <!-- AI Status Indicator -->
  <div id="ai-status-indicator" class="text-center">
    <%= render 'ai_status_indicator', campaign: @campaign %>
  </div>
</div>
