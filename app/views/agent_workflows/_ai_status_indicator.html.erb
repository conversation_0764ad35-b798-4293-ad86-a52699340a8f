<div class="inline-flex items-center px-3 py-2 rounded-md border <%= ai_status_indicator_classes(campaign.ai_workflow_status) %>">
  <div class="flex items-center">
    <% case campaign.ai_workflow_status %>
    <% when 'generating' %>
      <svg class="w-4 h-4 mr-2 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
      </svg>
    <% when 'completed' %>
      <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
      </svg>
    <% when 'error' %>
      <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
      </svg>
    <% else %>
      <span class="mr-2">🤖</span>
    <% end %>
    
    <span class="text-sm font-medium">
      <%= ai_status_message(campaign) %>
    </span>
  </div>
</div>
