<% content_for :title, "Create Account" %>

<!-- Sign Up Form -->
<div class="space-y-6">
  <!-- Header -->
  <div class="text-center">
    <h2 class="text-3xl font-bold text-gray-900">Create your account</h2>
    <p class="mt-2 text-gray-600">Start your 14-day free trial today</p>
    
    <!-- Free Trial Badge -->
    <div class="mt-4 inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
      <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
      </svg>
      Free 14-day trial • No credit card required
    </div>
  </div>

  <!-- Sign Up Form -->
  <%= form_with(model: resource, as: resource_name, url: registration_path(resource_name), local: true, class: "space-y-6") do |form| %>
    
    <!-- Name Fields Row -->
    <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
      <!-- First Name -->
      <div class="space-y-2">
        <%= form.label :first_name, class: "block text-sm font-medium text-gray-700" %>
        <div class="relative">
          <%= form.text_field :first_name, autofocus: true, autocomplete: "given-name",
              class: "block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 pl-12",
              placeholder: "First name" %>
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
            </svg>
          </div>
        </div>
      </div>
      
      <!-- Last Name -->
      <div class="space-y-2">
        <%= form.label :last_name, class: "block text-sm font-medium text-gray-700" %>
        <div class="relative">
          <%= form.text_field :last_name, autocomplete: "family-name",
              class: "block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 pl-12",
              placeholder: "Last name" %>
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
            </svg>
          </div>
        </div>
      </div>
    </div>

    <!-- Email Field -->
    <div class="space-y-2">
      <%= form.label :email, class: "block text-sm font-medium text-gray-700" %>
      <div class="relative">
        <%= form.email_field :email, autocomplete: "email",
            class: "block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 pl-12",
            placeholder: "Enter your email address" %>
        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"></path>
          </svg>
        </div>
      </div>
    </div>

    <!-- Password Field -->
    <div class="space-y-2">
      <%= form.label :password, class: "block text-sm font-medium text-gray-700" %>
      <% if @minimum_password_length %>
        <p class="text-xs text-gray-500">Minimum <%= @minimum_password_length %> characters</p>
      <% end %>
      <div class="relative">
        <%= form.password_field :password, autocomplete: "new-password",
            class: "block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 pl-12 pr-12",
            placeholder: "Create a secure password" %>
        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
          </svg>
        </div>
        <!-- Password Toggle Button -->
        <button type="button" id="password-toggle" class="absolute inset-y-0 right-0 pr-3 flex items-center">
          <svg id="password-show" class="h-5 w-5 text-gray-400 hover:text-gray-600 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
          </svg>
          <svg id="password-hide" class="h-5 w-5 text-gray-400 hover:text-gray-600 transition-colors hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L12 12m3.121-3.121a3 3 0 00-4.243-4.243m4.243 4.243L21 21"></path>
          </svg>
        </button>
      </div>
      
      <!-- Password Strength Indicator -->
      <div id="password-strength" class="hidden">
        <div class="flex space-x-1 mt-2">
          <div class="h-2 w-1/4 rounded-full bg-gray-200" id="strength-1"></div>
          <div class="h-2 w-1/4 rounded-full bg-gray-200" id="strength-2"></div>
          <div class="h-2 w-1/4 rounded-full bg-gray-200" id="strength-3"></div>
          <div class="h-2 w-1/4 rounded-full bg-gray-200" id="strength-4"></div>
        </div>
        <p id="strength-text" class="text-xs mt-1 text-gray-500"></p>
      </div>
    </div>

    <!-- Password Confirmation -->
    <div class="space-y-2">
      <%= form.label :password_confirmation, class: "block text-sm font-medium text-gray-700" %>
      <div class="relative">
        <%= form.password_field :password_confirmation, autocomplete: "new-password",
            class: "block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 pl-12 pr-12",
            placeholder: "Confirm your password" %>
        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
        <!-- Confirmation Status -->
        <div id="password-match" class="absolute inset-y-0 right-0 pr-3 flex items-center">
          <svg id="match-success" class="h-5 w-5 text-green-500 hidden" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
          </svg>
          <svg id="match-error" class="h-5 w-5 text-red-500 hidden" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
          </svg>
        </div>
      </div>
    </div>

    <!-- Terms and Conditions -->
    <div class="flex items-start space-x-3">
      <div class="flex items-center h-5">
        <input id="terms" name="terms" type="checkbox" required
               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded transition-colors duration-200">
      </div>
      <div class="text-sm">
        <label for="terms" class="text-gray-700 cursor-pointer">
          I agree to the 
          <a href="#" class="text-blue-600 hover:text-blue-500 transition-colors duration-200">Terms of Service</a>
          and 
          <a href="#" class="text-blue-600 hover:text-blue-500 transition-colors duration-200">Privacy Policy</a>
        </label>
      </div>
    </div>

    <!-- Submit Button -->
    <%= form.submit "Create account", 
        class: "group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transform hover:-translate-y-0.5 transition-all duration-200 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed",
        id: "submit-button" %>
        
    <!-- Trust Indicators -->
    <div class="text-xs text-gray-500 text-center space-y-1">
      <p>✓ 14-day free trial</p>
      <p>✓ No credit card required</p>
      <p>✓ Cancel anytime</p>
    </div>
  <% end %>

  <!-- Divider -->
  <div class="relative">
    <div class="absolute inset-0 flex items-center">
      <div class="w-full border-t border-gray-300"></div>
    </div>
    <div class="relative flex justify-center text-sm">
      <span class="px-2 bg-gray-50 text-gray-500">Already have an account?</span>
    </div>
  </div>

  <!-- Sign In Link -->
  <div class="text-center">
    <%= link_to new_session_path(resource_name), 
        class: "group relative inline-flex items-center justify-center py-3 px-6 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200" do %>
      <svg class="mr-2 h-4 w-4 group-hover:-translate-x-1 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 17l-5-5m0 0l5-5m-5 5h12"></path>
      </svg>
      <span>Sign in to your account</span>
    <% end %>
  </div>

  <!-- Back to Home -->
  <div class="text-center">
    <%= link_to "← Back to Home", root_path, 
        class: "text-sm text-gray-500 hover:text-gray-700 transition-colors duration-200" %>
  </div>
</div>

<!-- JavaScript for Form Enhancements -->
<script>
document.addEventListener('DOMContentLoaded', function() {
  const passwordField = document.getElementById('<%= "#{resource_name}_password" %>');
  const passwordConfirmField = document.getElementById('<%= "#{resource_name}_password_confirmation" %>');
  const passwordToggle = document.getElementById('password-toggle');
  const showIcon = document.getElementById('password-show');
  const hideIcon = document.getElementById('password-hide');
  const strengthIndicator = document.getElementById('password-strength');
  const strengthText = document.getElementById('strength-text');
  const matchSuccess = document.getElementById('match-success');
  const matchError = document.getElementById('match-error');
  const submitButton = document.getElementById('submit-button');
  const termsCheckbox = document.getElementById('terms');
  
  // Password Toggle
  if (passwordToggle && passwordField) {
    passwordToggle.addEventListener('click', function() {
      if (passwordField.type === 'password') {
        passwordField.type = 'text';
        showIcon.classList.add('hidden');
        hideIcon.classList.remove('hidden');
      } else {
        passwordField.type = 'password';
        showIcon.classList.remove('hidden');
        hideIcon.classList.add('hidden');
      }
    });
  }
  
  // Password Strength Indicator
  if (passwordField && strengthIndicator) {
    passwordField.addEventListener('input', function() {
      const password = passwordField.value;
      const strength = calculatePasswordStrength(password);
      
      if (password.length > 0) {
        strengthIndicator.classList.remove('hidden');
        updateStrengthIndicator(strength);
      } else {
        strengthIndicator.classList.add('hidden');
      }
    });
  }
  
  // Password Confirmation Matching
  if (passwordConfirmField && passwordField) {
    passwordConfirmField.addEventListener('input', checkPasswordMatch);
    passwordField.addEventListener('input', checkPasswordMatch);
  }
  
  // Terms checkbox validation
  if (termsCheckbox && submitButton) {
    termsCheckbox.addEventListener('change', function() {
      updateSubmitButton();
    });
  }
  
  function calculatePasswordStrength(password) {
    let score = 0;
    if (password.length >= 8) score++;
    if (/[a-z]/.test(password)) score++;
    if (/[A-Z]/.test(password)) score++;
    if (/[0-9]/.test(password)) score++;
    if (/[^A-Za-z0-9]/.test(password)) score++;
    return score;
  }
  
  function updateStrengthIndicator(strength) {
    const indicators = ['strength-1', 'strength-2', 'strength-3', 'strength-4'];
    const colors = ['bg-red-500', 'bg-orange-500', 'bg-yellow-500', 'bg-green-500'];
    const texts = ['Very Weak', 'Weak', 'Fair', 'Good', 'Strong'];
    
    // Reset all indicators
    indicators.forEach(id => {
      const element = document.getElementById(id);
      element.className = 'h-2 w-1/4 rounded-full bg-gray-200';
    });
    
    // Fill indicators based on strength
    for (let i = 0; i < Math.min(strength, 4); i++) {
      const element = document.getElementById(indicators[i]);
      element.className = `h-2 w-1/4 rounded-full ${colors[Math.min(strength - 1, 3)]}`;
    }
    
    strengthText.textContent = texts[Math.min(strength, 4)];
    strengthText.className = `text-xs mt-1 ${strength >= 3 ? 'text-green-600' : strength >= 2 ? 'text-yellow-600' : 'text-red-600'}`;
  }
  
  function checkPasswordMatch() {
    if (passwordConfirmField.value.length === 0) {
      matchSuccess.classList.add('hidden');
      matchError.classList.add('hidden');
      return;
    }
    
    if (passwordField.value === passwordConfirmField.value) {
      matchSuccess.classList.remove('hidden');
      matchError.classList.add('hidden');
      passwordConfirmField.classList.remove('border-red-300', 'focus:ring-red-500', 'focus:border-red-500');
      passwordConfirmField.classList.add('border-green-300', 'focus:ring-green-500', 'focus:border-green-500');
    } else {
      matchSuccess.classList.add('hidden');
      matchError.classList.remove('hidden');
      passwordConfirmField.classList.remove('border-green-300', 'focus:ring-green-500', 'focus:border-green-500');
      passwordConfirmField.classList.add('border-red-300', 'focus:ring-red-500', 'focus:border-red-500');
    }
    
    updateSubmitButton();
  }
  
  function updateSubmitButton() {
    const passwordsMatch = passwordField.value === passwordConfirmField.value && passwordField.value.length > 0;
    const termsAccepted = termsCheckbox.checked;
    const allValid = passwordsMatch && termsAccepted;
    
    submitButton.disabled = !allValid;
    
    if (!allValid) {
      submitButton.classList.add('opacity-50', 'cursor-not-allowed');
    } else {
      submitButton.classList.remove('opacity-50', 'cursor-not-allowed');
    }
  }
  
  // Initial validation check
  updateSubmitButton();
});
</script>