<%# Social Content Creation Interface - New %>
<% content_for :title, "Create Social Content - #{@campaign.name}" %>

<div class="bg-white">
  <!-- Header -->
  <div class="border-b border-gray-200 bg-white px-4 py-5 sm:px-6">
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-4">
        <%= link_to campaign_path(@campaign), class: "text-gray-500 hover:text-gray-700" do %>
          <%= render 'shared/icons/heroicon', name: 'arrow-left', class: 'w-5 h-5' %>
        <% end %>
        
        <div>
          <h1 class="text-2xl font-bold text-gray-900">Create Social Content</h1>
          <p class="text-sm text-gray-500"><%= @campaign.name %> • <%= @campaign.campaign_type.titleize %> Campaign</p>
        </div>
      </div>

      <div class="flex items-center space-x-3">
        <!-- Quick Start with AI -->
        <%= button_to generate_ai_content_campaign_social_content_path(@campaign),
            method: :post,
            params: { platforms: ['twitter', 'linkedin'], brand_voice: 'professional' },
            class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" do %>
          <%= render 'shared/icons/heroicon', name: 'sparkles', class: 'w-4 h-4 mr-2' %>
          Quick Start with AI
        <% end %>
      </div>
    </div>
  </div>

  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Getting Started Section -->
    <div class="mb-8">
      <div class="bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg p-6 text-white">
        <div class="flex items-center justify-between">
          <div>
            <h2 class="text-xl font-semibold mb-2">Get Started with Social Content</h2>
            <p class="text-indigo-100">Choose your platforms and let AI help you create engaging content, or start from scratch.</p>
          </div>
          <div class="hidden md:block">
            <%= render 'shared/icons/heroicon', name: 'rocket-launch', class: 'w-16 h-16 text-indigo-200' %>
          </div>
        </div>
      </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- Main Content Creation Form -->
      <div class="lg:col-span-2">
        <%= form_with model: [@campaign, @social_campaign], url: campaign_social_content_path(@campaign), local: true, data: { turbo: false }, class: "space-y-8" do |form| %>
          
          <!-- Step 1: Platform Selection -->
          <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
              <div class="flex items-center space-x-3">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center">
                    <span class="text-sm font-medium text-indigo-600">1</span>
                  </div>
                </div>
                <div>
                  <h2 class="text-lg font-medium text-gray-900">Choose Your Platforms</h2>
                  <p class="text-sm text-gray-500">Select the social media platforms where you want to publish content</p>
                </div>
              </div>
            </div>
            
            <div class="p-6">
              <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
                <% @available_platforms.each do |platform| %>
                  <% platform_limit = @platform_limits[platform] %>
                  
                  <label class="relative cursor-pointer platform-selector" data-platform="<%= platform %>">
                    <%= form.check_box :platforms, 
                        { multiple: true, class: "sr-only platform-checkbox", data: { platform: platform } }, 
                        platform, "" %>
                    
                    <div class="platform-card ring-1 ring-gray-300 rounded-lg p-4 hover:bg-gray-50 transition-all duration-200 hover:ring-2 hover:ring-indigo-500">
                      <div class="flex items-center space-x-3">
                        <div class="flex-shrink-0">
                          <%= render "shared/platform_icons/#{platform}", class: "w-8 h-8 text-gray-600" %>
                        </div>
                        <div class="flex-1 min-w-0">
                          <h3 class="text-sm font-medium text-gray-900"><%= platform.titleize %></h3>
                          <p class="text-xs text-gray-500">
                            <%= platform_limit[:character_limit] %> chars • 
                            <%= platform_limit[:hashtag_limit] %> hashtags
                          </p>
                        </div>
                      </div>
                      
                      <!-- Selection indicator -->
                      <div class="selection-indicator absolute top-2 right-2 hidden">
                        <%= render 'shared/icons/heroicon', name: 'check-circle', class: 'w-5 h-5 text-indigo-600' %>
                      </div>
                    </div>
                  </label>
                <% end %>
              </div>

              <!-- Quick Platform Sets -->
              <div class="mt-6 pt-6 border-t border-gray-200">
                <h4 class="text-sm font-medium text-gray-900 mb-3">Quick Sets</h4>
                <div class="flex flex-wrap gap-2">
                  <button type="button" 
                          class="platform-set-btn px-3 py-1 text-sm border border-gray-300 rounded-full hover:bg-gray-50"
                          data-platforms='["twitter", "linkedin"]'>
                    Professional (Twitter + LinkedIn)
                  </button>
                  <button type="button" 
                          class="platform-set-btn px-3 py-1 text-sm border border-gray-300 rounded-full hover:bg-gray-50"
                          data-platforms='["instagram", "tiktok"]'>
                    Visual (Instagram + TikTok)
                  </button>
                  <button type="button" 
                          class="platform-set-btn px-3 py-1 text-sm border border-gray-300 rounded-full hover:bg-gray-50"
                          data-platforms='["facebook", "instagram", "twitter"]'>
                    Mainstream (Facebook + Instagram + Twitter)
                  </button>
                  <button type="button" 
                          class="platform-set-btn px-3 py-1 text-sm border border-gray-300 rounded-full hover:bg-gray-50"
                          data-platforms='["twitter", "facebook", "instagram", "linkedin"]'>
                    All Major Platforms
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Step 2: Content Strategy -->
          <div class="bg-white shadow rounded-lg" id="content-strategy-section" style="display: none;">
            <div class="px-6 py-4 border-b border-gray-200">
              <div class="flex items-center space-x-3">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center">
                    <span class="text-sm font-medium text-indigo-600">2</span>
                  </div>
                </div>
                <div>
                  <h2 class="text-lg font-medium text-gray-900">Content Strategy</h2>
                  <p class="text-sm text-gray-500">Define your content approach and targeting</p>
                </div>
              </div>
            </div>
            
            <div class="p-6 space-y-6">
              <!-- Brand Voice Selection -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-3">Brand Voice</label>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                  <% %w[professional casual friendly authoritative].each do |voice| %>
                    <label class="cursor-pointer">
                      <input type="radio" name="brand_voice" value="<%= voice %>" class="sr-only brand-voice-radio">
                      <div class="brand-voice-option p-3 border border-gray-300 rounded-lg text-center hover:bg-gray-50 transition-colors">
                        <div class="text-sm font-medium text-gray-900"><%= voice.titleize %></div>
                        <div class="text-xs text-gray-500 mt-1">
                          <% case voice %>
                          <% when 'professional' %>
                            Formal and business-focused
                          <% when 'casual' %>
                            Relaxed and conversational
                          <% when 'friendly' %>
                            Warm and approachable
                          <% when 'authoritative' %>
                            Expert and confident
                          <% end %>
                        </div>
                      </div>
                    </label>
                  <% end %>
                </div>
              </div>

              <!-- Content Pillars -->
              <div>
                <label for="content_pillars" class="block text-sm font-medium text-gray-700 mb-2">
                  Content Pillars
                </label>
                <input type="text" 
                       id="content_pillars" 
                       name="content_pillars"
                       placeholder="e.g., industry insights, behind the scenes, customer stories"
                       class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500">
                <p class="text-xs text-gray-500 mt-1">Separate with commas. These themes will guide content creation.</p>
              </div>

              <!-- Target Demographics -->
              <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Age Range</label>
                  <select name="social_campaign[target_demographics][age_range]"
                          class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500">
                    <option value="">Select age range</option>
                    <option value="18-24">18-24 (Gen Z)</option>
                    <option value="25-34">25-34 (Millennials)</option>
                    <option value="35-44">35-44 (Gen X)</option>
                    <option value="45-54">45-54 (Gen X)</option>
                    <option value="55+">55+ (Boomers)</option>
                  </select>
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Primary Interests</label>
                  <input type="text" 
                         name="social_campaign[target_demographics][interests]"
                         placeholder="technology, business, lifestyle"
                         class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500">
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Geographic Focus</label>
                  <input type="text" 
                         name="social_campaign[target_demographics][location]"
                         placeholder="United States, Global"
                         class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500">
                </div>
              </div>
            </div>
          </div>

          <!-- Step 3: Content Creation -->
          <div class="bg-white shadow rounded-lg" id="content-creation-section" style="display: none;">
            <div class="px-6 py-4 border-b border-gray-200">
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                  <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center">
                      <span class="text-sm font-medium text-indigo-600">3</span>
                    </div>
                  </div>
                  <div>
                    <h2 class="text-lg font-medium text-gray-900">Create Content</h2>
                    <p class="text-sm text-gray-500">Write content for each selected platform</p>
                  </div>
                </div>
                
                <div class="flex items-center space-x-2">
                  <button type="button" 
                          id="generate-ai-content-btn"
                          class="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                    <%= render 'shared/icons/heroicon', name: 'sparkles', class: 'w-4 h-4 mr-1' %>
                    Generate with AI
                  </button>
                </div>
              </div>
            </div>
            
            <div class="p-6">
              <div id="platform-content-forms" class="space-y-6">
                <!-- Platform-specific forms will be dynamically added here -->
              </div>
            </div>
          </div>

          <!-- Global Settings -->
          <div class="bg-white shadow rounded-lg" id="global-settings-section" style="display: none;">
            <div class="px-6 py-4 border-b border-gray-200">
              <h3 class="text-lg font-medium text-gray-900">Global Settings</h3>
            </div>
            
            <div class="p-6 space-y-4">
              <!-- Global Hashtags -->
              <div>
                <label for="social_campaign_hashtags" class="block text-sm font-medium text-gray-700 mb-2">
                  Global Hashtags
                </label>
                <%= form.text_field :hashtags, 
                    placeholder: "#marketing #automation #ai",
                    class: "block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500" %>
                <p class="text-xs text-gray-500 mt-1">These hashtags will be added to all platform posts (within platform limits).</p>
              </div>

              <!-- Publishing Options -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 class="text-sm font-medium text-gray-700 mb-3">Publishing</h4>
                  <div class="space-y-2">
                    <label class="flex items-center">
                      <input type="radio" name="social_campaign[social_settings][publish_mode]" value="draft" checked
                             class="border-gray-300 text-indigo-600 focus:ring-indigo-500">
                      <span class="ml-2 text-sm text-gray-700">Save as draft</span>
                    </label>
                    <label class="flex items-center">
                      <input type="radio" name="social_campaign[social_settings][publish_mode]" value="schedule"
                             class="border-gray-300 text-indigo-600 focus:ring-indigo-500">
                      <span class="ml-2 text-sm text-gray-700">Schedule for later</span>
                    </label>
                    <label class="flex items-center">
                      <input type="radio" name="social_campaign[social_settings][publish_mode]" value="immediate"
                             class="border-gray-300 text-indigo-600 focus:ring-indigo-500">
                      <span class="ml-2 text-sm text-gray-700">Publish immediately</span>
                    </label>
                  </div>
                </div>

                <div>
                  <h4 class="text-sm font-medium text-gray-700 mb-3">Tracking</h4>
                  <div class="space-y-2">
                    <label class="flex items-center">
                      <input type="checkbox" name="social_campaign[social_settings][engagement_tracking]" checked
                             class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
                      <span class="ml-2 text-sm text-gray-700">Track engagement</span>
                    </label>
                    <label class="flex items-center">
                      <input type="checkbox" name="social_campaign[social_settings][hashtag_tracking]" checked
                             class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
                      <span class="ml-2 text-sm text-gray-700">Monitor hashtags</span>
                    </label>
                    <label class="flex items-center">
                      <input type="checkbox" name="social_campaign[social_settings][mention_monitoring]" checked
                             class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
                      <span class="ml-2 text-sm text-gray-700">Monitor mentions</span>
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Submit Button -->
          <div class="flex justify-end" id="submit-section" style="display: none;">
            <%= form.submit "Create Social Content", 
                class: "inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500",
                id: "submit-btn" %>
          </div>
        <% end %>
      </div>

      <!-- Sidebar -->
      <div class="space-y-6">
        <!-- Progress Indicator -->
        <div class="bg-white shadow rounded-lg">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Setup Progress</h3>
          </div>
          
          <div class="p-6">
            <div class="space-y-4">
              <div class="progress-item flex items-center" data-step="1">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center progress-circle">
                    <span class="text-sm font-medium text-gray-500">1</span>
                  </div>
                </div>
                <div class="ml-3">
                  <p class="text-sm font-medium text-gray-900">Select Platforms</p>
                  <p class="text-xs text-gray-500">Choose your social media channels</p>
                </div>
              </div>
              
              <div class="progress-item flex items-center" data-step="2">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center progress-circle">
                    <span class="text-sm font-medium text-gray-500">2</span>
                  </div>
                </div>
                <div class="ml-3">
                  <p class="text-sm font-medium text-gray-900">Content Strategy</p>
                  <p class="text-xs text-gray-500">Define your approach</p>
                </div>
              </div>
              
              <div class="progress-item flex items-center" data-step="3">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center progress-circle">
                    <span class="text-sm font-medium text-gray-500">3</span>
                  </div>
                </div>
                <div class="ml-3">
                  <p class="text-sm font-medium text-gray-900">Create Content</p>
                  <p class="text-xs text-gray-500">Write your posts</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Tips & Best Practices -->
        <div class="bg-white shadow rounded-lg">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Pro Tips</h3>
          </div>
          
          <div class="p-6">
            <div class="space-y-4">
              <div class="tip-item bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div class="flex">
                  <div class="flex-shrink-0">
                    <%= render 'shared/icons/heroicon', name: 'light-bulb', class: 'w-5 h-5 text-blue-600' %>
                  </div>
                  <div class="ml-3">
                    <h4 class="text-sm font-medium text-blue-900">Use Platform-Specific Hashtags</h4>
                    <p class="text-xs text-blue-700 mt-1">Research trending hashtags for each platform to maximize reach.</p>
                  </div>
                </div>
              </div>
              
              <div class="tip-item bg-green-50 border border-green-200 rounded-lg p-4">
                <div class="flex">
                  <div class="flex-shrink-0">
                    <%= render 'shared/icons/heroicon', name: 'clock', class: 'w-5 h-5 text-green-600' %>
                  </div>
                  <div class="ml-3">
                    <h4 class="text-sm font-medium text-green-900">Optimal Posting Times</h4>
                    <p class="text-xs text-green-700 mt-1">Post when your audience is most active for better engagement.</p>
                  </div>
                </div>
              </div>
              
              <div class="tip-item bg-purple-50 border border-purple-200 rounded-lg p-4">
                <div class="flex">
                  <div class="flex-shrink-0">
                    <%= render 'shared/icons/heroicon', name: 'sparkles', class: 'w-5 h-5 text-purple-600' %>
                  </div>
                  <div class="ml-3">
                    <h4 class="text-sm font-medium text-purple-900">AI Content Generation</h4>
                    <p class="text-xs text-purple-700 mt-1">Let AI create initial drafts, then personalize them for your brand.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Multi-step Form JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
  let currentStep = 1;
  let selectedPlatforms = [];
  
  // Platform selection handling
  const platformCheckboxes = document.querySelectorAll('.platform-checkbox');
  const platformCards = document.querySelectorAll('.platform-card');
  
  platformCheckboxes.forEach(checkbox => {
    checkbox.addEventListener('change', function() {
      const platform = this.dataset.platform;
      const card = this.closest('.platform-selector').querySelector('.platform-card');
      const indicator = this.closest('.platform-selector').querySelector('.selection-indicator');
      
      if (this.checked) {
        selectedPlatforms.push(platform);
        card.classList.add('ring-2', 'ring-indigo-500', 'bg-indigo-50');
        card.classList.remove('ring-1', 'ring-gray-300');
        indicator.classList.remove('hidden');
      } else {
        selectedPlatforms = selectedPlatforms.filter(p => p !== platform);
        card.classList.remove('ring-2', 'ring-indigo-500', 'bg-indigo-50');
        card.classList.add('ring-1', 'ring-gray-300');
        indicator.classList.add('hidden');
      }
      
      updateStepProgress();
    });
  });
  
  // Quick platform set buttons
  const platformSetBtns = document.querySelectorAll('.platform-set-btn');
  platformSetBtns.forEach(btn => {
    btn.addEventListener('click', function() {
      const platforms = JSON.parse(this.dataset.platforms);
      
      // Clear current selections
      platformCheckboxes.forEach(checkbox => {
        checkbox.checked = false;
        const card = checkbox.closest('.platform-selector').querySelector('.platform-card');
        const indicator = checkbox.closest('.platform-selector').querySelector('.selection-indicator');
        card.classList.remove('ring-2', 'ring-indigo-500', 'bg-indigo-50');
        card.classList.add('ring-1', 'ring-gray-300');
        indicator.classList.add('hidden');
      });
      
      // Select new platforms
      platforms.forEach(platform => {
        const checkbox = document.querySelector(`[data-platform="${platform}"]`);
        if (checkbox) {
          checkbox.checked = true;
          const card = checkbox.closest('.platform-selector').querySelector('.platform-card');
          const indicator = checkbox.closest('.platform-selector').querySelector('.selection-indicator');
          card.classList.add('ring-2', 'ring-indigo-500', 'bg-indigo-50');
          card.classList.remove('ring-1', 'ring-gray-300');
          indicator.classList.remove('hidden');
        }
      });
      
      selectedPlatforms = platforms;
      updateStepProgress();
    });
  });
  
  // Brand voice selection
  const brandVoiceRadios = document.querySelectorAll('.brand-voice-radio');
  brandVoiceRadios.forEach(radio => {
    radio.addEventListener('change', function() {
      // Update visual selection
      document.querySelectorAll('.brand-voice-option').forEach(option => {
        option.classList.remove('ring-2', 'ring-indigo-500', 'bg-indigo-50');
        option.classList.add('border-gray-300');
      });
      
      if (this.checked) {
        const option = this.closest('label').querySelector('.brand-voice-option');
        option.classList.add('ring-2', 'ring-indigo-500', 'bg-indigo-50');
        option.classList.remove('border-gray-300');
      }
      
      updateStepProgress();
    });
  });
  
  // AI content generation
  const generateAiBtn = document.getElementById('generate-ai-content-btn');
  if (generateAiBtn) {
    generateAiBtn.addEventListener('click', function() {
      if (selectedPlatforms.length === 0) {
        alert('Please select at least one platform first.');
        return;
      }
      
      // Collect form data for AI generation
      const formData = new FormData();
      formData.append('platforms', JSON.stringify(selectedPlatforms));
      
      const brandVoice = document.querySelector('.brand-voice-radio:checked');
      if (brandVoice) {
        formData.append('brand_voice', brandVoice.value);
      }
      
      const contentPillars = document.getElementById('content_pillars');
      if (contentPillars && contentPillars.value) {
        formData.append('content_pillars', contentPillars.value);
      }
      
      // Show loading state
      this.disabled = true;
      this.innerHTML = '<svg class="animate-spin h-4 w-4 mr-2" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>Generating...';
      
      // Make AI generation request
      fetch('<%= generate_ai_content_campaign_social_content_path(@campaign) %>', {
        method: 'POST',
        body: formData,
        headers: {
          'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
        }
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          // Populate content forms with AI-generated content
          populateContentForms(data.content);
        } else {
          alert('Failed to generate content: ' + data.message);
        }
      })
      .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while generating content.');
      })
      .finally(() => {
        this.disabled = false;
        this.innerHTML = '<svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3l14 14m0 0v-3.6M19 17H15.4"></path></svg>Generate with AI';
      });
    });
  }
  
  function updateStepProgress() {
    // Step 1: Platform selection
    if (selectedPlatforms.length > 0 && currentStep === 1) {
      showStep(2);
    }
    
    // Update progress indicators
    updateProgressIndicator();
    
    // Show/hide sections based on progress
    updateSectionVisibility();
  }
  
  function showStep(step) {
    currentStep = step;
    updateSectionVisibility();
    updateProgressIndicator();
    
    if (step === 3) {
      generateContentForms();
    }
  }
  
  function updateProgressIndicator() {
    const progressItems = document.querySelectorAll('.progress-item');
    
    progressItems.forEach((item, index) => {
      const stepNumber = index + 1;
      const circle = item.querySelector('.progress-circle');
      const span = circle.querySelector('span');
      
      if (stepNumber <= currentStep) {
        circle.classList.remove('bg-gray-200');
        circle.classList.add('bg-indigo-100');
        span.classList.remove('text-gray-500');
        span.classList.add('text-indigo-600');
      } else {
        circle.classList.add('bg-gray-200');
        circle.classList.remove('bg-indigo-100');
        span.classList.add('text-gray-500');
        span.classList.remove('text-indigo-600');
      }
      
      if (stepNumber === currentStep) {
        circle.classList.add('ring-2', 'ring-indigo-500');
      } else {
        circle.classList.remove('ring-2', 'ring-indigo-500');
      }
    });
  }
  
  function updateSectionVisibility() {
    const sections = ['content-strategy-section', 'content-creation-section', 'global-settings-section', 'submit-section'];
    
    sections.forEach((sectionId, index) => {
      const section = document.getElementById(sectionId);
      const stepNumber = index + 2; // Sections start from step 2
      
      if (stepNumber <= currentStep) {
        section.style.display = 'block';
      } else {
        section.style.display = 'none';
      }
    });
    
    // Show submit section when we reach step 3
    if (currentStep >= 3) {
      document.getElementById('submit-section').style.display = 'block';
    }
  }
  
  function generateContentForms() {
    const container = document.getElementById('platform-content-forms');
    container.innerHTML = '';
    
    selectedPlatforms.forEach(platform => {
      const formHtml = createPlatformContentForm(platform);
      container.insertAdjacentHTML('beforeend', formHtml);
    });
  }
  
  function createPlatformContentForm(platform) {
    const limits = {
      'twitter': { character_limit: 280, hashtag_limit: 2 },
      'facebook': { character_limit: 2200, hashtag_limit: 5 },
      'instagram': { character_limit: 2200, hashtag_limit: 30 },
      'linkedin': { character_limit: 3000, hashtag_limit: 3 },
      'tiktok': { character_limit: 150, hashtag_limit: 5 },
      'youtube': { character_limit: 5000, hashtag_limit: 15 }
    };
    
    const limit = limits[platform] || limits['twitter'];
    
    return `
      <div class="platform-content-form border border-gray-200 rounded-lg" data-platform="${platform}">
        <div class="bg-gray-50 px-4 py-3 border-b border-gray-200 flex items-center justify-between">
          <div class="flex items-center space-x-3">
            <div class="w-6 h-6 platform-icon-${platform}"></div>
            <h4 class="text-sm font-medium text-gray-900">${platform.charAt(0).toUpperCase() + platform.slice(1)} Content</h4>
          </div>
          <div class="text-xs text-gray-500">
            Limit: ${limit.character_limit} chars • ${limit.hashtag_limit} hashtags
          </div>
        </div>
        
        <div class="p-4">
          <textarea 
            name="social_campaign[content_variants][${platform}]"
            placeholder="Enter your ${platform} content here..."
            rows="4"
            class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
            data-platform="${platform}"
            data-limit="${limit.character_limit}"
          ></textarea>
          
          <div class="mt-2 flex justify-between text-xs text-gray-500">
            <span>Characters: <span class="char-count-${platform}">0</span> / ${limit.character_limit}</span>
            <span>Hashtags: <span class="hashtag-count-${platform}">0</span> / ${limit.hashtag_limit}</span>
          </div>
        </div>
      </div>
    `;
  }
  
  function populateContentForms(content) {
    selectedPlatforms.forEach(platform => {
      const textarea = document.querySelector(`textarea[data-platform="${platform}"]`);
      if (textarea && content[platform]) {
        textarea.value = content[platform];
        updateCharacterCount(textarea);
      }
    });
  }
  
  function updateCharacterCount(textarea) {
    const platform = textarea.dataset.platform;
    const content = textarea.value;
    const charCount = document.querySelector(`.char-count-${platform}`);
    const hashtagCount = document.querySelector(`.hashtag-count-${platform}`);
    
    if (charCount) charCount.textContent = content.length;
    if (hashtagCount) {
      const hashtags = (content.match(/#\w+/g) || []).length;
      hashtagCount.textContent = hashtags;
    }
  }
  
  // Initialize first step
  updateSectionVisibility();
  updateProgressIndicator();
});
</script>
