<% content_for :title, "AI Agents" %>

<!-- AI Agents Command Center -->
<div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/20">
  <div class="p-6 space-y-6">
    
    <!-- Header -->
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
      <div class="mb-4 lg:mb-0">
        <h1 class="text-3xl font-bold bg-gradient-to-r from-gray-900 via-gray-800 to-gray-600 bg-clip-text text-transparent">
          AI Agents Command Center
        </h1>
        <p class="text-gray-600 mt-1 flex items-center space-x-2">
          <span>Autonomous marketing intelligence at work</span>
          <span class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></span>
          <span class="text-sm text-green-600 font-medium"><%= @active_agents.count { |a| a[:status] == 'active' } %> Active</span>
        </p>
      </div>
      
      <!-- Quick Stats -->
      <div class="flex items-center space-x-4">
        <div class="bg-white rounded-xl p-4 shadow-sm border border-gray-100">
          <div class="text-2xl font-bold text-blue-600"><%= @agent_performance[:completed_tasks] %></div>
          <div class="text-sm text-gray-600">Tasks Today</div>
        </div>
        <div class="bg-white rounded-xl p-4 shadow-sm border border-gray-100">
          <div class="text-2xl font-bold text-green-600"><%= @agent_performance[:success_rate] %>%</div>
          <div class="text-sm text-gray-600">Success Rate</div>
        </div>
      </div>
    </div>

    <!-- Agent Performance Overview -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <!-- Total Tasks -->
      <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-lg transition-shadow">
        <div class="flex items-center justify-between mb-4">
          <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
            </svg>
          </div>
          <span class="text-xs font-medium text-blue-600 bg-blue-50 px-2 py-1 rounded-full">Active</span>
        </div>
        <h3 class="text-3xl font-bold text-gray-900"><%= @agent_performance[:total_tasks_today] %></h3>
        <p class="text-sm text-gray-600">Tasks Today</p>
        <div class="text-xs text-green-500 mt-1">↗ +12% from yesterday</div>
      </div>

      <!-- Success Rate -->
      <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-lg transition-shadow">
        <div class="flex items-center justify-between mb-4">
          <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
          </div>
          <span class="text-xs font-medium text-green-600 bg-green-50 px-2 py-1 rounded-full">Optimal</span>
        </div>
        <h3 class="text-3xl font-bold text-gray-900"><%= @agent_performance[:success_rate] %>%</h3>
        <p class="text-sm text-gray-600">Success Rate</p>
        <div class="text-xs text-green-500 mt-1">↗ +2.3% from last week</div>
      </div>

      <!-- Response Time -->
      <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-lg transition-shadow">
        <div class="flex items-center justify-between mb-4">
          <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
          </div>
          <span class="text-xs font-medium text-purple-600 bg-purple-50 px-2 py-1 rounded-full">Fast</span>
        </div>
        <h3 class="text-3xl font-bold text-gray-900"><%= @agent_performance[:average_response_time] %>s</h3>
        <p class="text-sm text-gray-600">Avg Response</p>
        <div class="text-xs text-green-500 mt-1">↘ -0.3s improvement</div>
      </div>

      <!-- Active Workflows -->
      <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-lg transition-shadow">
        <div class="flex items-center justify-between mb-4">
          <div class="w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
            </svg>
          </div>
          <span class="text-xs font-medium text-orange-600 bg-orange-50 px-2 py-1 rounded-full">Running</span>
        </div>
        <h3 class="text-3xl font-bold text-gray-900"><%= @agent_performance[:active_workflows] %></h3>
        <p class="text-sm text-gray-600">Active Workflows</p>
        <div class="text-xs text-blue-500 mt-1">3 scheduled next hour</div>
      </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- Active Agents Grid -->
      <div class="lg:col-span-2">
        <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-gray-900">Active AI Agents</h3>
            <span class="text-sm text-gray-600"><%= @active_agents.count %> agents deployed</span>
          </div>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <% @active_agents.each do |agent| %>
              <div class="agent-card bg-gradient-to-br from-gray-50 to-white rounded-xl p-4 border border-gray-100 hover:shadow-md transition-all group">
                <div class="flex items-start justify-between mb-4">
                  <div class="flex items-center space-x-3">
                    <div class="w-12 h-12 bg-gradient-to-br from-<%= agent[:color] %>-500 to-<%= agent[:color] %>-600 rounded-xl flex items-center justify-center">
                      <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
                      </svg>
                    </div>
                    <div>
                      <h4 class="font-semibold text-gray-900"><%= agent[:name] %></h4>
                      <p class="text-xs text-<%= agent[:color] %>-600 font-medium"><%= agent[:specialty] %></p>
                    </div>
                  </div>
                  <span class="status-indicator <%= agent[:status] %>">
                    <%= agent[:status].humanize %>
                  </span>
                </div>
                
                <p class="text-sm text-gray-600 mb-4"><%= agent[:description] %></p>
                
                <div class="space-y-2">
                  <div class="flex items-center justify-between text-sm">
                    <span class="text-gray-600">Tasks Completed</span>
                    <span class="font-medium"><%= agent[:tasks_completed] %></span>
                  </div>
                  <div class="flex items-center justify-between text-sm">
                    <span class="text-gray-600">Success Rate</span>
                    <span class="font-medium text-green-600"><%= agent[:success_rate] %>%</span>
                  </div>
                  <div class="flex items-center justify-between text-sm">
                    <span class="text-gray-600">Last Active</span>
                    <span class="font-medium text-gray-500"><%= time_ago_in_words(agent[:last_active]) %> ago</span>
                  </div>
                </div>
              </div>
            <% end %>
          </div>
        </div>
      </div>

      <!-- Right Column -->
      <div class="space-y-6">
        <!-- Recent Activities -->
        <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Recent Activities</h3>
          
          <div class="space-y-4">
            <% if @recent_activities.any? %>
              <% @recent_activities.first(5).each do |activity| %>
                <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                  <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                    <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
                    </svg>
                  </div>
                  <div class="flex-1 min-w-0">
                    <p class="text-sm font-medium text-gray-900 truncate">
                      <%= activity[:agent_type].titleize %>
                    </p>
                    <p class="text-xs text-gray-600 truncate">
                      <%= activity[:action] %> • <%= activity[:campaign_name] %>
                    </p>
                    <p class="text-xs text-gray-500">
                      <%= time_ago_in_words(activity[:created_at]) %> ago
                    </p>
                  </div>
                  <span class="status-badge-small <%= activity[:status] %>">
                    <%= activity[:status].titleize %>
                  </span>
                </div>
              <% end %>
            <% else %>
              <div class="text-center py-8">
                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
                  </svg>
                </div>
                <p class="text-gray-600 font-medium">No Recent Activities</p>
                <p class="text-sm text-gray-500">Agents will appear here when they start working</p>
              </div>
            <% end %>
          </div>
        </div>

        <!-- Agent Configuration -->
        <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">System Configuration</h3>
          
          <div class="space-y-3">
            <% @agent_configurations.each do |config, enabled| %>
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-700"><%= config.to_s.humanize %></span>
                <div class="flex items-center">
                  <% if enabled %>
                    <div class="w-2 h-2 bg-green-400 rounded-full mr-2"></div>
                    <span class="text-xs text-green-600 font-medium">Enabled</span>
                  <% else %>
                    <div class="w-2 h-2 bg-gray-300 rounded-full mr-2"></div>
                    <span class="text-xs text-gray-500 font-medium">Disabled</span>
                  <% end %>
                </div>
              </div>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
/* Agent card hover effects */
.agent-card:hover {
  transform: translateY(-2px);
}

/* Status indicators */
.status-indicator {
  @apply inline-flex items-center px-2 py-1 rounded-full text-xs font-medium;
}

.status-indicator.active {
  @apply bg-green-100 text-green-800;
}

.status-indicator.training {
  @apply bg-yellow-100 text-yellow-800;
}

.status-indicator.inactive {
  @apply bg-gray-100 text-gray-800;
}

/* Status badges for activities */
.status-badge-small {
  @apply inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium;
}

.status-badge-small.running {
  @apply bg-blue-100 text-blue-800;
}

.status-badge-small.completed {
  @apply bg-green-100 text-green-800;
}

.status-badge-small.failed {
  @apply bg-red-100 text-red-800;
}

.status-badge-small.pending {
  @apply bg-yellow-100 text-yellow-800;
}
</style>