/* Sidebar Enhancements */

/* Smooth transitions for sidebar */
#sidebar {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Overlay fade transition */
#sidebar-overlay {
  transition: opacity 0.3s ease-in-out;
}

/* Sidebar scrollbar styling */
#sidebar .overflow-y-auto::-webkit-scrollbar {
  width: 4px;
}

#sidebar .overflow-y-auto::-webkit-scrollbar-track {
  background: transparent;
}

#sidebar .overflow-y-auto::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.3);
  border-radius: 2px;
}

#sidebar .overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.5);
}

/* Responsive text animations */
@media (min-width: 1280px) {
  #sidebar .lg\:hidden.xl\:block {
    animation: fadeInText 0.2s ease-in-out;
  }
}

@keyframes fadeInText {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Focus states for accessibility */
#sidebar a:focus,
#sidebar button:focus {
  outline: 2px solid #3B82F6;
  outline-offset: 2px;
}

/* Active state enhancements */
#sidebar a.bg-blue-50 {
  position: relative;
}

#sidebar a.bg-blue-50::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 20px;
  background: linear-gradient(to bottom, #3B82F6, #8B5CF6);
  border-radius: 0 2px 2px 0;
}

/* Hover effects for navigation items */
#sidebar a:not(.bg-blue-50):hover {
  transform: translateX(2px);
  transition: transform 0.2s ease-in-out;
}

/* Mobile sidebar shadow */
@media (max-width: 1023px) {
  #sidebar {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  }
}

/* Prevent text selection on sidebar toggle */
#sidebar-toggle,
#sidebar-close {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* Loading state for sidebar */
.sidebar-loading {
  opacity: 0.7;
  pointer-events: none;
}

/* Responsive adjustments */
@media (min-width: 1024px) and (max-width: 1279px) {
  /* Compact sidebar on lg screens */
  #sidebar .lg\:hidden.xl\:block {
    display: none !important;
  }
  
  /* Adjust padding for compact mode */
  #sidebar .px-3 {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
    justify-content: center;
  }
}

/* Smooth main content adjustment */
main {
  transition: margin-left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Badge positioning in compact mode */
@media (min-width: 1024px) and (max-width: 1279px) {
  #sidebar .ml-auto {
    display: none;
  }
}

/* Tooltip for compact mode (future enhancement) */
.sidebar-tooltip {
  position: absolute;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  margin-left: 0.5rem;
  padding: 0.5rem 0.75rem;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  font-size: 0.875rem;
  border-radius: 0.375rem;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s ease-in-out;
  z-index: 1000;
}

.sidebar-tooltip::before {
  content: '';
  position: absolute;
  right: 100%;
  top: 50%;
  transform: translateY(-50%);
  border: 4px solid transparent;
  border-right-color: rgba(0, 0, 0, 0.8);
}

/* Show tooltip on hover in compact mode */
@media (min-width: 1024px) and (max-width: 1279px) {
  #sidebar a:hover .sidebar-tooltip {
    opacity: 1;
  }
}

/* Main Content Layout Enhancements */

/* Smooth main content adjustment */
main {
  transition: margin-left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Content container responsive behavior */
.content-container {
  min-height: calc(100vh - 4rem); /* Account for navbar height */
}

/* Responsive content spacing */
@media (max-width: 640px) {
  .content-container {
    padding-left: 1rem;
    padding-right: 1rem;
    padding-top: 1rem;
    padding-bottom: 1rem;
  }
}

@media (min-width: 641px) and (max-width: 1023px) {
  .content-container {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .content-container {
    padding-left: 2rem;
    padding-right: 2rem;
    padding-top: 2rem;
    padding-bottom: 2rem;
  }
}

/* Prevent horizontal overflow */
main {
  overflow-x: hidden;
}

/* Smooth scrolling for main content */
main .overflow-auto {
  scroll-behavior: smooth;
}

/* Custom scrollbar for main content */
main .overflow-auto::-webkit-scrollbar {
  width: 6px;
}

main .overflow-auto::-webkit-scrollbar-track {
  background: transparent;
}

main .overflow-auto::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.4);
  border-radius: 3px;
}

main .overflow-auto::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.6);
}

/* Content width constraints */
.content-wrapper {
  max-width: 100%;
  width: 100%;
}

/* Responsive grid adjustments */
@media (max-width: 768px) {
  .responsive-grid-cols-4 {
    grid-template-columns: repeat(2, 1fr);
  }

  .responsive-grid-cols-5 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .responsive-grid-cols-4,
  .responsive-grid-cols-5 {
    grid-template-columns: 1fr;
  }
}

/* Table responsive behavior */
@media (max-width: 1023px) {
  .responsive-table {
    display: none;
  }

  .responsive-cards {
    display: block;
  }
}

@media (min-width: 1024px) {
  .responsive-table {
    display: block;
  }

  .responsive-cards {
    display: none;
  }
}

/* Card spacing improvements */
.content-card {
  margin-bottom: 1.5rem;
}

@media (min-width: 768px) {
  .content-card {
    margin-bottom: 2rem;
  }
}

/* Form responsive improvements */
.responsive-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

@media (min-width: 768px) {
  .responsive-form {
    flex-direction: row;
    align-items: center;
    gap: 1rem;
  }
}

/* Button group responsive behavior */
.button-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

@media (min-width: 640px) {
  .button-group {
    flex-direction: row;
    gap: 0.75rem;
  }
}

/* Mobile-first content adjustments */
@media (max-width: 1023px) {
  /* Ensure full width on mobile when sidebar is hidden */
  main.mobile-full-width {
    margin-left: 0 !important;
  }
}

/* Loading states for content */
.content-loading {
  opacity: 0.7;
  pointer-events: none;
}

.content-loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Focus management for main content */
main:focus {
  outline: none;
}

/* Print styles */
@media print {
  main {
    margin-left: 0 !important;
    padding: 1rem !important;
  }

  #sidebar,
  .sidebar-overlay {
    display: none !important;
  }
}
