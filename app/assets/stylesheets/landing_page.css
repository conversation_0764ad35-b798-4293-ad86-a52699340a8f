/* Modern Landing Page Custom Styles */

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

/* Global Font Override */
* {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Gradient Text Utility */
.gradient-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
}

/* Glass Effect */
.glass-effect {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.18);
}

/* Enhanced Glass Effect for Dark Backgrounds */
.glass-effect-dark {
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Floating Animation */
.floating-element {
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { 
    transform: translateY(0px); 
  }
  50% { 
    transform: translateY(-20px); 
  }
}

/* Hover Lift Effect */
.hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-8px);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

/* Glow Effect */
.glow-effect {
  box-shadow: 0 0 40px rgba(59, 130, 246, 0.15);
}

.glow-effect:hover {
  box-shadow: 0 0 60px rgba(59, 130, 246, 0.25);
}

/* Blob Animation */
.blob {
  border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
  animation: blob 20s infinite;
}

@keyframes blob {
  0%, 100% { 
    border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%; 
  }
  25% { 
    border-radius: 58% 42% 75% 25% / 76% 46% 54% 24%; 
  }
  50% { 
    border-radius: 50% 50% 33% 67% / 55% 27% 73% 45%; 
  }
  75% { 
    border-radius: 33% 67% 58% 42% / 63% 68% 32% 37%; 
  }
}

/* Pulse Effect */
.pulse-slow {
  animation: pulse 3s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Fade In Animations */
.fade-in {
  opacity: 0;
  animation: fadeIn 0.8s ease-out forwards;
}

.fade-in-up {
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 0.8s ease-out forwards;
}

.fade-in-left {
  opacity: 0;
  transform: translateX(-30px);
  animation: fadeInLeft 0.8s ease-out forwards;
}

.fade-in-right {
  opacity: 0;
  transform: translateX(30px);
  animation: fadeInRight 0.8s ease-out forwards;
}

@keyframes fadeIn {
  to {
    opacity: 1;
  }
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Staggered Animation Delays */
.fade-in-up:nth-child(1) { animation-delay: 0.1s; }
.fade-in-up:nth-child(2) { animation-delay: 0.2s; }
.fade-in-up:nth-child(3) { animation-delay: 0.3s; }
.fade-in-up:nth-child(4) { animation-delay: 0.4s; }
.fade-in-up:nth-child(5) { animation-delay: 0.5s; }
.fade-in-up:nth-child(6) { animation-delay: 0.6s; }

/* Button Enhancements */
.btn-gradient {
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  transition: all 0.3s ease;
}

.btn-gradient:hover {
  background: linear-gradient(135deg, #2563eb 0%, #7c3aed 100%);
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
}

/* Card Hover Effects */
.card-hover {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Testimonial Cards */
.testimonial-card {
  position: relative;
  overflow: hidden;
}

.testimonial-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #ec4899);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.testimonial-card:hover::before {
  transform: scaleX(1);
}

/* Pricing Card Effects */
.pricing-card {
  position: relative;
  transition: all 0.3s ease;
}

.pricing-card:hover {
  transform: translateY(-8px);
}

.pricing-card.featured {
  transform: scale(1.05);
  border-color: #3b82f6;
}

.pricing-card.featured:hover {
  transform: scale(1.08) translateY(-8px);
}

/* Progress Bars */
.progress-bar {
  background: linear-gradient(90deg, #e5e7eb, #f3f4f6);
  overflow: hidden;
}

.progress-fill {
  background: linear-gradient(90deg, #3b82f6, #8b5cf6);
  transition: width 1s ease-in-out;
}

/* Loading States */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Smooth Scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #2563eb, #7c3aed);
}

/* Interactive Elements */
.interactive-hover {
  transition: all 0.2s ease;
}

.interactive-hover:hover {
  transform: scale(1.05);
}

.interactive-hover:active {
  transform: scale(0.98);
}

/* Focus States */
.focus-ring:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

/* Text Selection */
::selection {
  background: rgba(59, 130, 246, 0.2);
  color: #1e40af;
}

/* Mobile Optimizations */
@media (max-width: 768px) {
  .hover-lift:hover {
    transform: none;
    box-shadow: none;
  }
  
  .floating-element {
    animation: none;
  }
  
  .blob {
    animation-duration: 30s;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .floating-element,
  .blob,
  .pulse-slow {
    animation: none;
  }
  
  .hover-lift,
  .card-hover,
  .interactive-hover {
    transition: none;
  }
  
  .hover-lift:hover,
  .card-hover:hover,
  .interactive-hover:hover {
    transform: none;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .glass-effect {
    background: rgba(255, 255, 255, 0.98);
    border: 2px solid #000;
  }
  
  .gradient-text {
    background: #000;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}

/* Print Styles */
@media print {
  .floating-element,
  .blob,
  .hover-lift {
    animation: none;
    transform: none;
    box-shadow: none;
  }
  
  .glass-effect {
    background: #fff;
    border: 1px solid #ccc;
  }
}

/* Dark Mode Support (if implemented) */
@media (prefers-color-scheme: dark) {
  .glass-effect {
    background: rgba(0, 0, 0, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .skeleton {
    background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
  }
  
  ::-webkit-scrollbar-track {
    background: #1f2937;
  }
}

/* Custom Properties for Dynamic Theming */
:root {
  --primary-gradient: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  --secondary-gradient: linear-gradient(135deg, #10b981 0%, #059669 100%);
  --accent-gradient: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  --glass-bg: rgba(255, 255, 255, 0.95);
  --glass-border: rgba(255, 255, 255, 0.18);
  --shadow-color: rgba(0, 0, 0, 0.1);
  --animation-speed: 0.3s;
}

/* Utility Classes */
.gradient-primary {
  background: var(--primary-gradient);
}

.gradient-secondary {
  background: var(--secondary-gradient);
}

.gradient-accent {
  background: var(--accent-gradient);
}

.text-shadow {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.backdrop-blur {
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

/* Component Specific Styles */
.hero-section {
  min-height: 100vh;
  position: relative;
  overflow: hidden;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.testimonials-slider {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.pricing-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}
