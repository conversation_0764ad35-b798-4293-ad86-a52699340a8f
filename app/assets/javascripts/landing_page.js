// Modern Landing Page Interactive Effects
document.addEventListener('DOMContentLoaded', function() {
  
  // Intersection Observer for Fade-in Animations
  const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
  };

  const fadeObserver = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.classList.add('animate-fade-in');
        fadeObserver.unobserve(entry.target);
      }
    });
  }, observerOptions);

  // Observe elements that should fade in
  const fadeElements = document.querySelectorAll('.fade-in-up, .fade-in-left, .fade-in-right, .hover-lift');
  fadeElements.forEach(el => fadeObserver.observe(el));

  // Smooth Scrolling for Anchor Links
  document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
      e.preventDefault();
      const target = document.querySelector(this.getAttribute('href'));
      if (target) {
        target.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
      }
    });
  });

  // Enhanced Button Hover Effects
  const gradientButtons = document.querySelectorAll('.btn-gradient, .bg-gradient-to-r');
  gradientButtons.forEach(button => {
    button.addEventListener('mouseenter', function() {
      this.style.transform = 'translateY(-2px) scale(1.02)';
    });
    
    button.addEventListener('mouseleave', function() {
      this.style.transform = 'translateY(0) scale(1)';
    });
  });

  // Parallax Effect for Background Elements
  const parallaxElements = document.querySelectorAll('.blob, .floating-element');
  let ticking = false;

  function updateParallax() {
    const scrolled = window.pageYOffset;
    const rate = scrolled * -0.5;

    parallaxElements.forEach((element, index) => {
      const speed = 0.5 + (index * 0.1);
      element.style.transform = `translateY(${rate * speed}px)`;
    });
    
    ticking = false;
  }

  window.addEventListener('scroll', function() {
    if (!ticking) {
      requestAnimationFrame(updateParallax);
      ticking = true;
    }
  });

  // Progressive Loading for Images
  const imageObserver = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const img = entry.target;
        img.classList.add('fade-in');
        imageObserver.unobserve(img);
      }
    });
  });

  document.querySelectorAll('img[data-src]').forEach(img => {
    imageObserver.observe(img);
  });

  // Enhanced FAQ Functionality
  const faqItems = document.querySelectorAll('details');
  faqItems.forEach(item => {
    item.addEventListener('toggle', function() {
      if (this.open) {
        // Close other FAQ items
        faqItems.forEach(otherItem => {
          if (otherItem !== this && otherItem.open) {
            otherItem.open = false;
          }
        });
        
        // Smooth scroll to the opened item
        setTimeout(() => {
          this.scrollIntoView({
            behavior: 'smooth',
            block: 'nearest'
          });
        }, 100);
      }
    });
  });

  // Typing Animation for Headlines (if needed)
  function typeWriter(element, text, speed = 100) {
    let i = 0;
    element.innerHTML = '';
    
    function type() {
      if (i < text.length) {
        element.innerHTML += text.charAt(i);
        i++;
        setTimeout(type, speed);
      }
    }
    
    type();
  }

  // Initialize typing effect for specific elements
  const typingElements = document.querySelectorAll('[data-typing]');
  typingElements.forEach(element => {
    const text = element.getAttribute('data-typing');
    if (text) {
      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            typeWriter(element, text);
            observer.unobserve(element);
          }
        });
      });
      observer.observe(element);
    }
  });

  // Progress Bar Animations
  const progressBars = document.querySelectorAll('.progress-fill');
  const progressObserver = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const bar = entry.target;
        const width = bar.getAttribute('data-width') || '100';
        setTimeout(() => {
          bar.style.width = width + '%';
        }, 500);
        progressObserver.unobserve(bar);
      }
    });
  });

  progressBars.forEach(bar => progressObserver.observe(bar));

  // Enhanced Card Interactions
  const cards = document.querySelectorAll('.hover-lift, .card-hover');
  cards.forEach(card => {
    card.addEventListener('mouseenter', function() {
      this.style.zIndex = '10';
    });
    
    card.addEventListener('mouseleave', function() {
      this.style.zIndex = '1';
    });
  });

  // Mobile Touch Interactions
  let touchStartY = 0;
  let touchEndY = 0;

  document.addEventListener('touchstart', function(e) {
    touchStartY = e.changedTouches[0].screenY;
  });

  document.addEventListener('touchend', function(e) {
    touchEndY = e.changedTouches[0].screenY;
    handleSwipe();
  });

  function handleSwipe() {
    const swipeThreshold = 50;
    const diff = touchStartY - touchEndY;
    
    if (Math.abs(diff) > swipeThreshold) {
      // Add any swipe-based interactions here
      if (diff > 0) {
        // Swiped up
      } else {
        // Swiped down
      }
    }
  }

  // Navbar Scroll Effect
  const navbar = document.querySelector('nav');
  let lastScrollY = window.scrollY;

  window.addEventListener('scroll', () => {
    const currentScrollY = window.scrollY;
    
    if (navbar) {
      if (currentScrollY > 100) {
        navbar.classList.add('backdrop-blur-md', 'bg-white/90', 'shadow-lg');
      } else {
        navbar.classList.remove('backdrop-blur-md', 'bg-white/90', 'shadow-lg');
      }
    }
    
    lastScrollY = currentScrollY;
  });

  // Form Enhancements
  const forms = document.querySelectorAll('form');
  forms.forEach(form => {
    const inputs = form.querySelectorAll('input, textarea, select');
    
    inputs.forEach(input => {
      // Add focus effects
      input.addEventListener('focus', function() {
        this.parentElement.classList.add('ring-2', 'ring-blue-500');
      });
      
      input.addEventListener('blur', function() {
        this.parentElement.classList.remove('ring-2', 'ring-blue-500');
      });
      
      // Add validation feedback
      input.addEventListener('invalid', function() {
        this.parentElement.classList.add('ring-2', 'ring-red-500');
      });
      
      input.addEventListener('input', function() {
        if (this.validity.valid) {
          this.parentElement.classList.remove('ring-2', 'ring-red-500');
          this.parentElement.classList.add('ring-2', 'ring-green-500');
        }
      });
    });
  });

  // Stats Counter Animation
  const statsNumbers = document.querySelectorAll('[data-count]');
  const statsObserver = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const element = entry.target;
        const target = parseInt(element.getAttribute('data-count'));
        const duration = 2000; // 2 seconds
        const increment = target / (duration / 16); // 60fps
        let current = 0;
        
        const timer = setInterval(() => {
          current += increment;
          if (current >= target) {
            current = target;
            clearInterval(timer);
          }
          element.textContent = Math.floor(current).toLocaleString();
        }, 16);
        
        statsObserver.unobserve(element);
      }
    });
  });

  statsNumbers.forEach(stat => statsObserver.observe(stat));

  // Pricing Plan Interactions
  const pricingCards = document.querySelectorAll('.pricing-card');
  pricingCards.forEach(card => {
    card.addEventListener('click', function() {
      // Remove active state from all cards
      pricingCards.forEach(c => c.classList.remove('ring-4', 'ring-blue-500'));
      
      // Add active state to clicked card
      this.classList.add('ring-4', 'ring-blue-500');
    });
  });

  // Loading State Management
  function showLoadingState(element) {
    element.classList.add('opacity-50', 'pointer-events-none');
    element.innerHTML = '<span class="flex items-center justify-center"><svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>Loading...</span>';
  }

  function hideLoadingState(element, originalText) {
    element.classList.remove('opacity-50', 'pointer-events-none');
    element.innerHTML = originalText;
  }

  // Enhanced Button Click Handlers
  document.querySelectorAll('button[type="submit"], .btn-loading').forEach(button => {
    button.addEventListener('click', function(e) {
      if (this.closest('form')) {
        const form = this.closest('form');
        if (form.checkValidity()) {
          const originalText = this.innerHTML;
          showLoadingState(this);
          
          // Simulate async operation
          setTimeout(() => {
            hideLoadingState(this, originalText);
          }, 2000);
        }
      }
    });
  });

  // Performance Monitoring
  function measurePerformance() {
    if ('performance' in window) {
      window.addEventListener('load', () => {
        const perfData = performance.getEntriesByType('navigation')[0];
        const loadTime = perfData.loadEventEnd - perfData.loadEventStart;
        
        // Log performance metrics (you can send this to analytics)
        console.log('Page load time:', loadTime + 'ms');
        
        // Add performance indicator for slow connections
        if (loadTime > 3000) {
          const slowConnectionNotice = document.createElement('div');
          slowConnectionNotice.className = 'fixed bottom-4 right-4 bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-2 rounded-lg text-sm z-50';
          slowConnectionNotice.innerHTML = 'Slow connection detected. Some features may load gradually.';
          document.body.appendChild(slowConnectionNotice);
          
          setTimeout(() => {
            slowConnectionNotice.remove();
          }, 5000);
        }
      });
    }
  }

  measurePerformance();

  // Accessibility Enhancements
  document.addEventListener('keydown', function(e) {
    // ESC key closes modals and dropdowns
    if (e.key === 'Escape') {
      document.querySelectorAll('.modal, .dropdown').forEach(element => {
        element.classList.add('hidden');
      });
    }
    
    // Enter key activates buttons
    if (e.key === 'Enter' && e.target.getAttribute('role') === 'button') {
      e.target.click();
    }
  });

  // Reduce motion for users who prefer it
  if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
    document.documentElement.style.scrollBehavior = 'auto';
    
    // Disable animations
    const style = document.createElement('style');
    style.textContent = `
      *, *::before, *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
      }
    `;
    document.head.appendChild(style);
  }

  // Console welcome message
  console.log(
    '%cWelcome to AI Marketing Hub! 🚀',
    'color: #3B82F6; font-size: 16px; font-weight: bold;'
  );
  
  console.log(
    '%cBuilt with Rails 8, Hotwire, and modern web technologies.',
    'color: #6B7280; font-size: 12px;'
  );

});

// Export functions for testing or external use
window.AIMarketingHub = {
  typeWriter: typeWriter,
  showLoadingState: showLoadingState,
  hideLoadingState: hideLoadingState
};
