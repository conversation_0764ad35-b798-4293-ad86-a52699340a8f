# frozen_string_literal: true

require "dry/validation"

##
# Campaign Contract
#
# Validates campaign input parameters using dry-validation.
# Provides comprehensive validation rules for campaign creation and updates.
#
# @example Basic usage
#   contract = CampaignContract.new
#   result = contract.call(params)
#   if result.success?
#     # Process valid data
#     campaign = Campaign.create!(result.to_h)
#   else
#     # Handle validation errors
#     errors = result.errors.to_h
#   end
#
class CampaignContract < Dry::Validation::Contract
  # Define the parameter schema
  params do
    required(:name).filled(:string)
    required(:campaign_type).filled(:string)
    required(:target_audience).filled(:string)
    required(:budget_cents).filled(:integer)
    optional(:start_date).maybe(:date)
    optional(:end_date).maybe(:date)
    optional(:description).maybe(:string)
  end

  # Custom validation rules
  rule(:name) do
    key.failure("size cannot be greater than 255") if value && value.length > 255
  end

  rule(:campaign_type) do
    allowed_types = %w[email social seo multi_channel]
    unless allowed_types.include?(value)
      key.failure("must be one of: #{allowed_types.join(', ')}")
    end
  end

  rule(:budget_cents) do
    key.failure("must be greater than or equal to 0") if value && value < 0
  end

  rule(:target_audience) do
    if value && value.length < 3
      key.failure("size cannot be less than 3")
    end
  end

  rule(:start_date) do
    if value && value < Date.current
      key.failure("cannot be in the past")
    end
  end

  rule(:start_date, :end_date) do
    if values[:start_date] && values[:end_date]
      if values[:end_date] <= values[:start_date]
        key(:end_date).failure("must be after start date")
      end
    end
  end

  rule(:description) do
    if value && value.length > 1000
      key.failure("size cannot be greater than 1000")
    end
  end
end
