# frozen_string_literal: true

# Background job for individual specialist agent content generation
class ContentGenerationJob < ApplicationJob
  queue_as :content_generation
  retry_on StandardError, wait: :exponentially_longer, attempts: 2
  retry_on OpenAiService::RateLimitError, wait: 1.minute, attempts: 3

  def perform(campaign_id, agent_type, context: {})
    campaign = Campaign.find(campaign_id)

    ActsAsTenant.with_tenant(campaign.tenant) do
      agent_service = build_agent_service(agent_type, campaign)

      Rails.logger.info "🤖 Starting #{agent_type} content generation for campaign #{campaign_id}"

      result = agent_service.generate_campaign_content(context.with_indifferent_access)

      if result[:status] == "success"
        Rails.logger.info "✅ #{agent_type.capitalize} content generated successfully"
        broadcast_content_update(campaign, agent_type, result)
      else
        Rails.logger.error "❌ #{agent_type.capitalize} content generation failed: #{result[:message]}"
        broadcast_content_error(campaign, agent_type, result)
      end
    end
  rescue => error
    Rails.logger.error "Content generation job failed: #{error.message}"
    Rails.logger.error error.backtrace.join("\n")

    broadcast_content_error(
      Campaign.find(campaign_id),
      agent_type,
      { status: "error", message: error.message }
    )
    raise
  end

  private

  def build_agent_service(agent_type, campaign)
    case agent_type.to_s
    when "email"
      EmailSpecialistAgentService.new(campaign: campaign)
    when "social"
      SocialMediaAgentService.new(campaign: campaign)
    when "seo"
      SeoSpecialistAgentService.new(campaign: campaign)
    else
      raise ArgumentError, "Unknown agent type: #{agent_type}"
    end
  end

  def broadcast_content_update(campaign, agent_type, result)
    Turbo::StreamsChannel.broadcast_update_to(
      "campaign_#{campaign.id}",
      target: "#{agent_type}-content-status",
      partial: "agent_workflows/content_status",
      locals: {
        campaign: campaign,
        agent_type: agent_type,
        status: "completed",
        result: result
      }
    )
  end

  def broadcast_content_error(campaign, agent_type, result)
    Turbo::StreamsChannel.broadcast_update_to(
      "campaign_#{campaign.id}",
      target: "#{agent_type}-content-status",
      partial: "agent_workflows/content_status",
      locals: {
        campaign: campaign,
        agent_type: agent_type,
        status: "error",
        result: result
      }
    )
  end
end
