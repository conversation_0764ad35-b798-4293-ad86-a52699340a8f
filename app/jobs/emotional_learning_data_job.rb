# frozen_string_literal: true

# EmotionalLearningDataJob - Collects and processes data for improving emotional AI models
#
# This job supports continuous learning and improvement of the Marketing Therapist AI by:
# - Collecting successful emotional predictions and their outcomes
# - Storing training data for model refinement
# - Identifying patterns that improve emotional state detection accuracy
# - Feeding back customer response data to enhance future predictions
class EmotionalLearningDataJob < ApplicationJob
  queue_as :low_priority
  retry_on StandardError, wait: :exponentially_longer, attempts: 2

  def perform(learning_data)
    @learning_data = learning_data
    @tenant = Tenant.find(@learning_data[:tenant_id])

    Rails.logger.info "Processing emotional learning data for customer #{@learning_data[:customer_identifier]}"

    begin
      # Step 1: Validate and structure learning data
      structured_data = structure_learning_data

      # Step 2: Store for model training
      store_training_data(structured_data)

      # Step 3: Update accuracy metrics
      update_accuracy_metrics(structured_data)

      # Step 4: Identify improvement opportunities
      identify_improvement_opportunities(structured_data)

      # Step 5: Trigger model retraining if enough new data
      check_retraining_threshold

      Rails.logger.info "Successfully processed emotional learning data"

    rescue => error
      Rails.logger.error "EmotionalLearningDataJob failed: #{error.message}"
      Rails.logger.error error.backtrace.join("\n")

      # Continue execution even if learning fails - don't break main system
      Rails.logger.warn "Learning data collection failed but system continues normally"
    end
  end

  # Process customer response feedback to improve predictions
  def self.process_customer_feedback(tenant_id:, customer_identifier:, predicted_emotion:, actual_response:, campaign_id: nil)
    feedback_data = {
      type: "customer_feedback",
      tenant_id: tenant_id,
      customer_identifier: customer_identifier,
      predicted_emotion: predicted_emotion,
      actual_response: actual_response,
      campaign_id: campaign_id,
      feedback_timestamp: Time.current
    }

    perform_later(feedback_data)
  end

  # Process campaign outcome data to learn from results
  def self.process_campaign_outcome(tenant_id:, campaign_id:, customer_responses:, emotional_predictions:)
    outcome_data = {
      type: "campaign_outcome",
      tenant_id: tenant_id,
      campaign_id: campaign_id,
      customer_responses: customer_responses,
      emotional_predictions: emotional_predictions,
      outcome_timestamp: Time.current
    }

    perform_later(outcome_data)
  end

  # Process A/B test results for emotional approaches
  def self.process_ab_test_results(tenant_id:, test_id:, emotional_approach_a:, emotional_approach_b:, results:)
    ab_test_data = {
      type: "ab_test_results",
      tenant_id: tenant_id,
      test_id: test_id,
      emotional_approach_a: emotional_approach_a,
      emotional_approach_b: emotional_approach_b,
      results: results,
      test_timestamp: Time.current
    }

    perform_later(ab_test_data)
  end

  private

  def structure_learning_data
    case @learning_data[:type]
    when "customer_feedback"
      structure_feedback_data
    when "campaign_outcome"
      structure_outcome_data
    when "ab_test_results"
      structure_ab_test_data
    else
      structure_prediction_data
    end
  end

  def structure_feedback_data
    {
      data_type: "feedback",
      tenant_id: @learning_data[:tenant_id],
      customer_identifier: @learning_data[:customer_identifier],
      prediction: {
        emotion: @learning_data[:predicted_emotion],
        confidence: extract_confidence_score,
        timestamp: @learning_data[:feedback_timestamp]
      },
      actual_outcome: {
        response_type: @learning_data[:actual_response][:type],
        engagement_level: @learning_data[:actual_response][:engagement_level],
        sentiment: @learning_data[:actual_response][:sentiment],
        conversion: @learning_data[:actual_response][:converted] || false
      },
      accuracy_metrics: calculate_prediction_accuracy,
      learning_value: calculate_learning_value
    }
  end

  def structure_outcome_data
    {
      data_type: "campaign_outcome",
      tenant_id: @learning_data[:tenant_id],
      campaign_id: @learning_data[:campaign_id],
      sample_size: @learning_data[:customer_responses].size,
      aggregate_metrics: calculate_aggregate_metrics,
      emotional_distribution: analyze_emotional_distribution,
      outcome_patterns: identify_outcome_patterns,
      learning_insights: extract_campaign_insights
    }
  end

  def structure_ab_test_data
    {
      data_type: "ab_test",
      tenant_id: @learning_data[:tenant_id],
      test_id: @learning_data[:test_id],
      approach_comparison: {
        approach_a: @learning_data[:emotional_approach_a],
        approach_b: @learning_data[:emotional_approach_b],
        results_a: @learning_data[:results][:approach_a],
        results_b: @learning_data[:results][:approach_b]
      },
      statistical_significance: calculate_statistical_significance,
      winning_approach: determine_winning_approach,
      confidence_interval: calculate_confidence_interval,
      learning_recommendations: generate_ab_recommendations
    }
  end

  def structure_prediction_data
    {
      data_type: "prediction",
      tenant_id: @learning_data[:tenant_id],
      customer_identifier: @learning_data[:customer_identifier],
      prediction: {
        emotion: @learning_data[:predicted_emotion],
        confidence: @learning_data[:confidence_score],
        timestamp: @learning_data[:timestamp]
      },
      input_signals: {
        source: @learning_data[:source],
        behavioral_signals: anonymize_behavioral_signals(@learning_data[:behavioral_signals])
      },
      context: extract_prediction_context,
      quality_score: assess_prediction_quality
    }
  end

  def store_training_data(structured_data)
    # Store in a learning database or data warehouse
    # For this implementation, we'll store in the settings of a special learning record

    learning_record = find_or_create_learning_record(structured_data[:data_type])

    # Add to training dataset
    training_data = learning_record.settings["training_data"] || []
    training_data << structured_data

    # Keep only recent data (last 10,000 records per type)
    training_data = training_data.last(10_000)

    learning_record.update!(
      settings: learning_record.settings.merge({
        training_data: training_data,
        last_updated: Time.current,
        total_samples: training_data.size
      })
    )
  end

  def find_or_create_learning_record(data_type)
    # Use the emotional_resonance_profiles table to store learning data
    EmotionalResonanceProfile.find_or_create_by(
      tenant: @tenant,
      name: "learning_data_#{data_type}",
      primary_emotion: "trust" # Placeholder
    ) do |profile|
      profile.description = "Emotional AI learning data collection for #{data_type}"
      profile.created_by = @tenant.users.first # System user
      profile.emotion_weights = {}
      profile.tone_preferences = {}
      profile.content_guidelines = {}
      profile.psychographic_traits = {}
      profile.settings = {
        learning_data_type: data_type,
        training_data: [],
        created_at: Time.current
      }
    end
  end

  def update_accuracy_metrics(structured_data)
    return unless structured_data[:data_type] == "feedback"

    accuracy_record = find_or_create_accuracy_record
    current_metrics = accuracy_record.settings["accuracy_metrics"] || {}

    # Update overall accuracy
    total_predictions = (current_metrics["total_predictions"] || 0) + 1
    correct_predictions = (current_metrics["correct_predictions"] || 0)

    if structured_data[:accuracy_metrics][:prediction_correct]
      correct_predictions += 1
    end

    overall_accuracy = (correct_predictions.to_f / total_predictions * 100).round(2)

    # Update emotion-specific accuracy
    emotion = structured_data[:prediction][:emotion]
    emotion_metrics = current_metrics["by_emotion"] || {}
    emotion_total = (emotion_metrics.dig(emotion, "total") || 0) + 1
    emotion_correct = (emotion_metrics.dig(emotion, "correct") || 0)

    if structured_data[:accuracy_metrics][:prediction_correct]
      emotion_correct += 1
    end

    emotion_accuracy = (emotion_correct.to_f / emotion_total * 100).round(2)

    emotion_metrics[emotion] = {
      total: emotion_total,
      correct: emotion_correct,
      accuracy: emotion_accuracy
    }

    # Update confidence-based accuracy
    confidence_range = categorize_confidence(structured_data[:prediction][:confidence])
    confidence_metrics = current_metrics["by_confidence"] || {}
    confidence_total = (confidence_metrics.dig(confidence_range, "total") || 0) + 1
    confidence_correct = (confidence_metrics.dig(confidence_range, "correct") || 0)

    if structured_data[:accuracy_metrics][:prediction_correct]
      confidence_correct += 1
    end

    confidence_accuracy = (confidence_correct.to_f / confidence_total * 100).round(2)

    confidence_metrics[confidence_range] = {
      total: confidence_total,
      correct: confidence_correct,
      accuracy: confidence_accuracy
    }

    # Save updated metrics
    accuracy_record.update!(
      settings: accuracy_record.settings.merge({
        accuracy_metrics: {
          overall_accuracy: overall_accuracy,
          total_predictions: total_predictions,
          correct_predictions: correct_predictions,
          by_emotion: emotion_metrics,
          by_confidence: confidence_metrics,
          last_updated: Time.current
        }
      })
    )
  end

  def find_or_create_accuracy_record
    EmotionalResonanceProfile.find_or_create_by(
      tenant: @tenant,
      name: "accuracy_metrics",
      primary_emotion: "trust"
    ) do |profile|
      profile.description = "Emotional AI accuracy tracking"
      profile.created_by = @tenant.users.first
      profile.emotion_weights = {}
      profile.tone_preferences = {}
      profile.content_guidelines = {}
      profile.psychographic_traits = {}
      profile.settings = { accuracy_metrics: {} }
    end
  end

  def identify_improvement_opportunities(structured_data)
    return unless structured_data[:data_type] == "feedback"

    # Identify patterns in incorrect predictions
    if !structured_data[:accuracy_metrics][:prediction_correct]
      opportunity_record = find_or_create_opportunity_record
      opportunities = opportunity_record.settings["improvement_opportunities"] || []

      opportunity = {
        predicted_emotion: structured_data[:prediction][:emotion],
        actual_response: structured_data[:actual_outcome],
        confidence: structured_data[:prediction][:confidence],
        improvement_areas: identify_specific_improvement_areas(structured_data),
        timestamp: Time.current
      }

      opportunities << opportunity
      opportunities = opportunities.last(1000) # Keep recent opportunities

      opportunity_record.update!(
        settings: opportunity_record.settings.merge({
          improvement_opportunities: opportunities,
          last_updated: Time.current
        })
      )
    end
  end

  def find_or_create_opportunity_record
    EmotionalResonanceProfile.find_or_create_by(
      tenant: @tenant,
      name: "improvement_opportunities",
      primary_emotion: "trust"
    ) do |profile|
      profile.description = "Emotional AI improvement opportunities"
      profile.created_by = @tenant.users.first
      profile.emotion_weights = {}
      profile.tone_preferences = {}
      profile.content_guidelines = {}
      profile.psychographic_traits = {}
      profile.settings = { improvement_opportunities: [] }
    end
  end

  def check_retraining_threshold
    # Check if we have enough new data to trigger model retraining
    accuracy_record = find_or_create_accuracy_record
    total_predictions = accuracy_record.settings.dig("accuracy_metrics", "total_predictions") || 0

    # Trigger retraining every 1000 new predictions
    if total_predictions > 0 && (total_predictions % 1000).zero?
      Rails.logger.info "Retraining threshold reached (#{total_predictions} predictions) - triggering model update"

      # In production, this would trigger ML model retraining
      AlertLog.create!(
        tenant: @tenant,
        alert_type: "model_retraining_triggered",
        severity: "info",
        message: "Emotional AI model retraining triggered due to sufficient new data",
        metadata: {
          total_predictions: total_predictions,
          trigger_threshold: 1000
        }
      )
    end
  end

  # Helper methods

  def extract_confidence_score
    @learning_data[:confidence_score] || 50.0
  end

  def calculate_prediction_accuracy
    # This would compare predicted vs actual outcomes
    # For now, return a basic structure
    {
      prediction_correct: determine_prediction_correctness,
      confidence_appropriate: assess_confidence_appropriateness,
      outcome_match_score: calculate_outcome_match_score
    }
  end

  def determine_prediction_correctness
    # Simple heuristic - in production would be more sophisticated
    predicted = @learning_data[:predicted_emotion]
    actual_sentiment = @learning_data.dig(:actual_response, :sentiment)

    case predicted
    when "joy", "trust", "anticipation"
      actual_sentiment == "positive"
    when "anger", "fear", "sadness", "disgust"
      actual_sentiment == "negative"
    when "surprise"
      true # Surprise can lead to any sentiment
    else
      actual_sentiment == "neutral"
    end
  end

  def assess_confidence_appropriateness
    confidence = extract_confidence_score
    prediction_correct = determine_prediction_correctness

    if prediction_correct
      confidence >= 60.0 # Should have been confident if correct
    else
      confidence < 70.0 # Should have been less confident if wrong
    end
  end

  def calculate_outcome_match_score
    # Score how well the outcome matched expectations (0-100)
    engagement = @learning_data.dig(:actual_response, :engagement_level) || 50
    conversion = @learning_data.dig(:actual_response, :converted) ? 100 : 0

    (engagement + conversion) / 2.0
  end

  def calculate_learning_value
    # How valuable is this data point for learning (0-100)
    base_value = 50

    # Higher value for edge cases
    confidence = extract_confidence_score
    if confidence < 60.0 || confidence > 90.0
      base_value += 20
    end

    # Higher value for incorrect predictions (more to learn from)
    unless determine_prediction_correctness
      base_value += 30
    end

    [ base_value, 100 ].min
  end

  def calculate_aggregate_metrics
    responses = @learning_data[:customer_responses]

    {
      total_responses: responses.size,
      average_engagement: calculate_average_engagement(responses),
      conversion_rate: calculate_conversion_rate(responses),
      sentiment_distribution: calculate_sentiment_distribution(responses)
    }
  end

  def analyze_emotional_distribution
    predictions = @learning_data[:emotional_predictions]

    predictions.group_by { |p| p[:emotion] }.transform_values do |group|
      {
        count: group.size,
        average_confidence: group.sum { |p| p[:confidence] } / group.size.to_f,
        accuracy_rate: calculate_group_accuracy(group)
      }
    end
  end

  def identify_outcome_patterns
    # Identify patterns between emotional predictions and outcomes
    patterns = []

    # Pattern: High confidence predictions with low engagement
    # Pattern: Specific emotions that consistently over/under-perform
    # Pattern: Time-based patterns

    patterns
  end

  def extract_campaign_insights
    # Extract actionable insights from campaign data
    insights = []

    aggregate = calculate_aggregate_metrics

    if aggregate[:conversion_rate] > 5.0
      insights << "High conversion rate suggests effective emotional targeting"
    elsif aggregate[:conversion_rate] < 1.0
      insights << "Low conversion rate indicates emotional mismatch"
    end

    insights
  end

  def categorize_confidence(confidence_score)
    case confidence_score
    when 0..40 then "low"
    when 41..60 then "medium_low"
    when 61..80 then "medium_high"
    when 81..100 then "high"
    else "unknown"
    end
  end

  def identify_specific_improvement_areas(structured_data)
    areas = []

    # Confidence calibration issues
    confidence = structured_data[:prediction][:confidence]
    if confidence > 80.0 && !structured_data[:accuracy_metrics][:prediction_correct]
      areas << "overconfidence_in_prediction"
    elsif confidence < 50.0 && structured_data[:accuracy_metrics][:prediction_correct]
      areas << "underconfidence_in_prediction"
    end

    # Emotion-specific issues
    predicted_emotion = structured_data[:prediction][:emotion]
    if %w[anger fear sadness].include?(predicted_emotion)
      areas << "negative_emotion_detection"
    elsif %w[joy trust].include?(predicted_emotion)
      areas << "positive_emotion_detection"
    end

    areas
  end

  def anonymize_behavioral_signals(signals)
    # Remove personally identifiable information from signals
    anonymized = signals.deep_dup

    # Remove specific values, keep only patterns
    anonymized.each do |category, category_signals|
      category_signals.each do |signal, value|
        if value.is_a?(Numeric)
          # Round to ranges to preserve privacy
          anonymized[category][signal] = anonymize_numeric_value(value)
        elsif value.is_a?(String)
          anonymized[category][signal] = anonymize_string_value(value)
        end
      end
    end

    anonymized
  end

  def anonymize_numeric_value(value)
    # Convert to ranges for privacy
    case value
    when 0..10 then "very_low"
    when 11..30 then "low"
    when 31..70 then "medium"
    when 71..90 then "high"
    when 91..100 then "very_high"
    else "unknown"
    end
  end

  def anonymize_string_value(value)
    # Remove specific identifiers, keep general categories
    case value.downcase
    when /positive|good|great|excellent/ then "positive_sentiment"
    when /negative|bad|poor|terrible/ then "negative_sentiment"
    when /neutral|okay|average/ then "neutral_sentiment"
    else "unknown_sentiment"
    end
  end

  def extract_prediction_context
    {
      data_source: @learning_data[:source],
      prediction_time: @learning_data[:timestamp],
      tenant_context: extract_tenant_context
    }
  end

  def extract_tenant_context
    {
      tenant_size: categorize_tenant_size,
      industry: @tenant.settings["industry"] || "unknown",
      customer_base_size: categorize_customer_base
    }
  end

  def categorize_tenant_size
    user_count = @tenant.users.count
    case user_count
    when 1..5 then "small"
    when 6..25 then "medium"
    when 26..100 then "large"
    else "enterprise"
    end
  end

  def categorize_customer_base
    # This would look at actual customer data
    # For now, return a placeholder
    "medium"
  end

  def assess_prediction_quality
    # Assess the quality of input data for prediction
    confidence = @learning_data[:confidence_score] || 0
    signal_count = @learning_data[:behavioral_signals]&.keys&.size || 0

    base_quality = confidence / 100.0
    signal_bonus = [ signal_count * 0.1, 0.4 ].min

    ((base_quality + signal_bonus) * 100).round(2)
  end

  # Campaign outcome helper methods

  def calculate_average_engagement(responses)
    return 0 if responses.empty?

    total_engagement = responses.sum { |r| r[:engagement_level] || 0 }
    (total_engagement.to_f / responses.size).round(2)
  end

  def calculate_conversion_rate(responses)
    return 0 if responses.empty?

    conversions = responses.count { |r| r[:converted] }
    (conversions.to_f / responses.size * 100).round(2)
  end

  def calculate_sentiment_distribution(responses)
    return {} if responses.empty?

    sentiment_counts = responses.group_by { |r| r[:sentiment] || "unknown" }
                              .transform_values(&:size)

    total = responses.size
    sentiment_counts.transform_values { |count| (count.to_f / total * 100).round(2) }
  end

  def calculate_group_accuracy(group)
    # Calculate accuracy for a group of predictions
    # This would compare predictions to actual outcomes
    75.0 # Placeholder
  end

  # A/B test helper methods

  def calculate_statistical_significance
    # Calculate if the difference between A and B is statistically significant
    # This would use proper statistical tests
    results_a = @learning_data[:results][:approach_a]
    results_b = @learning_data[:results][:approach_b]

    # Placeholder calculation
    sample_size_a = results_a[:sample_size] || 0
    sample_size_b = results_b[:sample_size] || 0

    if sample_size_a >= 100 && sample_size_b >= 100
      "significant"
    elsif sample_size_a >= 50 && sample_size_b >= 50
      "marginal"
    else
      "insufficient_data"
    end
  end

  def determine_winning_approach
    results_a = @learning_data[:results][:approach_a]
    results_b = @learning_data[:results][:approach_b]

    score_a = calculate_approach_score(results_a)
    score_b = calculate_approach_score(results_b)

    if score_a > score_b
      "approach_a"
    elsif score_b > score_a
      "approach_b"
    else
      "tie"
    end
  end

  def calculate_approach_score(results)
    engagement = results[:engagement_rate] || 0
    conversion = results[:conversion_rate] || 0

    # Weighted score: 60% engagement, 40% conversion
    (engagement * 0.6) + (conversion * 0.4)
  end

  def calculate_confidence_interval
    # Calculate confidence interval for the difference
    # This would use proper statistical methods
    {
      lower_bound: -5.0,
      upper_bound: 15.0,
      confidence_level: 95
    }
  end

  def generate_ab_recommendations
    winning_approach = determine_winning_approach
    significance = calculate_statistical_significance

    recommendations = []

    if significance == "significant"
      recommendations << "Use #{winning_approach} as the primary emotional approach"
      recommendations << "Scale winning approach to broader audience"
    elsif significance == "marginal"
      recommendations << "Continue testing with larger sample size"
      recommendations << "Monitor results for significance"
    else
      recommendations << "Increase sample size before drawing conclusions"
      recommendations << "Consider testing different emotional approaches"
    end

    recommendations
  end
end
