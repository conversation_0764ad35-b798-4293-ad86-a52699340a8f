# frozen_string_literal: true

# EmotionalStateTrackingJob - Continuous emotional state monitoring and updates
#
# This job provides the continuous emotional intelligence backbone by:
# - Collecting behavioral signals from multiple sources
# - Updating customer emotional states in real-time
# - Triggering adaptive campaign adjustments when needed
# - Learning from interaction patterns to improve accuracy
class EmotionalStateTrackingJob < ApplicationJob
  queue_as :default
  retry_on StandardError, wait: :exponentially_longer, attempts: 3

  def perform(tenant_id:, customer_identifier:, behavioral_signals: {}, source: "system")
    @tenant = Tenant.find(tenant_id)
    @customer_identifier = customer_identifier
    @behavioral_signals = behavioral_signals
    @source = source

    Rails.logger.info "Processing emotional state update for customer #{@customer_identifier} from #{@source}"

    begin
      # Step 1: Find or create customer emotional state
      emotional_state = find_or_create_emotional_state

      # Step 2: Analyze new behavioral signals
      analysis_result = analyze_emotional_state(emotional_state)

      if analysis_result[:success]
        # Step 3: Update emotional state with new analysis
        update_success = emotional_state.update_emotional_state!(@behavioral_signals)

        if update_success
          # Step 4: Check for significant changes that require campaign adaptation
          if emotional_state.significant_emotional_change?
            Rails.logger.info "Significant emotional change detected for #{@customer_identifier}"
            trigger_adaptive_campaign_adjustment(emotional_state, analysis_result)
          end

          # Step 5: Update learning models for future accuracy
          update_learning_models(emotional_state, analysis_result)

          Rails.logger.info "Successfully updated emotional state: #{emotional_state.current_emotion} (#{emotional_state.confidence_score}%)"
        else
          Rails.logger.warn "Failed to update emotional state for customer #{@customer_identifier}"
        end
      else
        Rails.logger.error "Emotional state analysis failed: #{analysis_result[:error]}"
      end

    rescue => error
      Rails.logger.error "EmotionalStateTrackingJob failed: #{error.message}"
      Rails.logger.error error.backtrace.join("\n")

      # Log error for monitoring
      AlertLog.create!(
        tenant: @tenant,
        alert_type: "emotional_tracking_failure",
        severity: "medium",
        message: "Failed to process emotional state tracking: #{error.message}",
        metadata: {
          customer_identifier: @customer_identifier,
          source: @source,
          error_class: error.class.name,
          behavioral_signals_present: @behavioral_signals.present?
        }
      )

      raise
    end
  end

  # Process batch updates for multiple customers
  def self.perform_batch(tenant_id:, customer_updates:, source: "batch_system")
    customer_updates.each do |update|
      perform_later(
        tenant_id: tenant_id,
        customer_identifier: update[:customer_identifier],
        behavioral_signals: update[:behavioral_signals] || {},
        source: source
      )
    end
  end

  # Process email engagement signals
  def self.process_email_engagement(tenant_id:, customer_identifier:, engagement_data:)
    behavioral_signals = {
      email_engagement: {
        open_rate: engagement_data[:opened] ? 100 : 0,
        click_rate: engagement_data[:clicked] ? 100 : 0,
        time_to_open: engagement_data[:time_to_open_minutes] || 0,
        time_spent_reading: engagement_data[:read_time_seconds] || 0,
        forward_rate: engagement_data[:forwarded] ? 100 : 0,
        unsubscribe_action: engagement_data[:unsubscribed] ? 100 : 0
      }
    }

    perform_later(
      tenant_id: tenant_id,
      customer_identifier: customer_identifier,
      behavioral_signals: behavioral_signals,
      source: "email_engagement"
    )
  end

  # Process website behavior signals
  def self.process_website_behavior(tenant_id:, customer_identifier:, session_data:)
    behavioral_signals = {
      website_behavior: {
        session_duration: session_data[:duration_seconds] || 0,
        pages_per_session: session_data[:page_views] || 0,
        bounce_rate: session_data[:bounced] ? 100 : 0,
        scroll_depth: session_data[:max_scroll_percentage] || 0,
        click_patterns: session_data[:click_count] || 0,
        exit_behavior: determine_exit_behavior(session_data)
      }
    }

    perform_later(
      tenant_id: tenant_id,
      customer_identifier: customer_identifier,
      behavioral_signals: behavioral_signals,
      source: "website_analytics"
    )
  end

  # Process social media interaction signals
  def self.process_social_interaction(tenant_id:, customer_identifier:, social_data:)
    behavioral_signals = {
      social_interaction: {
        engagement_rate: calculate_social_engagement_rate(social_data),
        sentiment_of_comments: analyze_comment_sentiment(social_data[:comments] || []),
        sharing_behavior: social_data[:shares] || 0,
        response_time: social_data[:avg_response_time_minutes] || 0
      }
    }

    perform_later(
      tenant_id: tenant_id,
      customer_identifier: customer_identifier,
      behavioral_signals: behavioral_signals,
      source: "social_media"
    )
  end

  # Process purchase behavior signals
  def self.process_purchase_behavior(tenant_id:, customer_identifier:, purchase_data:)
    behavioral_signals = {
      purchase_patterns: {
        purchase_frequency: purchase_data[:purchases_last_30_days] || 0,
        cart_abandonment: purchase_data[:abandoned_carts] || 0,
        time_to_purchase: purchase_data[:avg_time_to_purchase_hours] || 0,
        price_sensitivity: calculate_price_sensitivity(purchase_data),
        product_category_shifts: analyze_category_shifts(purchase_data)
      }
    }

    perform_later(
      tenant_id: tenant_id,
      customer_identifier: customer_identifier,
      behavioral_signals: behavioral_signals,
      source: "purchase_analytics"
    )
  end

  # Process customer support interaction signals
  def self.process_support_interaction(tenant_id:, customer_identifier:, support_data:)
    behavioral_signals = {
      support_interaction: {
        ticket_frequency: support_data[:tickets_last_30_days] || 0,
        issue_complexity: rate_issue_complexity(support_data[:issues] || []),
        resolution_satisfaction: support_data[:satisfaction_score] || 50,
        communication_tone: analyze_communication_tone(support_data[:messages] || [])
      }
    }

    perform_later(
      tenant_id: tenant_id,
      customer_identifier: customer_identifier,
      behavioral_signals: behavioral_signals,
      source: "customer_support"
    )
  end

  private

  def find_or_create_emotional_state
    CustomerEmotionalState.find_or_create_by(
      tenant: @tenant,
      customer_identifier: @customer_identifier
    ) do |state|
      # Initialize with neutral emotional state
      state.current_emotion = "trust" # Start with trust as neutral positive
      state.emotional_intensity = "moderate"
      state.confidence_score = 50.0
      state.context_data = { initialized_by: @source }
      state.behavioral_signals = {}
      state.interaction_history = []
      state.last_interaction_at = Time.current
    end
  end

  def analyze_emotional_state(emotional_state)
    analyzer = EmotionalStateAnalyzer.new(
      customer_identifier: @customer_identifier,
      behavioral_signals: merge_behavioral_signals(emotional_state.behavioral_signals, @behavioral_signals),
      interaction_history: emotional_state.interaction_history,
      tenant: @tenant
    )

    analyzer.analyze
  end

  def merge_behavioral_signals(existing_signals, new_signals)
    # Merge new signals with existing ones, giving more weight to recent signals
    merged_signals = existing_signals.deep_dup

    new_signals.each do |category, signals|
      category_key = category.to_s
      merged_signals[category_key] ||= {}

      signals.each do |signal_type, value|
        signal_key = signal_type.to_s

        # Apply temporal weighting - recent signals get more influence
        existing_value = merged_signals[category_key][signal_key]
        if existing_value.present?
          # Weight new signal at 70%, existing at 30%
          merged_signals[category_key][signal_key] = (value * 0.7) + (existing_value * 0.3)
        else
          merged_signals[category_key][signal_key] = value
        end
      end
    end

    merged_signals
  end

  def trigger_adaptive_campaign_adjustment(emotional_state, analysis_result)
    AdaptiveCampaignAdjustmentJob.perform_later(
      customer_emotional_state_id: emotional_state.id,
      emotional_analysis: analysis_result
    )
  end

  def update_learning_models(emotional_state, analysis_result)
    # Update machine learning models based on the analysis results
    # This would feed back into the system to improve future predictions

    learning_data = {
      customer_identifier: @customer_identifier,
      behavioral_signals: @behavioral_signals,
      predicted_emotion: analysis_result[:primary_emotion],
      confidence_score: analysis_result[:confidence],
      source: @source,
      timestamp: Time.current,
      tenant_id: @tenant.id
    }

    # Store learning data for model training
    EmotionalLearningDataJob.perform_later(learning_data)
  end

  # Helper methods for processing different data sources

  def self.determine_exit_behavior(session_data)
    exit_method = session_data[:exit_method]
    time_on_page = session_data[:time_on_last_page] || 0

    case exit_method
    when "close_tab", "browser_close"
      time_on_page < 10 ? "abrupt" : "natural"
    when "navigation"
      "natural"
    when "back_button"
      time_on_page < 30 ? "quick" : "considered"
    else
      "unknown"
    end
  end

  def self.calculate_social_engagement_rate(social_data)
    interactions = (social_data[:likes] || 0) + (social_data[:comments] || 0) + (social_data[:shares] || 0)
    impressions = social_data[:impressions] || 1

    (interactions.to_f / impressions * 100).round(2)
  end

  def self.analyze_comment_sentiment(comments)
    return 50 if comments.empty? # Neutral sentiment

    # Simple sentiment analysis - in production would use AI/NLP
    positive_words = %w[great love awesome amazing excellent wonderful fantastic]
    negative_words = %w[hate terrible awful bad horrible disappointing frustrating]

    total_sentiment = 0
    comment_count = 0

    comments.each do |comment|
      next unless comment.is_a?(String)

      comment_words = comment.downcase.split
      positive_count = comment_words.count { |word| positive_words.include?(word) }
      negative_count = comment_words.count { |word| negative_words.include?(word) }

      if positive_count > negative_count
        total_sentiment += 75 # Positive
      elsif negative_count > positive_count
        total_sentiment += 25 # Negative
      else
        total_sentiment += 50 # Neutral
      end

      comment_count += 1
    end

    return 50 if comment_count.zero?
    (total_sentiment.to_f / comment_count).round(2)
  end

  def self.calculate_price_sensitivity(purchase_data)
    # Analyze price sensitivity based on purchase patterns
    purchases = purchase_data[:purchase_history] || []
    return 50 if purchases.empty?

    # Look for patterns in price vs purchase behavior
    price_points = purchases.map { |p| p[:price] || 0 }.compact
    return 50 if price_points.empty?

    avg_price = price_points.sum / price_points.size.to_f
    price_variance = price_points.map { |price| (price - avg_price) ** 2 }.sum / price_points.size.to_f

    # Higher variance might indicate lower price sensitivity
    # Lower variance might indicate higher price sensitivity
    sensitivity_score = 100 - [ Math.sqrt(price_variance) / avg_price * 100, 100 ].min
    sensitivity_score.round(2)
  end

  def self.analyze_category_shifts(purchase_data)
    # Analyze changes in product category preferences
    purchases = purchase_data[:purchase_history] || []
    return 0 if purchases.size < 2

    recent_purchases = purchases.last(5)
    earlier_purchases = purchases.first(5)

    recent_categories = recent_purchases.map { |p| p[:category] }.compact.tally
    earlier_categories = earlier_purchases.map { |p| p[:category] }.compact.tally

    # Calculate category shift score
    new_categories = recent_categories.keys - earlier_categories.keys
    shift_score = new_categories.size * 20 # 20 points per new category

    [ shift_score, 100 ].min
  end

  def self.rate_issue_complexity(issues)
    return 50 if issues.empty?

    complexity_scores = issues.map do |issue|
      case issue[:type]
      when "billing", "account" then 30
      when "technical", "bug" then 70
      when "integration", "api" then 90
      when "general", "question" then 20
      else 50
      end
    end

    (complexity_scores.sum / complexity_scores.size.to_f).round(2)
  end

  def self.analyze_communication_tone(messages)
    return 50 if messages.empty?

    # Simple tone analysis based on message characteristics
    total_tone_score = 0
    message_count = 0

    messages.each do |message|
      next unless message.is_a?(String)

      tone_score = 50 # Start neutral

      # Positive indicators
      tone_score += 10 if message.include?("please")
      tone_score += 10 if message.include?("thank")
      tone_score += 5 if message.end_with?("?") # Questions are generally neutral-positive

      # Negative indicators
      tone_score -= 15 if message.include?("!!!")
      tone_score -= 10 if message.upcase == message && message.length > 10 # ALL CAPS
      tone_score -= 20 if message.include?("terrible") || message.include?("awful")
      tone_score -= 10 if message.include?("frustrated") || message.include?("angry")

      total_tone_score += [ tone_score, 0 ].max.clamp(0, 100)
      message_count += 1
    end

    return 50 if message_count.zero?
    (total_tone_score.to_f / message_count).round(2)
  end
end
