# frozen_string_literal: true

module Agents
  # Background job for executing Social Media Specialist Agent campaigns
  # This is a stub implementation - will be expanded in future iterations
  #
  class SocialMediaSpecialistJob < ApplicationJob
    queue_as :agents
    retry_on StandardError, wait: :exponentially_longer, attempts: 3

    def perform(campaign_id:, tenant_id:, coordination_data: {})
      campaign = Campaign.find(campaign_id)
      tenant = Tenant.find(tenant_id)

      # Execute the social media specialist agent
      agent = Agents::SocialMediaSpecialistAgent.new(tenant: tenant)
      result = agent.execute_campaign(campaign, coordination_data)

      Rails.logger.info "Social Media Specialist Agent completed for campaign #{campaign_id}: #{result[:success]}"

    rescue StandardError => e
      Rails.logger.error "Social Media Specialist Job failed for campaign #{campaign_id}: #{e.message}"
      raise
    end
  end
end
