# frozen_string_literal: true

module Agents
  # Background job for executing SEO Specialist Agent campaigns
  # This is a stub implementation - will be expanded in future iterations
  #
  class SeoSpecialistJob < ApplicationJob
    queue_as :agents
    retry_on StandardError, wait: :exponentially_longer, attempts: 3

    def perform(campaign_id:, tenant_id:, coordination_data: {})
      campaign = Campaign.find(campaign_id)
      tenant = Tenant.find(tenant_id)

      # Execute the SEO specialist agent
      agent = Agents::SeoSpecialistAgent.new(tenant: tenant)
      result = agent.execute_campaign(campaign, coordination_data)

      Rails.logger.info "SEO Specialist Agent completed for campaign #{campaign_id}: #{result[:success]}"

    rescue StandardError => e
      Rails.logger.error "SEO Specialist Job failed for campaign #{campaign_id}: #{e.message}"
      raise
    end
  end
end
