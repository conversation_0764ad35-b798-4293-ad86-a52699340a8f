# frozen_string_literal: true

module Agents
  # Background job for executing Email Specialist Agent campaigns
  # Handles asynchronous email campaign execution with error handling and monitoring
  #
  class EmailSpecialistJob < ApplicationJob
    queue_as :agents
    retry_on StandardError, wait: :exponentially_longer, attempts: 3

    # Execute email specialist agent for a campaign
    def perform(campaign_id:, tenant_id:, coordination_data: {})
      @campaign = Campaign.find(campaign_id)
      @tenant = Tenant.find(tenant_id)
      @coordination_data = coordination_data

      validate_job_parameters!

      # Create workflow tracking record
      @workflow = create_agent_workflow

      begin
        # Execute the email specialist agent
        agent = Agents::EmailSpecialistAgent.new(tenant: @tenant)
        result = agent.execute_campaign(@campaign, @coordination_data)

        if result[:success]
          handle_successful_execution(result)
        else
          handle_failed_execution(result[:error])
        end

      rescue StandardError => e
        handle_job_error(e)
        raise # Re-raise to trigger retry mechanism
      end
    end

    private

    # Validate that all required parameters are present and valid
    def validate_job_parameters!
      raise ArgumentError, "Campaign not found" unless @campaign
      raise ArgumentError, "Tenant not found" unless @tenant
      raise ArgumentError, "Campaign does not belong to tenant" unless @campaign.tenant_id == @tenant.id

      # Validate coordination data structure
      unless @coordination_data.is_a?(Hash)
        raise ArgumentError, "Coordination data must be a hash"
      end
    end

    # Create workflow tracking record for monitoring
    def create_agent_workflow
      AgentWorkflow.create!(
        campaign: @campaign,
        tenant: @tenant,
        workflow_type: "email_specialist",
        status: "in_progress",
        progress_percent: 0,
        context_data: {
          coordination_data: @coordination_data,
          job_id: job_id,
          started_at: Time.current,
          priority: @coordination_data["priority"] || 2,
          timeline: @coordination_data["timeline"] || "24_hours"
        }
      )
    end

    # Handle successful agent execution
    def handle_successful_execution(result)
      @workflow.update!(
        status: "completed",
        progress_percent: 100,
        results: {
          email_campaign_id: result[:email_campaign_id],
          subject_lines: result[:subject_lines],
          selected_subject: result[:selected_subject],
          optimal_send_time: result[:optimal_send_time],
          personalization_applied: result[:personalization_applied],
          delivery_status: result[:delivery_status],
          completed_at: Time.current
        },
        completed_at: Time.current
      )

      # Notify Marketing Manager Agent of completion
      notify_completion_success(result)

      # Log successful execution
      Rails.logger.info "Email Specialist Agent completed successfully for campaign #{@campaign.id}"

      # Create success alert
      create_success_alert(result)
    end

    # Handle failed agent execution
    def handle_failed_execution(error_message)
      @workflow.update!(
        status: "failed",
        error_details: {
          error: error_message,
          failed_at: Time.current,
          job_id: job_id,
          retry_count: executions
        }
      )

      # Create failure alert
      create_failure_alert(error_message)

      # Log failure
      Rails.logger.error "Email Specialist Agent failed for campaign #{@campaign.id}: #{error_message}"
    end

    # Handle job-level errors (exceptions)
    def handle_job_error(error)
      @workflow&.update!(
        status: "failed",
        error_details: {
          error: error.message,
          error_class: error.class.name,
          backtrace: error.backtrace&.first(10),
          failed_at: Time.current,
          job_id: job_id,
          retry_count: executions
        }
      )

      # Create critical failure alert
      create_critical_failure_alert(error)

      # Log detailed error
      Rails.logger.error "Email Specialist Job failed for campaign #{@campaign.id}: #{error.message}"
      Rails.logger.error error.backtrace.join("\n")
    end

    # Notify Marketing Manager Agent of successful completion
    def notify_completion_success(result)
      # Update the parent orchestration workflow if it exists
      parent_workflow = find_parent_orchestration_workflow

      if parent_workflow
        update_parent_workflow_progress(parent_workflow)

        # Check if all specialist agents are complete
        if all_specialist_agents_complete?(parent_workflow)
          notify_orchestration_complete(parent_workflow)
        end
      end

      # Broadcast real-time update to UI
      broadcast_agent_completion(result)
    end

    # Find the parent Marketing Manager orchestration workflow
    def find_parent_orchestration_workflow
      AgentWorkflow.find_by(
        campaign: @campaign,
        tenant: @tenant,
        workflow_type: "marketing_orchestration",
        status: [ "in_progress", "completed" ]
      )
    end

    # Update parent workflow progress based on specialist completion
    def update_parent_workflow_progress(parent_workflow)
      specialist_workflows = AgentWorkflow.where(
        campaign: @campaign,
        tenant: @tenant,
        workflow_type: [ "email_specialist", "social_specialist", "seo_specialist" ]
      )

      completed_count = specialist_workflows.where(status: "completed").count
      total_count = specialist_workflows.count

      if total_count > 0
        progress = (completed_count.to_f / total_count * 100).round
        parent_workflow.update!(progress_percent: [ progress, 95 ].min) # Cap at 95% until fully complete
      end
    end

    # Check if all specialist agents have completed
    def all_specialist_agents_complete?(parent_workflow)
      specialist_workflows = AgentWorkflow.where(
        campaign: @campaign,
        tenant: @tenant,
        workflow_type: [ "email_specialist", "social_specialist", "seo_specialist" ]
      )

      incomplete_workflows = specialist_workflows.where.not(status: [ "completed", "failed" ])
      incomplete_workflows.empty?
    end

    # Notify orchestration completion
    def notify_orchestration_complete(parent_workflow)
      parent_workflow.update!(
        status: "completed",
        progress_percent: 100,
        completed_at: Time.current
      )

      # Trigger final aggregation and reporting
      AgentOrchestrationCompletionJob.perform_later(
        orchestration_workflow_id: parent_workflow.id
      )
    end

    # Broadcast real-time updates to the UI
    def broadcast_agent_completion(result)
      Turbo::StreamsChannel.broadcast_update_to(
        "campaign_#{@campaign.id}_agents",
        target: "email-specialist-status",
        partial: "agent_workflows/specialist_status",
        locals: {
          agent_type: "email",
          status: "completed",
          result: result,
          workflow: @workflow
        }
      )

      # Update overall campaign status
      Turbo::StreamsChannel.broadcast_update_to(
        "campaign_#{@campaign.id}",
        target: "campaign-ai-status-#{@campaign.id}",
        partial: "campaigns/ai_status",
        locals: {
          campaign: @campaign,
          status: determine_campaign_ai_status
        }
      )
    end

    # Determine overall campaign AI status
    def determine_campaign_ai_status
      workflows = AgentWorkflow.where(campaign: @campaign, tenant: @tenant)

      return "idle" if workflows.empty?
      return "failed" if workflows.any? { |w| w.status == "failed" }
      return "completed" if workflows.all? { |w| w.status == "completed" }
      "in_progress"
    end

    # Create success alert for monitoring
    def create_success_alert(result)
      AlertLog.create!(
        tenant: @tenant,
        alert_type: "agent_success",
        severity: "info",
        message: "Email Specialist Agent completed successfully for campaign '#{@campaign.name}'",
        metadata: {
          campaign_id: @campaign.id,
          workflow_id: @workflow.id,
          email_campaign_id: result[:email_campaign_id],
          subject_line: result[:selected_subject],
          delivery_status: result[:delivery_status],
          execution_time: Time.current - @workflow.created_at
        }
      )
    end

    # Create failure alert for monitoring
    def create_failure_alert(error_message)
      AlertLog.create!(
        tenant: @tenant,
        alert_type: "agent_failure",
        severity: "high",
        message: "Email Specialist Agent failed for campaign '#{@campaign.name}': #{error_message}",
        metadata: {
          campaign_id: @campaign.id,
          workflow_id: @workflow.id,
          error: error_message,
          coordination_data: @coordination_data,
          retry_count: executions
        }
      )
    end

    # Create critical failure alert for job-level errors
    def create_critical_failure_alert(error)
      AlertLog.create!(
        tenant: @tenant,
        alert_type: "agent_critical_failure",
        severity: "critical",
        message: "Email Specialist Job crashed for campaign '#{@campaign.name}': #{error.message}",
        metadata: {
          campaign_id: @campaign.id,
          workflow_id: @workflow&.id,
          error_class: error.class.name,
          error_message: error.message,
          backtrace: error.backtrace&.first(5),
          job_id: job_id,
          retry_count: executions,
          coordination_data: @coordination_data
        }
      )
    end
  end
end
