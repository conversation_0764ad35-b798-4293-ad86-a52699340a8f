# frozen_string_literal: true

class SocialPostingJob < ApplicationJob
  queue_as :social_media

  retry_on StandardError, wait: :exponentially_longer, attempts: 3
  discard_on SocialPlatformOAuthService::AuthorizationError

  def perform(scheduled_post_id)
    @scheduled_post = find_scheduled_post(scheduled_post_id)
    return unless should_post?

    Rails.logger.info "Processing scheduled post #{@scheduled_post.id} for platform #{@scheduled_post.platform}"

    begin
      result = post_to_platform

      if result[:success]
        handle_successful_post(result)
      else
        handle_failed_post(result[:error])
      end
    rescue => e
      handle_exception(e)
      raise # Re-raise to trigger retry mechanism
    end
  end

  private

  def find_scheduled_post(scheduled_post_id)
    # This would typically be a ScheduledPost model
    # For now, we'll create a mock structure
    OpenStruct.new(
      id: scheduled_post_id,
      platform: "twitter",
      content: "Sample post content",
      media_urls: [],
      scheduled_for: Time.current,
      status: "pending",
      campaign_id: 1,
      user_id: 1
    )
  end

  def should_post?
    return false unless @scheduled_post
    return false if @scheduled_post.status == "posted"
    return false if @scheduled_post.scheduled_for > Time.current + 5.minutes # Too early

    true
  end

  def post_to_platform
    oauth_service = SocialPlatformOAuthService.new(@scheduled_post.platform, user_id: @scheduled_post.user_id)

    # Get access token from stored configuration
    platform_config = get_platform_configuration
    return { success: false, error: "Platform not configured" } unless platform_config

    access_token = platform_config[:access_token]

    # Refresh token if needed
    if token_expired?(platform_config)
      access_token = refresh_access_token(oauth_service, platform_config)
      return { success: false, error: "Failed to refresh token" } unless access_token
    end

    # Prepare content data
    content_data = prepare_content_data

    # Post to platform
    oauth_service.post_content(access_token, content_data)
  rescue SocialPlatformOAuthService::AuthorizationError => e
    { success: false, error: "Authorization failed: #{e.message}" }
  rescue SocialPlatformOAuthService::ApiError => e
    { success: false, error: "API error: #{e.message}" }
  rescue => e
    { success: false, error: "Unexpected error: #{e.message}" }
  end

  def get_platform_configuration
    # This would typically fetch from a PlatformConfiguration model
    # For now, return a mock configuration
    {
      platform: @scheduled_post.platform,
      access_token: ENV["#{@scheduled_post.platform.upcase}_ACCESS_TOKEN"],
      refresh_token: ENV["#{@scheduled_post.platform.upcase}_REFRESH_TOKEN"],
      expires_at: 1.hour.from_now,
      user_id: @scheduled_post.user_id
    }
  end

  def token_expired?(config)
    return false unless config[:expires_at]
    config[:expires_at] < 10.minutes.from_now
  end

  def refresh_access_token(oauth_service, config)
    return nil unless config[:refresh_token]

    begin
      token_data = oauth_service.refresh_access_token(config[:refresh_token])

      # Update stored configuration with new token
      update_platform_configuration(config, token_data)

      token_data[:access_token]
    rescue => e
      Rails.logger.error "Failed to refresh access token for #{@scheduled_post.platform}: #{e.message}"
      nil
    end
  end

  def update_platform_configuration(config, token_data)
    # This would typically update a PlatformConfiguration model
    Rails.logger.info "Token refreshed for platform #{@scheduled_post.platform}"
  end

  def prepare_content_data
    content_data = {
      text: @scheduled_post.content,
      platform: @scheduled_post.platform
    }

    # Add media if present
    if @scheduled_post.media_urls&.any?
      case @scheduled_post.platform
      when "twitter"
        content_data[:media_ids] = upload_media_to_twitter(@scheduled_post.media_urls)
      when "facebook", "instagram"
        content_data[:media_urls] = @scheduled_post.media_urls
      when "linkedin"
        content_data[:media] = prepare_linkedin_media(@scheduled_post.media_urls)
      end
    end

    content_data
  end

  def upload_media_to_twitter(media_urls)
    # Implementation for uploading media to Twitter
    # This would typically use Twitter's media upload API
    []
  end

  def prepare_linkedin_media(media_urls)
    # Implementation for preparing LinkedIn media
    []
  end

  def handle_successful_post(result)
    Rails.logger.info "Successfully posted to #{@scheduled_post.platform}: #{result[:post_id]}"

    # Update scheduled post status
    update_scheduled_post_status("posted", {
      posted_at: Time.current,
      platform_post_id: result[:post_id],
      platform_url: result[:url]
    })

    # Track analytics
    track_post_analytics(result)

    # Send notification
    send_success_notification(result)
  end

  def handle_failed_post(error_message)
    Rails.logger.error "Failed to post to #{@scheduled_post.platform}: #{error_message}"

    # Update scheduled post status
    update_scheduled_post_status("failed", {
      error_message: error_message,
      failed_at: Time.current
    })

    # Send notification
    send_failure_notification(error_message)
  end

  def handle_exception(exception)
    Rails.logger.error "Exception in SocialPostingJob for post #{@scheduled_post.id}: #{exception.message}"
    Rails.logger.error exception.backtrace.join("\n")

    # Update scheduled post with error information
    update_scheduled_post_status("error", {
      error_message: exception.message,
      error_at: Time.current
    })
  end

  def update_scheduled_post_status(status, additional_data = {})
    # This would typically update a ScheduledPost model
    Rails.logger.info "Updating scheduled post #{@scheduled_post.id} status to #{status}"
  end

  def track_post_analytics(result)
    # Track initial post metrics
    analytics_data = {
      scheduled_post_id: @scheduled_post.id,
      campaign_id: @scheduled_post.campaign_id,
      platform: @scheduled_post.platform,
      platform_post_id: result[:post_id],
      posted_at: Time.current,
      initial_metrics: {
        impressions: 0,
        engagements: 0,
        clicks: 0,
        shares: 0
      }
    }

    # This would typically create a SocialPostAnalytics record
    Rails.logger.info "Tracking analytics for post #{result[:post_id]}"
  end

  def send_success_notification(result)
    # Send notification to user about successful post
    notification_data = {
      type: "social_post_success",
      user_id: @scheduled_post.user_id,
      campaign_id: @scheduled_post.campaign_id,
      platform: @scheduled_post.platform,
      post_url: result[:url],
      message: "Your post has been successfully published to #{@scheduled_post.platform.titleize}"
    }

    # This would typically use a notification service
    send_notification(notification_data)
  end

  def send_failure_notification(error_message)
    # Send notification to user about failed post
    notification_data = {
      type: "social_post_failure",
      user_id: @scheduled_post.user_id,
      campaign_id: @scheduled_post.campaign_id,
      platform: @scheduled_post.platform,
      error: error_message,
      message: "Failed to publish your post to #{@scheduled_post.platform.titleize}: #{error_message}"
    }

    # This would typically use a notification service
    send_notification(notification_data)
  end

  def send_notification(notification_data)
    # This would typically integrate with ActionCable, email, or push notifications
    Rails.logger.info "Sending notification: #{notification_data[:type]} to user #{notification_data[:user_id]}"
  end
end

# Companion job for collecting analytics after posting
class SocialAnalyticsCollectionJob < ApplicationJob
  queue_as :analytics

  def perform(platform_post_id, platform, scheduled_post_id)
    Rails.logger.info "Collecting analytics for #{platform} post #{platform_post_id}"

    oauth_service = SocialPlatformOAuthService.new(platform)

    # This would fetch engagement metrics from the platform
    # and update the analytics record

    # Schedule next collection (for ongoing metrics tracking)
    SocialAnalyticsCollectionJob.set(wait: 1.hour).perform_later(
      platform_post_id,
      platform,
      scheduled_post_id
    )
  end
end
