# frozen_string_literal: true

# BulkEmotionalAnalysisJob - Process bulk emotional analysis requests
#
# This job handles large-scale emotional analysis requests by:
# - Analyzing emotional compatibility for multiple customers and campaigns
# - Processing requests in batches to avoid overwhelming the system
# - Providing progress updates and comprehensive results
# - Optimizing resource usage across multiple AI calls
class BulkEmotionalAnalysisJob < ApplicationJob
  queue_as :default
  retry_on StandardError, wait: :exponentially_longer, attempts: 3

  def perform(tenant_id:, customer_identifiers:, campaign_ids:, requested_by: nil)
    @tenant = Tenant.find(tenant_id)
    @customer_identifiers = Array(customer_identifiers)
    @campaign_ids = Array(campaign_ids)
    @requested_by = requested_by
    @results = {}

    Rails.logger.info "Starting bulk emotional analysis: #{@customer_identifiers.size} customers x #{@campaign_ids.size} campaigns"

    begin
      # Step 1: Initialize progress tracking
      initialize_progress_tracking

      # Step 2: Validate inputs
      validation_result = validate_inputs
      return unless validation_result[:valid]

      # Step 3: Process in batches to manage resource usage
      process_analysis_batches

      # Step 4: Generate comprehensive results summary
      generate_results_summary

      # Step 5: Notify completion
      notify_completion

      Rails.logger.info "Bulk emotional analysis completed successfully"

    rescue => error
      Rails.logger.error "BulkEmotionalAnalysisJob failed: #{error.message}"
      Rails.logger.error error.backtrace.join("\n")
      
      handle_analysis_failure(error)
      raise
    end
  end

  private

  def initialize_progress_tracking
    @total_analyses = @customer_identifiers.size * @campaign_ids.size
    @completed_analyses = 0
    @batch_size = calculate_optimal_batch_size
    @start_time = Time.current

    @results = {
      tenant_id: @tenant.id,
      started_at: @start_time,
      customer_count: @customer_identifiers.size,
      campaign_count: @campaign_ids.size,
      total_analyses: @total_analyses,
      batch_size: @batch_size,
      compatibility_results: {},
      summary_statistics: {},
      recommendations: {},
      processing_metadata: {}
    }

    Rails.logger.info "Bulk analysis initialized: #{@total_analyses} total analyses in batches of #{@batch_size}"
  end

  def calculate_optimal_batch_size
    # Calculate batch size based on system capacity and analysis complexity
    base_batch_size = 10
    
    # Adjust based on number of campaigns (more campaigns = smaller batches)
    campaign_factor = [@campaign_ids.size / 5, 1].max
    adjusted_batch_size = base_batch_size / campaign_factor
    
    # Ensure minimum batch size of 2 and maximum of 20
    [adjusted_batch_size.round, 2].max.tap do |size|
      [size, 20].min
    end
  end

  def validate_inputs
    errors = []

    # Validate tenant access to customers
    existing_customers = @tenant.customer_emotional_states
                               .where(customer_identifier: @customer_identifiers)
                               .pluck(:customer_identifier)
    
    missing_customers = @customer_identifiers - existing_customers
    if missing_customers.any?
      Rails.logger.warn "Missing emotional states for customers: #{missing_customers.join(', ')}"
      # Continue with existing customers only
      @customer_identifiers = existing_customers
    end

    # Validate tenant access to campaigns
    existing_campaigns = @tenant.campaigns
                               .where(id: @campaign_ids)
                               .pluck(:id)
    
    missing_campaigns = @campaign_ids - existing_campaigns
    if missing_campaigns.any?
      errors << "Campaigns not found: #{missing_campaigns.join(', ')}"
    end

    # Validate minimum data requirements
    if @customer_identifiers.empty?
      errors << "No valid customers found for analysis"
    end

    if existing_campaigns.empty?
      errors << "No valid campaigns found for analysis"
    end

    if errors.any?
      log_validation_errors(errors)
      return { valid: false, errors: errors }
    end

    @campaign_ids = existing_campaigns
    @results[:validation] = {
      customers_analyzed: @customer_identifiers.size,
      campaigns_analyzed: @campaign_ids.size,
      missing_customers: missing_customers,
      missing_campaigns: missing_campaigns
    }

    { valid: true }
  end

  def process_analysis_batches
    customer_batches = @customer_identifiers.each_slice(@batch_size).to_a
    
    Rails.logger.info "Processing #{customer_batches.size} customer batches"

    customer_batches.each_with_index do |customer_batch, batch_index|
      Rails.logger.info "Processing batch #{batch_index + 1}/#{customer_batches.size} (#{customer_batch.size} customers)"
      
      batch_start_time = Time.current
      process_customer_batch(customer_batch, batch_index)
      batch_duration = Time.current - batch_start_time
      
      # Log batch completion
      Rails.logger.info "Batch #{batch_index + 1} completed in #{batch_duration.round(2)} seconds"
      
      # Brief pause between batches to prevent system overload
      sleep(0.5) unless batch_index == customer_batches.size - 1
    end
  end

  def process_customer_batch(customer_batch, batch_index)
    customer_batch.each do |customer_identifier|
      process_customer_analysis(customer_identifier, batch_index)
    end
  end

  def process_customer_analysis(customer_identifier, batch_index)
    # Get customer emotional state
    emotional_state = @tenant.customer_emotional_states
                            .find_by(customer_identifier: customer_identifier)
    
    return unless emotional_state

    customer_results = {
      customer_identifier: customer_identifier,
      emotional_state: {
        current_emotion: emotional_state.current_emotion,
        intensity: emotional_state.emotional_intensity,
        confidence: emotional_state.confidence_score,
        receptive: emotional_state.receptive_to_marketing?
      },
      campaign_analyses: {},
      overall_recommendations: []
    }

    # Analyze against each campaign
    @campaign_ids.each do |campaign_id|
      campaign = @tenant.campaigns.find(campaign_id)
      next unless campaign

      analysis_result = analyze_customer_campaign_compatibility(emotional_state, campaign)
      customer_results[:campaign_analyses][campaign_id] = analysis_result

      @completed_analyses += 1
      
      # Log progress periodically
      if @completed_analyses % 10 == 0
        progress_percentage = (@completed_analyses.to_f / @total_analyses * 100).round(1)
        Rails.logger.info "Progress: #{@completed_analyses}/#{@total_analyses} (#{progress_percentage}%)"
      end
    end

    # Generate overall recommendations for this customer
    customer_results[:overall_recommendations] = generate_customer_recommendations(emotional_state, customer_results[:campaign_analyses])

    @results[:compatibility_results][customer_identifier] = customer_results
  end

  def analyze_customer_campaign_compatibility(emotional_state, campaign)
    # Calculate compatibility score
    compatibility_score = emotional_state.campaign_compatibility_score(campaign)
    
    # Get prediction
    prediction = emotional_state.predict_campaign_response(campaign)
    
    # Determine recommended action
    recommended_action = determine_recommended_action(compatibility_score, prediction, emotional_state)
    
    # Calculate risk level
    risk_level = calculate_risk_level(prediction[:risk_factors])

    {
      campaign_id: campaign.id,
      campaign_name: campaign.name,
      campaign_type: campaign.campaign_type,
      compatibility_score: compatibility_score,
      predicted_engagement: prediction[:predicted_engagement],
      risk_level: risk_level,
      risk_factors: prediction[:risk_factors],
      recommended_action: recommended_action,
      optimal_timing: prediction[:optimal_timing],
      personalization_suggestions: prediction[:personalization_suggestions]
    }
  end

  def determine_recommended_action(compatibility_score, prediction, emotional_state)
    risk_factors = prediction[:risk_factors] || []
    high_risk_count = risk_factors.count { |risk| risk[:severity] == 'high' }

    if high_risk_count > 0
      "avoid_campaign"
    elsif compatibility_score < 30.0
      "major_adaptation_required"
    elsif compatibility_score < 60.0
      "minor_adaptation_recommended"
    elsif !emotional_state.receptive_to_marketing?
      "delay_until_receptive"
    elsif compatibility_score >= 80.0
      "proceed_as_planned"
    else
      "proceed_with_monitoring"
    end
  end

  def calculate_risk_level(risk_factors)
    return "low" if risk_factors.empty?

    high_risk_count = risk_factors.count { |risk| risk[:severity] == 'high' }
    medium_risk_count = risk_factors.count { |risk| risk[:severity] == 'medium' }

    if high_risk_count > 0
      "high"
    elsif medium_risk_count > 1
      "medium"
    elsif medium_risk_count > 0
      "low_medium"
    else
      "low"
    end
  end

  def generate_customer_recommendations(emotional_state, campaign_analyses)
    recommendations = []

    # Overall emotional state recommendations
    if emotional_state.confidence_score < 60.0
      recommendations << {
        type: "data_collection",
        priority: "medium",
        action: "Collect more behavioral data for better emotional insights",
        rationale: "Low confidence in emotional state detection"
      }
    end

    if !emotional_state.receptive_to_marketing?
      recommendations << {
        type: "timing",
        priority: "high",
        action: "Delay marketing until emotional state improves",
        rationale: "Customer not currently receptive to marketing"
      }
    end

    # Campaign-specific recommendations
    high_compatibility_campaigns = campaign_analyses.select { |_, analysis| analysis[:compatibility_score] >= 80.0 }
    if high_compatibility_campaigns.any?
      campaign_names = high_compatibility_campaigns.values.map { |analysis| analysis[:campaign_name] }
      recommendations << {
        type: "opportunity",
        priority: "high",
        action: "Prioritize high-compatibility campaigns: #{campaign_names.join(', ')}",
        rationale: "Excellent emotional alignment with these campaigns"
      }
    end

    low_compatibility_campaigns = campaign_analyses.select { |_, analysis| analysis[:compatibility_score] < 40.0 }
    if low_compatibility_campaigns.any?
      campaign_names = low_compatibility_campaigns.values.map { |analysis| analysis[:campaign_name] }
      recommendations << {
        type: "risk_mitigation",
        priority: "high",
        action: "Avoid or significantly adapt campaigns: #{campaign_names.join(', ')}",
        rationale: "Poor emotional alignment may damage customer relationship"
      }
    end

    recommendations
  end

  def generate_results_summary
    total_duration = Time.current - @start_time
    
    # Calculate summary statistics
    all_analyses = @results[:compatibility_results].values.flat_map { |customer| customer[:campaign_analyses].values }
    
    avg_compatibility = all_analyses.map { |analysis| analysis[:compatibility_score] }.sum / all_analyses.size.to_f
    avg_engagement = all_analyses.map { |analysis| analysis[:predicted_engagement] }.sum / all_analyses.size.to_f
    
    high_risk_analyses = all_analyses.count { |analysis| analysis[:risk_level] == "high" }
    proceed_as_planned = all_analyses.count { |analysis| analysis[:recommended_action] == "proceed_as_planned" }
    
    compatibility_distribution = all_analyses.group_by do |analysis|
      score = analysis[:compatibility_score]
      case score
      when 0..30 then "poor"
      when 31..60 then "fair"
      when 61..80 then "good"
      when 81..100 then "excellent"
      end
    end.transform_values(&:size)

    @results[:summary_statistics] = {
      total_duration_seconds: total_duration.round(2),
      analyses_per_second: (@total_analyses / total_duration).round(2),
      average_compatibility_score: avg_compatibility.round(2),
      average_predicted_engagement: avg_engagement.round(2),
      high_risk_count: high_risk_analyses,
      proceed_as_planned_count: proceed_as_planned,
      compatibility_distribution: compatibility_distribution,
      completion_rate: ((@completed_analyses.to_f / @total_analyses) * 100).round(2)
    }

    # Generate tenant-level recommendations
    @results[:recommendations] = generate_tenant_recommendations

    @results[:completed_at] = Time.current
    @results[:status] = "completed"

    Rails.logger.info "Results summary generated: #{avg_compatibility.round(2)}% avg compatibility, #{high_risk_analyses} high-risk analyses"
  end

  def generate_tenant_recommendations
    recommendations = []
    stats = @results[:summary_statistics]

    # Overall compatibility recommendations
    if stats[:average_compatibility_score] < 50.0
      recommendations << {
        type: "strategy_review",
        priority: "critical",
        action: "Review emotional targeting strategy",
        details: "Overall low compatibility suggests systematic issues with emotional alignment",
        affected_analyses: stats[:compatibility_distribution]["poor"] + stats[:compatibility_distribution]["fair"]
      }
    end

    # High risk recommendations
    if stats[:high_risk_count] > @total_analyses * 0.2
      recommendations << {
        type: "risk_management",
        priority: "high", 
        action: "Implement risk mitigation protocols",
        details: "#{stats[:high_risk_count]} analyses show high risk - review campaign messaging and timing",
        affected_analyses: stats[:high_risk_count]
      }
    end

    # Opportunity optimization
    if stats[:proceed_as_planned_count] > @total_analyses * 0.3
      recommendations << {
        type: "opportunity_optimization",
        priority: "medium",
        action: "Optimize high-performing emotional alignments",
        details: "#{stats[:proceed_as_planned_count]} analyses show excellent compatibility - scale these approaches",
        affected_analyses: stats[:proceed_as_planned_count]
      }
    end

    # Performance recommendations
    if stats[:average_predicted_engagement] < 50.0
      recommendations << {
        type: "engagement_improvement",
        priority: "high",
        action: "Improve emotional engagement strategies",
        details: "Low predicted engagement suggests need for stronger emotional connection",
        metric_value: stats[:average_predicted_engagement]
      }
    end

    recommendations
  end

  def notify_completion
    # Create success alert log
    AlertLog.create!(
      tenant: @tenant,
      alert_type: "bulk_emotional_analysis_completed",
      severity: "info",
      message: "Bulk emotional analysis completed successfully",
      metadata: {
        customer_count: @customer_identifiers.size,
        campaign_count: @campaign_ids.size,
        total_analyses: @total_analyses,
        duration_seconds: @results[:summary_statistics][:total_duration_seconds],
        average_compatibility: @results[:summary_statistics][:average_compatibility_score],
        high_risk_count: @results[:summary_statistics][:high_risk_count],
        requested_by: @requested_by
      }
    )

    # In production, could send email notification or webhook
    Rails.logger.info "Bulk analysis completed - results stored and notifications sent"
  end

  def handle_analysis_failure(error)
    # Log failure details
    AlertLog.create!(
      tenant: @tenant,
      alert_type: "bulk_emotional_analysis_failed",
      severity: "high",
      message: "Bulk emotional analysis failed: #{error.message}",
      metadata: {
        customer_count: @customer_identifiers.size,
        campaign_count: @campaign_ids.size,
        completed_analyses: @completed_analyses,
        total_analyses: @total_analyses,
        error_class: error.class.name,
        requested_by: @requested_by
      }
    )

    # Store partial results if any analyses completed
    if @completed_analyses > 0
      @results[:status] = "partial_failure"
      @results[:error] = error.message
      @results[:completed_analyses] = @completed_analyses
      
      Rails.logger.warn "Partial results saved: #{@completed_analyses}/#{@total_analyses} analyses completed"
    end
  end

  def log_validation_errors(errors)
    Rails.logger.error "Bulk analysis validation failed:"
    errors.each { |error| Rails.logger.error "  - #{error}" }
  end
end
