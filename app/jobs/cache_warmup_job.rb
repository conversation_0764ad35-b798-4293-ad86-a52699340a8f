# frozen_string_literal: true

##
# Cache Warmup Job
#
# Background job to warm up caches for improved performance.
# Preloads frequently accessed data to reduce database load and improve response times.
#
class CacheWarmupJob < ApplicationJob
  queue_as :low_priority

  ##
  # Perform cache warmup for a specific tenant
  #
  # @param tenant_id [Integer] Tenant ID to warm cache for
  #
  def perform(tenant_id)
    tenant = Tenant.find(tenant_id)
    cache_service = CampaignCacheService.new(tenant)

    Rails.logger.info "Starting cache warmup for tenant #{tenant_id}"

    begin
      # Set tenant context for multi-tenancy
      ActsAsTenant.with_tenant(tenant) do
        cache_service.warm_cache_for_tenant(background: false)
      end

      Rails.logger.info "Cache warmup completed for tenant #{tenant_id}"
    rescue => e
      Rails.logger.error "Cache warmup failed for tenant #{tenant_id}: #{e.message}"
      raise e
    end
  end
end
