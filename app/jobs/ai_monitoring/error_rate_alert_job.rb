# frozen_string_literal: true

##
# Error Rate Alert Job
#
# Sends alerts when AI error rates exceed acceptable thresholds.
# Helps identify provider issues or system problems early.
#
class ErrorRateAlertJob < ApplicationJob
  queue_as :high

  retry_on StandardError, wait: :exponentially_longer, attempts: 2

  def perform(tenant_id:, error_rate:, date:)
    tenant = Tenant.find(tenant_id)

    alert_data = {
      tenant: tenant,
      error_rate: error_rate.round(2),
      date: date,
      timestamp: Time.current
    }

    # Send notifications
    send_email_alert(alert_data) if email_alerts_enabled?(tenant)
    send_slack_alert(alert_data) if slack_alerts_enabled?(tenant)
    send_webhook_alert(alert_data) if webhook_alerts_enabled?(tenant)

    # Log alert
    log_error_rate_alert(alert_data)

    Rails.logger.warn("Error rate alert sent for tenant #{tenant.name}: #{error_rate}% error rate")
  rescue ActiveRecord::RecordNotFound
    Rails.logger.error("Tenant not found for error rate alert: #{tenant_id}")
  rescue => e
    Rails.logger.error("Failed to send error rate alert: #{e.message}")
    raise
  end

  private

  def send_email_alert(alert_data)
    tenant = alert_data[:tenant]
    admin_emails = tenant.users.where(role: [ :admin, :owner ]).pluck(:email)

    admin_emails.each do |email|
      ErrorRateAlertMailer.high_error_rate(
        email: email,
        tenant_name: tenant.name,
        error_rate: alert_data[:error_rate],
        date: alert_data[:date]
      ).deliver_now
    end
  end

  def send_slack_alert(alert_data)
    tenant = alert_data[:tenant]
    slack_webhook_url = tenant.settings.dig("notifications", "slack_webhook_url")

    return unless slack_webhook_url

    message = build_slack_message(alert_data)

    Faraday.post(slack_webhook_url) do |req|
      req.headers["Content-Type"] = "application/json"
      req.body = { text: message }.to_json
    end
  end

  def send_webhook_alert(alert_data)
    tenant = alert_data[:tenant]
    webhook_url = tenant.settings.dig("notifications", "error_webhook_url")

    return unless webhook_url

    payload = {
      event: "high_error_rate",
      tenant_id: tenant.id,
      tenant_name: tenant.name,
      error_rate: alert_data[:error_rate],
      date: alert_data[:date],
      timestamp: alert_data[:timestamp].iso8601
    }

    Faraday.post(webhook_url) do |req|
      req.headers["Content-Type"] = "application/json"
      req.body = payload.to_json
    end
  end

  def build_slack_message(alert_data)
    tenant = alert_data[:tenant]
    error_rate = alert_data[:error_rate]
    date = alert_data[:date]

    <<~MESSAGE
      🔴 **High AI Error Rate Alert**

      **Tenant:** #{tenant.name}
      **Error Rate:** #{error_rate}%
      **Date:** #{date}

      The AI service error rate has exceeded the 10% threshold.
      This may indicate issues with AI providers or system problems.

      Please investigate and take appropriate action.
    MESSAGE
  end

  def log_error_rate_alert(alert_data)
    tenant = alert_data[:tenant]

    AlertLog.create!(
      tenant: tenant,
      alert_type: "error_rate",
      severity: "warning",
      message: "High AI error rate detected: #{alert_data[:error_rate]}%",
      metadata: {
        error_rate: alert_data[:error_rate],
        date: alert_data[:date]
      }
    )
  end

  def email_alerts_enabled?(tenant)
    tenant.settings.dig("notifications", "email_enabled") != false
  end

  def slack_alerts_enabled?(tenant)
    tenant.settings.dig("notifications", "slack_enabled") == true &&
      tenant.settings.dig("notifications", "slack_webhook_url").present?
  end

  def webhook_alerts_enabled?(tenant)
    tenant.settings.dig("notifications", "webhook_enabled") == true &&
      tenant.settings.dig("notifications", "error_webhook_url").present?
  end
end
