# frozen_string_literal: true

##
# Store AI Usage Job
#
# Persists AI usage data to the database for long-term analytics and reporting.
# This job runs asynchronously to avoid blocking AI operations.
#
class StoreAiUsageJob < ApplicationJob
  queue_as :default

  retry_on StandardError, wait: :exponentially_longer, attempts: 3

  def perform(usage_data)
    # Store detailed usage record in database
    AiUsageRecord.create!(
      tenant_id: usage_data[:tenant_id],
      model: usage_data[:model],
      task_type: usage_data[:task_type],
      input_tokens: usage_data[:input_tokens],
      output_tokens: usage_data[:output_tokens],
      total_tokens: usage_data[:total_tokens],
      duration_ms: usage_data[:duration_ms],
      cost: usage_data[:cost],
      request_timestamp: Time.parse(usage_data[:timestamp]),
      metadata: {
        text_count: usage_data[:text_count],
        dimensions: usage_data[:dimensions]
      }.compact
    )

    Rails.logger.info("Stored AI usage record for tenant #{usage_data[:tenant_id]}")
  rescue ActiveRecord::RecordInvalid => e
    Rails.logger.error("Failed to store AI usage record: #{e.message}")
    raise
  rescue => e
    Rails.logger.error("Unexpected error storing AI usage: #{e.message}")
    raise
  end
end
