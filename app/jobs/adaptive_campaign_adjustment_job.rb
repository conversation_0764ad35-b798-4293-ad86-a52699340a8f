# frozen_string_literal: true

# AdaptiveCampaignAdjustmentJob - Real-time campaign adaptation based on emotional state changes
#
# This job implements the core "Marketing Therapist AI" real-time adaptation by:
# - Processing emotional state changes immediately
# - Automatically adjusting active campaigns
# - Preventing emotional mismatch in real-time
# - Logging all emotional adaptations for analysis
class AdaptiveCampaignAdjustmentJob < ApplicationJob
  queue_as :critical
  retry_on StandardError, wait: :exponentially_longer, attempts: 3

  def perform(customer_emotional_state_id:, emotional_analysis:)
    @customer_emotional_state = CustomerEmotionalState.find(customer_emotional_state_id)
    @emotional_analysis = emotional_analysis
    @tenant = @customer_emotional_state.tenant

    Rails.logger.info "Processing adaptive campaign adjustment for customer #{@customer_emotional_state.customer_identifier}"

    begin
      # Step 1: Find active campaigns affecting this customer
      active_campaigns = find_active_campaigns_for_customer

      if active_campaigns.empty?
        Rails.logger.info "No active campaigns found for customer #{@customer_emotional_state.customer_identifier}"
        return
      end

      # Step 2: Analyze emotional compatibility with active campaigns
      compatibility_results = analyze_campaign_compatibility(active_campaigns)

      # Step 3: Apply real-time adjustments to incompatible campaigns
      adjustment_results = apply_real_time_adjustments(compatibility_results)

      # Step 4: Log adaptation results
      log_adaptation_results(adjustment_results)

      # Step 5: Trigger notifications if critical adjustments were made
      trigger_notifications_if_needed(adjustment_results)

      Rails.logger.info "Successfully processed adaptive adjustments for #{active_campaigns.size} campaigns"

    rescue => error
      Rails.logger.error "AdaptiveCampaignAdjustmentJob failed: #{error.message}"
      Rails.logger.error error.backtrace.join("\n")

      # Log failure for monitoring
      AlertLog.create!(
        tenant: @tenant,
        alert_type: "adaptive_campaign_failure",
        severity: "high",
        message: "Failed to process adaptive campaign adjustment: #{error.message}",
        metadata: {
          customer_identifier: @customer_emotional_state.customer_identifier,
          emotional_state: @customer_emotional_state.current_emotion,
          error_class: error.class.name
        }
      )

      raise
    end
  end

  private

  def find_active_campaigns_for_customer
    # Find campaigns that are currently active and could be affecting this customer
    # This would typically involve matching campaign target audiences, segments, etc.

    customer_identifier = @customer_emotional_state.customer_identifier

    # For this implementation, we'll find campaigns by target audience matching
    # In production, this would use more sophisticated customer segmentation
    active_campaigns = @tenant.campaigns
                             .where(status: [ :active, :draft ])
                             .where("start_date <= ? OR start_date IS NULL", Date.current)
                             .where("end_date >= ? OR end_date IS NULL", Date.current)
                             .includes(:email_campaign, :social_campaign, :seo_campaign, :vibe_analysis_records)

    # Filter campaigns that might target this customer
    # This is a simplified version - production would use sophisticated targeting
    relevant_campaigns = active_campaigns.select do |campaign|
      campaign_targets_customer?(campaign, customer_identifier)
    end

    Rails.logger.info "Found #{relevant_campaigns.size} active campaigns potentially affecting customer"
    relevant_campaigns
  end

  def campaign_targets_customer?(campaign, customer_identifier)
    # Simplified customer targeting logic
    # In production, this would check:
    # - Customer segments
    # - Behavioral targeting rules
    # - Geographic targeting
    # - Demographic matching
    # - Previous campaign interactions

    # For now, assume all campaigns could target any customer
    # unless specifically excluded

    excluded_customers = campaign.settings.dig("excluded_customers") || []
    !excluded_customers.include?(customer_identifier)
  end

  def analyze_campaign_compatibility(campaigns)
    adapter = EmotionalCampaignAdapter.new(
      emotional_state: @customer_emotional_state,
      tenant: @tenant
    )

    compatibility_results = campaigns.map do |campaign|
      compatibility_score = @customer_emotional_state.campaign_compatibility_score(campaign)
      campaign_prediction = @customer_emotional_state.predict_campaign_response(campaign)

      {
        campaign: campaign,
        compatibility_score: compatibility_score,
        prediction: campaign_prediction,
        requires_adjustment: requires_adjustment?(compatibility_score, campaign_prediction),
        adjustment_urgency: calculate_adjustment_urgency(compatibility_score, campaign_prediction)
      }
    end

    # Sort by urgency (most urgent first)
    compatibility_results.sort_by { |result| -result[:adjustment_urgency] }
  end

  def requires_adjustment?(compatibility_score, prediction)
    # Adjustment required if:
    # 1. Low compatibility score (< 40%)
    # 2. High risk factors identified
    # 3. Customer not receptive and campaign is promotional

    low_compatibility = compatibility_score < 40.0
    high_risk = prediction[:risk_factors].any? { |risk| risk[:severity] == "high" }
    not_receptive = !@customer_emotional_state.receptive_to_marketing?

    low_compatibility || high_risk || not_receptive
  end

  def calculate_adjustment_urgency(compatibility_score, prediction)
    # Calculate urgency score (0-100) based on:
    # - Compatibility score (lower = more urgent)
    # - Risk severity (higher = more urgent)
    # - Customer receptivity (not receptive = more urgent)

    urgency_score = 0

    # Compatibility urgency (0-40 points)
    compatibility_urgency = [ 100 - compatibility_score, 40 ].min
    urgency_score += compatibility_urgency

    # Risk urgency (0-30 points)
    high_risk_count = prediction[:risk_factors].count { |risk| risk[:severity] == "high" }
    medium_risk_count = prediction[:risk_factors].count { |risk| risk[:severity] == "medium" }
    risk_urgency = (high_risk_count * 20) + (medium_risk_count * 10)
    urgency_score += [ risk_urgency, 30 ].min

    # Receptivity urgency (0-30 points)
    unless @customer_emotional_state.receptive_to_marketing?
      urgency_score += 30
    end

    [ urgency_score, 100 ].min
  end

  def apply_real_time_adjustments(compatibility_results)
    adjustment_results = []

    compatibility_results.each do |result|
      next unless result[:requires_adjustment]

      campaign = result[:campaign]
      urgency = result[:adjustment_urgency]

      Rails.logger.info "Applying real-time adjustment to campaign #{campaign.id} (urgency: #{urgency})"

      # Create adapter for this specific campaign
      adapter = EmotionalCampaignAdapter.new(
        emotional_state: @customer_emotional_state,
        tenant: @tenant,
        campaign: campaign
      )

      # Apply adjustments
      adjustment_result = adapter.apply_to_campaign(campaign)

      if adjustment_result[:success]
        adjustment_results << {
          campaign_id: campaign.id,
          campaign_name: campaign.name,
          adjustments_applied: adjustment_result[:applied_adjustments],
          urgency: urgency,
          status: "success"
        }

        # Create audit trail
        create_adjustment_audit_trail(campaign, adjustment_result, urgency)
      else
        Rails.logger.error "Failed to apply adjustments to campaign #{campaign.id}: #{adjustment_result[:error]}"
        adjustment_results << {
          campaign_id: campaign.id,
          campaign_name: campaign.name,
          error: adjustment_result[:error],
          urgency: urgency,
          status: "failed"
        }
      end
    end

    adjustment_results
  end

  def create_adjustment_audit_trail(campaign, adjustment_result, urgency)
    # Create audit record for compliance and analysis
    audit_data = {
      customer_identifier: @customer_emotional_state.customer_identifier,
      campaign_id: campaign.id,
      campaign_name: campaign.name,
      emotional_context: {
        emotion: @customer_emotional_state.current_emotion,
        intensity: @customer_emotional_state.emotional_intensity,
        confidence: @customer_emotional_state.confidence_score
      },
      adjustments_applied: adjustment_result[:applied_adjustments],
      urgency_score: urgency,
      adjustment_timestamp: Time.current,
      system_version: "marketing_therapist_v1.0"
    }

    # Store in campaign settings for audit purposes
    campaign.settings = campaign.settings.merge({
      emotional_adaptations: (campaign.settings["emotional_adaptations"] || {}).merge({
        audit_trail: (campaign.settings.dig("emotional_adaptations", "audit_trail") || []) + [ audit_data ]
      })
    })
    campaign.save!

    # Also create alert log for monitoring
    AlertLog.create!(
      tenant: @tenant,
      alert_type: "adaptive_campaign_adjustment",
      severity: urgency > 80 ? "high" : "medium",
      message: "Real-time campaign adjustment applied due to emotional state change",
      metadata: audit_data
    )
  end

  def log_adaptation_results(adjustment_results)
    total_campaigns = adjustment_results.size
    successful_adjustments = adjustment_results.count { |result| result[:status] == "success" }
    failed_adjustments = adjustment_results.count { |result| result[:status] == "failed" }

    Rails.logger.info "Adaptive campaign adjustment summary:"
    Rails.logger.info "  Customer: #{@customer_emotional_state.customer_identifier}"
    Rails.logger.info "  Emotional state: #{@customer_emotional_state.current_emotion} (#{@customer_emotional_state.confidence_score}%)"
    Rails.logger.info "  Total campaigns processed: #{total_campaigns}"
    Rails.logger.info "  Successful adjustments: #{successful_adjustments}"
    Rails.logger.info "  Failed adjustments: #{failed_adjustments}"

    # Log detailed results
    adjustment_results.each do |result|
      if result[:status] == "success"
        Rails.logger.info "  ✓ Campaign #{result[:campaign_id]}: #{result[:adjustments_applied].keys.join(', ')}"
      else
        Rails.logger.error "  ✗ Campaign #{result[:campaign_id]}: #{result[:error]}"
      end
    end
  end

  def trigger_notifications_if_needed(adjustment_results)
    # Trigger notifications for high-urgency adjustments or failures
    high_urgency_adjustments = adjustment_results.select { |result| result[:urgency] > 80 }
    failed_adjustments = adjustment_results.select { |result| result[:status] == "failed" }

    if high_urgency_adjustments.any?
      trigger_high_urgency_notification(high_urgency_adjustments)
    end

    if failed_adjustments.any?
      trigger_failure_notification(failed_adjustments)
    end

    # Trigger success notification for significant adaptations
    if adjustment_results.size > 5
      trigger_bulk_adaptation_notification(adjustment_results)
    end
  end

  def trigger_high_urgency_notification(high_urgency_adjustments)
    # In production, this would send notifications to marketing team
    Rails.logger.warn "HIGH URGENCY: #{high_urgency_adjustments.size} campaigns required immediate emotional adaptation"

    AlertLog.create!(
      tenant: @tenant,
      alert_type: "high_urgency_emotional_adaptation",
      severity: "high",
      message: "High-urgency emotional adaptations applied to multiple campaigns",
      metadata: {
        customer_identifier: @customer_emotional_state.customer_identifier,
        affected_campaigns: high_urgency_adjustments.map { |adj| adj[:campaign_id] },
        emotional_state: @customer_emotional_state.current_emotion,
        adaptation_count: high_urgency_adjustments.size
      }
    )
  end

  def trigger_failure_notification(failed_adjustments)
    Rails.logger.error "ADAPTATION FAILURES: #{failed_adjustments.size} campaigns failed to adapt"

    AlertLog.create!(
      tenant: @tenant,
      alert_type: "emotional_adaptation_failures",
      severity: "critical",
      message: "Some campaigns failed to adapt to emotional state changes",
      metadata: {
        customer_identifier: @customer_emotional_state.customer_identifier,
        failed_campaigns: failed_adjustments.map { |adj| { id: adj[:campaign_id], error: adj[:error] } },
        failure_count: failed_adjustments.size
      }
    )
  end

  def trigger_bulk_adaptation_notification(adjustment_results)
    Rails.logger.info "BULK ADAPTATION: #{adjustment_results.size} campaigns adapted for emotional state change"

    AlertLog.create!(
      tenant: @tenant,
      alert_type: "bulk_emotional_adaptation",
      severity: "info",
      message: "Large number of campaigns adapted due to emotional state change",
      metadata: {
        customer_identifier: @customer_emotional_state.customer_identifier,
        adaptation_count: adjustment_results.size,
        emotional_context: {
          emotion: @customer_emotional_state.current_emotion,
          intensity: @customer_emotional_state.emotional_intensity
        }
      }
    )
  end
end
