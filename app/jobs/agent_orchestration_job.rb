# frozen_string_literal: true

# Background job for orchestrating AI marketing workflows
class AgentOrchestrationJob < ApplicationJob
  queue_as :default
  retry_on StandardError, wait: :exponentially_longer, attempts: 3
  retry_on OpenAiService::RateLimitError, wait: 2.minutes, attempts: 5

  def perform(campaign_id, workflow_type: "multi_channel_coordination", context: {})
    campaign = Campaign.find(campaign_id)

    # Set tenant context for multi-tenancy
    ActsAsTenant.with_tenant(campaign.tenant) do
      orchestrator = MarketingManagerAgentService.new(
        campaign: campaign,
        context: context.with_indifferent_access
      )

      case workflow_type.to_s
      when "campaign_generation"
        success = orchestrator.generate_multi_channel_content
      when "performance_analysis"
        success = orchestrator.analyze_and_optimize_performance
      else
        success = orchestrator.orchestrate_campaign_workflow
      end

      if success
        Rails.logger.info "✅ Agent orchestration completed for campaign #{campaign_id}"
        broadcast_completion_update(campaign)
      else
        Rails.logger.error "❌ Agent orchestration failed for campaign #{campaign_id}"
        broadcast_error_update(campaign)
      end
    end
  rescue => error
    Rails.logger.error "Agent orchestration job failed: #{error.message}"
    Rails.logger.error error.backtrace.join("\n")

    # Find workflow and mark as failed
    workflow = AgentWorkflow.find_by(campaign_id: campaign_id, status: :running)
    workflow&.mark_as_failed!(error.message, {
      job_error: true,
      error_class: error.class.name,
      backtrace: error.backtrace&.first(5)
    })

    broadcast_error_update(Campaign.find(campaign_id)) if campaign_id
    raise
  end

  private

  def broadcast_completion_update(campaign)
    Turbo::StreamsChannel.broadcast_update_to(
      "campaign_#{campaign.id}",
      target: "campaign-status-#{campaign.id}",
      partial: "campaigns/status_badge",
      locals: { campaign: campaign }
    )
  end

  def broadcast_error_update(campaign)
    Turbo::StreamsChannel.broadcast_update_to(
      "campaign_#{campaign.id}",
      target: "campaign-ai-status-#{campaign.id}",
      partial: "campaigns/ai_status",
      locals: { campaign: campaign, status: "error" }
    )
  end
end
