# frozen_string_literal: true

# Background job for orchestrating AI agent workflows
# Uses Solid Queue for reliable job processing in Rails 8
class AgentOrchestrationJob < ApplicationJob
  queue_as :critical
  retry_on StandardError, wait: :exponentially_longer, attempts: 3

  # Job-level error handling
  rescue_from AgentOrchestrationService::ValidationError do |error|
    Rails.logger.error("Agent orchestration validation failed: #{error.message}")
    mark_workflow_as_failed(error.message)
  end

  rescue_from Timeout::Error do |error|
    Rails.logger.error("Agent orchestration timed out: #{error.message}")
    mark_workflow_as_failed("Workflow timed out after #{job_timeout} seconds")
  end

  def perform(campaign_id, workflow_type:, context: {}, workflow_id: nil)
    @campaign_id = campaign_id
    @workflow_type = workflow_type
    @context = context
    @workflow_id = workflow_id

    # Set up tenant context
    setup_tenant_context

    # Execute the orchestration
    execute_orchestration_with_monitoring

  rescue => error
    handle_job_failure(error)
    raise # Re-raise to let Solid Queue handle retries
  end

  private

  def setup_tenant_context
    @campaign = Campaign.find(@campaign_id)
    @tenant = @campaign.tenant
    
    # Set the current tenant for multi-tenancy
    ActsAsTenant.current_tenant = @tenant
    
    Rails.logger.info("Starting agent orchestration for campaign #{@campaign_id} (tenant: #{@tenant.id})")
  end

  def execute_orchestration_with_monitoring
    # Find or create workflow
    @workflow = find_or_create_workflow

    # Start performance monitoring
    start_time = Time.current
    
    # Create and execute orchestration service
    orchestration_service = AgentOrchestrationService.new(
      campaign: @campaign,
      workflow_type: @workflow_type,
      context: enhanced_context
    )

    # Execute the orchestration
    success = orchestration_service.orchestrate!
    
    # Log completion
    duration = Time.current - start_time
    log_orchestration_completion(success, duration)

    # Broadcast real-time updates
    broadcast_completion_update(success)

    success
  end

  def find_or_create_workflow
    if @workflow_id
      AgentWorkflow.find(@workflow_id)
    else
      @campaign.agent_workflows.create!(
        workflow_type: @workflow_type,
        status: :pending,
        context_data: enhanced_context,
        total_steps: calculate_workflow_steps
      )
    end
  end

  def enhanced_context
    @enhanced_context ||= @context.merge(
      job_metadata: {
        job_id: job_id,
        queue_name: queue_name,
        scheduled_at: scheduled_at,
        executed_at: Time.current,
        tenant_id: @tenant.id
      },
      performance_tracking: {
        timeout_limit: job_timeout,
        retry_attempt: executions,
        max_retries: 3
      },
      ai_configuration: extract_ai_configuration
    )
  end

  def calculate_workflow_steps
    case @workflow_type
    when 'marketing_orchestration'
      5 # Analysis, Strategy, Delegation, Monitoring, Aggregation
    when 'email_specialist'
      4 # Audience Analysis, Content Generation, Delivery Optimization, Scheduling
    when 'social_specialist'
      5 # Platform Analysis, Content Generation, Hashtag Optimization, Scheduling, Monitoring
    when 'seo_specialist'
      4 # Keyword Research, Content Optimization, Technical SEO, Monitoring Setup
    else
      3 # Default steps
    end
  end

  def extract_ai_configuration
    {
      primary_provider: Rails.application.credentials.dig(:ai, :primary_provider) || 'openai',
      fallback_provider: Rails.application.credentials.dig(:ai, :fallback_provider) || 'anthropic',
      model_preferences: @tenant.settings&.dig('ai_preferences') || default_model_preferences,
      rate_limits: extract_rate_limits,
      quality_settings: extract_quality_settings
    }
  end

  def default_model_preferences
    {
      content_generation: 'gpt-4o',
      analysis: 'gpt-4o-mini',
      optimization: 'claude-3-5-sonnet'
    }
  end

  def extract_rate_limits
    {
      requests_per_minute: 60,
      tokens_per_request: 4000,
      daily_budget_limit: @tenant.settings&.dig('ai_budget_limit') || 100.0
    }
  end

  def extract_quality_settings
    {
      creativity_level: @context.dig(:preferences, :creativity_level) || 'balanced',
      accuracy_threshold: @context.dig(:preferences, :accuracy_threshold) || 0.85,
      review_required: @context.dig(:preferences, :review_required) || true
    }
  end

  def log_orchestration_completion(success, duration)
    if success
      Rails.logger.info(
        "Agent orchestration completed successfully for campaign #{@campaign_id} " \
        "in #{duration.round(2)} seconds"
      )
    else
      Rails.logger.error(
        "Agent orchestration failed for campaign #{@campaign_id} " \
        "after #{duration.round(2)} seconds"
      )
    end

    # Log performance metrics
    log_performance_metrics(duration, success)
  end

  def log_performance_metrics(duration, success)
    # Record AI usage for billing/monitoring
    @tenant.ai_usage_records.create!(
      campaign: @campaign,
      workflow_type: @workflow_type,
      job_id: job_id,
      duration_seconds: duration,
      success: success,
      cost: calculate_orchestration_cost(duration),
      metadata: {
        steps_completed: @workflow&.progress_percent || 0,
        retry_count: executions,
        queue_wait_time: calculate_queue_wait_time
      }
    )
  end

  def calculate_orchestration_cost(duration)
    # Simplified cost calculation - in reality, this would be based on AI API usage
    base_cost = case @workflow_type
    when 'marketing_orchestration'
      0.50 # Base cost for orchestration
    when 'email_specialist'
      0.15
    when 'social_specialist'
      0.20
    when 'seo_specialist'
      0.25
    else
      0.10
    end

    # Adjust for duration (longer = more AI calls)
    duration_multiplier = [duration / 60.0, 0.5].max # Minimum 0.5x multiplier
    (base_cost * duration_multiplier).round(4)
  end

  def calculate_queue_wait_time
    return 0 unless scheduled_at && Time.current

    (Time.current - scheduled_at).to_f
  end

  def broadcast_completion_update(success)
    # Broadcast to tenant-specific channel for real-time dashboard updates
    Turbo::StreamsChannel.broadcast_replace_to(
      "agent_workflows_#{@tenant.id}",
      target: "workflow-item-#{@workflow.id}",
      partial: "dashboard/workflow_item",
      locals: { workflow: @workflow.reload }
    )

    # Broadcast metrics update
    broadcast_metrics_update
  end

  def broadcast_metrics_update
    Turbo::StreamsChannel.broadcast_replace_to(
      "agent_workflows_#{@tenant.id}",
      target: "agent-performance-metrics",
      partial: "dashboard/agent_performance_metrics",
      locals: { 
        metrics: calculate_current_metrics,
        tenant: @tenant 
      }
    )
  end

  def calculate_current_metrics
    workflows = @tenant.agent_workflows
    
    {
      total_workflows: workflows.count,
      active_workflows: workflows.active.count,
      success_rate: calculate_success_rate(workflows),
      average_duration: calculate_average_duration(workflows)
    }
  end

  def calculate_success_rate(workflows)
    finished_workflows = workflows.where(status: [:completed, :failed])
    return 0 if finished_workflows.empty?

    successful_workflows = finished_workflows.completed
    ((successful_workflows.count.to_f / finished_workflows.count) * 100).round(1)
  end

  def calculate_average_duration(workflows)
    completed_workflows = workflows.completed
                                   .where.not(started_at: nil)
                                   .where.not(completed_at: nil)

    return 0 if completed_workflows.empty?

    total_duration = completed_workflows.sum do |workflow|
      workflow.completed_at - workflow.started_at
    end

    (total_duration / completed_workflows.count).round(2)
  end

  def handle_job_failure(error)
    Rails.logger.error(
      "Agent orchestration job failed for campaign #{@campaign_id}: " \
      "#{error.class.name} - #{error.message}"
    )

    # Mark workflow as failed if it exists
    mark_workflow_as_failed(error.message, error) if @workflow

    # Send failure notification if configured
    send_failure_notification(error) if should_send_notifications?

    # Record failure metrics
    record_failure_metrics(error)
  end

  def mark_workflow_as_failed(message, error = nil)
    return unless @workflow

    error_details = {
      message: message,
      error_class: error&.class&.name,
      job_id: job_id,
      retry_count: executions,
      failed_at: Time.current
    }

    error_details[:backtrace] = error.backtrace&.first(10) if error

    @workflow.mark_as_failed!(message, error_details)
  end

  def send_failure_notification(error)
    # This could send email, Slack notification, etc.
    NotificationService.send_workflow_failure_alert(
      tenant: @tenant,
      campaign: @campaign,
      workflow: @workflow,
      error: error
    )
  rescue => notification_error
    Rails.logger.error("Failed to send failure notification: #{notification_error.message}")
  end

  def should_send_notifications?
    @tenant.settings&.dig('notifications', 'workflow_failures') != false
  end

  def record_failure_metrics(error)
    # Record failure for monitoring and alerting
    begin
      @tenant.ai_usage_records.create!(
        campaign: @campaign,
        workflow_type: @workflow_type,
        job_id: job_id,
        success: false,
        cost: 0.0,
        metadata: {
          error_class: error.class.name,
          error_message: error.message,
          retry_count: executions,
          failed_at: Time.current
        }
      )
    rescue => record_error
      Rails.logger.error("Failed to record failure metrics: #{record_error.message}")
    end
  end

  def job_timeout
    case @workflow_type
    when 'marketing_orchestration'
      30.minutes # Complex orchestration needs more time
    when 'email_specialist'
      10.minutes
    when 'social_specialist'
      15.minutes # Multiple platforms
    when 'seo_specialist'
      20.minutes # SEO analysis can be intensive
    else
      5.minutes
    end
  end
end
