# frozen_string_literal: true

##
# Store AI Usage Job
#
# Background job to store AI usage data in the database for long-term analytics.
# This job is called asynchronously from AiUsageTracker to avoid blocking
# the main request flow while ensuring data persistence.
#
# @example Usage
#   StoreAiUsageJob.perform_later(usage_data)
#
class StoreAiUsageJob < ApplicationJob
  queue_as :default

  # Retry configuration
  retry_on StandardError, wait: :exponentially_longer, attempts: 3

  def perform(usage_data)
    # Validate required data
    return unless usage_data.is_a?(Hash) && usage_data[:tenant_id].present?

    # Store in AI usage records table
    ai_usage_record = AiUsageRecord.create!(
      tenant_id: usage_data[:tenant_id],
      model_used: usage_data[:model],
      task_type: usage_data[:task_type],
      input_tokens: usage_data[:input_tokens] || 0,
      output_tokens: usage_data[:output_tokens] || 0,
      total_tokens: usage_data[:total_tokens] || 0,
      cost_cents: (usage_data[:cost] * 100).to_i,
      duration_ms: usage_data[:duration_ms] || 0,
      request_metadata: {
        provider: usage_data[:provider],
        endpoint: usage_data[:endpoint],
        temperature: usage_data[:temperature],
        max_tokens: usage_data[:max_tokens],
        user_agent: usage_data[:user_agent],
        ip_address: usage_data[:ip_address]
      }.compact,
      usage_date: Date.parse(usage_data[:date])
    )

    # Update aggregated metrics if needed
    update_monthly_aggregates(usage_data)

    # Send to external analytics if configured
    send_to_external_analytics(usage_data) if external_analytics_enabled?

    Rails.logger.info("Stored AI usage record: #{ai_usage_record.id}")
  rescue => e
    Rails.logger.error("Failed to store AI usage data: #{e.message}")
    Rails.logger.error(e.backtrace.join("\n"))
    raise
  end

  private

  def update_monthly_aggregates(usage_data)
    # Update monthly aggregated statistics for faster reporting
    date = Date.parse(usage_data[:date])
    month_key = date.beginning_of_month

    # This could be implemented as a separate model for monthly aggregates
    # For now, we'll use Rails cache with longer expiration
    monthly_key = "ai_usage_monthly:#{usage_data[:tenant_id]}:#{month_key}"
    monthly_data = Rails.cache.read(monthly_key) || {
      total_requests: 0,
      total_tokens: 0,
      total_cost: 0.0,
      models: {},
      task_types: {}
    }

    # Update totals
    monthly_data[:total_requests] += 1
    monthly_data[:total_tokens] += usage_data[:total_tokens] || 0
    monthly_data[:total_cost] += usage_data[:cost] || 0.0

    # Update model-specific data
    model = usage_data[:model] || "unknown"
    monthly_data[:models][model] ||= { requests: 0, tokens: 0, cost: 0.0 }
    monthly_data[:models][model][:requests] += 1
    monthly_data[:models][model][:tokens] += usage_data[:total_tokens] || 0
    monthly_data[:models][model][:cost] += usage_data[:cost] || 0.0

    # Update task-type-specific data
    task_type = usage_data[:task_type] || "unknown"
    monthly_data[:task_types][task_type] ||= { requests: 0, tokens: 0, cost: 0.0 }
    monthly_data[:task_types][task_type][:requests] += 1
    monthly_data[:task_types][task_type][:tokens] += usage_data[:total_tokens] || 0
    monthly_data[:task_types][task_type][:cost] += usage_data[:cost] || 0.0

    # Store updated monthly data (expires after 2 months)
    Rails.cache.write(monthly_key, monthly_data, expires_in: 2.months)
  end

  def send_to_external_analytics(usage_data)
    # Send to external analytics services (Datadog, New Relic, etc.)
    return unless ENV["EXTERNAL_ANALYTICS_ENABLED"] == "true"

    begin
      # Example: Send to Datadog
      if ENV["DATADOG_API_KEY"].present?
        send_to_datadog(usage_data)
      end

      # Example: Send to custom webhook
      if ENV["ANALYTICS_WEBHOOK_URL"].present?
        send_to_webhook(usage_data)
      end
    rescue => e
      Rails.logger.warn("Failed to send to external analytics: #{e.message}")
      # Don't re-raise - external analytics failures shouldn't fail the job
    end
  end

  def send_to_datadog(usage_data)
    # Implementation for Datadog metrics
    # This would use the Datadog gem if available
    Rails.logger.info("Would send to Datadog: #{usage_data[:model]} usage")
  end

  def send_to_webhook(usage_data)
    # Implementation for webhook analytics
    webhook_url = ENV["ANALYTICS_WEBHOOK_URL"]

    payload = {
      event: "ai_usage",
      timestamp: Time.current.iso8601,
      tenant_id: usage_data[:tenant_id],
      model: usage_data[:model],
      task_type: usage_data[:task_type],
      tokens: usage_data[:total_tokens],
      cost: usage_data[:cost],
      duration_ms: usage_data[:duration_ms]
    }

    # This would use Faraday or similar HTTP client
    Rails.logger.info("Would send to webhook: #{webhook_url}")
  end

  def external_analytics_enabled?
    ENV["EXTERNAL_ANALYTICS_ENABLED"] == "true"
  end
end
