# frozen_string_literal: true

class AgentWorkflowsController < ApplicationController
  before_action :authenticate_user!
  before_action :set_current_tenant
  before_action :set_campaign
  before_action :set_workflow, only: [ :show, :cancel, :retry ]

  def index
    @workflows = @campaign.agent_workflows
                          .includes(:campaign)
                          .recent
                          .limit(20)

    @active_workflows = @workflows.active
    @recent_workflows = @workflows.limit(10)

    # Real-time updates
    respond_to do |format|
      format.html
      format.turbo_stream
    end
  end

  def show
    @progress_data = calculate_progress_data
    @timeline = build_workflow_timeline

    respond_to do |format|
      format.html
      format.turbo_stream
      format.json { render json: workflow_json }
    end
  end

  def create
    workflow_params = params.require(:agent_workflow).permit(:workflow_type)
    context = extract_workflow_context

    # Start AI workflow asynchronously
    AgentOrchestrationJob.perform_later(
      @campaign.id,
      workflow_type: workflow_params[:workflow_type],
      context: context
    )

    redirect_to campaign_agent_workflows_path(@campaign),
                notice: "AI workflow started successfully! 🤖"
  end

  def generate_content
    agent_type = params[:agent_type]
    validate_agent_type!(agent_type)

    context = extract_content_context

    ContentGenerationJob.perform_later(@campaign.id, agent_type, context: context)

    respond_to do |format|
      format.turbo_stream do
        render turbo_stream: turbo_stream.update(
          "#{agent_type}-content-status",
          partial: "agent_workflows/generating_status",
          locals: { agent_type: agent_type, campaign: @campaign }
        )
      end
      format.html { redirect_to @campaign, notice: "#{agent_type.capitalize} content generation started!" }
    end
  end

  def cancel
    if @workflow.running? || @workflow.pending?
      @workflow.update!(status: :cancelled)

      respond_to do |format|
        format.turbo_stream do
          render turbo_stream: turbo_stream.update(
            "workflow-#{@workflow.id}",
            partial: "agent_workflows/workflow_card",
            locals: { workflow: @workflow }
          )
        end
        format.html { redirect_to campaign_agent_workflows_path(@campaign), notice: "Workflow cancelled" }
      end
    else
      redirect_to campaign_agent_workflows_path(@campaign), alert: "Cannot cancel this workflow"
    end
  end

  def retry
    if @workflow.failed?
      context = @workflow.context_data.except("error_details", "failed_at")

      AgentOrchestrationJob.perform_later(
        @campaign.id,
        workflow_type: @workflow.workflow_type,
        context: context
      )

      redirect_to campaign_agent_workflows_path(@campaign),
                  notice: "Workflow retry initiated! 🔄"
    else
      redirect_to campaign_agent_workflows_path(@campaign),
                  alert: "Can only retry failed workflows"
    end
  end

  def bulk_generate
    selected_types = params[:agent_types] || []
    valid_types = selected_types.select { |type| valid_agent_type?(type) }

    if valid_types.any?
      context = extract_content_context

      valid_types.each do |agent_type|
        ContentGenerationJob.perform_later(@campaign.id, agent_type, context: context)
      end

      flash[:notice] = "Content generation started for: #{valid_types.map(&:capitalize).join(', ')} 🚀"
    else
      flash[:alert] = "Please select at least one valid agent type"
    end

    redirect_to @campaign
  end

  def optimize_campaign
    context = {
      optimization_focus: params[:optimization_focus] || "performance",
      include_performance_data: params[:include_performance_data] == "true",
      optimization_goals: extract_optimization_goals
    }

    AgentOrchestrationJob.perform_later(
      @campaign.id,
      workflow_type: "performance_analysis",
      context: context
    )

    redirect_to campaign_agent_workflows_path(@campaign),
                notice: "AI optimization analysis started! 📊"
  end

  private

  def set_current_tenant
    ActsAsTenant.current_tenant = current_user.tenant
  end

  def current_tenant
    current_user.tenant
  end

  def set_campaign
    @campaign = current_tenant.campaigns.find(params[:campaign_id])
  end

  def set_workflow
    @workflow = @campaign.agent_workflows.find(params[:id])
  end

  def extract_workflow_context
    {
      user_id: current_user.id,
      initiated_by: current_user.full_name,
      optimization_goals: extract_optimization_goals,
      campaign_settings: @campaign.settings,
      tenant_settings: current_tenant.settings
    }
  end

  def extract_content_context
    {
      user_preferences: current_user_preferences,
      brand_guidelines: extract_brand_guidelines,
      content_goals: extract_content_goals,
      approval_workflow: extract_approval_settings
    }
  end

  def extract_optimization_goals
    params.dig(:workflow_context, :optimization_goals) ||
    @campaign.settings.dig("optimization_goals") || {
      "target_ctr" => 3.0,
      "target_conversion_rate" => 2.5,
      "target_roas" => 400.0
    }
  end

  def current_user_preferences
    {
      content_tone: params.dig(:preferences, :content_tone) || "professional",
      automation_level: params.dig(:preferences, :automation_level) || "balanced",
      review_required: params.dig(:preferences, :review_required) || true
    }
  end

  def extract_brand_guidelines
    current_tenant.settings.dig("brand_guidelines") || {
      "voice" => "professional",
      "tone" => "helpful",
      "style" => "modern"
    }
  end

  def extract_content_goals
    @campaign.settings.dig("content_goals") || [
      "increase_engagement",
      "drive_conversions",
      "build_awareness"
    ]
  end

  def extract_approval_settings
    {
      require_approval: current_tenant.settings.dig("require_content_approval") || false,
      approver_email: current_user.email,
      auto_publish: false
    }
  end

  def validate_agent_type!(agent_type)
    unless valid_agent_type?(agent_type)
      raise ArgumentError, "Invalid agent type: #{agent_type}"
    end
  end

  def valid_agent_type?(agent_type)
    %w[email social seo].include?(agent_type.to_s)
  end

  def calculate_progress_data
    return {} unless @workflow

    {
      current_step: @workflow.current_step,
      progress_percent: @workflow.progress_percent,
      total_steps: @workflow.total_steps,
      estimated_completion: estimate_completion_time,
      steps_completed: count_completed_steps,
      error_count: count_errors
    }
  end

  def estimate_completion_time
    return nil unless @workflow.running? && @workflow.started_at

    elapsed = Time.current - @workflow.started_at
    if @workflow.progress_percent > 0
      total_estimated = elapsed * (100.0 / @workflow.progress_percent)
      remaining = total_estimated - elapsed
      Time.current + remaining
    else
      nil
    end
  end

  def count_completed_steps
    @workflow.results.keys.count
  end

  def count_errors
    @workflow.error_details.dig("errors")&.count || 0
  end

  def build_workflow_timeline
    return [] unless @workflow

    timeline = []

    # Started event
    if @workflow.started_at
      timeline << {
        timestamp: @workflow.started_at,
        event: "Workflow Started",
        description: "#{@workflow.workflow_type.humanize} workflow initiated",
        status: "info"
      }
    end

    # Progress events from results
    @workflow.results.each do |step, data|
      timeline << {
        timestamp: data["completed_at"] || @workflow.updated_at,
        event: step.humanize,
        description: data["description"] || "#{step.humanize} completed",
        status: data["status"] || "success"
      }
    end

    # Error events
    if @workflow.error_details.any?
      timeline << {
        timestamp: @workflow.error_details["occurred_at"] || @workflow.updated_at,
        event: "Error Occurred",
        description: @workflow.error_details["message"],
        status: "error"
      }
    end

    # Completed event
    if @workflow.completed_at
      timeline << {
        timestamp: @workflow.completed_at,
        event: "Workflow Completed",
        description: "#{@workflow.workflow_type.humanize} finished successfully",
        status: "success"
      }
    end

    timeline.sort_by { |event| event[:timestamp] }
  end

  def workflow_json
    {
      id: @workflow.id,
      status: @workflow.status,
      workflow_type: @workflow.workflow_type,
      progress_percent: @workflow.progress_percent,
      current_step: @workflow.current_step,
      results: @workflow.results,
      error_details: @workflow.error_details,
      duration: @workflow.duration,
      started_at: @workflow.started_at,
      completed_at: @workflow.completed_at
    }
  end
end
