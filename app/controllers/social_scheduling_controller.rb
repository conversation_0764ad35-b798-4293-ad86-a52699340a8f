class SocialSchedulingController < ApplicationController
  before_action :authenticate_user!
  before_action :set_campaign
  before_action :set_social_campaign

  def index
    @scheduled_posts = @social_campaign.scheduled_posts.includes(:social_campaign)
    @calendar_data = build_calendar_data
    @optimal_times = calculate_optimal_posting_times
    @platform_stats = calculate_platform_scheduling_stats
  end

  def show
    @scheduled_post = @social_campaign.scheduled_posts.find(params[:id])
    @platform_metrics = fetch_platform_metrics(@scheduled_post.platform)
  end

  def new
    @scheduled_post = @social_campaign.scheduled_posts.build
    @available_platforms = @social_campaign.platforms
    @content_variants = @social_campaign.content_variants
    @optimal_times = calculate_optimal_posting_times
    @timezone_options = timezone_options_for_select
  end

  def create
    @scheduled_post = @social_campaign.scheduled_posts.build(scheduled_post_params)
    
    if @scheduled_post.save
      # Schedule background job for posting
      SocialPostingJob.set(wait_until: @scheduled_post.scheduled_at).perform_later(@scheduled_post.id)
      
      redirect_to campaign_social_scheduling_index_path(@campaign), 
                  notice: 'Post scheduled successfully!'
    else
      @available_platforms = @social_campaign.platforms
      @content_variants = @social_campaign.content_variants
      @optimal_times = calculate_optimal_posting_times
      @timezone_options = timezone_options_for_select
      render :new, status: :unprocessable_entity
    end
  end

  def update
    @scheduled_post = @social_campaign.scheduled_posts.find(params[:id])
    
    if @scheduled_post.update(scheduled_post_params)
      # Reschedule background job
      @scheduled_post.social_posting_jobs.pending.destroy_all
      SocialPostingJob.set(wait_until: @scheduled_post.scheduled_at).perform_later(@scheduled_post.id)
      
      redirect_to campaign_social_scheduling_path(@campaign, @scheduled_post), 
                  notice: 'Scheduled post updated successfully!'
    else
      render :show, status: :unprocessable_entity
    end
  end

  def destroy
    @scheduled_post = @social_campaign.scheduled_posts.find(params[:id])
    
    # Cancel background job
    @scheduled_post.social_posting_jobs.pending.destroy_all
    @scheduled_post.destroy
    
    redirect_to campaign_social_scheduling_index_path(@campaign), 
                notice: 'Scheduled post deleted successfully!'
  end

  def bulk_schedule
    content_params = params[:bulk_schedule]
    results = { success: 0, errors: [] }
    
    content_params[:posts].each do |post_data|
      scheduled_post = @social_campaign.scheduled_posts.build(
        platform: post_data[:platform],
        content: post_data[:content],
        scheduled_at: post_data[:scheduled_at],
        media_urls: post_data[:media_urls],
        hashtags: post_data[:hashtags],
        status: 'scheduled'
      )
      
      if scheduled_post.save
        SocialPostingJob.set(wait_until: scheduled_post.scheduled_at).perform_later(scheduled_post.id)
        results[:success] += 1
      else
        results[:errors] << {
          platform: post_data[:platform],
          errors: scheduled_post.errors.full_messages
        }
      end
    end
    
    if results[:errors].empty?
      redirect_to campaign_social_scheduling_index_path(@campaign), 
                  notice: "Successfully scheduled #{results[:success]} posts!"
    else
      flash[:alert] = "Scheduled #{results[:success]} posts with #{results[:errors].count} errors."
      redirect_to campaign_social_scheduling_index_path(@campaign)
    end
  end

  def calendar_data
    start_date = Date.parse(params[:start]) rescue 1.month.ago.beginning_of_month
    end_date = Date.parse(params[:end]) rescue 1.month.from_now.end_of_month
    
    posts = @social_campaign.scheduled_posts
                           .where(scheduled_at: start_date..end_date)
                           .includes(:social_campaign)
    
    events = posts.map do |post|
      {
        id: post.id,
        title: "#{post.platform.titleize}: #{truncate(post.content, length: 50)}",
        start: post.scheduled_at.iso8601,
        platform: post.platform,
        status: post.status,
        content: post.content,
        url: campaign_social_scheduling_path(@campaign, post)
      }
    end
    
    render json: events
  end

  def optimal_times
    platform = params[:platform]
    audience_data = fetch_audience_insights(@social_campaign, platform)
    optimal_times = SocialMediaAnalyticsService.calculate_optimal_times(audience_data)
    
    render json: {
      platform: platform,
      optimal_times: optimal_times,
      timezone: @social_campaign.campaign.timezone || 'UTC'
    }
  end

  def duplicate_schedule
    @source_post = @social_campaign.scheduled_posts.find(params[:id])
    @new_post = @source_post.dup
    @new_post.scheduled_at = @source_post.scheduled_at + 1.week
    @new_post.status = 'draft'
    
    if @new_post.save
      render json: { 
        success: true, 
        message: 'Post duplicated and scheduled for next week',
        post_id: @new_post.id 
      }
    else
      render json: { 
        success: false, 
        errors: @new_post.errors.full_messages 
      }
    end
  end

  def batch_reschedule
    post_ids = params[:post_ids]
    time_shift = params[:time_shift].to_i.hours
    
    posts = @social_campaign.scheduled_posts.where(id: post_ids, status: 'scheduled')
    
    posts.each do |post|
      new_time = post.scheduled_at + time_shift
      post.update(scheduled_at: new_time)
      
      # Reschedule background job
      post.social_posting_jobs.pending.destroy_all
      SocialPostingJob.set(wait_until: new_time).perform_later(post.id)
    end
    
    render json: { 
      success: true, 
      message: "Rescheduled #{posts.count} posts",
      updated_count: posts.count 
    }
  end

  private

  def set_campaign
    @campaign = current_user.campaigns.find(params[:campaign_id])
  end

  def set_social_campaign
    @social_campaign = @campaign.social_campaign || @campaign.create_social_campaign!
  end

  def scheduled_post_params
    params.require(:scheduled_post).permit(
      :platform, :content, :scheduled_at, :timezone, :status,
      :auto_hashtags, :include_link, :media_type,
      media_urls: [], hashtags: [], mention_handles: []
    )
  end

  def build_calendar_data
    start_date = 1.month.ago.beginning_of_month
    end_date = 2.months.from_now.end_of_month
    
    @social_campaign.scheduled_posts
                   .where(scheduled_at: start_date..end_date)
                   .group_by { |post| post.scheduled_at.to_date }
  end

  def calculate_optimal_posting_times
    platforms = @social_campaign.platforms
    optimal_times = {}
    
    platforms.each do |platform|
      audience_data = fetch_audience_insights(@social_campaign, platform)
      optimal_times[platform] = SocialMediaAnalyticsService.calculate_optimal_times(audience_data)
    end
    
    optimal_times
  end

  def calculate_platform_scheduling_stats
    stats = {}
    
    @social_campaign.platforms.each do |platform|
      platform_posts = @social_campaign.scheduled_posts.where(platform: platform)
      
      stats[platform] = {
        total_scheduled: platform_posts.scheduled.count,
        posted: platform_posts.posted.count,
        failed: platform_posts.failed.count,
        pending: platform_posts.pending.count,
        next_post: platform_posts.scheduled.order(:scheduled_at).first&.scheduled_at
      }
    end
    
    stats
  end

  def fetch_platform_metrics(platform)
    begin
      service = SocialMediaAnalyticsService.new(@social_campaign)
      service.fetch_platform_metrics(platform)
    rescue => e
      Rails.logger.error "Error fetching platform metrics: #{e.message}"
      {}
    end
  end

  def fetch_audience_insights(social_campaign, platform)
    begin
      service = SocialMediaAnalyticsService.new(social_campaign)
      service.fetch_audience_insights(platform)
    rescue => e
      Rails.logger.error "Error fetching audience insights: #{e.message}"
      { peak_hours: [9, 12, 15, 18], peak_days: [1, 2, 3, 4, 5] } # Fallback
    end
  end

  def timezone_options_for_select
    [
      ['Eastern Time (UTC-5)', 'America/New_York'],
      ['Central Time (UTC-6)', 'America/Chicago'],
      ['Mountain Time (UTC-7)', 'America/Denver'],
      ['Pacific Time (UTC-8)', 'America/Los_Angeles'],
      ['UTC', 'UTC'],
      ['London (UTC+0)', 'Europe/London'],
      ['Paris (UTC+1)', 'Europe/Paris'],
      ['Tokyo (UTC+9)', 'Asia/Tokyo'],
      ['Sydney (UTC+10)', 'Australia/Sydney']
    ]
  end
end
