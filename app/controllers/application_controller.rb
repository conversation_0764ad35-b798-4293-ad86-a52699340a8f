class ApplicationController < ActionController::Base
  # Only allow modern browsers supporting webp images, web push, badges, import maps, CSS nesting, and CSS :has.
  allow_browser versions: :modern

  # Set tenant when user is signed in
  before_action :set_current_tenant, if: :user_signed_in?
  before_action :configure_permitted_parameters, if: :devise_controller?

  protected

  def set_current_tenant
    if user_signed_in? && current_user.tenant
      ActsAsTenant.current_tenant = current_user.tenant
    end
  end

  def configure_permitted_parameters
    devise_parameter_sanitizer.permit(:sign_up, keys: [ :first_name, :last_name ])
    devise_parameter_sanitizer.permit(:account_update, keys: [ :first_name, :last_name ])
  end

  # Use custom layout for Devise controllers
  def layout_by_resource
    if devise_controller?
      "auth"
    else
      "application"
    end
  end

  layout :layout_by_resource
end
