# frozen_string_literal: true

class ProfilesController < ApplicationController
  before_action :authenticate_user!
  before_action :set_user_preferences, only: [ :show, :edit, :update, :update_preferences ]

  def show
    @user = current_user
    @recent_activity = get_recent_activity
    @profile_completion = calculate_profile_completion
  end

  def edit
    @user = current_user
  end

  def update
    @user = current_user

    if @user.update(user_params)
      redirect_to profile_path, notice: "Profile updated successfully."
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def update_avatar
    @user = current_user

    if params[:avatar].present?
      if @user.avatar.attach(params[:avatar])
        redirect_to profile_path, notice: "Avatar updated successfully."
      else
        redirect_to profile_path, alert: "Failed to upload avatar. Please try again."
      end
    else
      redirect_to profile_path, alert: "Please select an avatar to upload."
    end
  end

  def update_preferences
    @user = current_user

    if @user_preferences.update(preference_params)
      redirect_to profile_path, notice: "Preferences updated successfully."
    else
      redirect_to profile_path, alert: "Failed to update preferences."
    end
  end

  private

  def set_user_preferences
    @user_preferences = current_user.user_preference || current_user.build_user_preference
  end

  def user_params
    params.require(:user).permit(:first_name, :last_name, :email, :avatar)
  end

  def preference_params
    params.require(:user_preference).permit(
      :timezone, :language, :date_format, :theme,
      :email_notifications, :push_notifications, :sms_notifications,
      :marketing_emails, :product_updates, :security_alerts
    )
  end

  def get_recent_activity
    # Get recent user activities
    activities = []

    # Recent campaigns
    recent_campaigns = current_user.created_campaigns.recent.limit(5)
    recent_campaigns.each do |campaign|
      activities << {
        type: "campaign",
        action: "created",
        item: campaign,
        timestamp: campaign.created_at
      }
    end

    # Recent audiences
    recent_audiences = current_user.created_audiences.recent.limit(3)
    recent_audiences.each do |audience|
      activities << {
        type: "audience",
        action: "created",
        item: audience,
        timestamp: audience.created_at
      }
    end

    # Sort by timestamp and return latest 10
    activities.sort_by { |a| a[:timestamp] }.reverse.first(10)
  end

  def calculate_profile_completion
    completion_score = 0
    total_fields = 9

    # Basic info
    completion_score += 1 if current_user.first_name.present?
    completion_score += 1 if current_user.last_name.present?
    completion_score += 1 if current_user.email.present?
    completion_score += 1 if current_user.avatar.attached?

    # Preferences
    if @user_preferences.persisted?
      completion_score += 1 if @user_preferences.timezone.present?
      completion_score += 1 if @user_preferences.language.present?
      completion_score += 1 if @user_preferences.theme.present?
    end

    # Platform connections
    completion_score += 1 if current_user.platform_configurations.active.any?

    # Recent activity
    completion_score += 1 if current_user.created_campaigns.any?

    ((completion_score.to_f / total_fields) * 100).round
  end
end
