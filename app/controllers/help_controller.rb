# frozen_string_literal: true

class HelpController < ApplicationController
  before_action :authenticate_user!

  def index
    @popular_articles = get_popular_articles
    @recent_updates = get_recent_updates
    @quick_links = get_quick_links
    @support_stats = get_support_stats
  end

  def show
    @article = find_article(params[:id])
    @related_articles = get_related_articles(@article)
    @was_helpful = false
  end

  def contact
    @contact_form = ContactForm.new
    @support_categories = get_support_categories
  end

  def create_contact
    @contact_form = ContactForm.new(contact_params)
    @support_categories = get_support_categories

    if @contact_form.valid?
      # Create support ticket
      ticket = current_user.support_tickets.create!(
        subject: @contact_form.subject,
        description: @contact_form.message,
        category: @contact_form.category,
        priority: determine_priority(@contact_form.category),
        status: "open"
      )

      # Send notification email (would be handled by a background job)
      # SupportMailer.new_ticket(ticket).deliver_later

      redirect_to support_ticket_path(ticket), notice: "Your support request has been submitted. We'll get back to you soon!"
    else
      render :contact, status: :unprocessable_entity
    end
  end

  def faq
    @faq_categories = get_faq_categories
    @popular_faqs = get_popular_faqs
  end

  def documentation
    @doc_sections = get_documentation_sections
    @getting_started = get_getting_started_guides
  end

  private

  def find_article(id)
    # In a real app, this would query a knowledge base
    articles = get_all_articles
    articles.find { |article| article[:id] == id.to_i } || not_found
  end

  def get_popular_articles
    [
      { id: 1, title: "Getting Started with AI Marketing Hub", views: 1250, category: "Getting Started" },
      { id: 2, title: "Creating Your First Campaign", views: 980, category: "Campaigns" },
      { id: 3, title: "Setting Up Social Media Integrations", views: 875, category: "Integrations" },
      { id: 4, title: "Understanding Analytics and Reports", views: 720, category: "Analytics" },
      { id: 5, title: "Managing Team Members and Permissions", views: 650, category: "Account Management" }
    ]
  end

  def get_recent_updates
    [
      { title: "New AI Content Generation Features", date: 2.days.ago, type: "feature" },
      { title: "Enhanced Social Media Scheduling", date: 1.week.ago, type: "improvement" },
      { title: "Security Update: Two-Factor Authentication", date: 2.weeks.ago, type: "security" },
      { title: "API Rate Limiting Changes", date: 3.weeks.ago, type: "api" }
    ]
  end

  def get_quick_links
    [
      { title: "Create New Campaign", url: new_campaign_path, icon: "plus" },
      { title: "View Analytics", url: dashboard_path, icon: "chart-bar" },
      { title: "Manage Integrations", url: platform_configurations_path, icon: "cog" },
      { title: "Account Settings", url: account_settings_path, icon: "user-cog" }
    ]
  end

  def get_support_stats
    {
      avg_response_time: "2 hours",
      satisfaction_rate: "98%",
      articles_count: 156,
      tickets_resolved: "99.2%"
    }
  end

  def get_related_articles(article)
    # Simple related articles based on category
    get_all_articles.select { |a| a[:category] == article[:category] && a[:id] != article[:id] }.first(3)
  end

  def get_support_categories
    [
      "General Question",
      "Technical Issue",
      "Billing & Account",
      "Feature Request",
      "Integration Help",
      "Bug Report"
    ]
  end

  def get_faq_categories
    {
      "Getting Started" => [
        { question: "How do I create my first campaign?", answer: 'Navigate to the Campaigns section and click "New Campaign"...' },
        { question: "What integrations are available?", answer: "We support Facebook, Twitter, Instagram, LinkedIn, and more..." }
      ],
      "Billing & Plans" => [
        { question: "How do I upgrade my plan?", answer: "Go to Account Settings > Billing to upgrade your plan..." },
        { question: "Can I cancel anytime?", answer: "Yes, you can cancel your subscription at any time..." }
      ],
      "Technical" => [
        { question: "Why isn't my integration working?", answer: "Check your API credentials and permissions..." },
        { question: "How do I reset my password?", answer: 'Use the "Forgot Password" link on the login page...' }
      ]
    }
  end

  def get_popular_faqs
    [
      { question: "How do I create my first campaign?", category: "Getting Started" },
      { question: "What integrations are available?", category: "Integrations" },
      { question: "How do I upgrade my plan?", category: "Billing" }
    ]
  end

  def get_documentation_sections
    [
      { title: "API Documentation", description: "Complete API reference and examples", url: "#" },
      { title: "Campaign Management", description: "Learn how to create and manage campaigns", url: "#" },
      { title: "Analytics & Reporting", description: "Understanding your data and metrics", url: "#" },
      { title: "Integrations Guide", description: "Connect with your favorite platforms", url: "#" }
    ]
  end

  def get_getting_started_guides
    [
      { title: "Quick Start Guide", description: "5-minute setup to get you running", duration: "5 min" },
      { title: "First Campaign Tutorial", description: "Step-by-step campaign creation", duration: "15 min" },
      { title: "Integration Setup", description: "Connect your social accounts", duration: "10 min" }
    ]
  end

  def get_all_articles
    [
      { id: 1, title: "Getting Started with AI Marketing Hub", content: "Welcome to AI Marketing Hub...", category: "Getting Started" },
      { id: 2, title: "Creating Your First Campaign", content: "Learn how to create campaigns...", category: "Campaigns" },
      { id: 3, title: "Setting Up Social Media Integrations", content: "Connect your social accounts...", category: "Integrations" },
      { id: 4, title: "Understanding Analytics and Reports", content: "Make sense of your data...", category: "Analytics" },
      { id: 5, title: "Managing Team Members and Permissions", content: "Collaborate with your team...", category: "Account Management" }
    ]
  end

  def determine_priority(category)
    case category
    when "Technical Issue", "Bug Report"
      "high"
    when "Billing & Account"
      "medium"
    else
      "normal"
    end
  end

  def contact_params
    params.require(:contact_form).permit(:subject, :message, :category)
  end

  # Simple form object for contact form
  class ContactForm
    include ActiveModel::Model
    include ActiveModel::Attributes

    attribute :subject, :string
    attribute :message, :string
    attribute :category, :string

    validates :subject, presence: true, length: { minimum: 5, maximum: 100 }
    validates :message, presence: true, length: { minimum: 10, maximum: 1000 }
    validates :category, presence: true, inclusion: { in: [ "General Question", "Technical Issue", "Billing & Account", "Feature Request", "Integration Help", "Bug Report" ] }
  end
end
