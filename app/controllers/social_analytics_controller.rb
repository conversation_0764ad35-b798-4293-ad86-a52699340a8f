# frozen_string_literal: true

class SocialAnalyticsController < ApplicationController
  before_action :set_campaign
  before_action :authenticate_user!
  
  def index
    @analytics_summary = build_analytics_summary
    @platform_performance = build_platform_performance
    @engagement_trends = build_engagement_trends
    @top_posts = build_top_posts
    @audience_insights = build_audience_insights
    @hashtag_performance = build_hashtag_performance
    
    respond_to do |format|
      format.html
      format.json { render json: analytics_json_response }
    end
  end

  def platform_details
    @platform = params[:platform]
    @platform_analytics = build_platform_analytics(@platform)
    @platform_posts = build_platform_posts(@platform)
    @platform_metrics = build_platform_metrics(@platform)
    
    respond_to do |format|
      format.html
      format.json { render json: platform_analytics_json }
    end
  end

  def export
    @export_data = build_export_data
    
    respond_to do |format|
      format.csv { send_csv_export }
      format.xlsx { send_xlsx_export }
      format.json { render json: @export_data }
    end
  end

  def real_time
    @real_time_metrics = build_real_time_metrics
    
    render json: @real_time_metrics
  end

  def engagement_report
    @date_range = parse_date_range
    @engagement_data = build_engagement_report(@date_range)
    
    respond_to do |format|
      format.html
      format.json { render json: @engagement_data }
    end
  end

  def roi_analysis
    @roi_data = build_roi_analysis
    @cost_breakdown = build_cost_breakdown
    @revenue_attribution = build_revenue_attribution
    
    respond_to do |format|
      format.html
      format.json { render json: roi_analysis_json }
    end
  end

  private

  def set_campaign
    @campaign = current_user.campaigns.find(params[:campaign_id])
  end

  def build_analytics_summary
    # Mock data structure - would be replaced with actual analytics service
    {
      total_posts: 45,
      total_impressions: 125_430,
      total_engagements: 8_650,
      total_clicks: 2_340,
      total_shares: 890,
      total_comments: 456,
      total_likes: 7_304,
      engagement_rate: 6.9,
      click_through_rate: 1.87,
      cost_per_engagement: 0.85,
      roi_percentage: 234.5,
      top_performing_platform: 'Instagram',
      growth_rate: 12.3,
      audience_reach: 89_650,
      conversion_rate: 3.2
    }
  end

  def build_platform_performance
    [
      {
        platform: 'Instagram',
        posts: 18,
        impressions: 54_230,
        engagements: 4_120,
        engagement_rate: 7.6,
        growth: 18.2,
        color: '#E4405F'
      },
      {
        platform: 'Twitter',
        posts: 12,
        impressions: 32_450,
        engagements: 2_340,
        engagement_rate: 7.2,
        growth: 8.9,
        color: '#1DA1F2'
      },
      {
        platform: 'Facebook',
        posts: 8,
        impressions: 23_100,
        engagements: 1_540,
        engagement_rate: 6.7,
        growth: 5.4,
        color: '#4267B2'
      },
      {
        platform: 'LinkedIn',
        posts: 5,
        impressions: 12_340,
        engagements: 580,
        engagement_rate: 4.7,
        growth: 15.6,
        color: '#0077B5'
      },
      {
        platform: 'TikTok',
        posts: 2,
        impressions: 3_310,
        engagements: 70,
        engagement_rate: 2.1,
        growth: -2.3,
        color: '#000000'
      }
    ]
  end

  def build_engagement_trends
    # Last 30 days of engagement data
    dates = (30.days.ago.to_date..Date.current).to_a
    dates.map do |date|
      {
        date: date.strftime('%Y-%m-%d'),
        impressions: rand(1000..5000),
        engagements: rand(50..400),
        clicks: rand(10..100),
        shares: rand(5..50)
      }
    end
  end

  def build_top_posts
    [
      {
        id: 1,
        platform: 'Instagram',
        content: 'Check out our latest AI-powered marketing insights! 🚀 #AIMarketing #DigitalStrategy',
        posted_at: 2.days.ago,
        impressions: 8_430,
        engagements: 650,
        engagement_rate: 7.7,
        image_url: 'https://via.placeholder.com/400x300?text=AI+Marketing+Post'
      },
      {
        id: 2,
        platform: 'Twitter',
        content: 'The future of marketing is here. Our AI agents are revolutionizing campaign optimization. Thread 🧵',
        posted_at: 5.days.ago,
        impressions: 12_340,
        engagements: 890,
        engagement_rate: 7.2,
        image_url: nil
      },
      {
        id: 3,
        platform: 'LinkedIn',
        content: 'How AI is transforming B2B marketing strategies. Key insights from our latest campaign data.',
        posted_at: 1.week.ago,
        impressions: 5_670,
        engagements: 320,
        engagement_rate: 5.6,
        image_url: 'https://via.placeholder.com/600x400?text=B2B+Marketing+Insights'
      }
    ]
  end

  def build_audience_insights
    {
      demographics: {
        age_groups: [
          { range: '18-24', percentage: 15.2 },
          { range: '25-34', percentage: 34.8 },
          { range: '35-44', percentage: 28.6 },
          { range: '45-54', percentage: 16.1 },
          { range: '55+', percentage: 5.3 }
        ],
        gender: [
          { type: 'Female', percentage: 52.4 },
          { type: 'Male', percentage: 45.8 },
          { type: 'Other', percentage: 1.8 }
        ],
        locations: [
          { country: 'United States', percentage: 45.2 },
          { country: 'Canada', percentage: 12.8 },
          { country: 'United Kingdom', percentage: 9.6 },
          { country: 'Australia', percentage: 7.4 },
          { country: 'Germany', percentage: 6.1 }
        ]
      },
      interests: [
        { category: 'Digital Marketing', percentage: 78.4 },
        { category: 'Artificial Intelligence', percentage: 65.2 },
        { category: 'Business Strategy', percentage: 54.8 },
        { category: 'Technology', percentage: 48.3 },
        { category: 'Entrepreneurship', percentage: 42.1 }
      ],
      engagement_times: [
        { hour: 9, engagement_rate: 5.2 },
        { hour: 12, engagement_rate: 7.8 },
        { hour: 15, engagement_rate: 6.4 },
        { hour: 18, engagement_rate: 8.9 },
        { hour: 21, engagement_rate: 7.1 }
      ]
    }
  end

  def build_hashtag_performance
    [
      { hashtag: '#AIMarketing', usage_count: 15, avg_engagement: 245, performance_score: 8.7 },
      { hashtag: '#DigitalStrategy', usage_count: 12, avg_engagement: 189, performance_score: 7.4 },
      { hashtag: '#MarketingAutomation', usage_count: 8, avg_engagement: 156, performance_score: 6.9 },
      { hashtag: '#ContentMarketing', usage_count: 10, avg_engagement: 134, performance_score: 6.2 },
      { hashtag: '#SocialMediaTips', usage_count: 6, avg_engagement: 98, performance_score: 5.8 }
    ]
  end

  def build_platform_analytics(platform)
    # Detailed analytics for specific platform
    {
      platform: platform,
      total_posts: rand(10..25),
      total_impressions: rand(20_000..60_000),
      total_engagements: rand(1_000..5_000),
      follower_growth: rand(-50..200),
      best_posting_times: ['9:00 AM', '12:00 PM', '6:00 PM'],
      top_content_types: ['Images', 'Videos', 'Carousels'],
      engagement_breakdown: {
        likes: rand(2000..8000),
        comments: rand(100..500),
        shares: rand(50..300),
        clicks: rand(200..1000)
      }
    }
  end

  def build_platform_posts(platform)
    # Recent posts for specific platform
    5.times.map do |i|
      {
        id: i + 1,
        content: "Sample post content for #{platform} - post #{i + 1}",
        posted_at: (i + 1).days.ago,
        impressions: rand(1000..5000),
        engagements: rand(50..400),
        engagement_rate: (rand(300..800) / 100.0).round(1)
      }
    end
  end

  def build_platform_metrics(platform)
    # Time-series metrics for charts
    dates = (7.days.ago.to_date..Date.current).to_a
    dates.map do |date|
      {
        date: date.strftime('%Y-%m-%d'),
        impressions: rand(500..2000),
        engagements: rand(25..150),
        followers: rand(-5..15)
      }
    end
  end

  def build_export_data
    {
      campaign: @campaign.name,
      date_range: "#{30.days.ago.strftime('%B %d, %Y')} - #{Date.current.strftime('%B %d, %Y')}",
      summary: build_analytics_summary,
      platform_performance: build_platform_performance,
      top_posts: build_top_posts,
      hashtag_performance: build_hashtag_performance
    }
  end

  def build_real_time_metrics
    {
      timestamp: Time.current.iso8601,
      active_campaigns: 1,
      posts_today: rand(3..8),
      impressions_last_hour: rand(100..500),
      engagements_last_hour: rand(10..50),
      current_reach: rand(1000..5000),
      trending_hashtags: ['#AIMarketing', '#DigitalTransformation', '#MarketingTech']
    }
  end

  def build_engagement_report(date_range)
    {
      date_range: date_range,
      total_engagements: rand(5000..15000),
      engagement_breakdown: {
        likes: rand(3000..8000),
        comments: rand(200..800),
        shares: rand(100..500),
        clicks: rand(300..1200)
      },
      daily_engagement: date_range[:start].to_date.upto(date_range[:end].to_date).map do |date|
        {
          date: date.strftime('%Y-%m-%d'),
          engagements: rand(100..500)
        }
      end
    }
  end

  def build_roi_analysis
    {
      total_spend: 2_450.00,
      total_revenue: 8_234.50,
      roi_percentage: 236.1,
      cost_per_engagement: 0.28,
      cost_per_click: 1.05,
      cost_per_conversion: 24.50,
      lifetime_value: 156.75,
      payback_period: '2.3 months'
    }
  end

  def build_cost_breakdown
    [
      { category: 'Content Creation', amount: 800.00, percentage: 32.7 },
      { category: 'Ad Spend', amount: 1200.00, percentage: 49.0 },
      { category: 'Tool Subscriptions', amount: 250.00, percentage: 10.2 },
      { category: 'Analytics & Reporting', amount: 200.00, percentage: 8.1 }
    ]
  end

  def build_revenue_attribution
    [
      { source: 'Instagram Posts', revenue: 3_245.30, percentage: 39.4 },
      { source: 'Twitter Engagement', revenue: 2_134.80, percentage: 25.9 },
      { source: 'LinkedIn Content', revenue: 1_654.20, percentage: 20.1 },
      { source: 'Facebook Ads', revenue: 1_200.20, percentage: 14.6 }
    ]
  end

  def parse_date_range
    start_date = params[:start_date]&.to_date || 30.days.ago.to_date
    end_date = params[:end_date]&.to_date || Date.current
    
    {
      start: start_date,
      end: end_date
    }
  end

  def analytics_json_response
    {
      summary: @analytics_summary,
      platform_performance: @platform_performance,
      engagement_trends: @engagement_trends,
      top_posts: @top_posts,
      audience_insights: @audience_insights,
      hashtag_performance: @hashtag_performance
    }
  end

  def platform_analytics_json
    {
      analytics: @platform_analytics,
      posts: @platform_posts,
      metrics: @platform_metrics
    }
  end

  def roi_analysis_json
    {
      roi_data: @roi_data,
      cost_breakdown: @cost_breakdown,
      revenue_attribution: @revenue_attribution
    }
  end

  def send_csv_export
    csv_data = generate_csv_export
    send_data csv_data, 
              filename: "social_analytics_#{@campaign.id}_#{Date.current.strftime('%Y%m%d')}.csv",
              type: 'text/csv'
  end

  def send_xlsx_export
    # Would integrate with a gem like 'axlsx' for Excel exports
    send_data "Excel export not implemented",
              filename: "social_analytics_#{@campaign.id}_#{Date.current.strftime('%Y%m%d')}.xlsx",
              type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  end

  def generate_csv_export
    require 'csv'
    
    CSV.generate(headers: true) do |csv|
      csv << ['Campaign', 'Platform', 'Date', 'Impressions', 'Engagements', 'Engagement Rate']
      
      @platform_performance.each do |platform_data|
        csv << [
          @campaign.name,
          platform_data[:platform],
          Date.current.strftime('%Y-%m-%d'),
          platform_data[:impressions],
          platform_data[:engagements],
          "#{platform_data[:engagement_rate]}%"
        ]
      end
    end
  end
end
