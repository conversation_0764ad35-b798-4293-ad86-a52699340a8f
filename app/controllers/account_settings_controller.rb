# frozen_string_literal: true

class AccountSettingsController < ApplicationController
  before_action :authenticate_user!
  before_action :set_notification_settings, only: [ :show, :update_notifications ]

  def show
    @user = current_user
    @tenant = current_user.tenant
    @security_settings = get_security_settings
    @billing_info = get_billing_info
    @team_members = get_team_members if current_user.admin_or_owner?
  end

  def update
    @user = current_user

    if @user.update(account_params)
      redirect_to account_settings_path, notice: "Account settings updated successfully."
    else
      render :show, status: :unprocessable_entity
    end
  end

  def update_password
    @user = current_user

    if @user.valid_password?(params[:current_password])
      if @user.update(password_params)
        bypass_sign_in(@user) # Keep user signed in after password change
        redirect_to account_settings_path, notice: "Password updated successfully."
      else
        redirect_to account_settings_path, alert: "Failed to update password. Please check the requirements."
      end
    else
      redirect_to account_settings_path, alert: "Current password is incorrect."
    end
  end

  def update_notifications
    if @notification_settings.update(notification_params)
      redirect_to account_settings_path, notice: "Notification settings updated successfully."
    else
      redirect_to account_settings_path, alert: "Failed to update notification settings."
    end
  end

  def update_security
    @user = current_user

    # Handle two-factor authentication toggle
    if params[:enable_2fa] == "true"
      # Logic to enable 2FA would go here
      redirect_to account_settings_path, notice: "Two-factor authentication enabled."
    elsif params[:enable_2fa] == "false"
      # Logic to disable 2FA would go here
      redirect_to account_settings_path, notice: "Two-factor authentication disabled."
    else
      redirect_to account_settings_path, alert: "Invalid security setting."
    end
  end

  def delete_account
    @user = current_user

    if params[:confirmation] == @user.email
      # In a real app, you'd want to:
      # 1. Cancel any active subscriptions
      # 2. Archive data instead of deleting
      # 3. Send confirmation email
      # 4. Handle team ownership transfer

      # For now, just sign out and redirect
      sign_out(@user)
      redirect_to root_path, notice: "Account deletion initiated. You will receive a confirmation email."
    else
      redirect_to account_settings_path, alert: "Email confirmation does not match. Account not deleted."
    end
  end

  private

  def set_notification_settings
    @notification_settings = current_user.notification_setting || current_user.build_notification_setting
  end

  def account_params
    params.require(:user).permit(:email)
  end

  def password_params
    params.require(:user).permit(:password, :password_confirmation)
  end

  def notification_params
    params.require(:notification_setting).permit(
      :email_campaigns, :email_system, :email_marketing,
      :push_campaigns, :push_system, :push_marketing,
      :sms_campaigns, :sms_system, :sms_security,
      :digest_frequency, :quiet_hours_start, :quiet_hours_end
    )
  end

  def get_security_settings
    {
      two_factor_enabled: false, # Placeholder
      last_password_change: @user.updated_at,
      active_sessions: 1, # Placeholder
      login_attempts: 0 # Placeholder
    }
  end

  def get_billing_info
    # Placeholder for billing information
    {
      plan: "Professional",
      billing_cycle: "monthly",
      next_billing_date: 1.month.from_now,
      payment_method: "**** **** **** 1234"
    }
  end

  def get_team_members
    # Get team members for admin/owner users
    current_user.tenant.users.includes(:tenant).limit(10)
  end
end
