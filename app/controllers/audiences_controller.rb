# frozen_string_literal: true

class AudiencesController < ApplicationController
  before_action :authenticate_user!
  before_action :set_current_tenant
  before_action :set_audience, only: [:show, :edit, :update, :destroy]

  def index
    @audiences = build_audiences_scope
    @search_term = params[:search]
    @filter_params = filter_params
    
    respond_to do |format|
      format.html
      format.json { render json: format_audiences_for_json(@audiences) }
    end
  end

  def show
    @audience_analytics = build_audience_analytics(@audience)
    @campaigns = @audience.campaigns.includes(:campaign_metrics).recent.limit(10)
  end

  def new
    @audience = current_tenant.audiences.build
  end

  def create
    @audience = current_tenant.audiences.build(audience_params)
    @audience.created_by = current_user

    if @audience.save
      redirect_to @audience, notice: 'Audience was successfully created.'
    else
      render :new, status: :unprocessable_entity
    end
  end

  def edit
  end

  def update
    if @audience.update(audience_params)
      redirect_to @audience, notice: 'Audience was successfully updated.'
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    @audience.destroy
    redirect_to audiences_url, notice: 'Audience was successfully deleted.'
  end

  def analytics
    @audience = current_tenant.audiences.find(params[:id])
    @analytics_data = build_detailed_audience_analytics(@audience)
    render json: @analytics_data
  end

  def cultural_alignment
    @audience = current_tenant.audiences.find(params[:id])
    @alignment_data = build_cultural_alignment_data(@audience)
    render json: @alignment_data
  end

  def engagement_metrics
    @audience = current_tenant.audiences.find(params[:id])
    @engagement_data = build_engagement_metrics_data(@audience)
    render json: @engagement_data
  end

  private

  def set_current_tenant
    ActsAsTenant.current_tenant = current_user.tenant
  end

  def current_tenant
    current_user.tenant
  end

  def set_audience
    @audience = current_tenant.audiences.find(params[:id])
  end

  def audience_params
    params.require(:audience).permit(
      :name, :description, :target_demographics, :cultural_context,
      :primary_language, :secondary_languages, :geographic_regions,
      :age_range_min, :age_range_max, :interests, :behavioral_traits,
      :communication_preferences, :cultural_values, :engagement_patterns,
      demographics: {}, cultural_attributes: {}, preferences: {}
    )
  end

  def filter_params
    params.permit(:search, :cultural_context, :primary_language, :sort_by, :sort_direction)
  end

  def build_audiences_scope
    scope = current_tenant.audiences.includes(:campaigns, :created_by)
    
    # Apply search filter
    if params[:search].present?
      search_term = "%#{params[:search]}%"
      scope = scope.where(
        "name ILIKE ? OR description ILIKE ? OR target_demographics ILIKE ?",
        search_term, search_term, search_term
      )
    end

    # Apply cultural context filter
    if params[:cultural_context].present?
      scope = scope.where(cultural_context: params[:cultural_context])
    end

    # Apply language filter
    if params[:primary_language].present?
      scope = scope.where(primary_language: params[:primary_language])
    end

    # Apply sorting
    sort_by = params[:sort_by] || 'created_at'
    sort_direction = params[:sort_direction] || 'desc'
    
    case sort_by
    when 'name'
      scope = scope.order(name: sort_direction)
    when 'campaigns_count'
      scope = scope.left_joins(:campaigns).group(:id).order("COUNT(campaigns.id) #{sort_direction}")
    when 'cultural_alignment'
      scope = scope.order(cultural_alignment_score: sort_direction)
    else
      scope = scope.order(created_at: sort_direction)
    end

    scope.limit(20).offset((params[:page]&.to_i || 1 - 1) * 20)
  end

  def format_audiences_for_json(audiences)
    audiences.map do |audience|
      {
        id: audience.id,
        name: audience.name,
        description: audience.description,
        target_demographics: audience.target_demographics,
        cultural_context: audience.cultural_context,
        primary_language: audience.primary_language,
        campaigns_count: audience.campaigns.count,
        cultural_alignment_score: audience.cultural_alignment_score,
        engagement_score: calculate_engagement_score(audience),
        created_at: audience.created_at,
        updated_at: audience.updated_at
      }
    end
  end

  def build_audience_analytics(audience)
    campaigns = audience.campaigns.includes(:campaign_metrics, :vibe_analysis_records)
    
    {
      overview: build_audience_overview(audience, campaigns),
      performance_metrics: build_performance_metrics(campaigns),
      cultural_alignment: build_cultural_alignment_summary(audience, campaigns),
      engagement_trends: build_engagement_trends(campaigns),
      vibe_compatibility: build_vibe_compatibility(campaigns)
    }
  end

  def build_audience_overview(audience, campaigns)
    total_campaigns = campaigns.count
    active_campaigns = campaigns.active.count
    avg_engagement = calculate_avg_engagement(campaigns)
    cultural_score = audience.cultural_alignment_score || 0

    {
      total_campaigns: total_campaigns,
      active_campaigns: active_campaigns,
      avg_engagement_rate: avg_engagement,
      cultural_alignment_score: cultural_score,
      primary_language: audience.primary_language,
      geographic_regions: audience.geographic_regions,
      age_range: "#{audience.age_range_min}-#{audience.age_range_max}",
      last_campaign_date: campaigns.maximum(:created_at)
    }
  end

  def build_performance_metrics(campaigns)
    metrics = campaigns.joins(:campaign_metrics)
                      .group('campaigns.id')
                      .average('campaign_metrics.click_through_rate',
                              'campaign_metrics.conversion_rate',
                              'campaign_metrics.engagement_rate')

    {
      avg_ctr: metrics['click_through_rate']&.round(2) || 0,
      avg_conversion_rate: metrics['conversion_rate']&.round(2) || 0,
      avg_engagement_rate: metrics['engagement_rate']&.round(2) || 0,
      total_impressions: campaigns.joins(:campaign_metrics).sum('campaign_metrics.impressions'),
      total_clicks: campaigns.joins(:campaign_metrics).sum('campaign_metrics.clicks'),
      total_conversions: campaigns.joins(:campaign_metrics).sum('campaign_metrics.conversions')
    }
  end

  def build_cultural_alignment_summary(audience, campaigns)
    vibe_approved_campaigns = campaigns.where(vibe_status: 'vibe_approved').count
    total_analyzed = campaigns.where.not(vibe_status: ['pending', nil]).count
    
    cultural_scores = campaigns.where.not(cultural_relevance_score: nil)
                              .pluck(:cultural_relevance_score)
    
    {
      alignment_score: audience.cultural_alignment_score || 0,
      vibe_approval_rate: total_analyzed > 0 ? ((vibe_approved_campaigns.to_f / total_analyzed) * 100).round(1) : 0,
      avg_cultural_relevance: cultural_scores.any? ? (cultural_scores.sum / cultural_scores.length).round(2) : 0,
      cultural_context: audience.cultural_context,
      cultural_values: audience.cultural_values,
      communication_preferences: audience.communication_preferences
    }
  end

  def build_engagement_trends(campaigns)
    # Group campaigns by month and calculate engagement metrics
    monthly_data = campaigns.joins(:campaign_metrics)
                           .group_by_month(:created_at, last: 12)
                           .average('campaign_metrics.engagement_rate')

    monthly_data.transform_keys(&:to_s)
  end

  def build_vibe_compatibility(campaigns)
    vibe_status_distribution = campaigns.group(:vibe_status).count
    emotional_tone_distribution = campaigns.where.not(emotional_tone: nil)
                                          .group(:emotional_tone).count

    {
      vibe_status_distribution: vibe_status_distribution,
      emotional_tone_distribution: emotional_tone_distribution,
      compatibility_score: calculate_vibe_compatibility_score(campaigns)
    }
  end

  def calculate_engagement_score(audience)
    campaigns = audience.campaigns.includes(:campaign_metrics)
    return 0 if campaigns.empty?

    engagement_rates = campaigns.joins(:campaign_metrics)
                               .pluck('campaign_metrics.engagement_rate')
                               .compact

    return 0 if engagement_rates.empty?
    (engagement_rates.sum / engagement_rates.length).round(2)
  end

  def calculate_avg_engagement(campaigns)
    engagement_rates = campaigns.joins(:campaign_metrics)
                               .pluck('campaign_metrics.engagement_rate')
                               .compact

    return 0 if engagement_rates.empty?
    (engagement_rates.sum / engagement_rates.length).round(2)
  end

  def calculate_vibe_compatibility_score(campaigns)
    return 0 if campaigns.empty?

    vibe_approved = campaigns.where(vibe_status: 'vibe_approved').count
    total_with_vibe_status = campaigns.where.not(vibe_status: ['pending', nil]).count

    return 0 if total_with_vibe_status.zero?
    ((vibe_approved.to_f / total_with_vibe_status) * 100).round(1)
  end

  def build_detailed_audience_analytics(audience)
    # Implementation for detailed analytics API endpoint
    campaigns = audience.campaigns.includes(:campaign_metrics, :vibe_analysis_records)
    
    {
      audience_profile: build_audience_profile(audience),
      performance_analysis: build_detailed_performance_analysis(campaigns),
      cultural_insights: build_cultural_insights(audience, campaigns),
      engagement_analysis: build_detailed_engagement_analysis(campaigns),
      recommendations: build_audience_recommendations(audience, campaigns)
    }
  end

  def build_audience_profile(audience)
    {
      demographics: {
        age_range: "#{audience.age_range_min}-#{audience.age_range_max}",
        geographic_regions: audience.geographic_regions,
        primary_language: audience.primary_language,
        secondary_languages: audience.secondary_languages
      },
      psychographics: {
        interests: audience.interests,
        behavioral_traits: audience.behavioral_traits,
        cultural_values: audience.cultural_values,
        communication_preferences: audience.communication_preferences
      },
      engagement_patterns: audience.engagement_patterns
    }
  end

  def build_detailed_performance_analysis(campaigns)
    # Detailed performance analysis implementation
    {
      campaign_performance: campaigns.map { |c| campaign_performance_summary(c) },
      trend_analysis: calculate_performance_trends(campaigns),
      benchmark_comparison: compare_to_benchmarks(campaigns)
    }
  end

  def build_cultural_insights(audience, campaigns)
    # Cultural insights implementation
    {
      cultural_alignment_trends: calculate_cultural_trends(campaigns),
      vibe_analysis_insights: analyze_vibe_patterns(campaigns),
      cultural_recommendations: generate_cultural_recommendations(audience, campaigns)
    }
  end

  def build_detailed_engagement_analysis(campaigns)
    # Detailed engagement analysis implementation
    {
      engagement_by_channel: analyze_engagement_by_channel(campaigns),
      temporal_patterns: analyze_temporal_engagement_patterns(campaigns),
      content_type_performance: analyze_content_type_performance(campaigns)
    }
  end

  def build_audience_recommendations(audience, campaigns)
    # Generate recommendations based on audience data and campaign performance
    {
      optimization_opportunities: identify_optimization_opportunities(audience, campaigns),
      content_suggestions: generate_content_suggestions(audience, campaigns),
      targeting_improvements: suggest_targeting_improvements(audience, campaigns)
    }
  end

  # Helper methods for detailed analytics (placeholder implementations)
  def campaign_performance_summary(campaign)
    { id: campaign.id, name: campaign.name, performance_score: 0 }
  end

  def calculate_performance_trends(campaigns)
    {}
  end

  def compare_to_benchmarks(campaigns)
    {}
  end

  def calculate_cultural_trends(campaigns)
    {}
  end

  def analyze_vibe_patterns(campaigns)
    {}
  end

  def generate_cultural_recommendations(audience, campaigns)
    []
  end

  def analyze_engagement_by_channel(campaigns)
    {}
  end

  def analyze_temporal_engagement_patterns(campaigns)
    {}
  end

  def analyze_content_type_performance(campaigns)
    {}
  end

  def identify_optimization_opportunities(audience, campaigns)
    []
  end

  def generate_content_suggestions(audience, campaigns)
    []
  end

  def suggest_targeting_improvements(audience, campaigns)
    []
  end
end
