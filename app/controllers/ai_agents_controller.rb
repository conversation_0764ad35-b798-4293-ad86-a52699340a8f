# frozen_string_literal: true

class AiAgentsController < ApplicationController
  before_action :authenticate_user!
  before_action :ensure_tenant_set

  def index
    @active_agents = get_active_agents
    @agent_performance = get_agent_performance
    @workflow_stats = get_workflow_stats
    @recent_activities = get_recent_activities
    @agent_configurations = get_agent_configurations
  end

  def show
    @agent = find_agent(params[:id])
    @agent_metrics = get_agent_metrics(@agent)
    @recent_tasks = get_agent_tasks(@agent)
  end

  private

  def ensure_tenant_set
    if current_user&.tenant
      ActsAsTenant.current_tenant = current_user.tenant
    else
      redirect_to root_path, alert: "Please contact support to set up your account."
    end
  end

  def current_tenant
    current_user.tenant
  end

  def get_active_agents
    [
      {
        id: 'marketing_manager',
        name: 'Marketing Manager',
        description: 'Orchestrates multi-channel campaigns and strategic planning',
        status: 'active',
        tasks_completed: 156,
        success_rate: 94.2,
        specialty: 'Campaign Strategy',
        color: 'blue',
        last_active: 2.minutes.ago
      },
      {
        id: 'email_specialist',
        name: 'Email Specialist',
        description: 'Creates engaging email content and optimizes deliverability',
        status: 'active',
        tasks_completed: 89,
        success_rate: 91.8,
        specialty: 'Email Marketing',
        color: 'green',
        last_active: 5.minutes.ago
      },
      {
        id: 'social_media_agent',
        name: 'Social Media Agent',
        description: 'Manages social content creation and engagement tracking',
        status: 'active',
        tasks_completed: 234,
        success_rate: 88.7,
        specialty: 'Social Media',
        color: 'purple',
        last_active: 1.minute.ago
      },
      {
        id: 'seo_specialist',
        name: 'SEO Specialist',
        description: 'Optimizes content for search engines and keyword performance',
        status: 'active',
        tasks_completed: 67,
        success_rate: 92.1,
        specialty: 'SEO & Content',
        color: 'orange',
        last_active: 15.minutes.ago
      },
      {
        id: 'analytics_agent',
        name: 'Analytics Agent',
        description: 'Provides insights and performance analysis across all channels',
        status: 'active',
        tasks_completed: 78,
        success_rate: 96.5,
        specialty: 'Data Analysis',
        color: 'indigo',
        last_active: 3.minutes.ago
      },
      {
        id: 'customer_experience',
        name: 'Customer Experience Agent',
        description: 'Optimizes customer journey and engagement touchpoints',
        status: 'training',
        tasks_completed: 23,
        success_rate: 85.4,
        specialty: 'CX Optimization',
        color: 'pink',
        last_active: 30.minutes.ago
      }
    ]
  end

  def get_agent_performance
    {
      total_tasks_today: 47,
      completed_tasks: 45,
      success_rate: 95.7,
      average_response_time: 1.8,
      cost_efficiency: 'Excellent',
      active_workflows: 12
    }
  end

  def get_workflow_stats
    {
      total_workflows: current_tenant.agent_workflows.count,
      active_workflows: current_tenant.agent_workflows.where(status: 'running').count,
      completed_today: current_tenant.agent_workflows.where(
        status: 'completed',
        created_at: Date.current.beginning_of_day..Date.current.end_of_day
      ).count,
      success_rate: calculate_workflow_success_rate
    }
  end

  def get_recent_activities
    # Get recent agent workflows with enhanced data
    current_tenant.agent_workflows
                   .includes(:campaign)
                   .order(created_at: :desc)
                   .limit(10)
                   .map do |workflow|
      {
        id: workflow.id,
        agent_type: workflow.agent_type,
        action: workflow.workflow_config&.dig('action') || 'Process Campaign',
        campaign_name: workflow.campaign&.name || 'General Task',
        status: workflow.status,
        created_at: workflow.created_at,
        completion_time: workflow.completed_at
      }
    end
  end

  def get_agent_configurations
    {
      auto_optimization: true,
      smart_scheduling: true,
      cost_optimization: true,
      multi_model_routing: true,
      quality_assurance: true,
      real_time_monitoring: true
    }
  end

  def calculate_workflow_success_rate
    total = current_tenant.agent_workflows.count
    return 0 if total.zero?

    completed = current_tenant.agent_workflows.where(status: 'completed').count
    (completed.to_f / total * 100).round(1)
  end

  def find_agent(agent_id)
    get_active_agents.find { |agent| agent[:id] == agent_id }
  end

  def get_agent_metrics(agent)
    {
      daily_tasks: rand(15..45),
      weekly_performance: rand(85..98),
      cost_per_task: rand(0.15..0.85).round(2),
      error_rate: rand(1..8),
      uptime: rand(95..99.9).round(1)
    }
  end

  def get_agent_tasks(agent)
    [
      {
        task: 'Generate Email Content',
        status: 'completed',
        duration: '2.3s',
        timestamp: 5.minutes.ago
      },
      {
        task: 'Optimize Campaign Strategy',
        status: 'running',
        duration: '45s',
        timestamp: 2.minutes.ago
      },
      {
        task: 'Analyze Performance Metrics',
        status: 'completed',
        duration: '1.8s',
        timestamp: 8.minutes.ago
      }
    ]
  end
end