# frozen_string_literal: true

# Social Content Management Controller
# Handles social media content creation, editing, and platform management
class SocialContentController < ApplicationController
  before_action :authenticate_user!
  before_action :set_current_tenant
  before_action :set_campaign
  before_action :set_social_campaign, only: [ :show, :edit, :update, :destroy, :preview, :schedule ]

  # GET /campaigns/:campaign_id/social_content
  def show
    @platforms = @social_campaign&.platforms || []
    @content_variants = @social_campaign&.content_variants || {}
    @hashtags = @social_campaign&.hashtag_list || []
    @analytics_data = collect_analytics_data
    @scheduled_posts = @social_campaign&.scheduled_posts || []
  end

  # GET /campaigns/:campaign_id/social_content/new
  def new
    @social_campaign = @campaign.social_campaign || @campaign.build_social_campaign
    @selected_platforms = []
    @content_variants = {}
    @available_platforms = SocialCampaign::SUPPORTED_PLATFORMS
    @platform_limits = SocialMediaAgentService::PLATFORM_LIMITS
  end

  # GET /campaigns/:campaign_id/social_content/edit
  def edit
    @selected_platforms = @social_campaign.platforms
    @content_variants = @social_campaign.content_variants
    @available_platforms = SocialCampaign::SUPPORTED_PLATFORMS
    @platform_limits = SocialMediaAgentService::PLATFORM_LIMITS
    @hashtags = @social_campaign.hashtags
    @target_demographics = @social_campaign.target_demographics
  end

  # POST /campaigns/:campaign_id/social_content
  def create
    @social_campaign = @campaign.social_campaign || @campaign.build_social_campaign

    if @social_campaign.update(social_campaign_params)
      flash[:notice] = "Social content created successfully!"
      redirect_to campaign_social_content_path(@campaign)
    else
      @selected_platforms = social_campaign_params[:platforms] || []
      @content_variants = social_campaign_params[:content_variants] || {}
      @available_platforms = SocialCampaign::SUPPORTED_PLATFORMS
      @platform_limits = SocialMediaAgentService::PLATFORM_LIMITS
      render :new, status: :unprocessable_entity
    end
  end

  # PATCH/PUT /campaigns/:campaign_id/social_content
  def update
    if @social_campaign.update(social_campaign_params)
      flash[:notice] = "Social content updated successfully!"
      redirect_to campaign_social_content_path(@campaign)
    else
      @selected_platforms = @social_campaign.platforms
      @content_variants = @social_campaign.content_variants
      @available_platforms = SocialCampaign::SUPPORTED_PLATFORMS
      @platform_limits = SocialMediaAgentService::PLATFORM_LIMITS
      render :edit, status: :unprocessable_entity
    end
  end

  # DELETE /campaigns/:campaign_id/social_content
  def destroy
    @social_campaign.destroy
    flash[:notice] = "Social content deleted successfully!"
    redirect_to campaign_path(@campaign)
  end

  # GET /campaigns/:campaign_id/social_content/preview
  def preview
    platform = params[:platform]
    content = params[:content]

    render json: {
      platform: platform,
      content: content,
      character_count: content.to_s.length,
      character_limit: SocialMediaAgentService::PLATFORM_LIMITS.dig(platform, :character_limit),
      hashtag_count: extract_hashtag_count(content),
      hashtag_limit: SocialMediaAgentService::PLATFORM_LIMITS.dig(platform, :hashtag_limit),
      preview_html: render_platform_preview(platform, content)
    }
  end

  # POST /campaigns/:campaign_id/social_content/schedule
  def schedule
    scheduled_at = DateTime.parse(params[:scheduled_at])
    platform = params[:platform]
    content = params[:content]

    @social_campaign.add_scheduled_post(platform, content, scheduled_at)

    if @social_campaign.save
      flash[:notice] = "Post scheduled for #{platform.titleize} at #{scheduled_at.strftime('%B %d, %Y at %I:%M %p')}"
    else
      flash[:alert] = "Failed to schedule post. Please try again."
    end

    redirect_to campaign_social_content_path(@campaign)
  end

  # POST /campaigns/:campaign_id/social_content/generate_ai_content
  def generate_ai_content
    service = SocialMediaAgentService.new(campaign: @campaign)

    # Format parameters in the structure expected by the service
    context = {
      campaign_analysis: {
        settings: {
          social_platforms: params[:platforms] || [],
          brand_voice: params[:brand_voice] || "engaging",
          content_pillars: params[:content_pillars]&.split(",")&.map(&:strip)
        }
      }
    }

    result = service.generate_campaign_content(context)

    if result[:status] == "success"
      flash[:notice] = "AI content generated successfully!"
    else
      flash[:alert] = "Failed to generate AI content: #{result[:message]}"
    end

    redirect_to campaign_social_content_path(@campaign)
  end

  # POST /campaigns/:campaign_id/social_content/optimize
  def optimize_content
    service = SocialMediaAgentService.new(campaign: @campaign)
    result = service.optimize_social_content(
      performance_data: collect_analytics_data,
      optimization_goals: params[:optimization_goals]
    )

    if result[:status] == "success"
      flash[:notice] = "Content optimized successfully!"
    else
      flash[:alert] = "Failed to optimize content: #{result[:message]}"
    end

    redirect_to campaign_social_content_path(@campaign)
  end

  # POST /campaigns/:campaign_id/social_content/generate_hashtags
  def generate_hashtags
    service = SocialMediaAgentService.new(campaign: @campaign)
    result = service.generate_hashtag_strategy

    if result[:status] == "success"
      flash[:notice] = "Hashtag strategy generated successfully!"
    else
      flash[:alert] = "Failed to generate hashtags: #{result[:message]}"
    end

    redirect_to campaign_social_content_path(@campaign)
  end

  private

  def set_current_tenant
    ActsAsTenant.current_tenant = current_user.tenant
  end

  def set_campaign
    @campaign = current_user.tenant.campaigns.find(params[:campaign_id])
  end

  def set_social_campaign
    @social_campaign = @campaign.social_campaign
    redirect_to new_campaign_social_content_path(@campaign) unless @social_campaign
  end

  def social_campaign_params
    params.require(:social_campaign).permit(
      platforms: [],
      hashtags: [],
      content_variants: {},
      target_demographics: {},
      post_schedule: {},
      social_settings: {}
    )
  end

  def collect_analytics_data
    return {} unless @social_campaign

    service = SocialMediaAgentService.new(campaign: @campaign)
    service.send(:collect_social_metrics)
  rescue
    {}
  end

  def extract_hashtag_count(content)
    return 0 if content.blank?

    # Handle both string content and hash content (from AI generation)
    content_text = case content
    when Hash
                     content["content"] || content[:content] || ""
    when String
                     content
    else
                     ""
    end

    content_text.scan(/#\w+/).count
  end

  def render_platform_preview(platform, content)
    case platform
    when "twitter"
      render_twitter_preview(content)
    when "facebook"
      render_facebook_preview(content)
    when "instagram"
      render_instagram_preview(content)
    when "linkedin"
      render_linkedin_preview(content)
    when "tiktok"
      render_tiktok_preview(content)
    when "youtube"
      render_youtube_preview(content)
    else
      "<p>#{content}</p>"
    end
  end

  def render_twitter_preview(content)
    <<~HTML
      <div class="bg-white border border-gray-200 rounded-xl p-4 max-w-md">
        <div class="flex items-start space-x-3">
          <div class="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center">
            <span class="text-white font-semibold">#{current_user.initials}</span>
          </div>
          <div class="flex-1">
            <div class="flex items-center space-x-1">
              <span class="font-semibold text-gray-900">#{current_user.name}</span>
              <span class="text-gray-500">@#{current_user.name.downcase.gsub(' ', '')}</span>
              <span class="text-gray-500">·</span>
              <span class="text-gray-500">now</span>
            </div>
            <p class="text-gray-900 mt-1">#{content}</p>
          </div>
        </div>
      </div>
    HTML
  end

  def render_facebook_preview(content)
    <<~HTML
      <div class="bg-white border border-gray-200 rounded-lg p-4 max-w-lg">
        <div class="flex items-center space-x-3 mb-3">
          <div class="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
            <span class="text-white font-semibold">#{current_user.initials}</span>
          </div>
          <div>
            <div class="font-semibold text-gray-900">#{current_user.name}</div>
            <div class="text-gray-500 text-sm">Just now</div>
          </div>
        </div>
        <p class="text-gray-900">#{content}</p>
      </div>
    HTML
  end

  def render_instagram_preview(content)
    <<~HTML
      <div class="bg-white border border-gray-200 rounded-lg max-w-md">
        <div class="p-4">
          <div class="flex items-center space-x-3 mb-3">
            <div class="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
              <span class="text-white text-sm font-semibold">#{current_user.initials}</span>
            </div>
            <span class="font-semibold text-gray-900">#{current_user.name.downcase.gsub(' ', '')}</span>
          </div>
          <div class="bg-gray-200 h-80 mb-3 rounded-lg flex items-center justify-center">
            <span class="text-gray-500">Image placeholder</span>
          </div>
          <p class="text-gray-900">#{content}</p>
        </div>
      </div>
    HTML
  end

  def render_linkedin_preview(content)
    <<~HTML
      <div class="bg-white border border-gray-200 rounded-lg p-4 max-w-lg">
        <div class="flex items-start space-x-3">
          <div class="w-12 h-12 bg-blue-700 rounded-full flex items-center justify-center">
            <span class="text-white font-semibold">#{current_user.initials}</span>
          </div>
          <div class="flex-1">
            <div class="font-semibold text-gray-900">#{current_user.name}</div>
            <div class="text-gray-500 text-sm">#{current_user.job_title || 'Marketing Professional'}</div>
            <div class="text-gray-500 text-sm">Now</div>
            <p class="text-gray-900 mt-3">#{content}</p>
          </div>
        </div>
      </div>
    HTML
  end

  def render_tiktok_preview(content)
    <<~HTML
      <div class="bg-black text-white rounded-lg p-4 max-w-sm">
        <div class="flex items-center space-x-3 mb-3">
          <div class="w-8 h-8 bg-pink-500 rounded-full flex items-center justify-center">
            <span class="text-white text-sm font-semibold">#{current_user.initials}</span>
          </div>
          <span class="font-semibold">@#{current_user.name.downcase.gsub(' ', '')}</span>
        </div>
        <div class="bg-gray-800 h-96 mb-3 rounded-lg flex items-center justify-center">
          <span class="text-gray-400">Video placeholder</span>
        </div>
        <p class="text-white">#{content}</p>
      </div>
    HTML
  end

  def render_youtube_preview(content)
    <<~HTML
      <div class="bg-white border border-gray-200 rounded-lg max-w-lg">
        <div class="bg-gray-900 h-64 rounded-t-lg flex items-center justify-center">
          <span class="text-white">Video thumbnail</span>
        </div>
        <div class="p-4">
          <h3 class="font-semibold text-gray-900 mb-2">Video Title</h3>
          <div class="flex items-center space-x-2 mb-3">
            <div class="w-8 h-8 bg-red-600 rounded-full flex items-center justify-center">
              <span class="text-white text-sm font-semibold">#{current_user.initials}</span>
            </div>
            <span class="text-gray-700">#{current_user.name}</span>
          </div>
          <p class="text-gray-700 text-sm">#{content}</p>
        </div>
      </div>
    HTML
  end
end
