# frozen_string_literal: true

class Api::V1::CampaignsController < ApplicationController
  before_action :authenticate_user!
  before_action :set_current_tenant
  before_action :set_campaign, only: [ :show, :metrics, :progress ]

  # GET /api/v1/campaigns
  def index
    @campaigns = current_tenant.campaigns.includes(:email_campaign, :social_campaign, :seo_campaign)

    # Apply filters
    @campaigns = @campaigns.by_status(params[:status]) if params[:status].present?
    @campaigns = @campaigns.by_type(params[:type]) if params[:type].present?

    # Apply search
    if params[:search].present?
      @campaigns = @campaigns.where("name ILIKE ? OR description ILIKE ?",
                                   "%#{params[:search]}%", "%#{params[:search]}%")
    end

    # Apply sorting
    case params[:sort]
    when "name"
      @campaigns = @campaigns.order(:name)
    when "budget"
      @campaigns = @campaigns.order(:budget_cents)
    when "start_date"
      @campaigns = @campaigns.order(:start_date)
    when "status"
      @campaigns = @campaigns.order(:status)
    else
      @campaigns = @campaigns.recent
    end

    # Pagination
    page = params[:page] || 1
    per_page = params[:per_page] || 20
    @campaigns = @campaigns.limit(per_page).offset((page.to_i - 1) * per_page.to_i)

    render json: {
      campaigns: @campaigns.map { |campaign| campaign_summary(campaign) },
      meta: {
        page: page.to_i,
        per_page: per_page.to_i,
        total: current_tenant.campaigns.count
      }
    }
  end

  # GET /api/v1/campaigns/:id
  def show
    render json: {
      campaign: campaign_detail(@campaign),
      performance: campaign_performance(@campaign),
      associated_campaigns: associated_campaigns_data(@campaign)
    }
  end

  # GET /api/v1/campaigns/:id/metrics
  def metrics
    start_date = params[:start_date]&.to_date || 30.days.ago.to_date
    end_date = params[:end_date]&.to_date || Date.current

    metrics = @campaign.campaign_metrics.for_date_range(start_date, end_date).order(:metric_date)

    render json: {
      campaign_id: @campaign.id,
      date_range: {
        start_date: start_date,
        end_date: end_date
      },
      metrics: metrics.map { |metric| metric_data(metric) },
      summary: CampaignMetric.aggregate_for_campaign(@campaign, start_date, end_date)
    }
  end

  # GET /api/v1/campaigns/:id/progress
  def progress
    # Check if user wants agent workflow progress specifically
    if params[:type] == "agent_workflows"
      render_agent_workflow_progress
    else
      render_campaign_progress
    end
  end

  private

  def render_agent_workflow_progress
    begin
      active_workflows = @campaign.agent_workflows.active.includes(:campaign)

      progress_data = {
        campaign_id: @campaign.id,
        ai_status: @campaign.ai_workflow_status,
        has_active_workflows: @campaign.has_active_ai_workflows?,
        active_workflows: active_workflows.map do |workflow|
          {
            id: workflow.id,
            workflow_type: workflow.workflow_type,
            status: workflow.status,
            progress_percent: workflow.progress_percent,
            current_step: workflow.current_step,
            started_at: workflow.started_at,
            duration: workflow.duration
          }
        end,
        content_status: {
          email: content_generation_status(@campaign, "email"),
          social: content_generation_status(@campaign, "social"),
          seo: content_generation_status(@campaign, "seo")
        }
      }

      render json: progress_data
    rescue => e
      # Fallback if AgentWorkflow model doesn't exist yet
      render json: {
        campaign_id: @campaign.id,
        ai_status: "none",
        has_active_workflows: false,
        active_workflows: [],
        content_status: {},
        error: "Agent workflows not available yet"
      }
    end
  end

  def render_campaign_progress
    render json: {
      campaign_id: @campaign.id,
      progress_percentage: @campaign.progress_percentage,
      status: @campaign.status,
      start_date: @campaign.start_date,
      end_date: @campaign.end_date,
      duration_in_days: @campaign.duration_in_days,
      budget: {
        total: @campaign.budget_in_dollars,
        spent: calculate_spent_budget(@campaign),
        remaining: @campaign.budget_in_dollars - calculate_spent_budget(@campaign)
      },
      milestones: campaign_milestones(@campaign)
    }
  end

  def content_generation_status(campaign, agent_type)
    case agent_type.to_s
    when "email"
      campaign.email_campaign&.content.present?
    when "social"
      campaign.social_campaign&.content_variants&.any?
    when "seo"
      campaign.seo_campaign&.content_strategy&.any?
    else
      false
    end
  rescue
    false
  end

  def set_current_tenant
    ActsAsTenant.current_tenant = current_user.tenant
  end

  def current_tenant
    current_user.tenant
  end

  def set_campaign
    @campaign = current_tenant.campaigns.find(params[:id])
  end

  def campaign_summary(campaign)
    {
      id: campaign.id,
      name: campaign.name,
      description: campaign.description,
      campaign_type: campaign.campaign_type,
      status: campaign.status,
      target_audience: campaign.target_audience,
      budget: campaign.budget_in_dollars,
      start_date: campaign.start_date,
      end_date: campaign.end_date,
      progress_percentage: campaign.progress_percentage,
      created_at: campaign.created_at,
      updated_at: campaign.updated_at
    }
  end

  def campaign_detail(campaign)
    campaign_summary(campaign).merge({
      settings: campaign.settings,
      created_by: {
        id: campaign.created_by.id,
        name: campaign.created_by.full_name,
        email: campaign.created_by.email
      },
      duration_in_days: campaign.duration_in_days,
      can_be_activated: campaign.can_be_activated?
    })
  end

  def campaign_performance(campaign)
    metrics = campaign.campaign_metrics.recent(30)
    return {} if metrics.empty?

    {
      total_impressions: metrics.sum(:impressions),
      total_clicks: metrics.sum(:clicks),
      total_conversions: metrics.sum(:conversions),
      total_revenue: metrics.sum(:revenue_cents) / 100.0,
      total_cost: metrics.sum(:cost_cents) / 100.0,
      click_through_rate: calculate_ctr(metrics),
      conversion_rate: calculate_conversion_rate(metrics),
      return_on_ad_spend: calculate_roas(metrics)
    }
  end

  def associated_campaigns_data(campaign)
    data = {}

    if campaign.email_campaign
      data[:email] = {
        id: campaign.email_campaign.id,
        subject_line: campaign.email_campaign.subject_line,
        from_email: campaign.email_campaign.from_email,
        recipient_count: campaign.email_campaign.recipient_count,
        ready_to_send: campaign.email_campaign.ready_to_send?
      }
    end

    if campaign.social_campaign
      data[:social] = {
        id: campaign.social_campaign.id,
        platforms: campaign.social_campaign.platforms,
        hashtags: campaign.social_campaign.hashtag_list,
        estimated_reach: campaign.social_campaign.estimated_reach,
        ready_to_post: campaign.social_campaign.ready_to_post?
      }
    end

    if campaign.seo_campaign
      data[:seo] = {
        id: campaign.seo_campaign.id,
        target_keywords: campaign.seo_campaign.keyword_list,
        optimization_score: campaign.seo_campaign.optimization_score,
        estimated_traffic_increase: campaign.seo_campaign.estimated_traffic_increase,
        ready_to_optimize: campaign.seo_campaign.ready_to_optimize?
      }
    end

    data
  end

  def metric_data(metric)
    {
      date: metric.metric_date,
      impressions: metric.impressions,
      clicks: metric.clicks,
      conversions: metric.conversions,
      revenue: metric.revenue_in_dollars,
      cost: metric.cost_in_dollars,
      click_through_rate: metric.click_through_rate,
      conversion_rate: metric.conversion_rate,
      return_on_ad_spend: metric.return_on_ad_spend
    }
  end

  def campaign_milestones(campaign)
    milestones = []

    if campaign.start_date
      milestones << {
        name: "Campaign Start",
        date: campaign.start_date,
        completed: Date.current >= campaign.start_date
      }
    end

    if campaign.start_date && campaign.end_date
      mid_date = campaign.start_date + (campaign.duration_in_days / 2).days
      milestones << {
        name: "Mid-Campaign Review",
        date: mid_date,
        completed: Date.current >= mid_date
      }
    end

    if campaign.end_date
      milestones << {
        name: "Campaign End",
        date: campaign.end_date,
        completed: Date.current >= campaign.end_date
      }
    end

    milestones
  end

  def calculate_spent_budget(campaign)
    campaign.campaign_metrics.sum(:cost_cents) / 100.0
  end

  def calculate_ctr(metrics)
    total_impressions = metrics.sum(:impressions)
    total_clicks = metrics.sum(:clicks)
    return 0.0 if total_impressions.zero?

    (total_clicks.to_f / total_impressions * 100).round(2)
  end

  def calculate_conversion_rate(metrics)
    total_clicks = metrics.sum(:clicks)
    total_conversions = metrics.sum(:conversions)
    return 0.0 if total_clicks.zero?

    (total_conversions.to_f / total_clicks * 100).round(2)
  end

  def calculate_roas(metrics)
    total_cost = metrics.sum(:cost_cents)
    total_revenue = metrics.sum(:revenue_cents)
    return 0.0 if total_cost.zero?

    (total_revenue.to_f / total_cost * 100).round(2)
  end
end
