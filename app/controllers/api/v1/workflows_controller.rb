# frozen_string_literal: true

class Api::V1::WorkflowsController < ApplicationController
  before_action :authenticate_user!
  before_action :set_current_tenant

  # GET /api/v1/workflows/status
  def status
    workflows = current_tenant.agent_workflows
                              .active
                              .includes(:campaign)
                              .limit(20)

    workflow_data = workflows.map do |workflow|
      {
        id: workflow.id,
        status: workflow.status,
        progress_percent: workflow.progress_percent,
        current_step: workflow.current_step,
        workflow_type: workflow.workflow_type,
        campaign_name: workflow.campaign.name,
        updated_at: workflow.updated_at.iso8601
      }
    end

    render json: {
      workflows: workflow_data,
      total_active: workflows.count,
      last_updated: Time.current.iso8601
    }
  end

  # GET /api/v1/workflows/metrics
  def metrics
    workflows = current_tenant.agent_workflows

    metrics = {
      total_workflows: workflows.count,
      active_workflows: workflows.active.count,
      completed_workflows: workflows.completed.count,
      failed_workflows: workflows.failed.count,
      success_rate: calculate_success_rate(workflows),
      average_duration: calculate_average_duration(workflows),
      by_type: workflows.group(:workflow_type).count,
      by_status: workflows.group(:status).count
    }

    render json: {
      metrics: metrics,
      timestamp: Time.current.iso8601
    }
  end

  # GET /api/v1/workflows/:id
  def show
    workflow = current_tenant.agent_workflows.find(params[:id])

    render json: {
      workflow: {
        id: workflow.id,
        status: workflow.status,
        workflow_type: workflow.workflow_type,
        progress_percent: workflow.progress_percent,
        current_step: workflow.current_step,
        total_steps: workflow.total_steps,
        context_data: workflow.context_data,
        results: workflow.results,
        error_details: workflow.error_details,
        started_at: workflow.started_at&.iso8601,
        completed_at: workflow.completed_at&.iso8601,
        duration: workflow.duration,
        campaign: {
          id: workflow.campaign.id,
          name: workflow.campaign.name,
          type: workflow.campaign.campaign_type
        }
      }
    }
  end

  # PATCH /api/v1/workflows/:id/cancel
  def cancel
    workflow = current_tenant.agent_workflows.find(params[:id])

    if workflow.pending? || workflow.running?
      workflow.update!(status: :cancelled)

      render json: {
        success: true,
        message: "Workflow cancelled successfully",
        workflow: {
          id: workflow.id,
          status: workflow.status,
          updated_at: workflow.updated_at.iso8601
        }
      }
    else
      render json: {
        success: false,
        message: "Cannot cancel workflow in current status",
        current_status: workflow.status
      }, status: :unprocessable_entity
    end
  end

  # POST /api/v1/workflows/:id/retry
  def retry
    workflow = current_tenant.agent_workflows.find(params[:id])

    if workflow.failed?
      # Create new workflow with same configuration
      new_workflow = current_tenant.agent_workflows.create!(
        campaign: workflow.campaign,
        workflow_type: workflow.workflow_type,
        context_data: workflow.context_data.except("error_details", "failed_at"),
        total_steps: workflow.total_steps
      )

      # Queue the job
      AgentOrchestrationJob.perform_later(
        workflow.campaign_id,
        workflow_type: workflow.workflow_type,
        context: new_workflow.context_data,
        workflow_id: new_workflow.id
      )

      render json: {
        success: true,
        message: "Workflow retry initiated",
        original_workflow_id: workflow.id,
        new_workflow_id: new_workflow.id
      }
    else
      render json: {
        success: false,
        message: "Can only retry failed workflows",
        current_status: workflow.status
      }, status: :unprocessable_entity
    end
  end

  private

  def set_current_tenant
    ActsAsTenant.current_tenant = current_user.tenant
  end

  def current_tenant
    current_user.tenant
  end

  def calculate_success_rate(workflows)
    finished_workflows = workflows.where(status: [ :completed, :failed ])
    return 0 if finished_workflows.empty?

    successful_workflows = finished_workflows.completed
    ((successful_workflows.count.to_f / finished_workflows.count) * 100).round(1)
  end

  def calculate_average_duration(workflows)
    completed_workflows = workflows.completed
                                   .where.not(started_at: nil)
                                   .where.not(completed_at: nil)

    return 0 if completed_workflows.empty?

    total_duration = completed_workflows.sum do |workflow|
      workflow.completed_at - workflow.started_at
    end

    (total_duration / completed_workflows.count).round(2)
  end
end
