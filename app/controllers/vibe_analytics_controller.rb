# frozen_string_literal: true

class VibeAnalyticsController < ApplicationController
  before_action :authenticate_user!
  before_action :set_current_tenant

  def index
    @analytics_data = build_analytics_data
    @date_range = params[:date_range] || "30_days"
    @campaign_filter = params[:campaign_filter] || "all"

    respond_to do |format|
      format.html
      format.json { render json: @analytics_data }
    end
  end

  def emotional_trends
    @emotional_data = build_emotional_trends_data
    render json: @emotional_data
  end

  def cultural_breakdown
    @cultural_data = build_cultural_breakdown_data
    render json: @cultural_data
  end

  def authenticity_metrics
    @authenticity_data = build_authenticity_metrics_data
    render json: @authenticity_data
  end

  def vibe_performance
    @performance_data = build_vibe_performance_data
    render json: @performance_data
  end

  private

  def set_current_tenant
    ActsAsTenant.current_tenant = current_user.tenant
  end

  def current_tenant
    current_user.tenant
  end

  def date_range_scope
    case @date_range
    when "7_days"
      7.days.ago..Time.current
    when "30_days"
      30.days.ago..Time.current
    when "90_days"
      90.days.ago..Time.current
    when "1_year"
      1.year.ago..Time.current
    else
      30.days.ago..Time.current
    end
  end

  def campaigns_scope
    scope = current_tenant.campaigns.includes(:vibe_analysis_records, :authenticity_checks)
    scope = scope.where(created_at: date_range_scope)

    case @campaign_filter
    when "active"
      scope.active
    when "completed"
      scope.completed
    when "vibe_approved"
      scope.where(vibe_status: "vibe_approved")
    when "vibe_flagged"
      scope.where(vibe_status: "vibe_flagged")
    else
      scope
    end
  end

  def build_analytics_data
    campaigns = campaigns_scope

    {
      overview: build_overview_metrics(campaigns),
      emotional_trends: build_emotional_summary(campaigns),
      cultural_metrics: build_cultural_summary(campaigns),
      authenticity_stats: build_authenticity_summary(campaigns),
      vibe_status_distribution: build_vibe_status_distribution(campaigns),
      recent_activities: build_recent_activities(campaigns)
    }
  end

  def build_overview_metrics(campaigns)
    total_campaigns = campaigns.count
    vibe_approved = campaigns.where(vibe_status: "vibe_approved").count
    vibe_flagged = campaigns.where(vibe_status: "vibe_flagged").count
    avg_cultural_score = campaigns.where.not(cultural_relevance_score: nil).average(:cultural_relevance_score)&.round(2) || 0
    avg_authenticity_score = campaigns.where.not(authenticity_score: nil).average(:authenticity_score)&.round(2) || 0

    {
      total_campaigns: total_campaigns,
      vibe_approved: vibe_approved,
      vibe_flagged: vibe_flagged,
      approval_rate: total_campaigns > 0 ? ((vibe_approved.to_f / total_campaigns) * 100).round(1) : 0,
      avg_cultural_score: avg_cultural_score,
      avg_authenticity_score: avg_authenticity_score,
      avg_vibe_score: calculate_avg_vibe_score(campaigns)
    }
  end

  def build_emotional_summary(campaigns)
    emotional_tones = campaigns.where.not(emotional_tone: nil).group(:emotional_tone).count

    {
      distribution: emotional_tones,
      trending_tones: emotional_tones.sort_by { |_, count| -count }.first(5).to_h,
      total_analyzed: emotional_tones.values.sum
    }
  end

  def build_cultural_summary(campaigns)
    cultural_scores = campaigns.where.not(cultural_relevance_score: nil)

    score_ranges = {
      "Excellent (90-100)" => cultural_scores.where(cultural_relevance_score: 90..100).count,
      "Good (75-89)" => cultural_scores.where(cultural_relevance_score: 75..89).count,
      "Fair (60-74)" => cultural_scores.where(cultural_relevance_score: 60..74).count,
      "Poor (0-59)" => cultural_scores.where(cultural_relevance_score: 0..59).count
    }

    {
      score_distribution: score_ranges,
      average_score: cultural_scores.average(:cultural_relevance_score)&.round(2) || 0,
      total_analyzed: cultural_scores.count
    }
  end

  def build_authenticity_summary(campaigns)
    authenticity_scores = campaigns.where.not(authenticity_score: nil)

    score_ranges = {
      "Highly Authentic (90-100)" => authenticity_scores.where(authenticity_score: 90..100).count,
      "Authentic (75-89)" => authenticity_scores.where(authenticity_score: 75..89).count,
      "Moderately Authentic (60-74)" => authenticity_scores.where(authenticity_score: 60..74).count,
      "Low Authenticity (0-59)" => authenticity_scores.where(authenticity_score: 0..59).count
    }

    {
      score_distribution: score_ranges,
      average_score: authenticity_scores.average(:authenticity_score)&.round(2) || 0,
      total_analyzed: authenticity_scores.count,
      verification_rate: calculate_verification_rate(campaigns)
    }
  end

  def build_vibe_status_distribution(campaigns)
    campaigns.group(:vibe_status).count
  end

  def build_recent_activities(campaigns)
    campaigns.includes(:vibe_analysis_records, :authenticity_checks)
             .order(updated_at: :desc)
             .limit(10)
             .map do |campaign|
      {
        id: campaign.id,
        name: campaign.name,
        vibe_status: campaign.vibe_status,
        cultural_score: campaign.cultural_relevance_score,
        authenticity_score: campaign.authenticity_score,
        emotional_tone: campaign.emotional_tone,
        updated_at: campaign.updated_at
      }
    end
  end

  def calculate_avg_vibe_score(campaigns)
    scores = campaigns.where.not(cultural_relevance_score: nil, authenticity_score: nil)
                     .pluck(:cultural_relevance_score, :authenticity_score)

    return 0 if scores.empty?

    vibe_scores = scores.map { |cultural, authenticity| (authenticity * 0.6 + cultural * 0.4) }
    (vibe_scores.sum / vibe_scores.length).round(2)
  end

  def calculate_verification_rate(campaigns)
    total_with_scores = campaigns.where.not(authenticity_score: nil).count
    verified = campaigns.where("authenticity_score >= ?", 85).count

    return 0 if total_with_scores.zero?
    ((verified.to_f / total_with_scores) * 100).round(1)
  end

  def build_emotional_trends_data
    # Implementation for detailed emotional trends
    campaigns = campaigns_scope

    # Group by date and emotional tone
    trends = campaigns.joins(:vibe_analysis_records)
                     .where(vibe_analysis_records: { analysis_type: "emotional" })
                     .group_by_day(:created_at)
                     .group(:emotional_tone)
                     .count

    format_trends_data(trends)
  end

  def build_cultural_breakdown_data
    # Implementation for cultural analysis breakdown
    campaigns = campaigns_scope

    cultural_data = campaigns.where.not(cultural_relevance_score: nil)
                            .group_by_day(:created_at)
                            .average(:cultural_relevance_score)

    format_cultural_data(cultural_data)
  end

  def build_authenticity_metrics_data
    # Implementation for authenticity metrics
    campaigns = campaigns_scope

    authenticity_data = campaigns.where.not(authenticity_score: nil)
                               .group_by_day(:created_at)
                               .average(:authenticity_score)

    format_authenticity_data(authenticity_data)
  end

  def build_vibe_performance_data
    # Implementation for vibe performance correlation
    campaigns = campaigns_scope.includes(:campaign_metrics)

    performance_data = campaigns.map do |campaign|
      {
        vibe_score: campaign.vibe_score,
        performance_metrics: {
          ctr: campaign.campaign_metrics.average(:click_through_rate) || 0,
          conversion_rate: campaign.campaign_metrics.average(:conversion_rate) || 0,
          engagement_rate: campaign.campaign_metrics.average(:engagement_rate) || 0
        }
      }
    end

    format_performance_data(performance_data)
  end

  def format_trends_data(trends)
    # Format emotional trends data for charts
    trends.transform_keys { |key| key.is_a?(Array) ? key : [ key, "unknown" ] }
  end

  def format_cultural_data(cultural_data)
    # Format cultural data for charts
    cultural_data.transform_keys(&:to_s)
  end

  def format_authenticity_data(authenticity_data)
    # Format authenticity data for charts
    authenticity_data.transform_keys(&:to_s)
  end

  def format_performance_data(performance_data)
    # Format performance correlation data
    performance_data.reject { |data| data[:vibe_score].zero? }
  end
end
