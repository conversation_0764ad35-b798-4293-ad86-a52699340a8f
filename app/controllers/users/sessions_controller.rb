# frozen_string_literal: true

class Users::SessionsController < Devise::SessionsController
  protected

  def after_sign_in_path_for(resource)
    # Set tenant after successful sign in
    if resource.tenant
      ActsAsTenant.current_tenant = resource.tenant
      dashboard_path
    else
      # Handle case where user has no tenant
      sign_out(resource)
      redirect_to new_user_session_path, alert: "No tenant associated with this account."
    end
  end
end
