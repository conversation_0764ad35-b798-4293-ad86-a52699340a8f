class PlatformConfigurationsController < ApplicationController
  before_action :authenticate_user!

  def index
    @platform_configurations = current_user.platform_configurations.includes(:oauth_tokens)
    @available_platforms = %w[twitter facebook instagram linkedin tiktok youtube]
    @connected_platforms = @platform_configurations.where(is_active: true).pluck(:platform_name)
  end

  def show
    @platform_configuration = current_user.platform_configurations.find_by!(platform_name: params[:id])
    @oauth_token = @platform_configuration.oauth_tokens.active.first
    @recent_posts = fetch_recent_posts(@platform_configuration)
    @account_metrics = fetch_account_metrics(@platform_configuration)
  end

  def new
    @platform_name = params[:platform]
    redirect_to platform_configurations_path unless valid_platform?(@platform_name)
    
    @platform_configuration = current_user.platform_configurations.build(platform_name: @platform_name)
    @oauth_settings = platform_oauth_settings(@platform_name)
  end

  def create
    @platform_configuration = current_user.platform_configurations.build(platform_configuration_params)
    
    if @platform_configuration.save
      initiate_oauth_flow(@platform_configuration)
    else
      @oauth_settings = platform_oauth_settings(@platform_configuration.platform_name)
      render :new, status: :unprocessable_entity
    end
  end

  def update
    @platform_configuration = current_user.platform_configurations.find_by!(platform_name: params[:id])
    
    if @platform_configuration.update(platform_configuration_params)
      redirect_to platform_configuration_path(@platform_configuration.platform_name), 
                  notice: 'Platform configuration updated successfully.'
    else
      render :show, status: :unprocessable_entity
    end
  end

  def destroy
    @platform_configuration = current_user.platform_configurations.find_by!(platform_name: params[:id])
    @platform_configuration.oauth_tokens.destroy_all
    @platform_configuration.destroy
    
    redirect_to platform_configurations_path, notice: 'Platform disconnected successfully.'
  end

  def oauth_callback
    @platform_name = params[:platform]
    @platform_configuration = current_user.platform_configurations.find_by!(platform_name: @platform_name)
    
    begin
      oauth_service = SocialPlatformOAuthService.new(@platform_configuration)
      token_data = oauth_service.exchange_code_for_token(params[:code])
      
      @platform_configuration.oauth_tokens.create!(
        access_token: token_data[:access_token],
        refresh_token: token_data[:refresh_token],
        expires_at: token_data[:expires_at],
        token_type: token_data[:token_type],
        scope: token_data[:scope],
        is_active: true
      )
      
      @platform_configuration.update!(
        is_active: true,
        connected_account_id: token_data[:account_id],
        connected_account_name: token_data[:account_name],
        last_sync_at: Time.current
      )
      
      redirect_to platform_configuration_path(@platform_name), 
                  notice: "Successfully connected to #{@platform_name.titleize}!"
    rescue => e
      Rails.logger.error "OAuth callback error: #{e.message}"
      redirect_to platform_configurations_path, 
                  alert: "Failed to connect to #{@platform_name.titleize}. Please try again."
    end
  end

  def refresh_token
    @platform_configuration = current_user.platform_configurations.find_by!(platform_name: params[:id])
    
    begin
      oauth_service = SocialPlatformOAuthService.new(@platform_configuration)
      oauth_service.refresh_access_token
      
      redirect_to platform_configuration_path(@platform_configuration.platform_name), 
                  notice: 'Access token refreshed successfully.'
    rescue => e
      Rails.logger.error "Token refresh error: #{e.message}"
      redirect_to platform_configuration_path(@platform_configuration.platform_name), 
                  alert: 'Failed to refresh access token. Please reconnect the account.'
    end
  end

  def test_connection
    @platform_configuration = current_user.platform_configurations.find_by!(platform_name: params[:id])
    
    begin
      oauth_service = SocialPlatformOAuthService.new(@platform_configuration)
      test_result = oauth_service.test_connection
      
      if test_result[:success]
        @platform_configuration.update!(last_sync_at: Time.current)
        render json: { 
          success: true, 
          message: 'Connection test successful',
          account_info: test_result[:account_info]
        }
      else
        render json: { 
          success: false, 
          message: test_result[:error] || 'Connection test failed'
        }
      end
    rescue => e
      Rails.logger.error "Connection test error: #{e.message}"
      render json: { success: false, message: 'Connection test failed' }
    end
  end

  private

  def platform_configuration_params
    params.require(:platform_configuration).permit(
      :platform_name, :client_id, :client_secret, :webhook_url, 
      :auto_sync_enabled, :posting_enabled, :analytics_enabled,
      settings: {}
    )
  end

  def valid_platform?(platform_name)
    %w[twitter facebook instagram linkedin tiktok youtube].include?(platform_name)
  end

  def platform_oauth_settings(platform_name)
    case platform_name
    when 'twitter'
      {
        authorization_url: 'https://twitter.com/i/oauth2/authorize',
        token_url: 'https://api.twitter.com/2/oauth2/token',
        scopes: %w[tweet.read tweet.write users.read offline.access],
        required_fields: %w[client_id client_secret]
      }
    when 'facebook'
      {
        authorization_url: 'https://www.facebook.com/v18.0/dialog/oauth',
        token_url: 'https://graph.facebook.com/v18.0/oauth/access_token',
        scopes: %w[pages_manage_posts pages_read_engagement instagram_basic publish_video],
        required_fields: %w[client_id client_secret]
      }
    when 'instagram'
      {
        authorization_url: 'https://api.instagram.com/oauth/authorize',
        token_url: 'https://api.instagram.com/oauth/access_token',
        scopes: %w[user_profile user_media],
        required_fields: %w[client_id client_secret]
      }
    when 'linkedin'
      {
        authorization_url: 'https://www.linkedin.com/oauth/v2/authorization',
        token_url: 'https://www.linkedin.com/oauth/v2/accessToken',
        scopes: %w[w_member_social],
        required_fields: %w[client_id client_secret]
      }
    when 'tiktok'
      {
        authorization_url: 'https://www.tiktok.com/auth/authorize/',
        token_url: 'https://open-api.tiktok.com/oauth/access_token/',
        scopes: %w[user.info.basic video.upload],
        required_fields: %w[client_id client_secret]
      }
    when 'youtube'
      {
        authorization_url: 'https://accounts.google.com/o/oauth2/auth',
        token_url: 'https://oauth2.googleapis.com/token',
        scopes: %w[https://www.googleapis.com/auth/youtube.upload],
        required_fields: %w[client_id client_secret]
      }
    else
      {}
    end
  end

  def initiate_oauth_flow(platform_configuration)
    oauth_service = SocialPlatformOAuthService.new(platform_configuration)
    authorization_url = oauth_service.build_authorization_url(
      redirect_uri: oauth_callback_platform_configurations_url(platform: platform_configuration.platform_name)
    )
    
    redirect_to authorization_url, allow_other_host: true
  end

  def fetch_recent_posts(platform_configuration)
    return [] unless platform_configuration.oauth_tokens.active.exists?
    
    begin
      oauth_service = SocialPlatformOAuthService.new(platform_configuration)
      oauth_service.fetch_recent_posts(limit: 10)
    rescue => e
      Rails.logger.error "Failed to fetch recent posts: #{e.message}"
      []
    end
  end

  def fetch_account_metrics(platform_configuration)
    return {} unless platform_configuration.oauth_tokens.active.exists?
    
    begin
      oauth_service = SocialPlatformOAuthService.new(platform_configuration)
      oauth_service.fetch_account_metrics
    rescue => e
      Rails.logger.error "Failed to fetch account metrics: #{e.message}"
      {}
    end
  end
end
