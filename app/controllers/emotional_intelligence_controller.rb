# frozen_string_literal: true

# EmotionalIntelligenceController - Dashboard for Marketing Therapist AI
#
# This controller provides the user interface for:
# - Viewing customer emotional states in real-time
# - Monitoring campaign emotional compatibility
# - Managing emotional adaptations and adjustments
# - Analyzing emotional intelligence insights and trends
class EmotionalIntelligenceController < ApplicationController
  before_action :authenticate_user!
  before_action :set_customer_emotional_state, only: [ :show, :update_state, :predict_response ]

  # GET /emotional_intelligence
  def index
    @page_title = "Marketing Therapist AI - Emotional Intelligence Dashboard"

    # Get recent emotional states for overview
    @recent_emotional_states = current_tenant.customer_emotional_states
                                          .includes(:tenant)
                                          .order(last_interaction_at: :desc)
                                          .limit(20)

    # Calculate summary statistics
    @emotional_stats = calculate_emotional_stats

    # Get campaigns needing emotional review
    @campaigns_needing_review = find_campaigns_needing_emotional_review

    # Get recent emotional adaptations
    @recent_adaptations = get_recent_emotional_adaptations

    # Get emotional intelligence insights
    @insights = generate_emotional_insights

    respond_to do |format|
      format.html
      format.json { render json: dashboard_data }
    end
  end

  # GET /emotional_intelligence/customers/:customer_identifier
  def show
    @page_title = "Customer Emotional Profile - #{@customer_emotional_state.customer_identifier}"

    # Get detailed emotional journey
    @emotional_journey = @customer_emotional_state.emotional_journey(30.days)

    # Get campaign compatibility analysis
    @campaign_compatibility = analyze_campaign_compatibility

    # Get recommended next actions
    @recommendations = generate_customer_recommendations

    # Get emotional trajectory analysis
    @trajectory_analysis = analyze_emotional_trajectory

    respond_to do |format|
      format.html
      format.json { render json: customer_detail_data }
    end
  end

  # POST /emotional_intelligence/customers/:customer_identifier/update_state
  def update_state
    behavioral_signals = params.require(:behavioral_signals).permit!.to_h
    source = params[:source] || "manual_update"

    # Queue emotional state update
    EmotionalStateTrackingJob.perform_later(
      tenant_id: current_tenant.id,
      customer_identifier: @customer_emotional_state.customer_identifier,
      behavioral_signals: behavioral_signals,
      source: source
    )

    respond_to do |format|
      format.json do
        render json: {
          success: true,
          message: "Emotional state update queued for processing",
          customer_identifier: @customer_emotional_state.customer_identifier
        }
      end
      format.html do
        redirect_to emotional_intelligence_customer_path(@customer_emotional_state.customer_identifier),
                    notice: "Emotional state update queued for processing"
      end
    end
  end

  # POST /emotional_intelligence/customers/:customer_identifier/predict_response
  def predict_response
    campaign_id = params[:campaign_id]
    campaign = current_tenant.campaigns.find(campaign_id) if campaign_id

    if campaign
      prediction = @customer_emotional_state.predict_campaign_response(campaign)

      render json: {
        success: true,
        prediction: prediction,
        customer_emotion: @customer_emotional_state.current_emotion,
        compatibility_score: @customer_emotional_state.campaign_compatibility_score(campaign)
      }
    else
      render json: {
        success: false,
        error: "Campaign not found"
      }, status: :not_found
    end
  end

  # GET /emotional_intelligence/campaigns/:campaign_id/emotional_analysis
  def campaign_emotional_analysis
    @campaign = current_tenant.campaigns.find(params[:campaign_id])
    @page_title = "Emotional Analysis - #{@campaign.name}"

    # Get all customers and their emotional compatibility with this campaign
    @customer_compatibility = analyze_customer_emotional_compatibility(@campaign)

    # Get campaign emotional optimization suggestions
    @optimization_suggestions = generate_campaign_optimization_suggestions(@campaign)

    # Get emotional risk assessment
    @risk_assessment = assess_campaign_emotional_risks(@campaign)

    respond_to do |format|
      format.html
      format.json { render json: campaign_analysis_data }
    end
  end

  # POST /emotional_intelligence/campaigns/:campaign_id/apply_emotional_optimization
  def apply_emotional_optimization
    @campaign = current_tenant.campaigns.find(params[:campaign_id])
    optimization_type = params[:optimization_type]
    target_emotion = params[:target_emotion]

    case optimization_type
    when "emotional_targeting"
      result = apply_emotional_targeting_optimization(@campaign, target_emotion)
    when "timing_optimization"
      result = apply_timing_optimization(@campaign)
    when "content_adaptation"
      result = apply_content_adaptation(@campaign)
    else
      result = { success: false, error: "Unknown optimization type" }
    end

    render json: result
  end

  # GET /emotional_intelligence/insights
  def insights
    @page_title = "Emotional Intelligence Insights"

    # Get emotional trend analysis
    @emotional_trends = analyze_emotional_trends

    # Get prediction accuracy metrics
    @accuracy_metrics = get_prediction_accuracy_metrics

    # Get learning insights
    @learning_insights = get_learning_insights

    # Get improvement opportunities
    @improvement_opportunities = get_improvement_opportunities

    respond_to do |format|
      format.html
      format.json { render json: insights_data }
    end
  end

  # GET /emotional_intelligence/real_time_monitor
  def real_time_monitor
    @page_title = "Real-Time Emotional Monitoring"

    # Get currently active emotional states
    @active_states = current_tenant.customer_emotional_states
                                  .recent_interactions
                                  .includes(:tenant)
                                  .order(last_interaction_at: :desc)

    # Get campaigns requiring immediate attention
    @urgent_campaigns = find_urgent_emotional_interventions

    # Get real-time alerts
    @emotional_alerts = get_real_time_emotional_alerts

    respond_to do |format|
      format.html
      format.json { render json: real_time_data }
    end
  end

  # POST /emotional_intelligence/bulk_analyze
  def bulk_analyze
    customer_identifiers = params[:customer_identifiers] || []
    campaign_ids = params[:campaign_ids] || []

    if customer_identifiers.empty? || campaign_ids.empty?
      render json: { success: false, error: "Customer identifiers and campaign IDs required" }
      return
    end

    # Queue bulk analysis job
    BulkEmotionalAnalysisJob.perform_later(
      tenant_id: current_tenant.id,
      customer_identifiers: customer_identifiers,
      campaign_ids: campaign_ids,
      requested_by: current_user.id
    )

    render json: {
      success: true,
      message: "Bulk emotional analysis queued for processing",
      estimated_completion: 5.minutes.from_now
    }
  end

  private

  def set_customer_emotional_state
    customer_identifier = params[:customer_identifier] || params[:id]
    @customer_emotional_state = current_tenant.customer_emotional_states
                                            .find_by!(customer_identifier: customer_identifier)
  end

  def calculate_emotional_stats
    states = current_tenant.customer_emotional_states.recent_interactions

    {
      total_customers: states.count,
      emotion_distribution: states.group(:current_emotion).count,
      avg_confidence: states.average(:confidence_score)&.round(2) || 0,
      receptive_customers: states.select(&:receptive_to_marketing?).count,
      high_confidence_states: states.high_confidence.count
    }
  end

  def find_campaigns_needing_emotional_review
    current_tenant.campaigns
                  .active
                  .includes(:vibe_analysis_records)
                  .select do |campaign|
                    # Campaign needs review if:
                    # 1. No emotional analysis yet
                    # 2. Low emotional compatibility scores
                    # 3. High risk customers targeted

                    no_analysis = campaign.vibe_analysis_records.where(analysis_type: "emotional").empty?

                    if no_analysis
                      true
                    else
                      # Check for low compatibility with current customer emotional states
                      low_compatibility = check_campaign_emotional_compatibility(campaign) < 60.0
                      low_compatibility
                    end
                  end.first(10)
  end

  def check_campaign_emotional_compatibility(campaign)
    customer_states = current_tenant.customer_emotional_states.recent_interactions
    return 100.0 if customer_states.empty?

    compatibility_scores = customer_states.map do |state|
      state.campaign_compatibility_score(campaign)
    end

    compatibility_scores.sum / compatibility_scores.size.to_f
  end

  def get_recent_emotional_adaptations
    # Get recent alert logs for emotional adaptations
    current_tenant.alert_logs
                  .where(alert_type: [ "adaptive_campaign_adjustment", "high_urgency_emotional_adaptation" ])
                  .order(created_at: :desc)
                  .limit(10)
                  .map do |alert|
                    {
                      timestamp: alert.created_at,
                      type: alert.alert_type,
                      severity: alert.severity,
                      message: alert.message,
                      metadata: alert.metadata
                    }
                  end
  end

  def generate_emotional_insights
    insights = []

    # Analyze emotional trends
    states = current_tenant.customer_emotional_states.recent_interactions

    if states.any?
      # Most common emotion
      emotion_counts = states.group(:current_emotion).count
      most_common = emotion_counts.max_by { |_, count| count }

      if most_common
        insights << {
          type: "trend",
          title: "Dominant Customer Emotion",
          description: "#{most_common[0].humanize} is the most common emotional state (#{most_common[1]} customers)",
          action: "Consider campaigns that resonate with #{most_common[0]} emotion"
        }
      end

      # Low confidence alerts
      low_confidence_count = states.where("confidence_score < ?", 60.0).count
      if low_confidence_count > states.count * 0.3
        insights << {
          type: "alert",
          title: "Low Emotional Detection Confidence",
          description: "#{low_confidence_count} customers have low emotional detection confidence",
          action: "Collect more behavioral data for better emotional insights"
        }
      end

      # Receptivity insights
      receptive_count = states.select(&:receptive_to_marketing?).count
      receptivity_percentage = (receptive_count.to_f / states.count * 100).round(1)

      insights << {
        type: "opportunity",
        title: "Marketing Receptivity",
        description: "#{receptivity_percentage}% of customers are currently receptive to marketing",
        action: receptivity_percentage > 60 ? "Good time for promotional campaigns" : "Focus on supportive content"
      }
    end

    insights
  end

  def analyze_campaign_compatibility
    active_campaigns = current_tenant.campaigns.active.limit(10)

    active_campaigns.map do |campaign|
      compatibility_score = @customer_emotional_state.campaign_compatibility_score(campaign)
      prediction = @customer_emotional_state.predict_campaign_response(campaign)

      {
        campaign: {
          id: campaign.id,
          name: campaign.name,
          type: campaign.campaign_type
        },
        compatibility_score: compatibility_score,
        prediction: prediction,
        recommendation: generate_campaign_recommendation(compatibility_score, prediction)
      }
    end
  end

  def generate_campaign_recommendation(compatibility_score, prediction)
    if compatibility_score >= 70.0 && prediction[:predicted_engagement] >= 70.0
      "Excellent fit - proceed with campaign"
    elsif compatibility_score >= 50.0
      "Good fit with minor adjustments needed"
    elsif compatibility_score >= 30.0
      "Requires significant emotional adaptation"
    else
      "Poor fit - avoid or completely redesign approach"
    end
  end

  def generate_customer_recommendations
    adapter = EmotionalCampaignAdapter.new(
      emotional_state: @customer_emotional_state,
      tenant: current_tenant
    )

    real_time_recommendations = adapter.real_time_recommendations

    # Add customer-specific recommendations
    recommendations = real_time_recommendations[:recommendations] || []

    # Add timing recommendations
    approach = @customer_emotional_state.recommended_interaction_approach
    recommendations << {
      type: "timing",
      priority: "medium",
      action: "Optimal interaction timing: #{approach[:timing]}",
      details: "Based on current emotional state (#{@customer_emotional_state.current_emotion})"
    }

    # Add channel recommendations
    if @customer_emotional_state.current_emotion.in?(%w[anger disgust])
      recommendations << {
        type: "channel_warning",
        priority: "high",
        action: "Avoid all marketing channels temporarily",
        details: "Customer is in negative emotional state - focus on support only"
      }
    end

    recommendations
  end

  def analyze_emotional_trajectory
    return {} unless @customer_emotional_state.interaction_history.any?

    analyzer = EmotionalStateAnalyzer.new(
      customer_identifier: @customer_emotional_state.customer_identifier,
      behavioral_signals: @customer_emotional_state.behavioral_signals,
      interaction_history: @customer_emotional_state.interaction_history,
      tenant: current_tenant
    )

    analyzer.analyze_emotional_trajectory
  end

  def analyze_customer_emotional_compatibility(campaign)
    customer_states = current_tenant.customer_emotional_states
                                  .recent_interactions
                                  .includes(:tenant)

    compatibility_analysis = customer_states.map do |state|
      compatibility_score = state.campaign_compatibility_score(campaign)
      prediction = state.predict_campaign_response(campaign)

      {
        customer_identifier: state.customer_identifier,
        current_emotion: state.current_emotion,
        intensity: state.emotional_intensity,
        confidence: state.confidence_score,
        compatibility_score: compatibility_score,
        predicted_engagement: prediction[:predicted_engagement],
        risk_factors: prediction[:risk_factors].size,
        receptive: state.receptive_to_marketing?
      }
    end

    # Sort by compatibility score (best matches first)
    compatibility_analysis.sort_by { |analysis| -analysis[:compatibility_score] }
  end

  def generate_campaign_optimization_suggestions(campaign)
    suggestions = []

    # Analyze current emotional resonance
    emotional_analysis = campaign.vibe_analysis_records.find_by(analysis_type: "emotional")

    if emotional_analysis
      primary_emotion = emotional_analysis.analysis_data["primary_emotion"]
      resonance_score = emotional_analysis.vibe_score || 0

      if resonance_score < 60.0
        suggestions << {
          type: "emotional_intensity",
          priority: "high",
          suggestion: "Increase emotional resonance",
          details: "Current resonance score (#{resonance_score}) is below optimal threshold",
          implementation: "Review emotional triggers and messaging tone"
        }
      end

      # Check emotion-audience alignment
      customer_emotions = current_tenant.customer_emotional_states
                                      .recent_interactions
                                      .group(:current_emotion)
                                      .count

      if customer_emotions.any?
        most_common_emotion = customer_emotions.max_by { |_, count| count }.first

        if primary_emotion != most_common_emotion
          suggestions << {
            type: "emotion_alignment",
            priority: "medium",
            suggestion: "Consider targeting #{most_common_emotion} emotion",
            details: "Most customers are currently in #{most_common_emotion} state",
            implementation: "Adjust campaign emotional tone to match customer state"
          }
        end
      end
    else
      suggestions << {
        type: "missing_analysis",
        priority: "high",
        suggestion: "Perform emotional analysis",
        details: "Campaign lacks emotional intelligence analysis",
        implementation: "Run emotional resonance analysis before launch"
      }
    end

    suggestions
  end

  def assess_campaign_emotional_risks(campaign)
    risks = []

    # Check for emotional mismatches
    customer_states = current_tenant.customer_emotional_states.recent_interactions
    negative_emotion_customers = customer_states.where(current_emotion: %w[anger fear sadness disgust]).count

    if negative_emotion_customers > 0
      risk_percentage = (negative_emotion_customers.to_f / customer_states.count * 100).round(1)

      if risk_percentage > 30.0
        risks << {
          type: "negative_emotion_exposure",
          severity: "high",
          description: "#{risk_percentage}% of customers in negative emotional states",
          mitigation: "Delay campaign or switch to supportive messaging",
          affected_customers: negative_emotion_customers
        }
      end
    end

    # Check for low confidence states
    low_confidence_customers = customer_states.where("confidence_score < ?", 50.0).count
    if low_confidence_customers > customer_states.count * 0.4
      risks << {
        type: "low_confidence_predictions",
        severity: "medium",
        description: "High percentage of customers with uncertain emotional states",
        mitigation: "Collect more behavioral data before campaign launch",
        affected_customers: low_confidence_customers
      }
    end

    risks
  end

  def apply_emotional_targeting_optimization(campaign, target_emotion)
    # Apply emotional targeting optimization to campaign
    begin
      # Update campaign emotional targeting
      campaign.settings = campaign.settings.merge({
        emotional_optimization: {
          target_emotion: target_emotion,
          applied_at: Time.current,
          applied_by: current_user.id
        }
      })

      if campaign.save
        # Queue analysis of customers matching target emotion
        customers_with_target_emotion = current_tenant.customer_emotional_states
                                                    .where(current_emotion: target_emotion)
                                                    .count

        {
          success: true,
          message: "Emotional targeting optimization applied",
          target_emotion: target_emotion,
          matching_customers: customers_with_target_emotion
        }
      else
        {
          success: false,
          error: "Failed to save campaign optimization"
        }
      end
    rescue => error
      {
        success: false,
        error: "Optimization failed: #{error.message}"
      }
    end
  end

  def apply_timing_optimization(campaign)
    # Apply timing optimization based on customer emotional patterns
    begin
      # Analyze optimal timing for customer base
      customer_states = current_tenant.customer_emotional_states.recent_interactions

      # Calculate when most customers are in receptive states
      # This is a simplified version - production would use more sophisticated analysis

      optimal_timing = {
        immediate_send: customer_states.select(&:receptive_to_marketing?).count,
        delayed_send: customer_states.reject(&:receptive_to_marketing?).count
      }

      campaign.settings = campaign.settings.merge({
        timing_optimization: {
          analysis: optimal_timing,
          recommendation: optimal_timing[:immediate_send] > optimal_timing[:delayed_send] ? "send_now" : "delay_send",
          applied_at: Time.current,
          applied_by: current_user.id
        }
      })

      if campaign.save
        {
          success: true,
          message: "Timing optimization applied",
          recommendation: optimal_timing[:immediate_send] > optimal_timing[:delayed_send] ? "Send immediately" : "Delay sending",
          analysis: optimal_timing
        }
      else
        {
          success: false,
          error: "Failed to save timing optimization"
        }
      end
    rescue => error
      {
        success: false,
        error: "Timing optimization failed: #{error.message}"
      }
    end
  end

  def apply_content_adaptation(campaign)
    # Apply content adaptation based on customer emotional insights
    begin
      # Analyze customer emotional distribution
      customer_emotions = current_tenant.customer_emotional_states
                                      .recent_interactions
                                      .group(:current_emotion)
                                      .count

      dominant_emotion = customer_emotions.max_by { |_, count| count }&.first

      if dominant_emotion
        # Generate content adaptation recommendations
        adapter = EmotionalCampaignAdapter.new(
          emotional_state: current_tenant.customer_emotional_states.find_by(current_emotion: dominant_emotion),
          tenant: current_tenant,
          campaign: campaign
        )

        adjustments = adapter.generate_adjustments

        if adjustments[:success]
          campaign.settings = campaign.settings.merge({
            content_adaptation: {
              dominant_emotion: dominant_emotion,
              adjustments: adjustments[:adjustments],
              applied_at: Time.current,
              applied_by: current_user.id
            }
          })

          if campaign.save
            {
              success: true,
              message: "Content adaptation applied",
              dominant_emotion: dominant_emotion,
              adjustments_applied: adjustments[:adjustments].keys
            }
          else
            {
              success: false,
              error: "Failed to save content adaptation"
            }
          end
        else
          {
            success: false,
            error: "Failed to generate content adjustments: #{adjustments[:error]}"
          }
        end
      else
        {
          success: false,
          error: "No customer emotional data available for adaptation"
        }
      end
    rescue => error
      {
        success: false,
        error: "Content adaptation failed: #{error.message}"
      }
    end
  end

  def analyze_emotional_trends
    # Analyze emotional trends over time
    states = current_tenant.customer_emotional_states
                          .where("created_at >= ?", 30.days.ago)
                          .order(:created_at)

    # Group by week and analyze trends
    weekly_data = states.group_by_week(:created_at).group(:current_emotion).count

    {
      weekly_emotion_distribution: weekly_data,
      trend_analysis: calculate_trend_direction(weekly_data),
      dominant_emotions: states.group(:current_emotion).count.sort_by { |_, count| -count }.first(5)
    }
  end

  def calculate_trend_direction(weekly_data)
    # Simple trend calculation - compare recent weeks to earlier weeks
    # In production would use more sophisticated time series analysis

    recent_emotions = weekly_data.keys.select { |date| date >= 2.weeks.ago }
    earlier_emotions = weekly_data.keys.select { |date| date < 2.weeks.ago && date >= 4.weeks.ago }

    recent_positive = recent_emotions.sum { |date| weekly_data.dig(date, "joy") || 0 }
    earlier_positive = earlier_emotions.sum { |date| weekly_data.dig(date, "joy") || 0 }

    if recent_positive > earlier_positive
      "improving"
    elsif recent_positive < earlier_positive
      "declining"
    else
      "stable"
    end
  end

  def get_prediction_accuracy_metrics
    # Get accuracy metrics from learning data
    accuracy_record = current_tenant.emotional_resonance_profiles
                                  .find_by(name: "accuracy_metrics")

    if accuracy_record
      accuracy_record.settings["accuracy_metrics"] || {}
    else
      {
        overall_accuracy: 0,
        total_predictions: 0,
        by_emotion: {},
        by_confidence: {}
      }
    end
  end

  def get_learning_insights
    # Get insights from learning data
    opportunity_record = current_tenant.emotional_resonance_profiles
                                     .find_by(name: "improvement_opportunities")

    if opportunity_record
      opportunities = opportunity_record.settings["improvement_opportunities"] || []

      # Analyze patterns in opportunities
      {
        total_opportunities: opportunities.size,
        common_issues: analyze_common_issues(opportunities),
        recent_opportunities: opportunities.last(10)
      }
    else
      {
        total_opportunities: 0,
        common_issues: [],
        recent_opportunities: []
      }
    end
  end

  def analyze_common_issues(opportunities)
    return [] if opportunities.empty?

    issues = opportunities.flat_map { |opp| opp["improvement_areas"] || [] }
    issue_counts = issues.tally

    issue_counts.sort_by { |_, count| -count }.first(5).map do |issue, count|
      {
        issue: issue.humanize,
        count: count,
        percentage: (count.to_f / opportunities.size * 100).round(1)
      }
    end
  end

  def get_improvement_opportunities
    learning_insights = get_learning_insights
    accuracy_metrics = get_prediction_accuracy_metrics

    opportunities = []

    # Low accuracy opportunities
    if accuracy_metrics[:overall_accuracy] < 75.0
      opportunities << {
        type: "accuracy_improvement",
        priority: "high",
        description: "Overall prediction accuracy is below target (#{accuracy_metrics[:overall_accuracy]}%)",
        action: "Review and improve emotional detection algorithms"
      }
    end

    # Emotion-specific opportunities
    accuracy_metrics[:by_emotion]&.each do |emotion, metrics|
      if metrics["accuracy"] < 60.0
        opportunities << {
          type: "emotion_specific_improvement",
          priority: "medium",
          description: "#{emotion.humanize} emotion detection accuracy is low (#{metrics['accuracy']}%)",
          action: "Collect more training data for #{emotion} emotion patterns"
        }
      end
    end

    # Common issue opportunities
    learning_insights[:common_issues]&.each do |issue|
      if issue[:percentage] > 20.0
        opportunities << {
          type: "pattern_improvement",
          priority: "medium",
          description: "#{issue[:issue]} is a common issue (#{issue[:percentage]}% of cases)",
          action: "Address systematic issues with #{issue[:issue].downcase}"
        }
      end
    end

    opportunities
  end

  def find_urgent_emotional_interventions
    # Find campaigns that urgently need emotional intervention
    active_campaigns = current_tenant.campaigns.active
    urgent_campaigns = []

    active_campaigns.each do |campaign|
      # Check if campaign has high-risk emotional mismatches
      customer_states = current_tenant.customer_emotional_states.recent_interactions

      mismatched_customers = customer_states.select do |state|
        compatibility = state.campaign_compatibility_score(campaign)
        compatibility < 30.0 && state.current_emotion.in?(%w[anger disgust])
      end

      if mismatched_customers.any?
        urgent_campaigns << {
          campaign: campaign,
          risk_level: "high",
          affected_customers: mismatched_customers.size,
          recommended_action: "Pause campaign immediately",
          details: "#{mismatched_customers.size} customers in negative emotional states"
        }
      end
    end

    urgent_campaigns
  end

  def get_real_time_emotional_alerts
    # Get recent emotional alerts that need attention
    current_tenant.alert_logs
                  .where(alert_type: [
                    "high_urgency_emotional_adaptation",
                    "emotional_adaptation_failures",
                    "negative_emotion_spike"
                  ])
                  .where("created_at >= ?", 1.hour.ago)
                  .order(created_at: :desc)
                  .limit(10)
  end

  # Response data methods

  def dashboard_data
    {
      emotional_stats: @emotional_stats,
      campaigns_needing_review: @campaigns_needing_review.map { |c| campaign_summary(c) },
      recent_adaptations: @recent_adaptations,
      insights: @insights,
      last_updated: Time.current
    }
  end

  def customer_detail_data
    {
      customer: {
        identifier: @customer_emotional_state.customer_identifier,
        current_emotion: @customer_emotional_state.current_emotion,
        intensity: @customer_emotional_state.emotional_intensity,
        confidence: @customer_emotional_state.confidence_score,
        receptive: @customer_emotional_state.receptive_to_marketing?,
        last_interaction: @customer_emotional_state.last_interaction_at
      },
      emotional_journey: @emotional_journey,
      campaign_compatibility: @campaign_compatibility,
      recommendations: @recommendations,
      trajectory_analysis: @trajectory_analysis
    }
  end

  def campaign_analysis_data
    {
      campaign: campaign_summary(@campaign),
      customer_compatibility: @customer_compatibility,
      optimization_suggestions: @optimization_suggestions,
      risk_assessment: @risk_assessment,
      last_updated: Time.current
    }
  end

  def insights_data
    {
      emotional_trends: @emotional_trends,
      accuracy_metrics: @accuracy_metrics,
      learning_insights: @learning_insights,
      improvement_opportunities: @improvement_opportunities,
      last_updated: Time.current
    }
  end

  def real_time_data
    {
      active_states: @active_states.map { |state| customer_state_summary(state) },
      urgent_campaigns: @urgent_campaigns,
      emotional_alerts: @emotional_alerts.map { |alert| alert_summary(alert) },
      last_updated: Time.current
    }
  end

  def campaign_summary(campaign)
    {
      id: campaign.id,
      name: campaign.name,
      type: campaign.campaign_type,
      status: campaign.status,
      emotional_score: campaign.vibe_score || 0
    }
  end

  def customer_state_summary(state)
    {
      identifier: state.customer_identifier,
      emotion: state.current_emotion,
      intensity: state.emotional_intensity,
      confidence: state.confidence_score,
      receptive: state.receptive_to_marketing?,
      last_interaction: state.last_interaction_at
    }
  end

  def alert_summary(alert)
    {
      type: alert.alert_type,
      severity: alert.severity,
      message: alert.message,
      timestamp: alert.created_at,
      metadata: alert.metadata
    }
  end
end
