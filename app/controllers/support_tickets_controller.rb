# frozen_string_literal: true

class SupportTicketsController < ApplicationController
  before_action :authenticate_user!
  before_action :set_support_ticket, only: [ :show, :update, :close, :reopen ]

  def index
    @support_tickets = current_user.support_tickets
                                  .includes(:user)
                                  .order(created_at: :desc)
                                  .page(params[:page])
                                  .per(10)

    @ticket_stats = calculate_ticket_stats
    @filter_status = params[:status] || "all"

    # Filter by status if specified
    if @filter_status != "all"
      @support_tickets = @support_tickets.where(status: @filter_status)
    end
  end

  def show
    @messages = @support_ticket.support_messages.includes(:user).order(:created_at)
    @new_message = @support_ticket.support_messages.build
  end

  def create
    @support_ticket = current_user.support_tickets.build(support_ticket_params)
    @support_ticket.status = "open"
    @support_ticket.ticket_number = generate_ticket_number

    if @support_ticket.save
      # Create initial message
      @support_ticket.support_messages.create!(
        user: current_user,
        message: @support_ticket.description,
        message_type: "user"
      )

      redirect_to @support_ticket, notice: "Support ticket created successfully."
    else
      redirect_to contact_help_index_path, alert: "Failed to create support ticket."
    end
  end

  def update
    # Add a new message to the ticket
    @message = @support_ticket.support_messages.build(message_params)
    @message.user = current_user
    @message.message_type = "user"

    if @message.save
      # Update ticket status to 'waiting_for_support' if it was closed
      if @support_ticket.closed?
        @support_ticket.update(status: "waiting_for_support")
      end

      redirect_to @support_ticket, notice: "Message added successfully."
    else
      @messages = @support_ticket.support_messages.includes(:user).order(:created_at)
      @new_message = @message
      render :show, status: :unprocessable_entity
    end
  end

  def close
    if @support_ticket.update(status: "closed", closed_at: Time.current)
      redirect_to @support_ticket, notice: "Ticket closed successfully."
    else
      redirect_to @support_ticket, alert: "Failed to close ticket."
    end
  end

  def reopen
    if @support_ticket.update(status: "open", closed_at: nil)
      redirect_to @support_ticket, notice: "Ticket reopened successfully."
    else
      redirect_to @support_ticket, alert: "Failed to reopen ticket."
    end
  end

  private

  def set_support_ticket
    @support_ticket = current_user.support_tickets.find(params[:id])
  end

  def support_ticket_params
    params.require(:support_ticket).permit(:subject, :description, :category, :priority)
  end

  def message_params
    params.require(:support_message).permit(:message)
  end

  def generate_ticket_number
    # Generate a unique ticket number
    timestamp = Time.current.strftime("%Y%m%d")
    random_suffix = SecureRandom.hex(3).upcase
    "TKT-#{timestamp}-#{random_suffix}"
  end

  def calculate_ticket_stats
    tickets = current_user.support_tickets

    {
      total: tickets.count,
      open: tickets.where(status: "open").count,
      waiting_for_support: tickets.where(status: "waiting_for_support").count,
      closed: tickets.where(status: "closed").count,
      avg_response_time: "2 hours" # Placeholder
    }
  end
end
