# frozen_string_literal: true

namespace :assets do
  desc "Manage social media images for the application"
  namespace :social do
    desc "Generate JPG versions of SVG social media images"
    task convert_to_jpg: :environment do
      require "open3"

      assets_path = Rails.root.join("app", "assets", "images")
      svg_files = [
        "marketing-hub-og-image.svg",
        "marketing-hub-twitter-card.svg"
      ]

      puts "🔄 Converting SVG social media images to JPG..."

      svg_files.each do |svg_file|
        svg_path = assets_path.join(svg_file)
        jpg_path = assets_path.join(svg_file.gsub(".svg", ".jpg"))

        if File.exist?(svg_path)
          puts "📝 Processing #{svg_file}..."

          # Check if ImageMagick is available
          if system("which convert > /dev/null 2>&1")
            # Use ImageMagick if available
            stdout, stderr, status = Open3.capture3(
              "convert -background white -density 300 #{svg_path} #{jpg_path}"
            )

            if status.success?
              puts "✅ Created #{jpg_path.basename}"
            else
              puts "❌ Failed to convert #{svg_file}: #{stderr}"
              puts "💡 You can manually convert using online tools or install ImageMagick"
            end
          else
            puts "⚠️  ImageMagick not found. Install with: brew install imagemagick"
            puts "   Or use online conversion tool: https://convertio.co/svg-jpg/"
          end
        else
          puts "❌ SVG file not found: #{svg_file}"
        end
      end

      puts "\n🎉 Social media image conversion completed!"
    end

    desc "Validate that all required social media assets exist"
    task validate: :environment do
      assets_path = Rails.root.join("app", "assets", "images")
      required_files = [
        "marketing-hub-og-image.svg",
        "marketing-hub-twitter-card.svg"
      ]
      optional_files = [
        "marketing-hub-og-image.jpg",
        "marketing-hub-twitter-card.jpg"
      ]

      puts "🔍 Validating social media assets..."

      missing_required = []
      required_files.each do |file|
        file_path = assets_path.join(file)
        if File.exist?(file_path)
          puts "✅ #{file} - Found"
        else
          puts "❌ #{file} - Missing (Required)"
          missing_required << file
        end
      end

      optional_files.each do |file|
        file_path = assets_path.join(file)
        if File.exist?(file_path)
          puts "✅ #{file} - Found (Optional)"
        else
          puts "⚪ #{file} - Missing (Optional)"
        end
      end

      if missing_required.empty?
        puts "\n🎉 All required social media assets are present!"
      else
        puts "\n❌ Missing required assets: #{missing_required.join(', ')}"
        puts "💡 Run: rails assets:social:generate to create them"
        exit 1
      end
    end

    desc "Generate new social media images from templates"
    task regenerate: :environment do
      puts "🔄 Regenerating social media images..."
      SocialImageGeneratorService.new.generate_all
    end
  end
end
