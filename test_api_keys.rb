#!/usr/bin/env ruby
# Test script to verify AI provider API keys are properly configured

puts "🔍 Testing AI Provider Configuration..."
puts "=" * 50

# Test Rails credentials access
begin
  require_relative 'config/environment'
  
  puts "\n📋 Checking Rails Credentials:"
  puts "✅ Rails credentials loaded successfully"
  
  # Check each provider
  providers = {
    'OpenAI' => Rails.application.credentials.openai_api_key,
    'Anthropic' => Rails.application.credentials.anthropic_api_key,
    'Gemini' => Rails.application.credentials.gemini_api_key,
    'DeepSeek' => Rails.application.credentials.deepseek_api_key,
    'OpenRouter' => Rails.application.credentials.openrouter_api_key
  }
  
  puts "\n🔑 API Key Status:"
  providers.each do |provider, key|
    if key.present? && !key.include?('placeholder') && !key.include?('your_') && !key.include?('here')
      puts "✅ #{provider}: Configured (#{key[0..10]}...)"
    elsif key.present?
      puts "⚠️  #{provider}: Placeholder key detected"
    else
      puts "❌ #{provider}: Not configured"
    end
  end
  
  # Test environment variable fallbacks
  puts "\n🌍 Environment Variable Fallbacks:"
  env_vars = {
    'OpenAI' => ENV['OPENAI_API_KEY'],
    'Anthropic' => ENV['ANTHROPIC_API_KEY'],
    'Gemini' => ENV['GEMINI_API_KEY'],
    'DeepSeek' => ENV['DEEPSEEK_API_KEY'],
    'OpenRouter' => ENV['OPENROUTER_API_KEY']
  }
  
  env_vars.each do |provider, key|
    if key.present?
      puts "✅ #{provider}: Available in ENV (#{key[0..10]}...)"
    else
      puts "➖ #{provider}: Not set in ENV"
    end
  end
  
  # Test RubyLlmService configuration
  puts "\n🤖 Testing RubyLlmService:"
  begin
    tenant = Tenant.first
    if tenant
      service = RubyLlmService.new(tenant: tenant)
      health = service.provider_health_check
      
      puts "✅ RubyLlmService initialized successfully"
      puts "\n📊 Provider Health Check:"
      health.each do |provider, status|
        status_icon = status[:available] ? "✅" : "❌"
        puts "#{status_icon} #{provider.capitalize}: #{status[:available] ? 'Available' : 'Unavailable'}"
      end
    else
      puts "⚠️  No tenant found - create a tenant first"
    end
  rescue => e
    puts "❌ RubyLlmService error: #{e.message}"
  end
  
  puts "\n" + "=" * 50
  puts "🎯 Next Steps:"
  puts "1. Edit credentials: bundle exec rails credentials:edit"
  puts "2. Replace placeholder keys with real API keys"
  puts "3. Test email content generation in the app"
  puts "4. Check the AI Marketing Hub dashboard"
  
rescue => e
  puts "❌ Error loading Rails environment: #{e.message}"
  puts "\nMake sure you're in the Rails app directory and run:"
  puts "bundle install"
end
