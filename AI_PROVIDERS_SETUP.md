# AI Providers Configuration Guide

This guide explains how to configure multiple AI providers for the AI Marketing Hub application. The system supports OpenAI, Anthropic Claude, Google Gemini, DeepSeek, and OpenRouter.

## 🚀 Quick Setup (Development)

You need **at least one** AI provider configured. The system will automatically select the best provider based on task type, cost, and availability.

### 1. **Choose Your AI Providers**

**Recommended for beginners:** Start with OpenAI
**Cost-conscious:** Use DeepSeek or Gemini
**Enterprise:** Configure multiple providers for redundancy

### 2. **Get API Keys**

- **OpenAI**: Visit [OpenAI Platform](https://platform.openai.com/api-keys)
- **Anthropic**: Visit [Anthropic Console](https://console.anthropic.com/)
- **Google Gemini**: Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
- **DeepSeek**: Visit [DeepSeek Platform](https://platform.deepseek.com/api_keys)
- **OpenRouter**: Visit [OpenRouter](https://openrouter.ai/keys)

### 3. **Set up Environment Variables**
```bash
# Copy the example file
cp .env.example .env

# Edit the .env file and add your API keys
# You only need to configure the providers you want to use

# OpenAI (Recommended for most tasks)
echo "OPENAI_API_KEY=sk-your_actual_openai_key_here" >> .env

# Anthropic Claude (Great for complex reasoning)
echo "ANTHROPIC_API_KEY=sk-ant-your_actual_anthropic_key_here" >> .env

# Google Gemini (Cost-effective for high-volume)
echo "GEMINI_API_KEY=your_actual_gemini_key_here" >> .env

# DeepSeek (Most cost-effective)
echo "DEEPSEEK_API_KEY=your_actual_deepseek_key_here" >> .env

# OpenRouter (Access to multiple models)
echo "OPENROUTER_API_KEY=sk-or-your_actual_openrouter_key_here" >> .env
```

### 4. **Test the Configuration**
```bash
# Test all configured providers
ruby test_ai_providers_config.rb
```

## 🏭 Production Setup (Rails Credentials)

For production environments, use Rails encrypted credentials:

1. **Edit Rails Credentials**

   ```bash
   rails credentials:edit
   ```

2. **Add AI Provider API Keys**

   ```yaml
   # Add your AI provider keys to the credentials file
   openai_api_key: sk-your_actual_openai_key_here
   anthropic_api_key: sk-ant-your_actual_anthropic_key_here
   gemini_api_key: your_actual_gemini_key_here
   deepseek_api_key: your_actual_deepseek_key_here
   openrouter_api_key: sk-or-your_actual_openrouter_key_here

   # Keep existing content like:
   secret_key_base: your_existing_secret_key_base
   ```

3. **Deploy with Master Key**
   - Ensure `config/master.key` is available in production
   - Or set `RAILS_MASTER_KEY` environment variable

## 🔄 Configuration Priority

The application checks for API keys in this order:

1. **Tenant-specific settings** (highest priority)
   - Configured per-tenant in the admin interface

2. **Rails Credentials** (recommended for production)

   ```ruby
   Rails.application.credentials.openai_api_key
   Rails.application.credentials.anthropic_api_key
   # etc.
   ```

3. **Environment Variables** (good for development)

   ```ruby
   ENV["OPENAI_API_KEY"]
   ENV["ANTHROPIC_API_KEY"]
   # etc.
   ```

## 🤖 Provider Selection Strategies

The system automatically selects the best AI provider based on:

- **Task Type**: Different providers excel at different tasks
- **Cost Optimization**: Automatically chooses cost-effective options
- **Availability**: Falls back to available providers if primary is down
- **Budget**: Respects campaign budget constraints

### Task-Based Provider Selection

- **Creative Content**: OpenAI GPT-4o, Claude 3.5 Sonnet
- **Data Analysis**: Gemini 1.5 Pro, Claude 3 Opus
- **Cost-Sensitive**: DeepSeek, Gemini Flash, GPT-4o Mini
- **Complex Reasoning**: Claude 3 Opus, GPT-4o, Gemini Pro

## 🔧 Troubleshooting

### Error: "No AI providers configured"

This means none of the AI providers have been set up:

- Configure at least one AI provider using the Quick Setup guide above
- Check that your API keys are correctly formatted
- Verify the keys are not placeholder values

### Error: "API error: 401 Unauthorized"

This means your API key is invalid or expired:

- **OpenAI**: Check your key starts with `sk-` and verify credits
- **Anthropic**: Check your key starts with `sk-ant-` and verify account status
- **Gemini**: Verify your API key and Google Cloud project settings
- **DeepSeek**: Check your account balance and key validity
- **OpenRouter**: Verify your key starts with `sk-or-` and check credits

### Error: "Rate limit exceeded"

Your API usage has exceeded the rate limits:

- Wait a few minutes and try again
- The system will automatically switch to alternative providers
- Consider upgrading your plan with the affected provider
- Configure multiple providers for better redundancy

### Error: "No available providers for task type"

The system couldn't find a suitable provider:

- Configure additional AI providers
- Check that your providers support the required capabilities
- Verify provider health status in the application logs

## 🔒 Security Best Practices

1. **Never commit API keys to version control**
2. **Use different API keys for different environments**
3. **Regularly rotate your API keys**
4. **Monitor your API usage and costs across all providers**
5. **Use Rails credentials for production deployments**
6. **Set up billing alerts for each provider**

## 🧪 Testing the Setup

After configuration, test with:

```ruby
# In Rails console - Test multi-provider system
tenant = Tenant.first # or create a test tenant
service = RubyLlmService.new(tenant: tenant)
response = service.generate_content("Hello, this is a test!", task_type: :creative_content)
puts "Content: #{response.content}"
puts "Provider: #{response.provider}"
puts "Model: #{response.model}"
puts "Cost: $#{response.cost}"
```

Or use the comprehensive test script:

```bash
ruby test_ai_providers_config.rb
```

## 💰 Cost Optimization

The system automatically optimizes costs by:

- **Provider Selection**: Chooses the most cost-effective provider for each task
- **Model Selection**: Uses appropriate models based on complexity requirements
- **Budget Awareness**: Respects campaign budget constraints
- **Fallback Strategy**: Switches to cheaper alternatives when needed

### Estimated Costs (per 1K tokens):

- **DeepSeek**: ~$0.00014 (most economical)
- **Gemini Flash**: ~$0.00035
- **GPT-4o Mini**: ~$0.0015
- **Gemini Pro**: ~$0.0035
- **Claude 3.5 Sonnet**: ~$0.015
- **GPT-4o**: ~$0.03
- **Claude 3 Opus**: ~$0.075 (highest quality)
