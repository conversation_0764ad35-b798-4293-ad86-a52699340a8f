# ErrorHandlingService for centralized error handling across AI services
class ErrorHandlingService
  class << self
    def log_and_process(error:, context: {}, error_code: nil, retry_strategy: nil)
      # Log the error with context
      Rails.logger.error "#{error_code || 'ERROR'}: #{error.message}"
      Rails.logger.error "Context: #{context.inspect}"
      Rails.logger.error "Backtrace: #{error.backtrace[0..5].join("\n")}" if error.backtrace
      
      # Track error metrics
      track_error_metrics(error_code, context)
      
      # Apply retry strategy if specified
      apply_retry_strategy(error, retry_strategy, context) if retry_strategy
      
      # Notify relevant parties based on severity
      notify_if_critical(error, error_code, context)
      
      # Return structured error information
      {
        error_code: error_code,
        error_class: error.class.name,
        message: error.message,
        context: context,
        recovery_options: recovery_options_for(error, error_code)
      }
    end
    
    def user_friendly_message(error, default_message = "An unexpected error occurred")
      # Map error types to user-friendly messages
      case error
      when RubyLlmService::BudgetExceededError
        "AI usage limit reached. Please try again later or contact support to increase your limit."
      when RubyLlmService::RateLimitError
        "Our AI service is experiencing high demand. Please try again in a few minutes."
      when RubyLlmService::TokenLimitError
        "The content is too large for our AI to process. Please try with a smaller amount of text."
      when RubyLlmService::ProviderError
        "Our AI service is temporarily unavailable. We're working to restore service."
      when JSON::ParserError
        "We had trouble processing the AI response. Our team has been notified."
      else
        default_message
      end
    end
    
    def recovery_options_for(error, error_code)
      # Determine possible recovery actions based on error type
      case error
      when RubyLlmService::BudgetExceededError
        ['contact_support', 'upgrade_plan']
      when RubyLlmService::RateLimitError
        ['retry_later', 'use_template']
      when RubyLlmService::TokenLimitError
        ['reduce_content', 'split_request']
      when RubyLlmService::ProviderError
        ['retry', 'use_alternative_provider', 'manual_input']
      when JSON::ParserError
        ['retry', 'manual_input']
      else
        ['retry', 'contact_support']
      end
    end
    
    private
    
    def track_error_metrics(error_code, context)
      # Track error frequency and patterns
      service = context[:service] || 'unknown'
      tenant_id = context[:tenant_id] || 'unknown'
      
      # This would integrate with your metrics system (Prometheus, StatsD, etc.)
      # Example with Datadog:
      # Datadog::Statsd.increment('ai_marketing.errors', tags: ["error_code:#{error_code}", "service:#{service}", "tenant:#{tenant_id}"])
    end
    
    def apply_retry_strategy(error, strategy, context)
      case strategy
      when :exponential_backoff
        # Implement exponential backoff logic
        # Would typically use a background job with increasing delay
      when :failover
        # Try alternative providers
        # Would implement provider failover logic
      when :circuit_breaker
        # Update circuit breaker state
        # Would integrate with existing CircuitBreaker service
      end
    end
    
    def notify_if_critical(error, error_code, context)
      # Determine if this error requires immediate attention
      is_critical = [
        RubyLlmService::BudgetExceededError,
        RubyLlmService::ConfigurationError
      ].any? { |error_class| error.is_a?(error_class) }
      
      # Critical errors create an alert
      if is_critical
        AlertLog.create!(
          severity: 'critical',
          message: "#{error_code}: #{error.message}",
          source: context[:service] || 'AI Service',
          metadata: context,
          tenant_id: context[:tenant_id]
        )
        
        # Could also send immediate notifications via email/Slack/etc.
      end
    end
  end
end
